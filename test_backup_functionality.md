# Backup Server Functionality Test Guide

## Overview
This guide explains how to test the newly implemented backup server functionality.

## Features Implemented

### 1. **Web UI Enhancement**
- Added backup IP input field in General Settings
- Field only appears when server type is set to "Primary"
- Located below the Server Type dropdown

### 2. **Backend API Endpoints**
- `GET /api/backup-ip` - Retrieve current backup IP
- `POST /api/backup-ip` - Set backup IP and establish WebSocket connection
- `POST /api/test-backup-server` - Test connectivity to backup server
- `GET /api/health-status` - Get health monitoring status

### 3. **WebSocket Communication**
- Primary server connects to backup server via WebSocket
- Sends role change messages to switch backup server role
- Handles role change responses

### 4. **File-based Configuration**
- Backup IP stored in `/tmp/backup_ip` (not in database)
- Primary IP stored in `/tmp/primary_ip` (for health monitoring)
- Server roles stored in `/tmp/primary` and `/tmp/backup` files

### 5. **Health Monitoring**
- Backup servers automatically monitor primary server health
- Checks primary server every 10 seconds
- Auto-promotes to primary after 3 consecutive failures
- Stores primary server IP when receiving role change messages

## Testing Scenario

### Setup
1. **Server A (Primary)**: Start the application normally
2. **Server B (Backup)**: Start another instance on a different port or machine

### Test Steps

1. **Initial State**
   - Both servers start as "primary" by default
   - Check `/tmp/primary` file exists on both servers

2. **Configure Backup IP**
   - Open Server A's web interface
   - Go to Admin → General Settings
   - Set Server Type to "Primary"
   - Enter Server B's IP address in "Backup Server IP" field
   - Click Save (system will automatically test connectivity first)

3. **Verify Connection**
   - System will show toast notification: "Testing backup server connectivity..."
   - If successful: "✅ Successfully connected to backup server (Type: primary/backup)"
   - If failed: Clean error messages like:
     - "❌ Connection timeout - backup server is not responding"
     - "❌ Connection refused - backup server is not running"
     - "❌ Invalid IP address or hostname"
     - "❌ Network unreachable - check IP address and network connectivity"
   - Check Server A logs for WebSocket connection messages
   - Check Server B logs for incoming WebSocket connection
   - Verify Server B role changes to "backup"
   - Check `/tmp/backup` file exists on Server B

4. **Test Role Change Message**
   - Server A should send: `{"type": "role_change", "data": {"new_role": "backup"}}`
   - Server B should respond with success confirmation
   - Server B should switch from primary to backup role
   - Server B should start health monitoring of Server A

5. **Test Health Monitoring**
   - Server B should start monitoring Server A every 10 seconds
   - Check health status via `GET /api/health-status`
   - Stop Server A to simulate failure
   - After 3 failed checks (~30 seconds), Server B should auto-promote to primary

## Expected Behavior

### Server A (Primary)
- Maintains primary role
- Establishes WebSocket client connection to Server B
- Sends role change message to Server B
- Receives confirmation response

### Server B (Backup)
- Receives WebSocket connection from Server A
- Processes role change message
- Switches role from primary to backup
- Stores Server A's IP for health monitoring
- Starts health monitoring of Server A
- Sends success response back to Server A
- Auto-promotes to primary if Server A becomes unreachable

## File Locations

### Configuration Files
- `/tmp/primary` - Primary server flag
- `/tmp/backup` - Backup server flag
- `/tmp/backup_ip` - Backup server IP address (stored by primary)
- `/tmp/primary_ip` - Primary server IP address (stored by backup for monitoring)

### Code Files Modified
- `web/frontend/src/components/admin/GeneralSettings.tsx`
- `web/frontend/src/api/websocket.ts`
- `web/backend/utils/server_type.go`
- `web/backend/api/websocket/websocket_handler.go`
- `web/backend/api/server_type.go`
- `web/backend/service/websocket_client/client.go`
- `web/backend/main.go`

## Troubleshooting

### Common Issues
1. **Connection Failed**: Check if backup server is running and accessible
2. **Role Not Changed**: Verify WebSocket message handling in logs
3. **UI Not Showing**: Ensure server type is set to "Primary"

### Log Messages to Look For
- "Testing backup server connectivity..."
- "✅ Successfully connected to backup server (Type: primary/backup)"
- Clean error messages:
  - "❌ Connection timeout - backup server is not responding"
  - "❌ Connection refused - backup server is not running"
  - "❌ Invalid IP address or hostname"
  - "❌ Network unreachable - check IP address and network connectivity"
- "Connecting to backup server WebSocket: ws://[IP]:8080/ws"
- "Successfully connected to backup server: [IP]"
- "Received role change request: backup"
- "Storing primary server IP for health monitoring: [IP]"
- "Successfully changed server role to: backup"
- "Health monitor: Starting to monitor primary server at [IP]"
- "Health monitor: Primary server [IP] health check failed (attempt X/3)"
- "Health monitor: Primary server [IP] is down after 3 attempts. Promoting to primary..."
- "Health monitor: Successfully promoted to primary server"

## Security Considerations
- WebSocket connections are not authenticated in this implementation
- Consider adding authentication for production use
- Backup IP is stored in plain text in `/tmp/backup_ip`
