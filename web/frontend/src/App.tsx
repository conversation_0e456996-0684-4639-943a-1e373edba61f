import { Routes, Route } from "react-router-dom";
import { useState, useEffect } from "react";
import Layout from "./components/Layout";
import HomePage from "./pages/HomePage";
import RecorderPage from "./pages/RecorderPage";
import FileManagerPage from "./pages/FileManagerPage";
import AdminPage from "./pages/AdminPage";
import SystemPage from "./pages/SystemPage";
import LogsPage from "./pages/LogsPage";
import NotFoundPage from "./pages/NotFoundPage";
import SchedulersPage from "./pages/SchedulersPage";
import SchedulerPage from "./pages/SchedulerPage";
import AnalyticsPage from "./pages/AnalyticsPage";
import LoginPage from "./pages/LoginPage";
import RegisterPage from "./pages/RegisterPage";
import ProtectedRoute from "./components/ProtectedRoute";
import AdminRoute from "./components/AdminRoute";
import GlobalUploadStatus from "./components/upload/GlobalUploadStatus";
import BackupServerModal from "./components/BackupServerModal";
import { useUploadRestoration } from "./hooks/useUploadRestoration";

function App() {
  // Restore upload state from localStorage on app load
  useUploadRestoration();

  const [serverType, setServerType] = useState<string | null>(null);
  const [isLoadingServerType, setIsLoadingServerType] = useState(true);

  // Check server type when app loads
  useEffect(() => {
    const checkServerType = async () => {
      try {
        const response = await fetch('/api/server-type');
        const data = await response.json();
        setServerType(data.server_type);
      } catch (error) {
        console.error('Failed to fetch server type:', error);
        // Default to primary if API call fails
        setServerType('primary');
      } finally {
        setIsLoadingServerType(false);
      }
    };

    checkServerType();

    // Check server type every 30 seconds to detect changes
    const interval = setInterval(checkServerType, 30000);
    return () => clearInterval(interval);
  }, []);

  // Show loading state while checking server type
  if (isLoadingServerType) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        backgroundColor: '#f8fafc'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ 
            width: '40px', 
            height: '40px', 
            border: '4px solid #e2e8f0', 
            borderTop: '4px solid #3b82f6',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 16px'
          }}></div>
          <p style={{ color: '#6b7280', margin: 0 }}>Loading...</p>
        </div>
      </div>
    );
  }

  // Show backup server modal if server type is backup
  if (serverType === 'backup') {
    return <BackupServerModal isVisible={true} />;
  }

  // Show normal application if server type is primary
  return (
    <div style={{ position: "relative", minHeight: "100vh" }}>
      <Routes>
        {/* Public routes */}
        <Route path="/login" element={<LoginPage />} />
        <Route path="/register" element={<RegisterPage />} />

        {/* Protected routes */}
        <Route
          path="/"
          element={
            <ProtectedRoute>
              <Layout />
            </ProtectedRoute>
          }
        >
          <Route index element={<HomePage />} />
          <Route path="recorder" element={<RecorderPage />} />
          <Route path="file_manager" element={<FileManagerPage />} />
          <Route path="scheduler" element={<SchedulersPage />} />
          <Route path="scheduler/:id" element={<SchedulerPage />} />
          <Route path="analytics" element={<AnalyticsPage />} />
          <Route path="system" element={<SystemPage />} />
          <Route path="logs" element={<LogsPage />} />
          <Route
            path="admin"
            element={
              <AdminRoute>
                <AdminPage />
              </AdminRoute>
            }
          />
          <Route path="*" element={<NotFoundPage />} />
        </Route>
      </Routes>

      {/* Global upload status notification - shows on all pages */}
      <GlobalUploadStatus />

      {/* Add CSS for loading spinner animation */}
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}

export default App;
