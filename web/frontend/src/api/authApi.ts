import api from './axios';
import { LoginResponse, User, UserInput } from '@/types/auth';

// Login user
export const login = async (username: string, password: string): Promise<LoginResponse> => {
  const response = await api.post<LoginResponse>('/auth/login', { username, password });
  return response.data;
};

// Register new user
export const register = async (username: string, email: string, password: string): Promise<LoginResponse> => {
  const response = await api.post<LoginResponse>('/auth/register', {
    username,
    email,
    password,
    role: 'user' // Role is required by the backend
  });
  return response.data;
};

// Get current user
export const getCurrentUser = async (): Promise<User> => {
  const response = await api.get<User>('/users/me');
  return response.data;
};

// Get all users (admin only)
export const getUsers = async (): Promise<User[]> => {
  const response = await api.get<User[]>('/users');
  return response.data;
};

// Update user (admin only)
export const updateUser = async (id: number, userData: UserInput): Promise<User> => {
  const response = await api.put<User>(`/users/${id}`, userData);
  return response.data;
};

// Delete user (admin only)
export const deleteUser = async (id: number): Promise<void> => {
  await api.delete(`/users/${id}`);
};

// Get user by ID (admin only)
export const getUserById = async (id: number): Promise<User> => {
  const response = await api.get<User>(`/users/${id}`);
  return response.data;
};

// Create user (admin only)
export const createUser = async (user: UserInput): Promise<User> => {
  const response = await api.post<User>('/users', user);
  return response.data;
};
