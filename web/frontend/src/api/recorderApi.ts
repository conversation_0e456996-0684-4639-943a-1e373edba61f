import api from "./axios";
import {
  Recorder,
  RecorderInput,
  RecorderListResult,
  RecorderStatus,
  RtpUrl,
  ServiceInfo,
  VideoInfo,
  AudioInfo,
  RtpSender,
} from "../types/recorder";
import { NetworkInterface } from "../types/network";

// Get recorders with pagination
export const getRecorders = async (
  page: number = 1,
  limit: number = 10
): Promise<RecorderListResult> => {
  const response = await api.get<RecorderListResult>(
    `/recorders?page=${page}&limit=${limit}`
  );
  return response.data;
};

// Get a recorder by ID
export const getRecorderById = async (id: number): Promise<Recorder> => {
  const response = await api.get<Recorder>(`/recorders/${id}`);
  return response.data;
};

// Create a new recorder
export const createRecorder = async (
  recorder: RecorderInput
): Promise<Recorder> => {
  const response = await api.post<Recorder>("/recorders", recorder);
  return response.data;
};

// Update a recorder
export const updateRecorder = async (
  id: number,
  recorder: RecorderInput
): Promise<Recorder> => {
  const response = await api.put<Recorder>(`/recorders/${id}`, recorder);
  return response.data;
};

// Delete a recorder
export const deleteRecorder = async (id: number): Promise<void> => {
  await api.delete(`/recorders/${id}`);
};

// Start a recorder
export const startRecorder = async (
  id: number,
  serviceId?: number,
  sourceIP?: string
): Promise<void> => {
  const requestBody: any = {};

  if (serviceId !== undefined) {
    requestBody.service_id = serviceId;
  }

  if (sourceIP !== undefined) {
    requestBody.source_ip = sourceIP;
  }

  await api.post(
    `/recorders/${id}/start`,
    Object.keys(requestBody).length > 0 ? requestBody : {}
  );
};

// Stop a recorder
export const stopRecorder = async (id: number): Promise<void> => {
  await api.post(`/recorders/${id}/stop`);
};

// Mark a recorder as failed
export const failRecorder = async (id: number): Promise<void> => {
  await api.post(`/recorders/${id}/fail`);
};

// Join a recorder stream
export const joinRecorder = async (id: number): Promise<void> => {
  await api.post(`/recorders/${id}/join`);
};

// Unjoin a recorder stream
export const unjoinRecorder = async (id: number): Promise<void> => {
  await api.post(`/recorders/${id}/unjoin`);
};

// Get recorder status
export const getRecorderStatus = async (): Promise<RecorderStatus[]> => {
  const response = await api.get<RecorderStatus[]>("/recorders/status");
  return response.data;
};

// Get RTP URLs
export const getRtpUrls = async (): Promise<RtpUrl[]> => {
  const response = await api.get<RtpUrl[]>("/rtp-urls");
  return response.data;
};

// Get network interfaces
export const getNetworkInterfaces = async (): Promise<NetworkInterface[]> => {
  const response = await api.get<NetworkInterface[]>("/network/interfaces");
  return response.data;
};

// Import recordings to file manager
export const importRecordings = async (): Promise<{
  message: string;
  imported_count: number;
  total_files: number;
}> => {
  const response = await api.post<{
    message: string;
    imported_count: number;
    total_files: number;
  }>("/recorders/import-recordings");
  return response.data;
};

// Get services for a recorder
export const getRecorderServices = async (
  id: number
): Promise<{
  services: ServiceInfo[];
  ts_sync: boolean;
  video_info?: VideoInfo;
  audio_info?: AudioInfo;
}> => {
  const response = await api.get<{
    services: ServiceInfo[];
    ts_sync: boolean;
    video_info?: VideoInfo;
    audio_info?: AudioInfo;
  }>(`/recorders/${id}/services`);
  return response.data;
};

// Get RTP senders for a multicast address
export const getRtpSenders = async (
  multicastAddr: string,
  port: number,
  networkInterface?: string
): Promise<{
  multicast_addr: string;
  port: number;
  network_interface?: string;
  senders: RtpSender[];
}> => {
  const requestData: any = {
    multicast_addr: multicastAddr,
    port: port,
  };

  if (networkInterface) {
    requestData.network_interface = networkInterface;
  }

  const response = await api.post<{
    multicast_addr: string;
    port: number;
    network_interface?: string;
    senders: RtpSender[];
  }>("/rtp-senders", requestData);
  return response.data;
};
