import api from './axios';
import { SystemStats, AlarmThresholds } from '../types/system';

// Get current system statistics
export const getSystemStats = async (): Promise<SystemStats> => {
  const response = await api.get('/system/stats');
  return response.data;
};

// Get current alarm thresholds
export const getAlarmThresholds = async (): Promise<AlarmThresholds> => {
  const response = await api.get('/system/thresholds');
  return response.data;
};

// Update alarm thresholds
export const updateAlarmThresholds = async (thresholds: AlarmThresholds): Promise<void> => {
  await api.put('/system/thresholds', thresholds);
};
