import axios from './axios';

export interface LogLevel {
  DEBUG: 'debug';
  INFO: 'info';
  WARN: 'warn';
  ERROR: 'error';
  FATAL: 'fatal';
}

export interface LogEntry {
  id: number;
  timestamp: string;
  level: 'debug' | 'info' | 'warn' | 'error' | 'fatal';
  message: string;
  source: string;
  user_id?: number;
  request_id?: string;
  metadata?: string;
  created_at: string;
}

export interface LogFilter {
  start_date?: string;
  end_date?: string;
  level?: 'debug' | 'info' | 'warn' | 'error' | 'fatal';
  source?: string;
  user_id?: number;
  search?: string;
  limit?: number;
  offset?: number;
}

export interface LogStats {
  total_logs: number;
  logs_by_level: Record<string, number>;
  logs_by_source: Record<string, number>;
  last_log_time?: string;
}

export interface LogExportRequest {
  format: 'csv' | 'json';
  filter: LogFilter;
}

export interface LogExportResponse {
  download_url: string;
  expires_at: string;
}

export interface LogsResponse {
  logs: LogEntry[];
  total_count: number;
  limit: number;
  offset: number;
}

export interface LogSourcesResponse {
  sources: string[];
}

// API functions
export const logsApi = {
  // Get logs with filtering and pagination
  getLogs: async (filter: LogFilter = {}): Promise<LogsResponse> => {
    const params = new URLSearchParams();
    
    Object.entries(filter).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value.toString());
      }
    });

    const response = await axios.get(`/logs?${params.toString()}`);
    return response.data;
  },

  // Get log statistics
  getLogStats: async (filter: LogFilter = {}): Promise<LogStats> => {
    const params = new URLSearchParams();
    
    Object.entries(filter).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value.toString());
      }
    });

    const response = await axios.get(`/logs/stats?${params.toString()}`);
    return response.data;
  },

  // Get available log sources
  getLogSources: async (): Promise<LogSourcesResponse> => {
    const response = await axios.get('/logs/sources');
    return response.data;
  },

  // Export logs
  exportLogs: async (request: LogExportRequest): Promise<LogExportResponse> => {
    const response = await axios.post('/logs/export', request);
    return response.data;
  },

  // Cleanup old logs (admin only)
  cleanupOldLogs: async (retentionDays: number): Promise<{ message: string; deleted_count: number; retention_days: number }> => {
    const response = await axios.delete(`/logs/cleanup?retention_days=${retentionDays}`);
    return response.data;
  },
};

// WebSocket connection for real-time log streaming
export class LogsWebSocket {
  private ws: WebSocket | null = null;
  private url: string;
  private token: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private isConnecting = false;

  constructor() {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    this.url = `${protocol}//${host}/ws/logs`;
    this.token = localStorage.getItem('token') || '';
  }

  connect(onMessage: (data: any) => void, onError?: (error: Event) => void): void {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      return;
    }

    this.isConnecting = true;

    try {
      // Add token to WebSocket URL for authentication
      const wsUrl = `${this.url}?token=${encodeURIComponent(this.token)}`;
      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        console.log('Connected to logs WebSocket');
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        
        // Send subscription message
        this.send({
          type: 'subscribe',
          filter: {} // Can be customized later
        });
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          onMessage(data);
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      this.ws.onclose = (event) => {
        console.log('Logs WebSocket connection closed:', event.code, event.reason);
        this.isConnecting = false;
        this.ws = null;

        // Attempt to reconnect if not a normal closure
        if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
          setTimeout(() => {
            this.reconnectAttempts++;
            console.log(`Attempting to reconnect to logs WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            this.connect(onMessage, onError);
          }, this.reconnectDelay * this.reconnectAttempts);
        }
      };

      this.ws.onerror = (error) => {
        console.error('Logs WebSocket error:', error);
        this.isConnecting = false;
        if (onError) {
          onError(error);
        }
      };
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      this.isConnecting = false;
      if (onError) {
        onError(error as Event);
      }
    }
  }

  send(message: any): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    }
  }

  disconnect(): void {
    if (this.ws) {
      this.ws.close(1000, 'User disconnected');
      this.ws = null;
    }
    this.reconnectAttempts = this.maxReconnectAttempts; // Prevent reconnection
  }

  isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }
}

export default logsApi;
