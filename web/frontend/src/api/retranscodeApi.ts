import axios from "./axios";

export interface RetranscodeJob {
  id: number;
  file_id: number;
  status: "pending" | "processing" | "completed" | "failed";
  progress: number;
  error_message?: string;
  started_at?: string;
  completed_at?: string;
  filename?: string;
}

export interface RetranscodeStatus {
  is_active: boolean;
  total_jobs: number;
  completed_jobs: number;
  failed_jobs: number;
  overall_progress: number;
  current_job?: RetranscodeJob;
}

export interface StartRetranscodeResponse {
  message: string;
  jobs_created: number;
}

// Start retranscoding process for all files
export const startRetranscoding =
  async (): Promise<StartRetranscodeResponse> => {
    const response = await axios.post("/admin/retranscode/start");
    return response.data;
  };

// Get current retranscoding status
export const getRetranscodeStatus = async (): Promise<RetranscodeStatus> => {
  const response = await axios.get("/admin/retranscode/status");
  return response.data;
};

// Get list of retranscode jobs
export const getRetranscodeJobs = async (): Promise<RetranscodeJob[]> => {
  const response = await axios.get("/admin/retranscode/jobs");
  return response.data;
};

// Activate new codec settings (replace original files with transcoded ones)
export const activateNewCodecSettings = async (): Promise<{
  message: string;
}> => {
  const response = await axios.post("/admin/retranscode/activate");
  return response.data;
};
