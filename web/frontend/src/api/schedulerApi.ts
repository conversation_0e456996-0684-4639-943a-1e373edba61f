import api from "./axios";
import {
  Guide,
  IFile,
  IFolder,
  Schedule,
  ScheduleListResult,
  SchedulerStatus,
  Element as IElement, FileInfo,
} from "@/types/schedule.ts";

interface IConvertItem {
  filename: string;
  location: string;
}

export const getSchedules = async (
  page: number = 1,
  limit: number = 10
): Promise<ScheduleListResult> => {
  const response = await api.get<ScheduleListResult>(
    `/schedule?page=${page}&limit=${limit}`
  );
  return response.data;
};

export const createSchedule = async (schedule: Schedule): Promise<Schedule> => {
  const response = await api.post<Schedule>(`/schedule`, schedule);
  return response.data;
};

export const getSchedule = async (id: number): Promise<Schedule> => {
  const response = await api.get<Schedule>(`/schedule/${id}`);
  return response.data;
};

export const updateSchedule = async (
  id: number,
  schedule: Schedule
): Promise<Schedule> => {
  const response = await api.put<Schedule>(`/schedule/${id}`, schedule);
  return response.data;
};

export const deleteSchedule = async (id: number): Promise<void> => {
  await api.delete(`/schedule/${id}`);
};

export const getGuideByScheduleId = async (id: number): Promise<Guide> => {
  const response = await api.get<Guide>(`/schedule/${id}/guide`);
  return response.data;
};

export const generateGuideByScheduleId = async (id: number): Promise<Guide> => {
  const response = await api.post<Guide>(`/schedule/${id}/guide`);
  return response.data;
};

export const changeGuideProgram = async (
    schedulerId: number,
    originalProgram: IElement,
    newFile: FileInfo
    ): Promise<Guide> => {
  const response = await api.post<Guide>(
      `/schedule/${schedulerId}/guide/change-program`,  {
          'original_program': originalProgram,
          'new_file': newFile
        }
      );
  return response.data;
};



export const getAllFilesGroupedByFolders = async (): Promise<IFolder> => {
  const response = await api.get<IFolder>(`/schedule/files`);
  return response.data;
};

export const getMatchingFilesGroupedByFolders = async (duration: number): Promise<IFolder> => {
  const response = await api.get<IFolder>(`/schedule/files?duration=${duration}`);
  return response.data;
};

export const getFileInfoById = async (
  id: number
): Promise<IConvertItem | null> => {
  const response = await api.get<IConvertItem>(`/files/${id}`);
  return response.data;
};

export const batchUpdateFiles = async (files: IFile[]): Promise<void> => {
  const updatedFiles = files.map((file) => {
    return {
      id: Number(file.id),
      name: file.name,
      description: file.description.String,
      episode: file.episode.String,
    };
  });

  await api.post<IFolder>(`/schedule/files`, { items: updatedFiles });
};

export const getSchedulersStatus = async (): Promise<
  Record<string, SchedulerStatus>
> => {
  try {
    const response = await api.get<Record<string, SchedulerStatus>>(
      `/schedule/status`
    );

    // Validate the response structure
    if (!response.data || typeof response.data !== "object") {
      console.error(
        "Invalid scheduler status response structure:",
        response.data
      );
      return {};
    }

    // Validate each status object has the expected properties
    const validatedStatus: Record<string, SchedulerStatus> = {};
    for (const [key, status] of Object.entries(response.data)) {
      if (
        status &&
        typeof status.id === "number" &&
        typeof status.short_id === "string" &&
        typeof status.is_active === "boolean" &&
        typeof status.rtp_synced === "boolean"
      ) {
        validatedStatus[key] = status;
      } else {
        console.warn("Invalid status object for key:", key, status);
      }
    }

    return validatedStatus;
  } catch (error) {
    console.error("Error fetching scheduler status:", error);
    return {};
  }
};
