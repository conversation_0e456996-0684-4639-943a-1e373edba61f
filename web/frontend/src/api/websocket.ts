import { RecorderStatusUpdate } from "../types/recorder";
import { SystemStats } from "../types/system";
import { FileStatusUpdate } from "../types/files";
import { RetranscodeStatus } from "./retranscodeApi";

type RecorderCallback = (update: RecorderStatusUpdate) => void;
type SystemCallback = (stats: SystemStats) => void;
type FileCallback = (update: FileStatusUpdate) => void;
type RetranscodeCallback = (status: RetranscodeStatus) => void;
type RoleChangeCallback = (response: any) => void;

class WebSocketService {
  private socket: WebSocket | null = null;
  private reconnectTimer: number | null = null;
  private recorderCallbacks: RecorderCallback[] = [];
  private systemCallbacks: SystemCallback[] = [];
  private fileCallbacks: FileCallback[] = [];
  private retranscodeCallbacks: RetranscodeCallback[] = [];
  private roleChangeCallbacks: RoleChangeCallback[] = [];
  private connected: boolean = false;

  constructor() {
    this.connect();
  }

  private connect() {
    // Close existing socket if any
    if (this.socket) {
      this.socket.close();
    }

    // Create new WebSocket connection
    const protocol = window.location.protocol === "https:" ? "wss:" : "ws:";
    const host = window.location.host;
    this.socket = new WebSocket(`${protocol}//${host}/ws`);

    // Set up event handlers
    this.socket.onopen = this.handleOpen.bind(this);
    this.socket.onmessage = this.handleMessage.bind(this);
    this.socket.onclose = this.handleClose.bind(this);
    this.socket.onerror = this.handleError.bind(this);
  }

  private handleOpen() {
    console.log("WebSocket connection established");
    this.connected = true;

    // Clear any reconnect timer
    if (this.reconnectTimer) {
      window.clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  private handleMessage(event: MessageEvent) {
    try {
      const message = JSON.parse(event.data);

      // Handle different message types
      if (message.type === "recorder_status") {
        this.notifyRecorderCallbacks(message.payload);
      } else if (message.type === "system_stats") {
        this.notifySystemCallbacks(message.payload);
      } else if (message.type === "file_status") {
        this.notifyFileCallbacks(message.payload);
      } else if (message.type === "retranscode_status") {
        this.notifyRetranscodeCallbacks(message.payload);
      } else if (message.type === 'role_change_response') {
        this.notifyRoleChangeCallbacks(message);
      } else {
        console.warn("Unknown WebSocket message type:", message.type);
      }
    } catch (error) {
      console.error("Failed to parse WebSocket message:", error);
    }
  }

  private handleClose(event: CloseEvent) {
    console.log(`WebSocket connection closed: ${event.code} ${event.reason}`);
    this.connected = false;

    // Attempt to reconnect after 5 seconds
    this.reconnectTimer = window.setTimeout(() => {
      console.log("Attempting to reconnect WebSocket...");
      this.connect();
    }, 5000);
  }

  private handleError(error: Event) {
    console.error("WebSocket error:", error);
  }

  private notifyRecorderCallbacks(update: RecorderStatusUpdate) {
    this.recorderCallbacks.forEach((callback) => {
      try {
        callback(update);
      } catch (error) {
        console.error("Error in recorder WebSocket callback:", error);
      }
    });
  }

  private notifySystemCallbacks(stats: SystemStats) {
    this.systemCallbacks.forEach((callback) => {
      try {
        callback(stats);
      } catch (error) {
        console.error("Error in system WebSocket callback:", error);
      }
    });
  }

  private notifyFileCallbacks(update: FileStatusUpdate) {
    this.fileCallbacks.forEach((callback) => {
      try {
        callback(update);
      } catch (error) {
        console.error("Error in file WebSocket callback:", error);
      }
    });
  }

  private notifyRetranscodeCallbacks(status: RetranscodeStatus) {
    this.retranscodeCallbacks.forEach((callback) => {
      try {
        callback(status);
      } catch (error) {
        console.error("Error in retranscode WebSocket callback:", error);
      }
    });
  }

  private notifyRoleChangeCallbacks(response: any) {
    this.roleChangeCallbacks.forEach(callback => {
      try {
        callback(response);
      } catch (error) {
        console.error('Error in role change WebSocket callback:', error);
      }
    });
  }

  public subscribeToRecorderUpdates(callback: RecorderCallback): () => void {
    this.recorderCallbacks.push(callback);

    // Return unsubscribe function
    return () => {
      this.recorderCallbacks = this.recorderCallbacks.filter(
        (cb) => cb !== callback
      );
    };
  }

  public subscribeToSystemStats(callback: SystemCallback): () => void {
    this.systemCallbacks.push(callback);

    // Return unsubscribe function
    return () => {
      this.systemCallbacks = this.systemCallbacks.filter(
        (cb) => cb !== callback
      );
    };
  }

  public subscribeToFileUpdates(callback: FileCallback): () => void {
    this.fileCallbacks.push(callback);

    // Return unsubscribe function
    return () => {
      this.fileCallbacks = this.fileCallbacks.filter((cb) => cb !== callback);
    };
  }

  public subscribeToRetranscodeUpdates(
    callback: RetranscodeCallback
  ): () => void {
    this.retranscodeCallbacks.push(callback);

    // Return unsubscribe function
    return () => {
      this.retranscodeCallbacks = this.retranscodeCallbacks.filter(
        (cb) => cb !== callback
      );
    };
  }

  public subscribeToRoleChanges(callback: RoleChangeCallback): () => void {
    this.roleChangeCallbacks.push(callback);

    // Return unsubscribe function
    return () => {
      this.roleChangeCallbacks = this.roleChangeCallbacks.filter(cb => cb !== callback);
    };
  }

  public isConnected(): boolean {
    return this.connected;
  }

  public disconnect() {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }

    if (this.reconnectTimer) {
      window.clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    this.recorderCallbacks = [];
    this.systemCallbacks = [];
    this.fileCallbacks = [];
    this.retranscodeCallbacks = [];
    this.roleChangeCallbacks = [];
    this.connected = false;
  }
}

// Create a singleton instance
const webSocketService = new WebSocketService();

export default webSocketService;
