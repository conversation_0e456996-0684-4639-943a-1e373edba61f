import api from "./axios";
import {
  ContentAnalyticsListResult,
  ContentAnalyticsSummary,
  ScheduleAnalyticsSummary,
  ContentAnalytics,
  DateRangeAnalyticsResult,
} from "@/types/analytics";

export const getScheduleAnalytics = async (
  scheduleId: number,
  page: number = 1,
  limit: number = 20
): Promise<ContentAnalyticsListResult> => {
  const response = await api.get<ContentAnalyticsListResult>(
    `/analytics/schedule/${scheduleId}?page=${page}&limit=${limit}`
  );
  return response.data;
};

export const getContentAnalytics = async (
  scheduleId: number,
  contentPath: string
): Promise<ContentAnalyticsSummary> => {
  const response = await api.get<ContentAnalyticsSummary>(
    `/analytics/schedule/${scheduleId}/content?path=${encodeURIComponent(
      contentPath
    )}`
  );
  return response.data;
};

export const getScheduleSummary = async (
  scheduleId: number
): Promise<ScheduleAnalyticsSummary> => {
  const response = await api.get<ScheduleAnalyticsSummary>(
    `/analytics/schedule/${scheduleId}/summary`
  );
  return response.data;
};

export const getContentSummary = async (
  contentPath: string
): Promise<ContentAnalyticsSummary> => {
  const response = await api.get<ContentAnalyticsSummary>(
    `/analytics/content/${encodeURIComponent(contentPath)}`
  );
  return response.data;
};

// Legacy function for backward compatibility (unpaginated)
export const getAnalyticsForDateRange = async (
  startDate: string,
  endDate: string,
  scheduleId?: number
): Promise<{ items: ContentAnalytics[] }> => {
  let url = `/analytics/date-range?start=${encodeURIComponent(
    startDate
  )}&end=${encodeURIComponent(endDate)}`;

  if (scheduleId) {
    url += `&schedule_id=${scheduleId}`;
  }

  const response = await api.get<{ items: ContentAnalytics[] }>(url);
  return response.data;
};

// New paginated function for date range analytics
export const getAnalyticsForDateRangePaginated = async (
  startDate: string,
  endDate: string,
  page: number = 1,
  limit: number = 1000,
  scheduleId?: number
): Promise<DateRangeAnalyticsResult> => {
  let url = `/analytics/date-range?start=${encodeURIComponent(
    startDate
  )}&end=${encodeURIComponent(endDate)}&page=${page}&limit=${limit}`;

  if (scheduleId) {
    url += `&schedule_id=${scheduleId}`;
  }

  const response = await api.get<DateRangeAnalyticsResult>(url);
  return response.data;
};

// Get aggregate statistics for a date range (total counts without pagination)
export const getAnalyticsStatsForDateRange = async (
  startDate: string,
  endDate: string,
  scheduleId?: number
): Promise<{
  total_plays: number;
  total_duration: number;
  unique_outputs: number;
}> => {
  let url = `/analytics/date-range/stats?start=${encodeURIComponent(
    startDate
  )}&end=${encodeURIComponent(endDate)}`;

  if (scheduleId) {
    url += `&schedule_id=${scheduleId}`;
  }

  const response = await api.get<{
    total_plays: number;
    total_duration: number;
    unique_outputs: number;
  }>(url);
  return response.data;
};
