import api from './axios';
import { User } from '@/types/auth';
import { NetworkInterface, NetworkInterfaceInput } from '@/types/network';
import { CodecSettings, CodecSettingsInput } from '@/types/codec';
import {GeneralSettings, GeneralSettingsInput} from "@/types/general.ts";

// Get pending users
export const getPendingUsers = async (): Promise<User[]> => {
  const response = await api.get<User[]>('/admin/users/pending');
  return response.data;
};

// Approve user
export const approveUser = async (id: number): Promise<void> => {
  await api.post(`/admin/users/${id}/approve`);
};

// Reject user
export const rejectUser = async (id: number): Promise<void> => {
  await api.post(`/admin/users/${id}/reject`);
};

// Get network interfaces
export const getNetworkInterfaces = async (): Promise<NetworkInterface[]> => {
  const response = await api.get<NetworkInterface[]>('/admin/network/interfaces');
  return response.data;
};

export const getGeneralSettings = async (): Promise<GeneralSettings> => {
  const response = await api.get<GeneralSettings>('/admin/general-settings');
  return response.data;
};
export const updateGeneralSettings = async (input: GeneralSettingsInput): Promise<void> => {
  await api.put('/admin/general-settings', input);
};

// Update network interface
export const updateNetworkInterface = async (name: string, input: NetworkInterfaceInput): Promise<void> => {
  await api.put(`/admin/network/interfaces/${name}`, input);
};

// Get codec settings
export const getCodecSettings = async (): Promise<CodecSettings> => {
  const response = await api.get<CodecSettings>('/admin/codec-settings');
  return response.data;
};

// Update codec settings
export const updateCodecSettings = async (input: CodecSettingsInput): Promise<void> => {
  await api.put('/admin/codec-settings', input);
};
