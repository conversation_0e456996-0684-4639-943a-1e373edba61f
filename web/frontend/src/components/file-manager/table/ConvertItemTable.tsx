import { useSelector } from "react-redux";
import { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  HiOutlineDocumentText,
  HiOutlineClock,
  HiEye,
  HiDownload,
  HiTrash,
  HiChevronLeft,
  HiPencil,
} from "react-icons/hi";
import {
  selectConvertItems,
  selectFolders,
} from "../../../redux/fileManagerSlice";
import { formatBytes, formatDate } from "../../../utils/fileUtils";
import { ConvertItem, FileStatus } from "../../../types/files";
import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Spinner,
  Tooltip,
  Checkbox,
} from "@nextui-org/react";
import { downloadFile } from "../../../api/filesApi";
import {
  getRetranscodeJobs,
  RetranscodeJob,
} from "../../../api/retranscodeApi";
import webSocketService from "../../../api/websocket";
import "./ConvertItemTable.css";

const statusName = (status: number, isRetranscoding?: boolean): string => {
  if (isRetranscoding) {
    return "Retranscoding";
  }

  switch (status) {
    case 0:
      return "Success";
    case 1:
      return "Queue";
    case 2:
      return "Failed";
    case 3:
      return "Transcoding";
    default:
      return "";
  }
};

// This will be defined inside the component

interface ConvertItemTableProps {
  location: string;
  setLocation: (location: string) => void;
  isLoading: boolean;
  setDeletingFile: (file: ConvertItem) => void;
  setPreviewFile: (file: ConvertItem) => void;
  setRenamingFile: (file: ConvertItem) => void;
  selectedFiles: ConvertItem[];
  setSelectedFiles: (files: ConvertItem[]) => void;
}

const ConvertItemTable = ({
  location,
  setLocation,
  isLoading,
  setDeletingFile,
  setPreviewFile,
  setRenamingFile,
  selectedFiles,
  setSelectedFiles,
}: ConvertItemTableProps) => {
  const items = useSelector(selectConvertItems);
  const folders = useSelector(selectFolders);
  const [retranscodeJobs, setRetranscodeJobs] = useState<RetranscodeJob[]>([]);

  // Debug logging


  // Load retranscode jobs to check which files are being retranscoded
  useEffect(() => {
    const fetchRetranscodeJobs = async () => {
      try {
        const jobs = await getRetranscodeJobs();
        setRetranscodeJobs(jobs || []); // Ensure we have an array even if API returns null
      } catch (error) {
        console.error("Failed to fetch retranscode jobs:", error);
        setRetranscodeJobs([]); // Set empty array on error to prevent crashes
      }
    };

    fetchRetranscodeJobs();
  }, []);

  // Subscribe to retranscode status updates via WebSocket
  useEffect(() => {
    const unsubscribe = webSocketService.subscribeToRetranscodeUpdates(() => {
      // When retranscode status changes, refresh the jobs list
      getRetranscodeJobs()
        .then((jobs) => setRetranscodeJobs(jobs || []))
        .catch((error) => {
          console.error("Failed to refresh retranscode jobs:", error);
          // Don't update state on error to avoid disrupting the UI
        });
    });

    return () => {
      unsubscribe();
    };
  }, []);

  // Check if a file is being retranscoded
  const isRetranscoding = (fileId: number): boolean => {
    try {
      return (
        Array.isArray(retranscodeJobs) &&
        retranscodeJobs.some(
          (job) =>
            job.file_id === fileId &&
            (job.status === "pending" || job.status === "processing")
        )
      );
    } catch (error) {
      console.error("Error checking retranscode status:", error);
      return false;
    }
  };

  // Get retranscode progress for a file
  const getRetranscodeProgress = (fileId: number): number => {
    try {
      if (!Array.isArray(retranscodeJobs)) return 0;

      const job = retranscodeJobs.find(
        (job) =>
          job.file_id === fileId &&
          (job.status === "pending" || job.status === "processing")
      );
      return job?.progress || 0;
    } catch (error) {
      console.error("Error getting retranscode progress:", error);
      return 0;
    }
  };

  // Helper function to check if a file is being processed (transcoding, in queue, or retranscoding)
  const isProcessing = (file: ConvertItem): boolean => {
    return (
      file.status === FileStatus.Transcoding ||
      file.status === FileStatus.Queue ||
      isRetranscoding(file.id)
    );
  };

  const isSelected = (file: ConvertItem) => {
    return selectedFiles.some((selectedFile) => selectedFile.id === file.id);
  };

  const handleSelectFile = (file: ConvertItem, isSelected: boolean) => {
    // Prevent selecting files that are being processed (transcoding or in queue)
    if (isProcessing(file)) {
      return;
    }

    if (isSelected) {
      setSelectedFiles([...selectedFiles, file]);
    } else {
      setSelectedFiles(
        selectedFiles.filter((selectedFile) => selectedFile.id !== file.id)
      );
    }
  };

  const handleSelectAll = (isSelected: boolean) => {
    if (isSelected) {
      // Filter out files that are being processed (transcoding or in queue)
      const selectableFiles = items.filter((file) => !isProcessing(file));
      setSelectedFiles([...selectableFiles]);
    } else {
      setSelectedFiles([]);
    }
  };

  const renderTableRows = () => {
    const rows = [];

    // Add "go back" row if not at root
    if (location !== "/") {
      const parentLocation = location.substring(0, location.lastIndexOf("/"));
      const goToParent = parentLocation || "/";

      rows.push(
        <TableRow
          key="go-back"
          onClick={() => setLocation(goToParent)}
          className="table-row folder-row go-back-row"
        >
          <TableCell className="table-cell"> </TableCell>
          <TableCell className="table-cell">
            <div className="folder-cell">
              <HiChevronLeft className="folder-icon" style={{ opacity: 0.7 }} />
              <Tooltip content="Go to parent directory" placement="top">
                <span className="folder-name go-back-text"> ... </span>
              </Tooltip>
            </div>
          </TableCell>
          <TableCell className="table-cell"> </TableCell>
          <TableCell className="table-cell"> </TableCell>
          <TableCell className="table-cell"> </TableCell>
          <TableCell className="table-cell"> </TableCell>
          <TableCell className="table-cell"> </TableCell>
          <TableCell className="table-cell"> </TableCell>
        </TableRow>
      );
    }

    // Add folder rows
    if (folders.length > 0) {
      for (let i = 0; i < folders.length; i++) {
        rows.push(
          <TableRow
            key={`folder-${i}`}
            onClick={() =>
              setLocation(
                [location === "/" ? "" : location, folders[i]].join("/")
              )
            }
            className="table-row folder-row"
          >
            <TableCell className="table-cell"> </TableCell>
            <TableCell className="table-cell">
              <div className="folder-cell">
                <HiFolder className="folder-icon" />
                <span className="folder-name">{folders[i]}</span>
              </div>
            </TableCell>
            <TableCell className="table-cell"> </TableCell>
            <TableCell className="table-cell"> </TableCell>
            <TableCell className="table-cell"> </TableCell>
            <TableCell className="table-cell"> </TableCell>
            <TableCell className="table-cell"> </TableCell>
            <TableCell className="table-cell"> </TableCell>
          </TableRow>
        );
      }
    }

    // Add file rows
    if (items.length > 0) {
      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        rows.push(
          <TableRow key={`file-${i}`} className="table-row">
            <TableCell className="table-cell">
              <Tooltip
                content={
                  isProcessing(item)
                    ? "Selection disabled during processing"
                    : ""
                }
                placement="top"
                isDisabled={!isProcessing(item)}
              >
                <Checkbox
                  isSelected={isSelected(item)}
                  onValueChange={(isSelected) =>
                    handleSelectFile(item, isSelected)
                  }
                  onClick={(e) => e.stopPropagation()}
                  aria-label={`Select ${item.name || item.filename}`}
                  color="primary"
                  size="lg"
                  radius="sm"
                  className={`custom-checkbox ${
                    isProcessing(item) ? "disabled" : ""
                  }`}
                  isDisabled={isProcessing(item)}
                />
              </Tooltip>
            </TableCell>
            <TableCell className="table-cell">
              <div className="file-cell">
                <HiOutlineDocumentText className="file-icon" />
                <Tooltip content={item.filename} placement="top">
                  <span className="file-name">
                    {item.filename || item.name}
                  </span>
                </Tooltip>
              </div>
            </TableCell>
            <TableCell className="table-cell">
              <div className="file-size">{formatBytes(item.size)}</div>
            </TableCell>
            <TableCell className="table-cell">
              <div className="file-video-codec">
                {item.video_codec ?? "Unknown"}
              </div>
            </TableCell>
            <TableCell className="table-cell">
              <div className="file-audio-codec">
                {item.audio_codec ?? "Unknown"}
              </div>
            </TableCell>
            <TableCell className="table-cell">
              <div
                className={`file-status ${
                  isRetranscoding(item.id)
                    ? "status-retranscoding"
                    : `status-${statusName(item.status).toLowerCase()}`
                }`}
              >
                {isRetranscoding(item.id)
                  ? `Retranscoding (${getRetranscodeProgress(item.id)}%)`
                  : statusName(item.status)}
              </div>
            </TableCell>
            <TableCell className="table-cell">
              <div className="file-date">
                <HiOutlineClock className="date-icon" />
                {formatDate(item.created_at)}
              </div>
            </TableCell>
            <TableCell className="table-cell">
              <div className="action-buttons">
                <Tooltip
                  content={
                    isProcessing(item)
                      ? "Actions disabled during processing"
                      : "Preview"
                  }
                  placement="top"
                >
                  <button
                    onClick={() => !isProcessing(item) && setPreviewFile(item)}
                    className={`action-button preview-button ${
                      isProcessing(item) ? "disabled" : ""
                    }`}
                    disabled={isProcessing(item)}
                  >
                    <HiEye className="action-icon" />
                  </button>
                </Tooltip>
                <Tooltip
                  content={
                    isProcessing(item)
                      ? "Actions disabled during processing"
                      : "Download"
                  }
                  placement="top"
                >
                  <button
                    onClick={(e) => {
                      if (!isProcessing(item)) {
                        e.stopPropagation();
                        downloadFile(item.id);
                      }
                    }}
                    className={`action-button download-button ${
                      isProcessing(item) ? "disabled" : ""
                    }`}
                    disabled={isProcessing(item)}
                  >
                    <HiDownload className="action-icon" />
                  </button>
                </Tooltip>
                <Tooltip
                  content={
                    isProcessing(item)
                      ? "Actions disabled during processing"
                      : "Rename"
                  }
                  placement="top"
                >
                  <button
                    onClick={(e) => {
                      if (!isProcessing(item)) {
                        e.stopPropagation();
                        setRenamingFile(item);
                      }
                    }}
                    className={`action-button rename-button ${
                      isProcessing(item) ? "disabled" : ""
                    }`}
                    disabled={isProcessing(item)}
                  >
                    <HiPencil className="action-icon" />
                  </button>
                </Tooltip>
                <Tooltip
                  content={
                    isProcessing(item)
                      ? "Actions disabled during processing"
                      : "Delete"
                  }
                  placement="top"
                >
                  <button
                    onClick={(e) => {
                      if (!isProcessing(item)) {
                        e.stopPropagation();
                        setDeletingFile(item);
                      }
                    }}
                    className={`action-button delete-button ${
                      isProcessing(item) ? "disabled" : ""
                    }`}
                    disabled={isProcessing(item)}
                  >
                    <HiTrash className="action-icon" />
                  </button>
                </Tooltip>
              </div>
            </TableCell>
          </TableRow>
        );
      }
    }

    return rows;
  };

  // Add error handling for rendering
  try {
    return (
      <div className="convert-item-table">
        <div className="file-manager-table-container">
          <Table
            aria-label="Files table"
            classNames={{
              base: "table-container",
              table: "w-full h-full",
              thead: "table-header",
              tbody: "table-body",
              th: "table-header-cell",
              td: "table-cell",
            }}
            shadow="none"
            removeWrapper
          >
            <TableHeader>
              <TableColumn
                className="checkbox-column"
                style={{
                  width: "60px",
                  minWidth: "60px",
                  color: "#fff",
                }}
              >
                {items.length > 0 && (
                  <Checkbox
                    isSelected={
                      selectedFiles.length === items.length && items.length > 0
                    }
                    isIndeterminate={
                      selectedFiles.length > 0 &&
                      selectedFiles.length < items.length
                    }
                    onValueChange={handleSelectAll}
                    aria-label="Select all files"
                    color="primary"
                    size="lg"
                    radius="sm"
                    className="custom-checkbox"
                  />
                )}
              </TableColumn>
              <TableColumn style={{ width: "25%", color: "#fff" }}>
                File Name
              </TableColumn>
              <TableColumn style={{ width: "10%", color: "#fff" }}>
                Size
              </TableColumn>
              <TableColumn style={{ width: "12%", color: "#fff" }}>
                Video Codec
              </TableColumn>
              <TableColumn style={{ width: "12%", color: "#fff" }}>
                Audio Codec
              </TableColumn>
              <TableColumn
                style={{ width: "10%", textAlign: "center", color: "#fff" }}
              >
                Status
              </TableColumn>
              <TableColumn style={{ width: "18%", color: "#fff" }}>
                Date Modified
              </TableColumn>
              <TableColumn
                style={{ width: "13%", textAlign: "center", color: "#fff" }}
              >
                Actions
              </TableColumn>
            </TableHeader>
            <TableBody
              isLoading={isLoading}
              loadingContent={
                <div className="loading-content">
                  <Spinner color="primary" size="lg" />
                  <div>Loading files and folders...</div>
                </div>
              }
              emptyContent={
                <div className="empty-content">
                  <div className="empty-icons">
                    <HiOutlineDocumentText className="empty-icon" />
                    <HiFolder className="empty-icon folder" />
                  </div>
                  <div>
                    <p className="empty-title">No files found</p>
                    <p className="empty-description">
                      Upload files or create a folder to get started
                    </p>
                  </div>
                </div>
              }
            >
              {renderTableRows()}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  } catch (error) {
    console.error("Error rendering ConvertItemTable:", error);
    return (
      <div className="convert-item-table">
        <div className="file-manager-table-container">
          <div className="empty-content">
            <div className="empty-title">Error Loading Files</div>
            <div className="empty-description">
              There was an error displaying the file table. Please check the
              console for details.
            </div>
          </div>
        </div>
      </div>
    );
  }
};

export default ConvertItemTable;
