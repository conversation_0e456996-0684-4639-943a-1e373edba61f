/* Modern File Manager Table Styles */
.convert-item-table {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.file-manager-table-container {
  border-radius: 12px;
  overflow: hidden;
  background-color: #1a1a1a;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  border: 1px solid #333;
  transition: all 0.3s ease;
  flex: 1;
  min-height: 400px;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.file-manager-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  flex: 1;
}

.file-manager-table-header {
  background: linear-gradient(90deg, #1e1e1e, #252525);
  position: sticky;
  top: 0;
  z-index: 10;
}

.file-manager-table-header-cell {
  color: #fff;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.85rem;
  letter-spacing: 0.5px;
  padding: 0.75rem 1.25rem;
  text-align: left;
  border-bottom: 2px solid #333;
  position: relative;
  height: 48px;
}

.file-manager-table-header-cell::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(
    90deg,
    rgba(100, 108, 255, 0) 0%,
    rgba(100, 108, 255, 0.3) 50%,
    rgba(100, 108, 255, 0) 100%
  );
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.file-manager-table-header-cell:hover::after {
  transform: scaleX(1);
}

.file-manager-table-body {
  background-color: transparent;
}

.file-manager-table-row {
  transition: all 0.2s ease;
  border-bottom: 1px solid #333;
  height: 52px;
}

.file-manager-table-row:last-child {
  border-bottom: none;
}

.file-manager-table-row:hover {
  background-color: rgba(100, 108, 255, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.file-manager-table-cell {
  padding: 0.5rem 1.25rem;
  color: #e0e0e0;
  font-size: 0.95rem;
  vertical-align: middle;
  height: 52px;
}

/* For NextUI Table compatibility */
.table-container {
  flex: 1;
  overflow: auto;
  min-height: 400px;
  width: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 12px;
  border: 1px solid #333;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  scrollbar-width: thin;
}

.table-header {
  background-color: #1e1e1e;
  position: sticky;
  top: 0;
  z-index: 10;
  border-bottom: 2px solid #333;
}

.table-header-cell {
  padding: 0.75rem 1.25rem;
  text-align: left;
  font-size: 0.85rem;
  font-weight: 600;
  color: #fff;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  white-space: nowrap;
  height: 48px;
}

.table-body {
  background-color: transparent;
}

.table-row {
  transition: all 0.2s ease;
  border-bottom: 1px solid #333;
  height: 52px;
}

.table-row:hover {
  background-color: rgba(100, 108, 255, 0.05);
  transform: translateY(-2px);
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  padding: 0.5rem 1.25rem;
  vertical-align: middle;
  color: #e0e0e0;
  font-size: 0.95rem;
  white-space: nowrap;
  height: 52px;
}

/* Folder row styling */
.folder-row {
  cursor: pointer;
  background-color: rgba(100, 108, 255, 0.05);
}

.folder-row:hover {
  background-color: rgba(100, 108, 255, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.go-back-row {
  background-color: rgba(100, 108, 255, 0.02);
  border-bottom: 1px dashed #333;
}

.go-back-row:hover {
  background-color: rgba(100, 108, 255, 0.08);
}

/* Folder cell styling */
.folder-cell {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0;
  height: auto;
  width: 100%;
}

.folder-icon {
  color: #aaa !important;
  font-size: 1.5rem;
  min-width: 1.5rem;
}

.folder-name {
  color: #fff;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px;
  font-size: 0.95rem;
}

.go-back-text {
  font-weight: 600;
  letter-spacing: 2px;
  opacity: 0.7;
}

/* File cell styling */
.file-cell {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Checkbox column and cell styling */
.checkbox-column {
  width: 50px !important;
  min-width: 50px !important;
  max-width: 50px !important;
}

.checkbox-cell {
  width: 50px !important;
  min-width: 50px !important;
  max-width: 50px !important;
  padding: 0 !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  position: relative !important;
  z-index: 2 !important;
  background-color: rgba(30, 30, 30, 0.3) !important;
  border-right: 1px solid rgba(100, 108, 255, 0.05) !important;
  transition: background-color 0.2s ease !important;
}

/* Ensure checkbox is visible on dark backgrounds */
.table-row:hover .checkbox-cell {
  background-color: rgba(30, 30, 30, 0.4) !important;
}

/* Checkbox wrapper for row checkboxes */
.checkbox-wrapper {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  width: 100% !important;
  height: 100% !important;
}

/* Header checkbox styling */
.select-all-container {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  width: 100% !important;
  height: 100% !important;
}

.checkbox-column .nextui-checkbox-container,
.checkbox-column [data-slot="wrapper"] {
  margin: 0 auto !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

.checkbox-column .nextui-checkbox-base,
.checkbox-column [data-slot="base"] {
  background-color: rgba(100, 108, 255, 0.1) !important;
  border: 1.5px solid rgba(100, 108, 255, 0.6) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

.checkbox-column .nextui-checkbox-base:hover,
.checkbox-column [data-slot="base"]:hover {
  background-color: rgba(100, 108, 255, 0.2) !important;
  transform: scale(1.03) !important;
}

/* Direct styling for checkbox input */
.checkbox-cell input[type="checkbox"] {
  opacity: 1 !important;
  position: relative !important;
  width: 20px !important;
  height: 20px !important;
  margin: 0 !important;
  cursor: pointer !important;
  z-index: 1 !important;
  accent-color: #2196f3 !important;
}

/* Checkbox styling */
.table-cell .nextui-checkbox-container,
.table-cell [data-slot="wrapper"] {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  width: 100% !important;
  height: 100% !important;
  min-width: 24px !important;
  min-height: 24px !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Custom checkbox styling */
.table-cell [data-slot="base"],
.table-cell .nextui-checkbox-base,
.checkbox-cell input[type="checkbox"] + div {
  --checkbox-size: 20px;
  width: var(--checkbox-size) !important;
  height: var(--checkbox-size) !important;
  border-radius: 6px !important;
  border: 1.5px solid #2196f3 !important;
  background-color: rgba(100, 108, 255, 0.08) !important;
  transition: all 0.2s ease !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  position: relative !important;
  overflow: visible !important;
  min-width: var(--checkbox-size) !important;
  min-height: var(--checkbox-size) !important;
  margin: 0 auto !important;
  opacity: 1 !important;
  visibility: visible !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Add a glow effect to make it more visible */
.table-cell [data-slot="base"]::after {
  content: "";
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  border-radius: 7px;
  background: transparent;
  box-shadow: 0 0 0 1px rgba(100, 108, 255, 0.15);
  opacity: 0;
  transition: opacity 0.2s ease, box-shadow 0.2s ease;
  pointer-events: none;
}

.table-cell [data-slot="base"]:hover::after {
  opacity: 1;
  box-shadow: 0 0 0 2px rgba(100, 108, 255, 0.2);
}

.table-cell [data-slot="base"]:hover,
.table-cell .nextui-checkbox-base:hover {
  background-color: rgba(100, 108, 255, 0.15) !important;
  transform: scale(1.03) !important;
  box-shadow: 0 0 6px rgba(100, 108, 255, 0.3) !important;
  border-color: rgba(100, 108, 255, 0.8) !important;
}

.table-cell [data-selected="true"] [data-slot="base"],
.table-cell .nextui-checkbox-base[data-selected="true"] {
  background-color: #2196f3 !important;
  border-color: #2196f3 !important;
  box-shadow: 0 2px 6px rgba(100, 108, 255, 0.4) !important;
  transform: scale(1.03) !important;
  animation: checkboxPulse 0.3s ease-out !important;
}

@keyframes checkboxPulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(100, 108, 255, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 6px rgba(100, 108, 255, 0);
  }
  100% {
    transform: scale(1.03);
    box-shadow: 0 2px 6px rgba(100, 108, 255, 0.4);
  }
}

.table-cell [data-slot="icon"],
.table-cell .nextui-checkbox-icon {
  color: white !important;
  font-size: 14px !important;
  animation: scaleIn 0.2s ease-out !important;
  stroke-width: 2.5px !important;
  filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.2)) !important;
}

/* Fix for double checkmark issue */
.nextui-checkbox-base svg:nth-child(2),
[data-slot="base"] svg:nth-child(2),
.nextui-checkbox-icon:nth-child(2),
[data-slot="icon"]:nth-child(2) {
  display: none !important;
}

/* Ensure only one checkmark is visible */
.nextui-checkbox-base svg,
[data-slot="base"] svg {
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
}

@keyframes scaleIn {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.15);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Indeterminate checkbox */
.table-cell [data-indeterminate="true"] [data-slot="base"],
.table-cell .nextui-checkbox-base[data-indeterminate="true"] {
  background-color: rgba(100, 108, 255, 0.4) !important;
  border-color: #2196f3 !important;
  box-shadow: 0 2px 6px rgba(100, 108, 255, 0.3) !important;
  position: relative !important;
}

.table-cell [data-indeterminate="true"] [data-slot="base"]::before,
.table-cell .nextui-checkbox-base[data-indeterminate="true"]::before {
  content: "";
  position: absolute;
  width: 10px;
  height: 2px;
  background-color: white;
  border-radius: 1px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}

.file-icon {
  color: #aaa;
  font-size: 1.5rem;
  min-width: 1.5rem;
}

.file-name {
  color: #fff;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px;
  font-size: 0.95rem;
  display: inline-block;
}

/* Size cell styling */
.file-size {
  font-size: 0.9rem;
  color: #aaa;
  font-family: monospace;
}

.file-video-codec {
  font-size: 0.85rem;
  color: #2196f3;
  font-weight: 500;
}

.file-audio-codec {
  font-size: 0.85rem;
  color: #ff9800;
  font-weight: 500;
}

/* Status cell styling */
.file-status {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.4rem 0.75rem;
  border-radius: 8px;
  font-size: 0.8rem;
  font-weight: 600;
  min-width: 100px;
  text-align: center;
}

.status-success {
  background-color: rgba(27, 94, 32, 0.8);
  color: #ffffff;
  border: 1px solid rgba(27, 94, 32, 0.9);
}

.status-queue {
  background-color: rgba(191, 115, 0, 0.8);
  color: #ffffff;
  border: 1px solid rgba(191, 115, 0, 0.9);
}

.status-failed {
  background-color: rgba(183, 28, 28, 0.8);
  color: #ffffff;
  border: 1px solid rgba(183, 28, 28, 0.9);
}

.status-transcoding {
  background-color: rgba(13, 71, 161, 0.8);
  color: #ffffff;
  border: 1px solid rgba(13, 71, 161, 0.9);
}

.status-retranscoding {
  background-color: rgba(1, 24, 32, 0.8);
  color: #ffffff;
  border: 1px solid rgba(21, 101, 132, 0.9);
  animation: pulse-retranscode 2s infinite;
}

@keyframes pulse-retranscode {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

/* Date cell styling */
.file-date {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #aaa;
  font-size: 0.9rem;
}

.date-icon {
  color: #ffa500;
  font-size: 1.2rem;
}

/* Actions cell styling */
.action-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border: 1px solid #444;
  border-radius: 6px;
  padding: 0.4rem;
  color: #fff;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
  width: 32px;
  height: 32px;
  box-sizing: border-box;
}

.preview-button {
  color: #ffa500;
  border-color: rgba(255, 165, 0, 0.3);
}

.preview-button:hover {
  background-color: rgba(255, 165, 0, 0.1);
  border-color: #ffa500;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.download-button {
  color: #4caf50;
  border-color: rgba(76, 175, 80, 0.3);
}

.download-button:hover {
  background-color: rgba(76, 175, 80, 0.1);
  border-color: #4caf50;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.rename-button {
  color: #2196f3;
  border-color: rgba(33, 150, 243, 0.3);
}

.rename-button:hover {
  background-color: rgba(33, 150, 243, 0.1);
  border-color: #2196f3;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.delete-button {
  color: #f44336;
  border-color: rgba(244, 67, 54, 0.3);
}

.delete-button:hover {
  background-color: rgba(244, 67, 54, 0.1);
  border-color: #f44336;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.action-icon {
  width: 16px;
  height: 16px;
  display: block;
}

/* Disabled action buttons */
.action-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  border-color: #444;
  color: #888;
  pointer-events: none;
  transform: none !important;
  box-shadow: none !important;
  background-color: rgba(0, 0, 0, 0.1);
}

.action-button.disabled:hover {
  background-color: rgba(0, 0, 0, 0.1);
  transform: none;
  box-shadow: none;
  border-color: #444;
}

/* Disabled checkbox */
.custom-checkbox.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  border-color: #444;
  background-color: rgba(0, 0, 0, 0.1);
  pointer-events: none;
}

/* Empty and loading states */
.loading-content {
  padding: 3rem;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  height: 100%;
  min-height: 300px;
}

.loading-spinner {
  color: #2196f3;
  font-size: 2rem;
}

.empty-content {
  padding: 3rem;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  height: 100%;
  min-height: 300px;
  flex: 1;
  width: 100%;
}

.empty-icons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.empty-icon {
  font-size: 2.5rem;
  color: #333;
}

.empty-icon.folder {
  color: #2196f3;
  opacity: 0.5;
}

.empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #2196f3;
}

.empty-description {
  font-size: 1rem;
  color: #aaa;
  margin-bottom: 1rem;
  max-width: 400px;
}

/* Custom Checkbox Styling */
.custom-checkbox {
  --checkbox-size: 20px;
  width: var(--checkbox-size);
  height: var(--checkbox-size);
  border-radius: 6px;
  border: 1.5px solid #2196f3;
  background-color: rgba(100, 108, 255, 0.08);
  transition: all 0.2s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: visible;
  min-width: var(--checkbox-size);
  min-height: var(--checkbox-size);
  margin: 0 auto;
  opacity: 1;
  visibility: visible;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.custom-checkbox:hover {
  background-color: rgba(100, 108, 255, 0.15);
  transform: scale(1.03);
  box-shadow: 0 0 6px rgba(100, 108, 255, 0.3);
  border-color: rgba(100, 108, 255, 0.8);
}

.custom-checkbox[data-selected="true"] {
  background-color: #2196f3;
  border-color: #2196f3;
  box-shadow: 0 2px 6px rgba(100, 108, 255, 0.4);
  transform: scale(1.03);
  animation: checkboxPulse 0.3s ease-out;
}

@keyframes checkboxPulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(100, 108, 255, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 6px rgba(100, 108, 255, 0);
  }
  100% {
    transform: scale(1.03);
    box-shadow: 0 2px 6px rgba(100, 108, 255, 0.4);
  }
}
