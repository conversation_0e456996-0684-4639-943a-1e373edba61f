import { Fragment } from 'react';
import { HiFolderPlus, HiHome, HiChevronRight } from 'react-icons/hi2';
import { Tooltip } from '@nextui-org/react';
import './LocationMenu.css';

interface LocationMenuProps {
  location: string;
  setLocation: (location: string) => void;
  setIsShowCreateFolderModal: (show: boolean) => void;
}

const LocationMenu = ({ location, setLocation, setIsShowCreateFolderModal }: LocationMenuProps) => {
  return (
    <div className="location-menu">
      <button
        className="home-button"
        onClick={() => setLocation('/')}
      >
        <HiHome className="home-icon" />
      </button>

      <div className="breadcrumb-container">
        {location.split('/').map((folder, index, arr) => {
          if (index > 0 && !folder) return null;

          const isRoot = index === 0 && !folder;
          const currentPath = '/' + arr.slice(1, index + 1).join('/');
          const isLast = index === arr.length - 1;

          if (isRoot) return null; // Skip root as we have the home button

          return (
            <Fragment key={index}>
              {index > 1 && <HiChevronRight className="breadcrumb-separator" />}

              {isLast ? (
                <span className="breadcrumb-item current">
                  {folder || 'Home'}
                </span>
              ) : (
                <Tooltip content={`Go to ${folder || 'Home'}`} placement="bottom">
                  <button
                    onClick={() => setLocation(currentPath)}
                    className="breadcrumb-item clickable"
                  >
                    {folder || 'Home'}
                  </button>
                </Tooltip>
              )}
            </Fragment>
          );
        })}
      </div>

      <Tooltip content="Create new folder" placement="bottom">
        <button
          onClick={() => setIsShowCreateFolderModal(true)}
          className="new-folder-button"
        >
          <HiFolderPlus className="folder-icon" />
          New Folder
        </button>
      </Tooltip>
    </div>
  );
};

export default LocationMenu;
