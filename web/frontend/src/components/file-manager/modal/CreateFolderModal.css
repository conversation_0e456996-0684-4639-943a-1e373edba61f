/* Specific styles for create folder modal */
.close-button {
  background: none;
  border: none;
  color: #888;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.close-button:hover {
  color: #fff;
}

.modal-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #333;
  font-size: 1.25rem;
  font-weight: 600;
  color: #2196f3;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #333;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.input-group {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.input-label {
  width: 80px;
  color: #888;
  font-size: 0.9rem;
}

.input-field {
  flex: 1;
}

.input-wrapper {
  background-color: #222;
  border: 1px solid #333;
  border-radius: 4px;
  transition: all 0.2s;
}

.input-wrapper:focus-within {
  border-color: #2196f3;
}

.input {
  background-color: transparent;
  color: white;
  padding: 0.5rem 0.75rem;
  width: 100%;
  border: none;
  outline: none;
  font-size: 0.9rem;
}

.info-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background-color: rgba(33, 150, 243, 0.1);
  border: 1px solid rgba(33, 150, 243, 0.2);
  border-radius: 4px;
  color: #2196f3;
  font-size: 0.8rem;
  margin-top: 1rem;
}

.warning-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background-color: rgba(255, 152, 0, 0.1);
  border: 1px solid rgba(255, 152, 0, 0.2);
  border-radius: 4px;
  color: #ff9800;
  font-size: 0.9rem;
  margin: 1rem 0;
}

.info-icon,
.warning-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.cancel-button {
  background-color: transparent;
  border: 1px solid #333;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.9rem;
  min-width: 100px;
}

.cancel-button:hover {
  background-color: #333;
}

.create-button {
  background-color: transparent;
  border: 1px solid #2196f3;
  color: #2196f3;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.9rem;
  min-width: 100px;
}

.create-button:hover {
  background-color: rgba(33, 150, 243, 0.1);
}
