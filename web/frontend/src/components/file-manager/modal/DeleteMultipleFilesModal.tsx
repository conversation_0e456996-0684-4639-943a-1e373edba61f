import { useState } from "react";
import { HiInformationCircle, HiXMark } from "react-icons/hi2";
import { Spinner } from "@nextui-org/react";
import { ConvertItem } from "../../../types/files";
import { deleteMultipleFiles } from "../../../api/filesApi";
import { toast } from "react-toastify";
import "./DeleteFilesModal.css";
import "./CommonModal.css";

interface DeleteMultipleFilesModalProps {
  files: ConvertItem[];
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const DeleteMultipleFilesModal = ({
  files,
  isOpen,
  onClose,
  onSuccess,
}: DeleteMultipleFilesModalProps) => {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    if (!files.length) return;

    setIsDeleting(true);
    try {
      const result = await deleteMultipleFiles(files.map((file) => file.id));

      // Always refresh the file list to show the ones that were successfully deleted
      onSuccess();
      onClose();

      if (result.success) {
        toast.success(`${files.length} file(s) deleted successfully`);
      } else {
        console.error("Failed to delete some files:", result.errors);

        if (result.failedCount === files.length) {
          // All files failed
          toast.error(`Failed to delete all ${files.length} files`);
        } else {
          // Some files failed, some succeeded
          toast.warning(
            `Deleted ${
              files.length - result.failedCount
            } files, but failed to delete ${result.failedCount} files`
          );
        }
      }
    } catch (error) {
      console.error("Error during deletion:", error);
      toast.error("An unexpected error occurred during deletion");
    } finally {
      setIsDeleting(false);
    }
  };

  if (!files.length) return null;

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h2>Delete Files</h2>
          <button
            className="close-button"
            onClick={onClose}
            disabled={isDeleting}
          >
            <HiXMark />
          </button>
        </div>
        <div className="modal-body">
          <div className="delete-message">
            {isDeleting ? (
              <div className="delete-loading">
                <Spinner size="lg" color="danger" />
                <p className="delete-loading-text">
                  Deleting {files.length} file(s)...
                </p>
                <p className="delete-loading-subtext">
                  Please wait while the files are being removed
                </p>
              </div>
            ) : (
              <>
                <p className="delete-question">
                  Are you sure you want to delete {files.length} file(s)?
                </p>
                <div className="delete-files-list">
                  {files.slice(0, 5).map((file, index) => (
                    <div key={index} className="delete-filename">
                      {file.location}
                      {file.filename}
                    </div>
                  ))}
                  {files.length > 5 && (
                    <div className="delete-more-files">
                      ...and {files.length - 5} more file(s)
                    </div>
                  )}
                </div>
                <div className="delete-warning">
                  <HiInformationCircle className="warning-icon" />
                  <span>
                    This will also remove all transcoded files and clear all
                    connections in the scheduler
                  </span>
                </div>
              </>
            )}
          </div>
        </div>
        <div className="modal-footer">
          <button
            className="modal-button cancel-button"
            onClick={onClose}
            disabled={isDeleting}
          >
            Cancel
          </button>
          <button
            className="modal-button delete-button"
            onClick={handleDelete}
            disabled={isDeleting}
          >
            {isDeleting ? "Deleting..." : "Delete"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default DeleteMultipleFilesModal;
