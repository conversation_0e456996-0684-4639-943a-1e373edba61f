/* Specific styles for delete files modal */
.close-button {
  background: none;
  border: none;
  color: #888;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.close-button:hover {
  color: #fff;
}

.close-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.close-button:disabled:hover {
  color: #888;
}

.modal-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #333;
  font-size: 1.25rem;
  font-weight: 600;
  color: #f44336;
}

.modal-body {
  padding: 1.5rem;
  text-align: center;
}

.modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #333;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.delete-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.delete-question {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
}

.delete-filename {
  color: #f44336;
  font-weight: 500;
  word-break: break-all;
  padding: 0.5rem;
  background-color: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.2);
  border-radius: 4px;
  margin: 0.5rem 0;
  font-family: monospace;
  font-size: 0.9rem;
  max-width: 100%;
  width: 100%;
  text-align: left;
}

.delete-files-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
}

.delete-more-files {
  color: #888;
  font-style: italic;
  text-align: center;
  padding: 0.5rem;
  font-size: 0.9rem;
}

.delete-warning {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background-color: rgba(33, 150, 243, 0.1);
  border: 1px solid rgba(33, 150, 243, 0.2);
  border-radius: 4px;
  color: #2196f3;
  font-size: 0.8rem;
  margin-top: 1rem;
  text-align: left;
}

.warning-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.cancel-button {
  background-color: transparent;
  border: 1px solid #333;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.9rem;
  min-width: 100px;
}

.cancel-button:hover {
  background-color: #333;
}

.cancel-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: transparent;
}

.cancel-button:disabled:hover {
  background-color: transparent;
}

.delete-button {
  background-color: transparent;
  border: 1px solid #f44336;
  color: #f44336;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.9rem;
  min-width: 100px;
}

.delete-button:hover {
  background-color: rgba(244, 67, 54, 0.1);
}

.delete-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: transparent;
  border-color: #666;
  color: #666;
}

.delete-button:disabled:hover {
  background-color: transparent;
  border-color: #666;
  color: #666;
}

/* Loading state styles */
.delete-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 2rem 1rem;
  min-height: 150px;
}

.delete-loading-text {
  font-size: 1.1rem;
  font-weight: 500;
  color: #f44336;
  margin: 0;
}

.delete-loading-subtext {
  font-size: 0.9rem;
  color: #888;
  margin: 0;
  text-align: center;
  max-width: 300px;
  line-height: 1.4;
}
