import { HiInformationCircle, HiXMark } from 'react-icons/hi2';
import { ConvertItem } from '../../../types/files';
import { deleteFile } from '../../../api/filesApi';
import { toast } from 'react-toastify';
import './DeleteFilesModal.css';
import './CommonModal.css';

interface DeleteFilesModalProps {
  file: ConvertItem | null;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const DeleteFilesModal = ({ file, isOpen, onClose, onSuccess }: DeleteFilesModalProps) => {
  const handleDelete = async () => {
    if (!file) return;

    const result = await deleteFile(file.id);

    if (result.success) {
      toast.success('File deleted successfully');
      onSuccess();
      onClose();
    } else {
      console.error('Failed to delete file:', result.error);
      toast.error(`Failed to delete file: ${result.error}`);
    }
  };

  if (!file) return null;

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h2>Delete File</h2>
          <button className="close-button" onClick={onClose}>
            <HiXMark />
          </button>
        </div>
        <div className="modal-body">
          <div className="delete-message">
            <p className="delete-question">
              Are you sure you want to delete the file?
            </p>
            <div className="delete-filename">
              {file.location}{file.filename}
            </div>
            <div className="delete-warning">
              <HiInformationCircle className="warning-icon" />
              <span>This will also remove all transcoded files and clear all connections in the scheduler</span>
            </div>
          </div>
        </div>
        <div className="modal-footer">
          <button
            className="modal-button cancel-button"
            onClick={onClose}
          >
            Cancel
          </button>
          <button
            className="modal-button delete-button"
            onClick={handleDelete}
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  );
};

export default DeleteFilesModal;
