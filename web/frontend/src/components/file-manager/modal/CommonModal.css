/* Common Modal Styles for File Manager */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: #1a1a1a;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #333;
}

.modal-header h2 {
  margin: 0;
  color: #2196f3;
  font-size: 1.25rem;
}

.modal-body {
  padding: 1rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1rem;
  border-top: 1px solid #333;
}

.modal-button {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 0.9rem;
}

.cancel-button {
  background-color: #2a2a2a;
  color: #aaa;
  border: 1px solid #3a3a3a;
}

.cancel-button:hover {
  background-color: #3a3a3a;
  color: #fff;
  border-color: #2196f3;
}

.confirm-button {
  background-color: #2a2a2a;
  color: white;
  border: 1px solid #3a3a3a;
}

.confirm-button:hover {
  background-color: #3a3a3a;
  border-color: #2196f3;
}

.delete-button {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
  border: 1px solid #f44336;
}

.delete-button:hover {
  background-color: rgba(244, 67, 54, 0.2);
}
