/* Specific styles for preview modal */
.preview-modal-content {
  width: 760px; /* Fixed width to match original Wails project */
  max-width: 90%;
  max-height: 90vh;
  padding: 0;
  background-color: #1a1a1a;
}

.preview-content {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 405px; /* Fixed height to match original Wails project (720x405) */
  background-color: #000;
}

.player-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 20;
  gap: 1rem;
}

.loading-text {
  color: #888;
  font-size: 0.9rem;
  margin-top: 0.5rem;
}

.close-button {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 30;
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
}

.close-button:hover {
  color: #f44336;
  transform: scale(1.1);
}

.close-icon {
  font-size: 1.5rem;
}

.file-info {
  padding: 1rem;
  background-color: #222;
  border-top: 1px solid #333;
}

.file-name {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #2196f3;
}

.file-details {
  display: flex;
  gap: 1rem;
  font-size: 0.8rem;
  color: #888;
}

.file-detail {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.detail-icon {
  font-size: 0.9rem;
}

/* Error display styles */
.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
}

.error-message {
  display: flex;
  flex-direction: column;
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.error-message p {
  font-size: 1rem;
  line-height: 1.5;
  color: #ddd;
}
