import { useRef, useState, useEffect } from 'react';
import { HiInformationCircle, HiXMark } from 'react-icons/hi2';
import { ConvertItem } from '../../../types/files';
import { renameFile } from '../../../api/filesApi';
import { toast } from 'react-toastify';
import axios, { AxiosError } from 'axios';
import './CommonModal.css';
import './RenameFileModal.css';

interface RenameFileModalProps {
  file: ConvertItem | null;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const RenameFileModal = ({ file, isOpen, onClose, onSuccess }: RenameFileModalProps) => {
  const [name, setName] = useState<string>(file?.name || file?.filename || '');
  const inputRef = useRef<HTMLInputElement>(null);

  // Reset the name when the file changes
  useEffect(() => {
    if (file) {
      setName(file.name || file.filename || '');
    }
  }, [file]);

  const handleRename = async () => {
    if (!file) return;

    // Validate the name
    if (!name.trim()) {
      if (inputRef.current) {
        inputRef.current.style.borderColor = 'red';
      }
      return;
    }

    try {
      // Call the API to rename the file (changes actual filename)
      await renameFile(file.id, name.trim());

      toast.success('File renamed successfully');
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Failed to rename file:', error);

      // Type guard to check if error is an AxiosError
      if (axios.isAxiosError(error)) {
        const axiosError = error as AxiosError;

        if (axiosError.response) {
          console.error('Error response:', axiosError.response);

          if (axiosError.response.status === 404) {
            toast.error('Rename endpoint not found. The server may need to be restarted.');
          } else if (axiosError.response.data && typeof axiosError.response.data === 'object') {
            const errorData = axiosError.response.data as { error?: string };
            if (errorData.error) {
              toast.error(`Failed to rename file: ${errorData.error}`);
            } else {
              toast.error(`Failed to rename file: ${axiosError.response.statusText}`);
            }
          } else {
            toast.error(`Failed to rename file: ${axiosError.response.statusText}`);
          }
        } else if (axiosError.request) {
          toast.error('No response received from server');
        } else {
          toast.error(`Failed to rename file: ${axiosError.message}`);
        }
      } else {
        toast.error('Failed to rename file');
      }
    }
  };

  if (!isOpen || !file) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h2>Rename File</h2>
          <button className="close-button" onClick={onClose}>
            <HiXMark />
          </button>
        </div>
        <div className="modal-body">
          <div className="input-group">
            <div className="input-label">New Name</div>
            <div className="input-field">
              <div className="input-wrapper">
                <input
                  ref={inputRef}
                  type="text"
                  className="input"
                  placeholder="Enter new name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  autoFocus
                />
              </div>
            </div>
          </div>
          <div className="file-info">
            <div className="file-path">
              <span className="label">Current path:</span>
              <span className="value">{file.location}{file.filename}</span>
            </div>
            <div className="info-message">
              <HiInformationCircle className="info-icon" />
              <span>This will rename the actual file on disk</span>
            </div>
          </div>
        </div>
        <div className="modal-footer">
          <button
            className="modal-button cancel-button"
            onClick={onClose}
          >
            Cancel
          </button>
          <button
            className="modal-button confirm-button"
            onClick={handleRename}
          >
            Rename
          </button>
        </div>
      </div>
    </div>
  );
};

export default RenameFileModal;
