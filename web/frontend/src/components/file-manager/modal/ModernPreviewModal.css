/* Modern Video Player Styles */
.modern-preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(8px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modern-preview-container {
  width: 85%;
  max-width: 1280px;
  display: flex;
  flex-direction: column;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(100, 108, 255, 0.2);
  background-color: #121212;
  animation: scaleIn 0.3s ease;
  position: relative;
}

@keyframes scaleIn {
  from { transform: scale(0.95); }
  to { transform: scale(1); }
}

.modern-player-wrapper {
  position: relative;
  width: 100%;
  background-color: #000;
  aspect-ratio: 16/9;
  overflow: hidden;
}

.modern-player-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Custom controls overlay */
.modern-player-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 1rem;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 10;
}

.modern-player-wrapper:hover .modern-player-controls {
  opacity: 1;
}

/* Loading state */
.modern-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 20;
}

.modern-loading-spinner {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 3px solid rgba(100, 108, 255, 0.2);
  border-top-color: #2196f3;
  animation: spin 1s linear infinite;
  box-shadow: 0 0 20px rgba(100, 108, 255, 0.5);
  position: relative;
}

.modern-loading-spinner::before {
  content: '';
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border-radius: 50%;
  border: 1px solid rgba(100, 108, 255, 0.1);
  animation: pulse-ring 2s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes pulse-ring {
  0% { transform: scale(0.8); opacity: 0.8; }
  50% { transform: scale(1.2); opacity: 0.4; }
  100% { transform: scale(0.8); opacity: 0.8; }
}

.modern-loading-text {
  margin-top: 1rem;
  color: #fff;
  font-size: 1rem;
  font-weight: 500;
  letter-spacing: 1px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Error state */
.modern-error-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 20;
}

.modern-error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 80%;
}

.modern-error-icon {
  font-size: 4rem;
  color: #f44336;
  margin-bottom: 1.5rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 0.6; transform: scale(0.95); }
  50% { opacity: 1; transform: scale(1); }
  100% { opacity: 0.6; transform: scale(0.95); }
}

.modern-error-message {
  font-size: 1.2rem;
  line-height: 1.6;
  color: #fff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Close button */
.video-close-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 30;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(4px);
}

.video-close-btn:hover {
  background: rgba(244, 67, 54, 0.8);
  transform: scale(1.1);
  box-shadow: 0 0 15px rgba(244, 67, 54, 0.5);
}

.video-close-icon {
  font-size: 1.5rem;
}

/* File info section */
.modern-file-info {
  padding: 1.5rem;
  background-color: #121212;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.modern-file-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modern-file-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: #fff;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 80%;
}

.modern-file-details {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
}

.modern-file-detail {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  transition: all 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.modern-file-detail:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  border-color: rgba(100, 108, 255, 0.3);
}

.modern-detail-icon {
  font-size: 1.1rem;
  color: #2196f3;
}

.modern-detail-text {
  font-size: 0.9rem;
  color: #e0e0e0;
}

/* Media controls */
.modern-media-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
}

.modern-media-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: rgba(100, 108, 255, 0.1);
  border: 1px solid rgba(100, 108, 255, 0.3);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: #2196f3;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.modern-media-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: all 0.6s ease;
}

.modern-media-button:hover::before {
  left: 100%;
}

.modern-media-button:hover {
  background-color: rgba(100, 108, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  border-color: rgba(100, 108, 255, 0.5);
}

.modern-media-icon {
  font-size: 1.1rem;
  transition: transform 0.3s ease;
}

.modern-media-button:hover .modern-media-icon {
  transform: scale(1.2);
}
