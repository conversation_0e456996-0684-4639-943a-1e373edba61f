.move-message {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.move-question {
  font-size: 1rem;
  margin-bottom: 0.5rem;
  color: #eee;
}

.move-files-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
  max-height: 150px;
  overflow-y: auto;
  padding: 0.5rem;
  background-color: rgba(33, 150, 243, 0.05);
  border-radius: 4px;
  border: 1px solid rgba(33, 150, 243, 0.1);
}

.move-filename {
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  color: #ddd;
  padding: 0.25rem 0.5rem;
  background-color: rgba(33, 150, 243, 0.1);
  border-radius: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.move-more-files {
  font-size: 0.85rem;
  color: #aaa;
  font-style: italic;
  text-align: center;
  margin-top: 0.5rem;
}

.folder-selection {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.folder-selection label {
  font-size: 0.9rem;
  color: #eee;
  font-weight: 500;
}

.folder-select-container {
  position: relative;
}

.folder-select {
  width: 100%;
  padding: 0.75rem;
  background-color: #2a2a2a;
  color: #fff;
  border: 1px solid #444;
  border-radius: 4px;
  font-size: 0.9rem;
  appearance: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Courier New', monospace;
}

.folder-select:hover {
  border-color: #2196f3;
  background-color: #333;
}

.folder-select:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

.folder-select-container::after {
  content: '▼';
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #2196f3;
  pointer-events: none;
  font-size: 0.8rem;
}

.folder-select option {
  background-color: #2a2a2a;
  color: #fff;
  padding: 0.5rem;
}

.move-error {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background-color: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.3);
  border-radius: 4px;
  color: #f44336;
  font-size: 0.9rem;
  margin-bottom: 1rem;
  animation: fadeIn 0.3s ease-out;
}

.error-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.move-info {
  margin-top: 1rem;
  padding: 1rem;
  background-color: rgba(33, 150, 243, 0.05);
  border-radius: 4px;
  border: 1px solid rgba(33, 150, 243, 0.1);
}

.move-path {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-family: 'Courier New', monospace;
}

.source-path, .destination-path {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.path-label {
  font-size: 0.8rem;
  color: #aaa;
}

.path-value {
  font-size: 0.9rem;
  color: #fff;
  background-color: rgba(33, 150, 243, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  border: 1px solid rgba(33, 150, 243, 0.2);
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.path-arrow {
  color: #2196f3;
  font-size: 1.5rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.7; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.1); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-5px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Override confirm button for move action */
.modal-button.confirm-button {
  background-color: rgba(33, 150, 243, 0.1);
  color: #2196f3;
  border: 1px solid #2196f3;
}

.modal-button.confirm-button:hover:not(:disabled) {
  background-color: rgba(33, 150, 243, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(33, 150, 243, 0.2);
}

.modal-button.confirm-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
