import { useRef, useState } from 'react';
import { HiInformationCircle, HiXMark } from 'react-icons/hi2';
import './CreateFolderModal.css';
import './CommonModal.css';

interface CreateFolderModalProps {
  addFolder: (name: string) => void;
  isOpen: boolean;
  onClose: () => void;
  folders: string[];
}

const CreateFolderModal = ({ addFolder, isOpen, onClose, folders }: CreateFolderModalProps) => {
  const [name, setName] = useState<string>('');
  const [showWarning, setShowWarning] = useState<boolean>(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleCreate = () => {
    if (!name.trim()) {
      return;
    }

    // Check if folder already exists
    if (folders.includes(name.trim())) {
      setShowWarning(true);
      return;
    }

    // If folder doesn't exist, create it
    addFolder(name);
    setName('');
    onClose();
  };

  const handleNavigateToExisting = () => {
    // User confirmed they want to navigate to the existing folder
    addFolder(name);
    setName('');
    setShowWarning(false);
    onClose();
  };

  const handleCancelWarning = () => {
    setShowWarning(false);
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        {!showWarning ? (
          <>
            <div className="modal-header">
              <h2>Create Folder</h2>
              <button className="close-button" onClick={onClose}>
                <HiXMark />
              </button>
            </div>
            <div className="modal-body">
              <div className="input-group">
                <div className="input-label">Name</div>
                <div className="input-field">
                  <div className="input-wrapper">
                    <input
                      ref={inputRef}
                      type="text"
                      className="input"
                      placeholder="Folder Name"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      autoFocus
                    />
                  </div>
                </div>
              </div>
              <div className="info-message">
                <HiInformationCircle className="info-icon" />
                <span>If no files are uploaded to the folder, it will not be saved</span>
              </div>
            </div>
            <div className="modal-footer">
              <button
                className="modal-button cancel-button"
                onClick={onClose}
              >
                Cancel
              </button>
              <button
                className="modal-button confirm-button"
                onClick={handleCreate}
              >
                Create
              </button>
            </div>
          </>
        ) : (
          <>
            <div className="modal-header">
              <h2>Folder Already Exists</h2>
              <button className="close-button" onClick={handleCancelWarning}>
                <HiXMark />
              </button>
            </div>
            <div className="modal-body">
              <div className="warning-message">
                <HiInformationCircle className="warning-icon" />
                <span>The folder "{name}" already exists. Do you want to navigate to it?</span>
              </div>
            </div>
            <div className="modal-footer">
              <button
                className="modal-button cancel-button"
                onClick={handleCancelWarning}
              >
                Cancel
              </button>
              <button
                className="modal-button confirm-button"
                onClick={handleNavigateToExisting}
              >
                Navigate
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default CreateFolderModal;
