import { useState, useEffect } from 'react';
import { HiInformationCircle, HiXMark, HiArrowRight } from 'react-icons/hi2';
import { ConvertItem } from '../../../types/files';
import { getFoldersByLocation, moveFiles } from '../../../api/filesApi';
import { toast } from 'react-toastify';
import './CommonModal.css';
import './MoveFilesModal.css';

interface MoveFilesModalProps {
  files: ConvertItem[];
  isOpen: boolean;
  currentLocation: string;
  onClose: () => void;
  onSuccess: (destinationLocation?: string) => void;
}

const MoveFilesModal = ({ files, isOpen, currentLocation, onClose, onSuccess }: MoveFilesModalProps) => {
  const [folders, setFolders] = useState<string[]>([]);
  const [selectedFolder, setSelectedFolder] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');

  // Fetch available folders when the modal opens
  useEffect(() => {
    if (isOpen) {
      fetchFolders();
    }
  }, [isOpen]);

  const fetchFolders = async () => {
    setIsLoading(true);
    try {
      // Get all folders at the root level
      const rootFolders = await getFoldersByLocation('/');
      setFolders(rootFolders);

      // Default to root if no folders are available
      if (rootFolders.length === 0) {
        setSelectedFolder('/');
      } else {
        setSelectedFolder('');
      }
    } catch (error) {
      console.error('Failed to fetch folders:', error);
      setError('Failed to load destination folders');
      toast.error('Failed to load destination folders');
    } finally {
      setIsLoading(false);
    }
  };

  const handleMove = async () => {
    if (!selectedFolder) {
      setError('Please select a destination folder');
      return;
    }

    // Don't move if source and destination are the same
    const destinationPath = selectedFolder === '/' ? '/' : `/${selectedFolder}/`;
    if (destinationPath === currentLocation) {
      setError('Source and destination folders are the same');
      return;
    }

    setIsLoading(true);
    try {
      const result = await moveFiles(
        files.map(file => file.id),
        destinationPath
      );

      // Only close the modal here, we'll call onSuccess based on the result

      if (result.success) {
        // Show success toast with destination info
        const isSameLocation = destinationPath === currentLocation;
        const message = isSameLocation
          ? `${files.length} file(s) moved successfully`
          : `${files.length} file(s) moved to ${destinationPath}`;

        // Create a toast with a button to navigate to the destination folder if it's different
        if (!isSameLocation) {
          toast.success(
            <div>
              {message}
              <button
                onClick={() => onSuccess(destinationPath)}
                style={{
                  marginLeft: '10px',
                  padding: '4px 8px',
                  background: '#2196f3',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  fontFamily: 'monospace',
                  fontSize: '12px'
                }}
              >
                Go to folder
              </button>
            </div>,
            {
              autoClose: 5000,
              closeOnClick: false
            }
          );

          // Close the modal but don't navigate automatically
          onClose();

          // Still call onSuccess to refresh the current view
          onSuccess();
        } else {
          // If same location, just show a simple toast and refresh
          toast.success(message);

          // Close the modal and refresh the current view
          onSuccess();
          onClose();
        }
      } else {
        console.error('Failed to move some files:', result.errors);

        if (result.failedCount === files.length) {
          // All files failed
          toast.error(`Failed to move all ${files.length} files`);

          // Show the first error in the UI for more context
          if (result.errors && result.errors.length > 0) {
            setError(result.errors[0]);
          }

          // Don't close the modal so the user can see the error and try again
        } else {
          // Some files failed, some succeeded
          toast.warning(`Moved ${files.length - result.failedCount} files, but failed to move ${result.failedCount} files`);

          // Navigate to the destination folder anyway since some files were moved
          onSuccess(destinationPath);
          onClose();
        }
      }
    } catch (error) {
      console.error('Error moving files:', error);
      setError('Failed to move files');
      toast.error('Failed to move files');

      // Don't close the modal on error so the user can see the error message
      // and potentially try again
    } finally {
      setIsLoading(false);
    }
  };

  if (!files.length || !isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h2>Move Files</h2>
          <button className="close-button" onClick={onClose}>
            <HiXMark />
          </button>
        </div>
        <div className="modal-body">
          <div className="move-message">
            <p className="move-question">
              Select a destination folder to move {files.length} file(s):
            </p>
            <div className="move-files-list">
              {files.slice(0, 5).map((file, index) => (
                <div key={index} className="move-filename">
                  {file.filename}
                </div>
              ))}
              {files.length > 5 && (
                <div className="move-more-files">
                  ...and {files.length - 5} more file(s)
                </div>
              )}
            </div>

            <div className="folder-selection">
              <label htmlFor="destination-folder">Destination Folder:</label>
              <div className="folder-select-container">
                <select
                  id="destination-folder"
                  value={selectedFolder}
                  onChange={(e) => {
                    setSelectedFolder(e.target.value);
                    setError('');
                  }}
                  className="folder-select"
                  disabled={isLoading}
                >
                  <option value="">Select a folder</option>
                  <option value="/">Root</option>
                  {folders.map((folder, index) => (
                    <option key={index} value={folder}>
                      /{folder}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {error && (
              <div className="move-error">
                <HiInformationCircle className="error-icon" />
                <span>{error}</span>
              </div>
            )}

            <div className="move-info">
              <div className="move-path">
                <div className="source-path">
                  <span className="path-label">From:</span>
                  <span className="path-value">{currentLocation}</span>
                </div>
                <HiArrowRight className="path-arrow" />
                <div className="destination-path">
                  <span className="path-label">To:</span>
                  <span className="path-value">
                    {selectedFolder === '/' ? '/' : `/${selectedFolder}/`}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="modal-footer">
          <button
            className="modal-button cancel-button"
            onClick={onClose}
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            className="modal-button confirm-button"
            onClick={handleMove}
            disabled={isLoading || !selectedFolder}
          >
            {isLoading ? 'Moving...' : 'Move Files'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default MoveFilesModal;
