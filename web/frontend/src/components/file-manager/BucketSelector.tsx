import { useEffect, useState, useRef } from 'react';
import { Spinner } from '@nextui-org/react';
import { HiCloudArrowUp, HiChevronDown } from 'react-icons/hi2';
import { getBuckets, Bucket, testBucketConnection } from '../../api/bucketsApi';
import { toast } from 'react-toastify';
import './BucketSelector.css';

interface BucketSelectorProps {
  selectedBucket: string | null;
  onBucketChange: (bucketName: string | null) => void;
  disabled?: boolean;
}

const BucketSelector = ({ selectedBucket, onBucketChange, disabled = false }: BucketSelectorProps) => {
  const [buckets, setBuckets] = useState<Bucket[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const fetchBuckets = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const bucketsData = await getBuckets();
        setBuckets(bucketsData);
        
        // If no bucket is selected and there's a default bucket, select it
        if (!selectedBucket && bucketsData.length > 0) {
          const defaultBucket = bucketsData.find(bucket => bucket.default);
          if (defaultBucket) {
            onBucketChange(defaultBucket.bucketName);
          } else {
            // If no default bucket, select the first one
            onBucketChange(bucketsData[0].bucketName);
          }
        }
      } catch (error) {
        console.error('Failed to fetch buckets:', error);
        setError('Failed to load buckets');
        toast.error('Failed to load buckets');
      } finally {
        setIsLoading(false);
      }
    };

    fetchBuckets();
  }, [selectedBucket, onBucketChange]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleBucketSelect = (bucketName: string) => {
    onBucketChange(bucketName);
    setIsOpen(false);
  };

  const toggleDropdown = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  const selectedBucketData = buckets.find(bucket => bucket.bucketName === selectedBucket);

  // Test function for debugging
  const handleTestConnection = async () => {
    try {
      const result = await testBucketConnection();
      console.log('MongoDB Test Result:', result);
      toast.success(`MongoDB connected! Found ${result.document_count} buckets`);
    } catch (error) {
      console.error('MongoDB Test Error:', error);
      toast.error('Failed to connect to MongoDB');
    }
  };

  if (isLoading) {
    return (
      <div className="bucket-selector-loading">
        <Spinner size="sm" />
        <span>Loading buckets...</span>
      </div>
    );
  }

  if (error || buckets.length === 0) {
    return (
      <div className="bucket-selector-error">
        <HiCloudArrowUp className="error-icon" />
        <span>{error || 'No buckets available'}</span>
      </div>
    );
  }

  return (
    <div className="bucket-selector" ref={dropdownRef}>
      <div className="bucket-selector-label">Storage Bucket</div>
      <div
        className={`bucket-selector-trigger ${disabled ? 'disabled' : ''} ${isOpen ? 'open' : ''}`}
        onClick={toggleDropdown}
      >
        <div className="bucket-selector-content">
          <HiCloudArrowUp className="bucket-icon" />
          <div className="bucket-selector-text">
            {selectedBucketData ? (
              <div>
                <div className="bucket-title">{selectedBucketData.title}</div>
                <div className="bucket-name">{selectedBucketData.bucketName}</div>
              </div>
            ) : (
              <div className="bucket-placeholder">Select a bucket</div>
            )}
          </div>
          {selectedBucketData?.default && (
            <span className="bucket-badge">Default</span>
          )}
        </div>
        <HiChevronDown className={`bucket-chevron ${isOpen ? 'open' : ''}`} />
      </div>

      {isOpen && (
        <div className="bucket-dropdown">
          {buckets.map((bucket) => (
            <div
              key={bucket.bucketName}
              className={`bucket-option ${selectedBucket === bucket.bucketName ? 'selected' : ''}`}
              onClick={() => handleBucketSelect(bucket.bucketName)}
            >
              <div className="bucket-option-content">
                <div className="bucket-title">{bucket.title}</div>
                <div className="bucket-name">{bucket.bucketName}</div>
              </div>
              {bucket.default && (
                <span className="bucket-badge">Default</span>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default BucketSelector;
