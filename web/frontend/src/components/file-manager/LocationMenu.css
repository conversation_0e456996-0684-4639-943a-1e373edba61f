.location-menu {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 1.25rem;
  background-color: rgba(26, 26, 26, 0.8);
  border-radius: 12px;
  border: 1px solid rgba(51, 51, 51, 0.8);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(4px);
  animation: fadeInMenu 0.5s ease-out;
}

@keyframes fadeInMenu {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.location-menu:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  border-color: rgba(100, 108, 255, 0.2);
  background-color: rgba(28, 28, 28, 0.9);
}

.location-menu::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(100, 108, 255, 0.05), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
  z-index: 0;
}

.location-menu:hover::before {
  transform: translateX(100%);
}

.home-button {
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  padding: 0.4rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.home-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(100, 108, 255, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.home-button:hover {
  color: #2196f3;
  background-color: rgba(42, 42, 42, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(100, 108, 255, 0.2);
}

.home-button:hover::before {
  opacity: 1;
}

.home-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 3px rgba(100, 108, 255, 0.1);
}

.home-icon {
  font-size: 1.3rem;
  transition: all 0.3s ease;
}

.home-button:hover .home-icon {
  transform: scale(1.1);
}

.breadcrumb-container {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  overflow-x: auto;
  padding: 0.4rem 0.75rem;
  flex-grow: 1;
  position: relative;
  z-index: 1;
  scrollbar-width: thin;
  scrollbar-color: rgba(100, 108, 255, 0.3) transparent;
}

.breadcrumb-container::-webkit-scrollbar {
  height: 4px;
}

.breadcrumb-container::-webkit-scrollbar-track {
  background: transparent;
}

.breadcrumb-container::-webkit-scrollbar-thumb {
  background-color: rgba(100, 108, 255, 0.3);
  border-radius: 2px;
}

.breadcrumb-separator {
  color: #555;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.breadcrumb-item {
  padding: 0.4rem 0.75rem;
  border-radius: 6px;
  font-size: 0.95rem;
  white-space: nowrap;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
}

.breadcrumb-item.clickable {
  cursor: pointer;
  color: #aaa;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.breadcrumb-item.clickable::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(100, 108, 255, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.breadcrumb-item.clickable:hover {
  color: white;
  background-color: rgba(42, 42, 42, 0.8);
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.breadcrumb-item.clickable:hover::before {
  opacity: 1;
}

.breadcrumb-item.clickable:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.breadcrumb-item.current {
  color: #2196f3;
  font-weight: 600;
  background-color: rgba(100, 108, 255, 0.1);
  box-shadow: 0 0 0 1px rgba(100, 108, 255, 0.2);
  animation: pulseCurrentItem 2s infinite alternate;
}

@keyframes pulseCurrentItem {
  0% { box-shadow: 0 0 0 1px rgba(100, 108, 255, 0.2); }
  100% { box-shadow: 0 0 0 1px rgba(100, 108, 255, 0.4); }
}

.new-folder-button {
  background-color: rgba(42, 42, 42, 0.8);
  color: white;
  border: 1px solid rgba(51, 51, 51, 0.8);
  padding: 0.4rem 0.9rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  display: flex;
  align-items: center;
  gap: 0.4rem;
  font-size: 0.85rem;
  font-weight: 500;
  margin-left: auto;
  position: relative;
  z-index: 1;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.new-folder-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(100, 108, 255, 0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
  z-index: -1;
}

.new-folder-button:hover {
  background-color: rgba(58, 58, 58, 0.9);
  border-color: #2196f3;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(100, 108, 255, 0.2);
}

.new-folder-button:hover::before {
  transform: translateX(100%);
}

.new-folder-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(100, 108, 255, 0.1);
}

.folder-icon {
  font-size: 1rem;
  transition: all 0.3s ease;
  color: #2196f3;
  filter: drop-shadow(0 0 3px rgba(100, 108, 255, 0.2));
}

.new-folder-button:hover .folder-icon {
  transform: scale(1.1) rotate(5deg);
  filter: drop-shadow(0 0 5px rgba(100, 108, 255, 0.4));
}
