/* Custom Bucket Selector Styles */
.bucket-selector {
  position: relative;
  min-width: 480px;
  max-width: 320px;
}

.bucket-selector-loading {
  display: flex;
  width: 100%;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  color: #6b7280;
  font-size: 14px;
}

.bucket-selector-error {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  color: #ef4444;
  font-size: 14px;
}

.bucket-selector-error .error-icon {
  width: 16px;
  height: 16px;
}

.bucket-selector-label {
  font-size: 12px;
  color: #9ca3af;
  margin-bottom: 4px;
  font-weight: 500;
}

.bucket-selector-trigger {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;
  min-height: 44px;
  border-radius: 10px;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  color: #ffffff;
  min-height: 44px;
  border-radius: 10px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.bucket-selector-trigger:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(59, 130, 246, 0.4);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.bucket-selector-trigger.open {
  background: rgba(255, 255, 255, 0.12);
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.bucket-selector-trigger.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.bucket-selector-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.bucket-selector-text {
  flex: 1;
}

.bucket-icon {
  width: 16px;
  height: 16px;
  color: #6b7280;
  flex-shrink: 0;
}

.bucket-chevron {
  width: 16px;
  height: 16px;
  color: #6b7280;
  transition: transform 0.2s ease;
  flex-shrink: 0;
}

.bucket-chevron.open {
  transform: rotate(180deg);
}

.bucket-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #1f2937;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  max-height: 300px;
  overflow-y: auto;
  z-index: 1000;
  margin-top: 4px;
  padding: 8px;
}

.bucket-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 2px 0;
}

.bucket-option:hover {
  background-color: rgba(59, 130, 246, 0.1);
}

.bucket-option.selected {
  background-color: rgba(59, 130, 246, 0.2);
}

.bucket-option-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.bucket-title {
  font-weight: 500;
  color: #ffffff;
  font-size: 14px;
}

.bucket-name {
  font-size: 12px;
  color: #9ca3af;
}

.bucket-placeholder {
  color: #9ca3af;
  font-size: 14px;
}

.bucket-badge {
  display: inline-flex;
  align-items: center;
  background: #3b82f6;
  color: #ffffff;
  font-size: 10px;
  font-weight: 600;
  padding: 3px 8px;
  border-radius: 12px;
  white-space: nowrap;
}

/* Responsive design */
@media (max-width: 768px) {
  .bucket-selector {
    min-width: 200px;
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .bucket-selector {
    width: 100%;
  }

  .bucket-selector-trigger {
    font-size: 13px;
    padding: 10px 12px;
    min-height: 40px;
  }

  .bucket-title {
    font-size: 13px;
  }

  .bucket-name {
    font-size: 11px;
  }
}
