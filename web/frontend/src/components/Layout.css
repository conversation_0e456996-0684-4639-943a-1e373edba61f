.layout {
  display: flex;
  flex-direction: row;
  height: 100vh;
  width: 100%;
  background-color: #121212;
  position: relative;
  overflow: hidden; /* Ensures the footer stays within bounds */
}

.content {
  flex: 1;
  overflow-y: auto;
  background-color: #121212;
  transition: margin-left 0.3s ease;
  padding: 1rem;
  padding-top: 72px;
  /* margin-left: 250px; */
}

.content-expanded {
  margin-left: 70px;
}

@media (max-width: 768px) {
  .layout {
    flex-direction: column;
  }

  .content {
    margin-left: 0;
    padding-top: 60px;
  }
}
