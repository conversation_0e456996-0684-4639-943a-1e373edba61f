import { useState, useEffect, useRef } from "react";
import { HiInformationCircle, HiXMark, HiCheckCircle } from "react-icons/hi2";
import { useDispatch, useSelector } from "react-redux";
import {
  changeSchedulerDetail,
  selectSchedule,
} from "@/redux/schedulerSlice.ts";
import { toast } from "react-toastify";
import { getNetworkInterfaces } from "@/api/recorderApi";
import { getSchedules } from "@/api/schedulerApi";
import { NetworkInterface } from "@/types/network";
import { Schedule } from "@/types/schedule";
import "./OutputSettingModal.css";

interface OutputSettingModalProps {
  close: () => void;
  setChangeAction: (value: boolean) => void;
}

const OutputSettingModal = ({
  close,
  setChangeAction,
}: OutputSettingModalProps) => {
  const dispatch = useDispatch();
  const schedule = useSelector(selectSchedule);
  const inputRef = useRef<HTMLInputElement>(null);

  // Extract the host:port part from the full RTP URL if it exists
  const extractAddressAndPort = (fullUrl: string): string => {
    if (!fullUrl) return "";
    try {
      const url = new URL(fullUrl);
      return url.host; // Returns hostname:port or just hostname
    } catch (e) {
      return fullUrl.replace(/^rtp:\/\//, "");
    }
  };

  const [addressAndPort, setAddressAndPort] = useState<string>(
    extractAddressAndPort(schedule.output_url || "")
  );
  const [isValid, setIsValid] = useState<boolean | null>(null);
  const [networkInterfaces, setNetworkInterfaces] = useState<
    NetworkInterface[]
  >([]);
  const [selectedInterface, setSelectedInterface] = useState<string>(
    schedule.network_interface || ""
  );
  const [loading, setLoading] = useState(false);
  const [allSchedules, setAllSchedules] = useState<Schedule[]>([]);
  const [validationError, setValidationError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Load network interfaces
        const interfaces = await getNetworkInterfaces();
        setNetworkInterfaces(interfaces);

        // Load all schedules for validation
        const schedulesResult = await getSchedules(1, 100);
        setAllSchedules(schedulesResult.items);
      } catch (error) {
        console.error("Failed to fetch data:", error);
        toast.error("Failed to load required data");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleNetworkInterfaceChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    setSelectedInterface(e.target.value);
  };

  // Validate IP address format
  const isValidIPv4 = (ip: string): boolean => {
    // IPv4 regex pattern
    const ipv4Pattern = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipv4Pattern.test(ip);
  };

  // Validate port number
  const isValidPort = (port: string): boolean => {
    const portNum = parseInt(port, 10);
    return !isNaN(portNum) && portNum >= 1 && portNum <= 65535;
  };

  // Validate the RTP settings
  const validateRtpSettings = (): boolean => {
    // Reset validation error
    setValidationError(null);
    setIsValid(null);

    // If address and port is empty, no validation needed
    if (!addressAndPort.trim()) {
      return true;
    }

    try {
      // Split the address and port
      const [ipAddress, port] = addressAndPort.split(':');

      // Clean up the IP address and port (in case they contain invalid characters)
      const cleanIpAddress = ipAddress ? ipAddress.replace(/[^0-9.]/g, '') : '';
      const cleanPort = port ? port.replace(/[^0-9]/g, '') : '';

      // Validate IP address
      if (!isValidIPv4(cleanIpAddress)) {
        setValidationError("Invalid IP address format. Must be a valid IPv4 address (e.g., ***********)");
        setIsValid(false);
        return false;
      }

      // Validate port if provided
      if (cleanPort !== undefined) {
        if (!isValidPort(cleanPort)) {
          setValidationError("Invalid port number. Must be between 1 and 65535");
          setIsValid(false);
          return false;
        }
      } else {
        setValidationError("Port number is required (e.g., ***********:5001)");
        setIsValid(false);
        return false;
      }

      // Construct the full RTP URL for conflict checking
      const formattedAddressAndPort = `${cleanIpAddress}:${cleanPort}`;
      const fullRtpUrl = `rtp://${formattedAddressAndPort}`;

      // Check if the address and port are already in use by another schedule
      const conflictingSchedule = allSchedules.find(
        (s) =>
          s.id !== schedule.id &&
          s.output_url &&
          s.output_url.toLowerCase() === fullRtpUrl.toLowerCase()
      );

      if (conflictingSchedule) {
        setValidationError(
          `This address and port are already in use by another schedule: "${conflictingSchedule.name}"`
        );
        setIsValid(false);
        return false;
      }

      // Update the input with the cleaned value if it's different
      if (formattedAddressAndPort !== addressAndPort) {
        setAddressAndPort(formattedAddressAndPort);
      }

      setIsValid(true);
      return true;
    } catch (error) {
      // General error
      setValidationError("Invalid format. Please use format: ***********:5001");
      setIsValid(false);
      return false;
    }
  };

  // Add effect to provide real-time feedback without being too restrictive
  useEffect(() => {
    if (addressAndPort.trim()) {
      // Only show validation feedback if the input looks like it might be complete
      // This prevents showing errors while the user is still typing
      const [ipAddress, port] = addressAndPort.split(':');

      // Only validate if we have something that looks like a complete IP address
      // (has 3 dots) or if we have a port
      const hasPotentiallyCompleteIp = (ipAddress.match(/\./g) || []).length === 3;
      const hasPort = port !== undefined && port.length > 0;

      if (hasPotentiallyCompleteIp || hasPort) {
        const isIpValid = isValidIPv4(ipAddress);
        const isPortValid = port !== undefined && isValidPort(port);

        // Only set validation state if we have enough information to make a determination
        if (hasPotentiallyCompleteIp && hasPort) {
          setIsValid(isIpValid && isPortValid ? true : false);

          if (!isIpValid) {
            setValidationError("Invalid IP address format");
          } else if (!isPortValid) {
            setValidationError("Invalid port number");
          } else {
            setValidationError(null);
          }
        } else {
          // If we don't have complete information, show a neutral state
          setIsValid(null);
          setValidationError(null);
        }
      } else {
        // Still typing, don't show validation errors yet
        setIsValid(null);
        setValidationError(null);
      }
    } else {
      // Empty input, reset validation state
      setIsValid(null);
      setValidationError(null);
    }
  }, [addressAndPort]);

  const handleSave = () => {
    try {
      // Validate RTP settings
      if (!validateRtpSettings()) {
        return;
      }

      // Construct the full RTP URL
      const fullRtpUrl = addressAndPort.trim() ? `rtp://${addressAndPort}` : "";

      // Save output URL
      dispatch(
        changeSchedulerDetail({
          field: "output_url",
          value: fullRtpUrl,
        })
      );

      // Save network interface
      dispatch(
        changeSchedulerDetail({
          field: "network_interface",
          value: selectedInterface,
        })
      );

      setChangeAction(true);
      close();
    } catch (err) {
      console.error("Failed to update output settings: ", err);
      toast.error("Failed to save output settings");
    }
  };

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h2>Output Settings</h2>
          <button className="close-button" onClick={close}>
            <HiXMark />
          </button>
        </div>
        <div className="modal-body">
          <div className="input-group">
            <div className="input-label">Output URL</div>
            <div className="input-field">
              <div className="input-wrapper">
                <div className="rtp-input-container">
                  <span className="rtp-prefix">rtp://</span>
                  <input
                    ref={inputRef}
                    type="text"
                    value={addressAndPort}
                    onChange={(e) => {
                      // Allow any input without filtering
                      setAddressAndPort(e.target.value);

                      // Clear validation error when user types
                      setValidationError(null);
                    }}
                    placeholder="***********:5001"
                    className={`rtp-input bg-[#151515A3] p-3 w-full h-[40px] focus-visible:outline-0 border rounded-md ${
                      isValid === true
                        ? "valid"
                        : isValid === false
                        ? "invalid"
                        : "border-[#EDEDEDFF]"
                    }`}
                    onBlur={() => {
                      if (addressAndPort.trim()) {
                        // Format the input to clean it up
                        const [ipPart, portPart] = addressAndPort.split(':');

                        // Extract only valid characters for IP (numbers and dots)
                        const cleanIpPart = ipPart ? ipPart.replace(/[^0-9.]/g, '') : '';

                        // Extract only valid characters for port (numbers)
                        const cleanPortPart = portPart ? portPart.replace(/[^0-9]/g, '') : '';

                        // Reconstruct the formatted value
                        const formattedValue = cleanPortPart
                          ? `${cleanIpPart}:${cleanPortPart}`
                          : cleanIpPart;

                        // Update the input with the cleaned value
                        if (formattedValue !== addressAndPort) {
                          setAddressAndPort(formattedValue);
                        }

                        // Validate the formatted value
                        validateRtpSettings();
                      }
                    }}
                  />
                  {isValid === true && (
                    <HiCheckCircle className="absolute right-3 text-green-500" size={20} />
                  )}
                </div>
              </div>
              {validationError && (
                <div className="validation-error mt-1 text-red-500 text-sm">
                  {validationError}
                </div>
              )}
            </div>
          </div>

          <div className="input-group mt-4">
            <div className="input-label">Network Interface</div>
            <div className="input-field">
              <div className="input-wrapper">
                <select
                  value={selectedInterface}
                  onChange={handleNetworkInterfaceChange}
                  className="bg-[#151515A3] p-3 w-full h-[40px] focus-visible:outline-0 focus:border-[#ffa500] border border-[#EDEDEDFF] rounded-md"
                >
                  <option value="">Auto (System Default)</option>
                  {loading ? (
                    <option disabled>Loading interfaces...</option>
                  ) : networkInterfaces.length === 0 ? (
                    <option disabled>No network interfaces found</option>
                  ) : (
                    networkInterfaces.map((iface) => (
                      <option key={iface.name} value={iface.name}>
                        {iface.name} - {iface.ip_address}
                      </option>
                    ))
                  )}
                </select>
              </div>
            </div>
          </div>

          <div className="info-message mt-4">
            <HiInformationCircle className="info-icon" />
            <span>
              Enter the URL where the scheduler output will be streamed and
              select a network interface (optional)
            </span>
          </div>
        </div>
        <div className="modal-footer">
          <button className="modal-button cancel-button" onClick={close}>
            Cancel
          </button>
          <button
            className="modal-button confirm-button"
            onClick={handleSave}
            disabled={!!validationError}
          >
            Save
          </button>
        </div>
      </div>
    </div>
  );
};

export default OutputSettingModal;
