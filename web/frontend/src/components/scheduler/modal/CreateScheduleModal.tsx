import { useRef, useState } from "react";
import { HiXMark } from "react-icons/hi2";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { createSchedule } from "@/api/schedulerApi.ts";
import { EmptyScheduler } from "@/redux/schedulerSlice.ts";
import { generateShortId } from "@/utils/formatters.ts";

interface CreateScheduleModalProps {
  setIsLoading: (loading: boolean) => void;
  reloadPage: () => void;
  close: () => void;
}

const CreateScheduleModal = ({
  close,
  setIsLoading,
}: CreateScheduleModalProps) => {
  const navigate = useNavigate();
  const [name, setName] = useState<string>("");
  const inputRef = useRef<HTMLInputElement>(null);

  const handleCreate = async () => {
    if (!name) {
      if (inputRef.current) {
        inputRef.current.style.borderColor = "red";
      }
      return;
    }

    setIsLoading(true);
    try {
      const response = await createSchedule({
        ...EmptyScheduler,
        name: name,
        short_id: generateShortId(5),
      });
      navigate(`/scheduler/${response.id}`)
    } catch (err) {
      console.error("Failed to create scheduler: ", err)
      toast.error("Failed to create schedule")
    } finally {
      close();
      setIsLoading(false);
    }
  };

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h2>Create Schedule</h2>
          <button className="close-button" onClick={close}>
            <HiXMark />
          </button>
        </div>
        <div className="modal-body">
          <div className="input-group">
            <div className="input-label">Name</div>
            <div className="input-field">
              <div className="input-wrapper">
                <input
                  ref={inputRef}
                  className="modal-input"
                  placeholder="Name"
                  value={name}
                  onChange={(e) => {
                    setName(e.target.value);
                    if (e.target.value) {
                      e.target.style.borderColor = "#4D4D4D";
                    }
                  }}
                />
              </div>
            </div>
          </div>
        </div>
        <div className="modal-footer">
          <button
            className="modal-button cancel-button"
            onClick={close}
          >
            Cancel
          </button>
          <button
            className="modal-button confirm-button"
            onClick={handleCreate}
          >
            Create
          </button>
        </div>
      </div>
    </div>
  );
}

export default CreateScheduleModal;
