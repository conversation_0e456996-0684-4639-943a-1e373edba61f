import { useEffect, useState, useRef } from "react";
import { HiOutlineXCircle } from "react-icons/hi2";
import {
  HiP<PERSON>,
  HiP<PERSON>e,
  <PERSON>V<PERSON>umeUp,
  <PERSON>VolumeOff,
  HiOutlineDocumentText,
  HiOutlineClock,
  HiOutlineInformationCircle,
  HiOutlineFilm,
} from "react-icons/hi";
import ReactPlayer from "react-player";
import axios from "axios";
import "./ModernPreviewModal.css";

interface PreviewModalProps {
  onClose: () => void;
  shortId: string;
}

const PreviewModal = ({ onClose, shortId }: PreviewModalProps) => {
  const [isPlayerLoading, setIsPlayerLoading] = useState<boolean>(true);
  const [playerError, setPlayerError] = useState<boolean>(false);
  const [previewURL, setPreviewURL] = useState<string>("");
  const [isPlaying, setIsPlaying] = useState<boolean>(true);
  const [isMuted, setIsMuted] = useState<boolean>(false);
  const [progress, setProgress] = useState<number>(0);
  const [duration, setDuration] = useState<number>(0);
  const playerRef = useRef<ReactPlayer>(null);

  useEffect(() => {
    setPlayerError(false);
    setIsPlayerLoading(true);

    // Fetch the preview URL from the backend
    axios.get(`/api/v1/playout/preview/${shortId}`)
      .then(response => {
        setPreviewURL(response.data.previewUrl);
      })
      .catch(error => {
        console.error("Failed to get preview URL:", error);
        setPlayerError(true);
        setIsPlayerLoading(false);
      });
  }, [shortId]);

  const handlePlayerPlay = () => {
    setIsPlayerLoading(false);
    setIsPlaying(true);
  };

  const handlePlayerPause = () => {
    setIsPlaying(false);
  };

  const handlePlayerError = () => {
    setIsPlayerLoading(false);
    setPlayerError(true);
  };

  const togglePlay = () => {
    setIsPlaying(!isPlaying);
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
  };

  const handleProgress = (state: { played: number; playedSeconds: number; loaded: number; loadedSeconds: number }) => {
    setProgress(state.playedSeconds);
  };

  const handleDuration = (duration: number) => {
    setDuration(duration);
  };

  // Format seconds to HH:MM:SS
  const formatTime = (seconds: number): string => {
    if (isNaN(seconds)) return "00:00:00";

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    return [hours, minutes, secs]
      .map(val => val.toString().padStart(2, '0'))
      .join(':');
  };

  return (
    <div className="modern-preview-overlay">
      <div className="modern-preview-container">
        <div className="modern-player-wrapper">
          {isPlayerLoading && (
            <div className="modern-loading-overlay">
              <div className="modern-loading-spinner"></div>
              <div className="modern-loading-text">Loading stream preview...</div>
            </div>
          )}
          <div className="modern-player-container">
            {playerError ? (
              <div className="modern-error-container">
                <div className="modern-error-content">
                  <HiOutlineXCircle className="modern-error-icon" />
                  <div className="modern-error-message">
                    Failed to load stream preview. The stream may not be active or there might be a connection issue.
                  </div>
                </div>
              </div>
            ) : previewURL && (
              <ReactPlayer
                ref={playerRef}
                width="100%"
                height="100%"
                url={previewURL}
                volume={0.5}
                playing={isPlaying}
                muted={isMuted}
                controls={true}
                onPlay={handlePlayerPlay}
                onPause={handlePlayerPause}
                onError={handlePlayerError}
                onProgress={handleProgress}
                onDuration={handleDuration}
                config={{
                  file: {
                    forceHLS: true,
                    hlsOptions: {
                      maxBufferLength: 5,
                      maxMaxBufferLength: 10,
                      liveSyncDuration: 2
                    }
                  }
                }}
                style={{ backgroundColor: "#000" }}
              />
            )}
          </div>
          <button
            onClick={onClose}
            className="video-close-btn"
            title="Close"
            aria-label="Close preview"
          >
            ×
          </button>
        </div>

        <div className="modern-file-info">
          <div className="modern-file-header">
            <h3 className="modern-file-name">Stream Preview: {shortId}</h3>
            <div className="modern-media-controls">
              <button
                className="modern-media-button"
                onClick={togglePlay}
                title={isPlaying ? "Pause" : "Play"}
              >
                {isPlaying ? <HiPause className="modern-media-icon" /> : <HiPlay className="modern-media-icon" />}
                <span>{isPlaying ? "Pause" : "Play"}</span>
              </button>
              <button
                className="modern-media-button"
                onClick={toggleMute}
                title={isMuted ? "Unmute" : "Mute"}
              >
                {isMuted ?
                  <HiVolumeOff className="modern-media-icon" /> :
                  <HiVolumeUp className="modern-media-icon" />
                }
                <span>{isMuted ? "Unmute" : "Mute"}</span>
              </button>
            </div>
          </div>

          <div className="modern-file-details">
            <div className="modern-file-detail" title="Stream ID">
              <HiOutlineDocumentText className="modern-detail-icon" />
              <span className="modern-detail-text">ID: {shortId}</span>
            </div>
            <div className="modern-file-detail" title="Current Time">
              <HiOutlineClock className="modern-detail-icon" />
              <span className="modern-detail-text">{formatTime(progress)} / {formatTime(duration)}</span>
            </div>
            <div className="modern-file-detail" title="Stream Location">
              <HiOutlineInformationCircle className="modern-detail-icon" />
              <span className="modern-detail-text">Live Stream</span>
            </div>
            <div className="modern-file-detail" title="Video Duration">
              <HiOutlineFilm className="modern-detail-icon" />
              <span className="modern-detail-text">{formatTime(duration)}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default PreviewModal;