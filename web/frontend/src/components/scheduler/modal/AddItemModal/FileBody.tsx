import { Fragment, useEffect, useState } from "react";
import { OrbitProgress } from "react-loading-indicators";
import {
  HiChevronRight,
  HiDocument,
  HiFolder,
  HiHome, HiInformationCircle,
  HiMiniPlusCircle,
  HiOutlineArchiveBoxXMark,
} from "react-icons/hi2";
import { HiDocumentRemove, HiFolderRemove } from "react-icons/hi";
import { useDispatch } from "react-redux";
import { addNewItem } from "@/redux/schedulerSlice.ts";
import { FileInfo, IFolder } from "@/types/schedule.ts";
import { getAllFilesGroupedByFolders } from "@/api/schedulerApi.ts";
import { Tooltip } from "@nextui-org/react";
import moment from "moment";
import { Tab, Tabs } from "@heroui/react";

interface FolderBodyProps {
  type: string;
  day: string;
  start: string;
  end: string;
  close: () => void;
  setChangeAction: (value: boolean) => void;
}

const FileBody = ({
  type,
  close,
  day,
  start,
  end,
  setChangeAction,
}: FolderBodyProps) => {
  const dispatch = useDispatch();

  const [files, setFiles] = useState<FileInfo[]>([]);
  const [folders, setFolders] = useState<string[]>([]);
  const [fillerFiles, setFillerFiles] = useState<FileInfo[]>([]);
  const [fillerFolders, setFillerFolders] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState<string>("content");
  const [loading, setLoading] = useState<boolean>(false);
  const [activeFolder, setActiveFolder] = useState<IFolder | null>(null);
  const [mainFolder, setMainFolder] = useState<IFolder | null>(null);

  useEffect(() => {
    const loadFiles = async () => {
      setLoading(true);

      const files = await getAllFilesGroupedByFolders();
      setActiveFolder(files);
      setMainFolder(files);

      setLoading(false);
    };

    loadFiles();
  }, []);

  const addItem = () => {
    let firstDay = moment(start).utc().day();
    let lastDay = moment(end).utc().day();

    if (firstDay != lastDay) {
      let startTime = start;
      let endTime =
        moment(start)
          .utc()
          .add(1, "days")
          .set("hours", 0)
          .set("minutes", 0)
          .set("seconds", 0)
          .format("YYYY-MM-DDTHH:mm:ss") + "Z";

      if (endTime !== end) {
        for (firstDay; firstDay <= lastDay; firstDay++) {
          dispatch(
            addNewItem({
              type: type,
              day: day,
              item: {
                type: "folder",
                start: startTime,
                end: endTime,
                connection: "",
                link: "",
                folders: folders,
                files: files,
                fillers: {
                  files: fillerFiles,
                  folders: fillerFolders,
                  pre_filler: null,
                },
                name: "",
                description: "",
                port: "",
                mode: "",
                expire_date: "",
                expire_time: "",
              },
            })
          );

          startTime = endTime;
          endTime =
            moment(startTime)
              .utc()
              .add(1, "days")
              .set("hours", 0)
              .set("minutes", 0)
              .set("seconds", 0)
              .format("YYYY-MM-DDTHH:mm:ss") + "Z";

          if (moment(endTime).isAfter(moment(end))) {
            endTime = end;
          }
        }

        setChangeAction(true);
        close();

        return;
      }
    }

    dispatch(
      addNewItem({
        type: type,
        day: day,
        item: {
          type: "folder",
          start: start,
          end: end,
          connection: "",
          link: "",
          folders: folders,
          files: files,
          fillers: {
            files: fillerFiles,
            folders: fillerFolders,
            pre_filler: null,
          },
          name: "",
          description: "",
          port: "",
          mode: "",
          expire_date: "",
          expire_time: "",
        },
      })
    );
    setChangeAction(true);
    close();
  };

  const addFolder = (addFolder: string | null | undefined) => {
    if (!addFolder) {
      return;
    }

    let alreadyAdded;
    switch (activeTab) {
      case "content":
        alreadyAdded = folders.find(folder => folder === addFolder);
        if (alreadyAdded) {
          return;
        }
        setFolders([...folders, addFolder]);
        break;
      case "fillers":
        alreadyAdded = fillerFolders.find(folder => folder === addFolder);
        if (alreadyAdded) {
          return;
        }
        setFillerFolders([...fillerFolders, addFolder]);
        break;
    }
  };

  const addFile = (addFile: string | null | undefined, id: number) => {
    if (!addFile) {
      return;
    }

    let alreadyAdded;
    switch (activeTab) {
      case "content":
        alreadyAdded = files.find(file => Number(file.id) === Number(id));
        if (alreadyAdded) {
          return;
        }
        setFiles([...files, {
          id: Number(id),
          path: addFile,
        }]);
        break;
      case "fillers":
        alreadyAdded = fillerFiles.find(file => Number(file.id) === Number(id));
        if (alreadyAdded) {
          return;
        }
        setFillerFiles([...fillerFiles, {
          id: Number(id),
          path: addFile,
        }]);
        break;
    }
  };

  const deleteFolder = (deleteFolder: string) => {
    switch (activeTab) {
      case "content":
        setFolders(folders.filter(folder => !(deleteFolder === folder)));
        break;
      case "fillers":
        setFillerFolders(fillerFolders.filter(folder => !(deleteFolder === folder)));
        break;
    }
  };

  const deleteFile = (deleteFile: FileInfo) => {
    switch (activeTab) {
      case "content":
        setFiles(files.filter(file => !(Number(deleteFile.id) === Number(file.id))));
        break;
      case "fillers":
        setFillerFiles(fillerFiles.filter(file => !(Number(deleteFile.id) === Number(file.id))));
        break;
    }
  };

  const selectActiveFolder = (path: string, folder: string) => {
    const index = path.search(folder);
    if (index === -1 || !mainFolder) {
      return;
    }

    const searchPath = path.substring(0, index) + folder;
    const searchFolder = searchFolderByPath(mainFolder, searchPath);

    setActiveFolder(searchFolder);
  };

  const searchFolderByPath = (data: IFolder, path: string): IFolder | null => {
    if (data.path === path) {
      return data;
    }

    if (data.folders && data.folders.length > 0) {
      for (const folder of data.folders) {
        const result = searchFolderByPath(folder, path);
        if (result) {
          return result;
        }
      }
    }

    return null;
  };

  const durationToTime = (time: number): string => {
    time = Math.round(time);
    let hours = Math.floor(time / 3600);
    let minutes = Math.floor((time - hours * 3600) / 60);
    let seconds = time - hours * 3600 - minutes * 60;

    return (
      (hours < 10 ? "0" + hours : hours) +
      ":" +
      (minutes < 10 ? "0" + minutes : minutes) +
      ":" +
      (seconds < 10 ? "0" + seconds : seconds)
    );
  };

  return (
    <>
      <div className="modal-body">
        {loading && (
          <div>
            <OrbitProgress color="#ffa500" size="small" />
          </div>
        )}
        {!loading && (
          <div className={"filler-body"}>
            {activeFolder && (
              <div>
                <div className={"location-menu"}>
                  <Tooltip content={`Go to 'Home'`} placement="bottom">
                    <button
                      onClick={() => selectActiveFolder(activeFolder.path, "/")}
                      className="breadcrumb-item clickable"
                    >
                      <HiHome className="home-icon" />
                    </button>
                  </Tooltip>
                  {activeFolder.path.split("/").map((folder, index, arr) => {
                    if (index > 0 && !folder) return null;

                    const isRoot = index === 0 && !folder;
                    const isLast = index === arr.length - 1;

                    if (isRoot) return null;

                    return (
                      <Fragment key={index}>
                        {index > 1 && (
                          <HiChevronRight className="breadcrumb-separator" />
                        )}

                        {isLast ? (
                          <span className="breadcrumb-item current">
                            {folder || "Home"}
                          </span>
                        ) : (
                          <Tooltip
                            content={`Go to ${folder || "Home"}`}
                            placement="bottom"
                          >
                            <button
                              onClick={() =>
                                selectActiveFolder(activeFolder.path, folder)
                              }
                              className="breadcrumb-item clickable"
                            >
                              {folder || "Home"}
                            </button>
                          </Tooltip>
                        )}
                      </Fragment>
                    );
                  })}
                </div>
                {activeFolder.folders.map((folder) => (
                  <div
                    className={"select-folder"}
                    key={`folder-${folder.folder}`}
                  >
                    <div
                      className={"select-folder-name"}
                      onClick={() => setActiveFolder(folder)}
                    >
                      <HiFolder />
                      <span>{folder.folder}</span>
                    </div>
                    <div className={"select-folder-button"}>
                      {folder.files.length > 0 && (
                        <button
                          onClick={() => {
                            addFolder(folder.path);
                          }}
                        >
                          <HiMiniPlusCircle />
                        </button>
                      )}
                    </div>
                  </div>
                ))}
                {activeFolder.files.map((file) => {
                  const filePath =
                    (activeFolder?.path === "/" ? "" : activeFolder?.path) +
                    "/" +
                    file.fileName;
                  return (
                    <div className={"select-folder"} key={`file-${file.name}`}>
                      <div
                        className={"select-folder-name"}
                        onClick={() => addFile(filePath, file.id)}
                      >
                        <HiDocument />
                        <span>
                          <span className={"file-duration"}>
                            {durationToTime(file.duration)}
                          </span>
                          {file.fileName}
                        </span>
                      </div>
                      <div className={"select-folder-button"}>
                        <button onClick={() => addFile(filePath, file.id)}>
                          <HiMiniPlusCircle />
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
            {!activeFolder && (
              <div className={"info-message"}>
                <HiOutlineArchiveBoxXMark />
                Folder is empty
              </div>
            )}
          </div>
        )}
        <div className={'filler-tabs'}>
          <Tabs aria-label="Options" onSelectionChange={(key) => setActiveTab(key.toString())} >
            <Tab key="content" title="Content" >
              <div className={"added-files"}>
                {folders.map((folder) => {
                  return (
                    <div className={"select-folder"}>
                <span>
                  {folder}
                </span>
                      <div className={"select-folder-button remove-button"}>
                        <button onClick={() => deleteFolder(folder)}>
                          <HiFolderRemove />
                        </button>
                      </div>
                    </div>
                  );
                })}
                {files.map((file) => {
                  return (
                    <div className={"select-folder"}>
                <span>
                  {file.path}
                </span>
                      <div className={"select-folder-button remove-button"}>
                        <button onClick={() => deleteFile(file)}>
                          <HiDocumentRemove />
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>
            </Tab>
            <Tab key="fillers" title="Fillers" >
              { fillerFiles.length === 0 && fillerFolders.length === 0 && (
                <div className={"info-message"}>
                  <HiInformationCircle
                    className={"h-[25px] w-[25px] min-w-[25px]"}
                  />
                  <p className={"flex items-center justify-center"}>
                    You can set up special fillers that will be used only within this block,
                    instead of the main fillers that are set up for the entire schedule.
                    <br />
                    *If the current fillers are unavailable, the main ones will be used.
                  </p>
                </div>
              ) }
              { (fillerFiles.length > 0 || fillerFolders.length > 0) && (
                <div className={"added-files"}>
                  {fillerFolders.map((folder) => {
                    return (
                      <div className={"select-folder"}>
                <span>
                  {folder}
                </span>
                        <div className={"select-folder-button remove-button"}>
                          <button onClick={() => deleteFolder(folder)}>
                            <HiFolderRemove />
                          </button>
                        </div>
                      </div>
                    );
                  })}
                  {fillerFiles.map((file) => {
                    return (
                      <div className={"select-folder"}>
                <span>
                  {file.path}
                </span>
                        <div className={"select-folder-button remove-button"}>
                          <button onClick={() => deleteFile(file)}>
                            <HiDocumentRemove />
                          </button>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </Tab>
          </Tabs>
        </div>
      </div>
      <div className="modal-footer">
        <button className="modal-button cancel-button" onClick={close}>
          Cancel
        </button>
        <button
          disabled={files.length === 0 && folders.length === 0}
          onClick={addItem}
          className="modal-button confirm-button"
        >
          Add
        </button>
      </div>
    </>
  );
};

export default FileBody;
