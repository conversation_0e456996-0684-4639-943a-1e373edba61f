import { HiXMark } from "react-icons/hi2";
import FileBody from "@/components/scheduler/modal/AddItemModal/FileBody.tsx";

interface AddItemModalProps {
  type: string;
  day: string;
  start: string;
  end: string;
  close: () => void;
  setChangeAction: (value: boolean) => void;
}

const AddItemModal = ({
  type: dayType,
  close,
  day,
  start,
  end,
  setChangeAction,
}: AddItemModalProps) => {
  return (
    <div className="modal-overlay">
      <div className="modal-content files-modal">
        <div className="modal-header">
          <h2>Add Item</h2>
          <button className="close-button" onClick={close}>
            <HiXMark />
          </button>
        </div>
        <FileBody
          type={dayType}
          start={start}
          day={day}
          end={end}
          close={close}
          setChangeAction={setChangeAction}
        />
      </div>
    </div>
  );
}

export default AddItemModal;
