/* Output Setting Modal Styles */
select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 100%;
  background-color: #151515a3;
  color: #fff;
  border: 1px solid #edededff;
  border-radius: 0.375rem;
  cursor: pointer;
  font-size: 0.9rem;
}

select:focus {
  outline: none;
  border-color: #ffa500;
}

select option {
  background-color: #1e1e1e;
  color: #fff;
  padding: 0.5rem;
}

.info-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
  color: #aaa;
  font-size: 0.85rem;
}

.info-icon {
  color: #2196f3;
  font-size: 1.1rem;
}

.validation-error {
  color: #ff5252;
  font-size: 0.85rem;
  margin-top: 0.25rem;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.confirm-button:disabled {
  background-color: #333 !important;
  color: #777 !important;
  cursor: not-allowed;
}

/* RTP URL Input Styling */
.rtp-input-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.rtp-prefix {
  position: absolute;
  left: 12px;
  color: #aaa;
  font-family: 'Roboto', sans-serif;
  z-index: 10;
  pointer-events: none;
  font-weight: 500;
}

.rtp-input {
  font-family: 'Roboto', sans-serif;
  transition: all 0.3s ease;
  padding-left: 48px !important; /* Space for the prefix */
}

.rtp-input:focus {
  border-color: #2196f3 !important;
  box-shadow: 0 0 0 1px rgba(33, 150, 243, 0.2);
  transform: translateY(-1px);
}

.rtp-input.valid {
  border-color: #4caf50 !important;
}

.rtp-input.invalid {
  border-color: #f44336 !important;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(5px); }
  to { opacity: 1; transform: translateY(0); }
}

.validation-error {
  animation: fadeIn 0.3s ease-out;
}
