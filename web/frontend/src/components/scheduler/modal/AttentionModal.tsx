import { HiInformationCircle, HiXMark } from "react-icons/hi2";
import { useDispatch, useSelector } from "react-redux";
import { openScheduler, selectSchedule } from "@/redux/schedulerSlice.ts";
import { generateGuideByScheduleId, updateSchedule } from "@/api/schedulerApi.ts";
import { toast } from "react-toastify";

interface AttentionModalProps {
  close: () => void;
  setIsLoading: (loading: boolean) => void;
}

const AttentionModal = ({
  close,
  setIsLoading,
}: AttentionModalProps) => {
  const dispatch = useDispatch();
  const schedule = useSelector(selectSchedule);

  const handleGenerateGuide = async () => {
    setIsLoading(true);
    try {
      const updatedSchedule = await updateSchedule(schedule.id, schedule);
      const updatedGuide = await generateGuideByScheduleId(schedule.id);

      dispatch(openScheduler({
        schedule: updatedSchedule,
        guide: updatedGuide,
      }));
    } catch (err) {
      console.error("Failed generate guide: ", err)
      toast.error("Failed generate guide")
    } finally {
      close();
      setIsLoading(false);
    }
  };

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h2>Regenerate Guide</h2>
          <button className="close-button" onClick={close}>
            <HiXMark />
          </button>
        </div>
        <div className="modal-body">
          <h3 style={{ paddingBottom: "20px" }}>
            Are you sure you want to force update the guide now?
          </h3>
          <p className={"text-justify text-small"}>
            The guide updates automatically every 12 hours and creates a
            schedule for the day that will be in 3 days. This allows you
            to maintain a static and up-to-date EPG. If you run the
            update now, the guide will update and overwrite the data
            immediately after the current program. The following
            problems may occur after the update:
          </p>
          <ul className={"text-justify text-small"}>
            <li>
              If the EPG is already in use somewhere, there may be
              discrepancies between the previously saved EPG and the
              current HLS
            </li>
            <li>
              Sorting of episodes may be disrupted or videos that have
              already been shown may be shown again
            </li>
          </ul>
          <div className="info-message">
            <HiInformationCircle className="info-icon" />
            <span>
                The latest changes to the scheduler will now also be saved
            </span>
          </div>

        </div>
        <div className="modal-footer">
          <button
            className="modal-button cancel-button"
            onClick={close}
          >
            Cancel
          </button>
          <button
            className="modal-button confirm-button"
            onClick={handleGenerateGuide}
          >
            Update
          </button>
        </div>
      </div>
    </div>
  );
}

export default AttentionModal;
