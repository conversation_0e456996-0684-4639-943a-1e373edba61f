import { HiXMark } from "react-icons/hi2";
import './EpgChangeFileModal.css';
import FileBody from "@/components/scheduler/modal/EpgChangeFileModal/FileBody.tsx";
import {Element as IElement, Schedule} from '@/types/schedule'

interface EpgChangeFileModalProps {
  close: () => void;
  setIsLoading: (value: boolean) => void;
  file: IElement
  schedule: Schedule
}

const EpgChangeFileModal = ({
  close,
  setIsLoading,
  file,
  schedule
}: EpgChangeFileModalProps) => {
  return (
    <div className="modal-overlay">
      <div className="modal-content files-modal">
        <div className="modal-header">
          <h2>EPG Change File</h2>
          <button className="close-button" onClick={close}>
            <HiXMark />
          </button>
        </div>
        <FileBody
          close={close}
          setIsLoading={setIsLoading}
          selectFile={file}
          schedule={schedule}
        />
      </div>
    </div>
  );
}

export default EpgChangeFileModal;
