import { Fragment, useEffect, useState } from "react";
import { OrbitProgress } from "react-loading-indicators";
import {
  HiChevronRight,
  HiDocument,
  HiFolder,
  HiH<PERSON>, HiMiniArrowPath,
  HiOutlineArchiveBoxXMark,
} from "react-icons/hi2";
import {FileInfo, IFile, IFolder, Schedule} from "@/types/schedule.ts";
import {changeGuideProgram, getAllFilesGroupedByFolders} from "@/api/schedulerApi.ts";
import { Tooltip } from "@nextui-org/react";
import { Element as IElement } from '@/types/schedule'
import {updateGuide} from "@/redux/schedulerSlice.ts";
import {useDispatch} from "react-redux";

interface FolderBodyProps {
  close: () => void;
  setIsLoading: (value: boolean) => void;
  selectFile: IElement,
  schedule: Schedule
}

const FileBody = ({
  close,
  setIsLoading,
  selectFile,
  schedule
}: FolderBodyProps) => {
  const dispatch = useDispatch();
  const [file, setFile] = useState<FileInfo | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [activeFolder, setActiveFolder] = useState<IFolder | null>(null);
  const [mainFolder, setMainFolder] = useState<IFolder | null>(null);
  const [folderIsEmpty, setFolderIsEmpty] = useState<boolean>(false);
  const [durationRange, setDurationRange] = useState<{min: number, max: number}>({
    min: 0,
    max: 99999
  });

  useEffect(() => {
    const loadFiles = async () => {
      setLoading(true);
/*
      const startTime = new Date(selectFile.start);
      const endTime = new Date(selectFile.end);
      const diffMs = endTime.getTime() - startTime.getTime();
      const blockTimeInSec = Math.floor(diffMs / 1000);

      let folder = await getMatchingFilesGroupedByFolders(blockTimeInSec);*/

      let folder = await getAllFilesGroupedByFolders();

      setSelectFileDurationRange(folder);
      setMainFolder(folder);

      if(selectFile?.file?.folder) {
        const searchFolder = searchFolderByPath(
            folder,
            selectFile.file.folder.endsWith('/') ? selectFile.file.folder.slice(0, -1) : selectFile.file.folder
        );
        if(searchFolder) {
          folder = searchFolder;
        }
      }

      setActiveFolder(folder);

      setLoading(false);
    };

    loadFiles();
  }, []);

  useEffect(() => {
    if (activeFolder) {
      setFolderIsEmpty(false);
      checkEmptyFolder(activeFolder);
    }
  }, [activeFolder]);

  const changeItem = async () => {
    setIsLoading(true);
    if(file && selectFile) {
      const guide = await changeGuideProgram(schedule.id, selectFile, file)
      dispatch(updateGuide(guide))
    }
    setIsLoading(false);
    close();
  };

  const setSelectFileDurationRange = (folder: IFolder) => {
    const currentFile = searchFileByPath(folder, selectFile.file.folder, selectFile.title)
    if(currentFile) {
      setDurationRange(getMaxAndMinFileDuration(currentFile.duration))
    }
  }

  function getMaxAndMinFileDuration(duration: number): {min: number, max: number} {
    const interval = 1800;

    let adjusted = duration - 1;
    if (adjusted < 0) {
      adjusted = 0;
    }

    const lower = Math.floor(adjusted / interval) * interval;
    const upper = lower + interval;

    return {min: lower, max: upper};
  }

  const changeFile = (newFilePath: string | null | undefined, id: number) => {
    if (!newFilePath) {
      return;
    }
    setFile({
      id: Number(id),
      path: newFilePath,
    });
  };

  const checkEmptyFolder = (folder: IFolder) => {
    let countFiles = 0;
    folder?.files.map((file) => {
      const filePath =
          (folder?.path === "/" ? "" : folder?.path) +
          "/" +
          file.fileName;
      if (filePath === selectFile.file.folder + selectFile.title) return;

      countFiles++;
      return file;
    });

    if(countFiles <= 0 && folder?.folders?.length <= 0) {
      setFolderIsEmpty(true)
    }
  }
  const selectActiveFolder = (path: string, folder: string) => {
    const index = path.search(folder);
    if (index === -1 || !mainFolder) {
      return;
    }

    const searchPath = path.substring(0, index) + folder;
    const searchFolder = searchFolderByPath(mainFolder, searchPath);

    setActiveFolder(searchFolder);
  };

  const searchFolderByPath = (data: IFolder, path: string): IFolder | null => {
    if (data.path === path) {
      return data;
    }

    if (data.folders && data.folders.length > 0) {
      for (const folder of data.folders) {
        const result = searchFolderByPath(folder, path);
        if (result) {
          return result;
        }
      }
    }

    return null;
  };
  const searchFileByPath = (data: IFolder, path: string, fileName: string): IFile | null => {

    if (data.files && data.files.length > 0) {
      for(const file of data.files) {
        if(file.fileName == fileName) {
          return file;
        }
      }
    }

    if (data.folders && data.folders.length > 0) {
      for (const folder of data.folders) {
        const result = searchFileByPath(folder, path, fileName);
        if (result) {
          return result;
        }
      }
    }

    return null;
  };

  const durationToTime = (time: number): string => {
    time = Math.round(time);
    let hours = Math.floor(time / 3600);
    let minutes = Math.floor((time - hours * 3600) / 60);
    let seconds = time - hours * 3600 - minutes * 60;

    return (
      (hours < 10 ? "0" + hours : hours) +
      ":" +
      (minutes < 10 ? "0" + minutes : minutes) +
      ":" +
      (seconds < 10 ? "0" + seconds : seconds)
    );
  };

  return (
    <>
      <div className="modal-body">
        {loading && (
          <div>
            <OrbitProgress color="#ffa500" size="small" />
          </div>
        )}

        {!loading && (
          <div className={"filler-body"}>
            {activeFolder && (
              <div>
                <div className={"location-menu"}>
                  <Tooltip content={`Go to 'Home'`} placement="bottom">
                    <button
                      onClick={() => selectActiveFolder(activeFolder.path, "/")}
                      className="breadcrumb-item clickable"
                    >
                      <HiHome className="home-icon" />
                    </button>
                  </Tooltip>
                  {activeFolder.path.split("/").map((folder, index, arr) => {
                    if (index > 0 && !folder) return null;

                    const isRoot = index === 0 && !folder;
                    const isLast = index === arr.length - 1;

                    if (isRoot) return null;

                    return (
                      <Fragment key={index}>
                        {index > 1 && (
                          <HiChevronRight className="breadcrumb-separator" />
                        )}

                        {isLast ? (
                          <span className="breadcrumb-item current">
                            {folder || "Home"}
                          </span>
                        ) : (
                          <Tooltip
                            content={`Go to ${folder || "Home"}`}
                            placement="bottom"
                          >
                            <button
                              onClick={() =>
                                selectActiveFolder(activeFolder.path, folder)
                              }
                              className="breadcrumb-item clickable"
                            >
                              {folder || "Home"}
                            </button>
                          </Tooltip>
                        )}
                      </Fragment>
                    );
                  })}
                </div>

                {activeFolder.folders.map((folder) => (
                  <div
                    className={"select-folder"}
                    key={`folder-${folder.folder}`}
                  >
                    <div
                      className={"select-folder-name"}
                      onClick={() => setActiveFolder(folder)}
                    >
                      <HiFolder />
                      <span>{folder.folder}</span>
                    </div>

                  </div>
                ))}
                { activeFolder.files.map((file) => {
                  const filePath =
                    (activeFolder?.path === "/" ? "" : activeFolder?.path) +
                    "/" +
                    file.fileName;
                  if(filePath === selectFile.file.folder + selectFile.title) return;

                  const canReplace = file.duration > durationRange.min && file.duration <= durationRange.max;
                  return (
                    <div className={`select-folder ${!canReplace ? 'disabled' : ''}`} key={`file-${file.name}`}>
                      <div
                        className={"select-folder-name"}
                        onClick={() => changeFile(filePath, file.id)}
                      >
                        <HiDocument />
                        <span>
                          <span className={"file-duration"}>
                            {durationToTime(file.duration)}
                          </span>
                          {file.fileName}
                        </span>
                      </div>
                      {canReplace
                          && (<div className={"select-folder-button"}>
                                    <button
                                      onClick={() => changeFile(filePath, file.id)}
                                    >
                                      <HiMiniArrowPath  />
                                    </button>
                              </div>)
                      }
                    </div>
                  );
                })}
              </div>
            )}
            {(!activeFolder || folderIsEmpty) && (
              <div className={"info-message"}>
                <HiOutlineArchiveBoxXMark />
                Folder is empty
              </div>
            )}
          </div>
        )}
        <div
          className={
            "w-full h-[100px] pt-2 gap-2 border-t-1 border-t-[#9e9e9e] overflow-auto epg-files-tabs"
          }
        >
              <div className={"added-files"}>
                { file && (
                    <div className={"select-folder"} key={file.id}>

                      <span>{selectFile.file.folder + selectFile.title}</span>
                      <span>
                        <HiMiniArrowPath  />
                      </span>
                      <span>{file.path}</span>
                    </div>
                )}
              </div>
        </div>
      </div>
      <div className="modal-footer">
        <button className="modal-button" onClick={close}>
          Close
        </button>
        { file &&
            <button onClick={changeItem} className="modal-button confirm-button">
          Save
        </button> }
      </div>
    </>
  );
};

export default FileBody;
