import { useEffect, useState, useRef } from "react";
import { HiOutlineXCircle } from "react-icons/hi2";
import {
  HiP<PERSON>,
  HiPause,
  HiVolumeUp,
  HiVolumeOff,
  HiOutlineClock,
  HiOutlineInformationCircle,
  HiOutlineFilm
} from "react-icons/hi";
import ReactPlayer from "react-player";
import "./ModernPreviewModal.css";

interface PreviewModalProps {
  close: () => void;
  previewURL: string;
}

const PreviewModal = ({ close, previewURL }: PreviewModalProps) => {
  const [isPlayerLoading, setIsPlayerLoading] = useState<boolean>(true);
  const [playerError, setPlayerError] = useState<boolean>(false);
  const [isPlaying, setIsPlaying] = useState<boolean>(true);
  const [isMuted, setIsMuted] = useState<boolean>(false);
  const [, setProgress] = useState<number>(0);
  const [duration, setDuration] = useState<number>(0);
  const playerRef = useRef<ReactPlayer>(null);

  useEffect(() => {
    setPlayerError(false);
    setIsPlayerLoading(true);
  }, [previewURL]);

  const handlePlayerPlay = () => {
    setIsPlayerLoading(false);
    setIsPlaying(true);
  };

  const handlePlayerPause = () => {
    setIsPlaying(false);
  };

  const handlePlayerError = () => {
    setIsPlayerLoading(false);
    setPlayerError(true);
  };

  const togglePlay = () => {
    setIsPlaying(!isPlaying);
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
  };

  const handleProgress = (state: { played: number; playedSeconds: number; loaded: number; loadedSeconds: number }) => {
    setProgress(state.playedSeconds);
  };

  const handleDuration = (duration: number) => {
    setDuration(duration);
  };

  const formatTime = (seconds: number): string => {
    if (!seconds) return "00:00";
    const date = new Date(seconds * 1000);
    const hh = date.getUTCHours();
    const mm = date.getUTCMinutes();
    const ss = date.getUTCSeconds().toString().padStart(2, "0");
    if (hh) {
      return `${hh}:${mm.toString().padStart(2, "0")}:${ss}`;
    }
    return `${mm}:${ss}`;
  };

  // Extract filename from URL
  const getFilename = (url: string): string => {
    try {
      const decodedUrl = decodeURIComponent(url);
      // Extract filename from URL
      const urlParts = decodedUrl.split('/');
      return urlParts[urlParts.length - 1] || "Video Preview";
    } catch (error) {
      // If there's an error parsing the URL, return a fallback
      return "Video Preview";
    }
  };

  const filename = getFilename(previewURL);

  return (
    <div className="modern-preview-overlay">
      <div className="modern-preview-container">
        <div className="modern-player-wrapper">
          {isPlayerLoading && (
            <div className="modern-loading-overlay">
              <div className="modern-loading-spinner"></div>
              <div className="modern-loading-text">Loading video...</div>
            </div>
          )}
          <div className="modern-player-container">
            {playerError ? (
              <div className="modern-error-container">
                <div className="modern-error-content">
                  <HiOutlineXCircle className="modern-error-icon" />
                  <div className="modern-error-message">
                    Failed to load video. The file may be corrupted or in an unsupported format.
                  </div>
                </div>
              </div>
            ) : (
              <ReactPlayer
                ref={playerRef}
                width="100%"
                height="100%"
                url={previewURL}
                playing={isPlaying}
                controls={true}
                volume={0.5}
                muted={isMuted}
                onPlay={handlePlayerPlay}
                onPause={handlePlayerPause}
                onError={handlePlayerError}
                onProgress={handleProgress}
                onDuration={handleDuration}
                config={{
                  file: {
                    attributes: {
                      controlsList: 'nodownload',
                      disablePictureInPicture: true,
                    },
                    forceVideo: true,
                  }
                }}
                style={{ backgroundColor: '#000' }}
              />
            )}
          </div>
          <button
            onClick={close}
            className="video-close-btn"
            title="Close"
          >
            ×
          </button>
        </div>

        <div className="modern-file-info">
          <div className="modern-file-header">
            <h3 className="modern-file-name">{filename}</h3>
            <div className="modern-media-controls">
              <button
                className="modern-media-button"
                onClick={togglePlay}
                title={isPlaying ? "Pause" : "Play"}
              >
                {isPlaying ? <HiPause className="modern-media-icon" /> : <HiPlay className="modern-media-icon" />}
                <span>{isPlaying ? "Pause" : "Play"}</span>
              </button>
              <button
                className="modern-media-button"
                onClick={toggleMute}
                title={isMuted ? "Unmute" : "Mute"}
              >
                {isMuted ?
                  <HiVolumeOff className="modern-media-icon" /> :
                  <HiVolumeUp className="modern-media-icon" />
                }
                <span>{isMuted ? "Unmute" : "Mute"}</span>
              </button>
            </div>
          </div>

          <div className="modern-file-details">
            <div className="modern-file-detail">
              <HiOutlineFilm className="modern-detail-icon" />
              <span className="modern-detail-text">Video Preview</span>
            </div>
            {duration > 0 && (
              <div className="modern-file-detail">
                <HiOutlineClock className="modern-detail-icon" />
                <span className="modern-detail-text">{formatTime(duration)}</span>
              </div>
            )}
            <div className="modern-file-detail">
              <HiOutlineInformationCircle className="modern-detail-icon" />
              <span className="modern-detail-text">From Guide View</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default PreviewModal;