import { useState } from "react";
import { HiXMark } from "react-icons/hi2";
import QuestionModal from "@/components/scheduler/modal/ContentSettingModal/QuestionModal.tsx";
import { toast } from "react-toastify";
import FileBody from "@/components/scheduler/modal/ContentSettingModal/FileBody.tsx";
import { IFile } from "@/types/schedule.ts";
import { batchUpdateFiles } from "@/api/schedulerApi.ts";
import './ContentSettingModal.css';

interface ContentSettingModalProps {
  close: () => void;
  setIsLoading: (value: boolean) => void;
}

export default function ContentSettingModal({
  close,
  setIsLoading,
}: ContentSettingModalProps) {
  const [updatingFiles, setUpdatingFiles] = useState<IFile[]>([]);
  const [isOpenQuestion, setIsOpenQuestion] = useState<boolean>(false);

  const handleClose = () => {
    if (updatingFiles.length === 0) {
      return close();
    }

    setIsOpenQuestion(true);
  }

  const handleSave = async () => {
    setIsLoading(true);
    try {
      await batchUpdateFiles(updatingFiles);
    } catch (err) {
      console.error("Failed to save changing: ", err)
      toast.error("Failed to save changing")
    } finally {
      setIsLoading(false);
      close();
    }
  }

  return (
    <>
      <div className="modal-overlay">
        <div className="modal-content files-modal">
          <div className="modal-header">
            <h2>Content Metadata</h2>
            <button className="close-button" onClick={close}>
              <HiXMark />
            </button>
          </div>
            <FileBody
              close={handleClose}
              handleSave={handleSave}
              updatingFiles={updatingFiles}
              setUpdatingFiles={setUpdatingFiles}
            />
        </div>
      </div>
      {
        isOpenQuestion &&
        <QuestionModal
          approve={close}
          reject={() => setIsOpenQuestion(false)}
        />
      }
    </>
  );
}
