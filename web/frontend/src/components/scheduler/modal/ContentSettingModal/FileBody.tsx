import { Fragment, useEffect, useRef, useState } from "react";
import { OrbitProgress } from "react-loading-indicators";
import {
  HiChevronRight,
  HiFolder,
  HiHome,
  HiOutlineArchiveBoxXMark,
} from "react-icons/hi2";
import path from "path-browserify";
import { IFile, IFolder } from "@/types/schedule.ts";
import { getAllFilesGroupedByFolders } from "@/api/schedulerApi.ts";
import {
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
  Tooltip,
} from "@nextui-org/react";
import { CSSProperties } from "react";

// Define CSS styles
const styles = {
  activeRow: {
    backgroundColor: "rgba(255, 165, 0, 0.1)",
    transition: "background-color 0.3s ease",
  } as CSSProperties,
  editorRow: {
    transition: "max-height 0.3s ease, opacity 0.3s ease",
    overflow: "hidden",
  } as CSSProperties,
  editorVisible: {
    maxHeight: "600px",
    opacity: 1,
  } as CSSProperties,
  editorHidden: {
    maxHeight: 0,
    opacity: 0,
  } as CSSProperties,
  fileForm: {
    padding: "16px",
    margin: 0,
    transition: "transform 0.3s ease, opacity 0.3s ease",
    background: "rgba(0, 0, 0, 0.02)",
    borderRadius: "8px",
    boxShadow: "0 4px 12px rgba(0, 0, 0, 0.05)",
  } as CSSProperties,
  formVisible: {
    transform: "translateY(0)",
    opacity: 1,
  } as CSSProperties,
  formHidden: {
    transform: "translateY(-20px)",
    opacity: 0,
  } as CSSProperties,
  fileTitle: {
    marginTop: 0,
    marginBottom: "16px",
    fontSize: "1.2rem",
    color: "#555",
  } as CSSProperties,
  formField: {
    marginBottom: "16px",
  } as CSSProperties,
  formLabel: {
    display: "block",
    marginBottom: "8px",
    fontWeight: 500,
    color: "#666",
  } as CSSProperties,
  formInput: {
    width: "100%",
    padding: "10px 12px",
    border: "1px solid #ddd",
    borderRadius: "6px",
    transition: "border-color 0.2s ease, box-shadow 0.2s ease",
  } as CSSProperties,
  formInputFocus: {
    borderColor: "#ffa500",
    boxShadow: "0 0 0 2px rgba(255, 165, 0, 0.2)",
    outline: "none",
  } as CSSProperties,
  formTextarea: {
    minHeight: "100px",
    resize: "vertical",
    width: "100%",
    padding: "10px 12px",
    border: "1px solid #ddd",
    borderRadius: "6px",
    transition: "border-color 0.2s ease, box-shadow 0.2s ease",
  } as CSSProperties,
  formActions: {
    display: "flex",
    flexDirection: "row",
    gap: "12px",
    justifyContent: "flex-end",
    marginTop: "20px",
  } as CSSProperties,
  modalButton: {
    padding: "8px 16px",
    borderRadius: "6px",
    transition: "background-color 0.2s ease, transform 0.1s ease",
  } as CSSProperties,
  modalButtonHover: {
    transform: "translateY(-1px)",
  } as CSSProperties,
  modalButtonActive: {
    transform: "translateY(1px)",
  } as CSSProperties,
  zeroWidthCell: {
    width: 0,
    padding: 0,
    margin: 0,
    height: 0,
    border: "none",
    overflow: "hidden",
    opacity: 0,
  } as CSSProperties,
};

interface FolderBodyProps {
  close: () => void;
  handleSave: () => void;
  updatingFiles: IFile[];
  setUpdatingFiles: (updatingFiles: IFile[]) => void;
}

export default function FileBody({
  close,
  handleSave,
  updatingFiles,
  setUpdatingFiles,
}: FolderBodyProps) {
  const [loading, setLoading] = useState<boolean>(false);
  const [activeFolder, setActiveFolder] = useState<IFolder | null>(null);
  const [mainFolder, setMainFolder] = useState<IFolder | null>(null);
  const [activeFile, setActiveFile] = useState<IFile | null>(null);
  const [updatedFile, setUpdatedFile] = useState<IFile | null>(null);
  const [editorVisible, setEditorVisible] = useState<boolean>(false);
  const myRef = useRef<HTMLDivElement | null>(null);

  const scrollToElement = () => {
    myRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    const loadFiles = async () => {
      setLoading(true);

      const files = await getAllFilesGroupedByFolders();
      setMainFolder(files);
      setActiveFolder(files);

      setLoading(false);
    };

    loadFiles();
  }, []);

  const selectActiveFolder = (path: string, folder: string) => {
    const index = path.search(folder);
    if (index === -1 || !mainFolder) {
      return;
    }

    const searchPath = path.substring(0, index) + folder;
    const searchFolder = searchFolderByPath(mainFolder, searchPath);

    setActiveFolder(searchFolder);
  };

  const searchFolderByPath = (data: IFolder, path: string): IFolder | null => {
    if (data.path === path) {
      return data;
    }

    if (data.folders && data.folders.length > 0) {
      for (const folder of data.folders) {
        const result = searchFolderByPath(folder, path);
        if (result) {
          return result;
        }
      }
    }

    return null;
  };

  const durationToTime = (time: number): string => {
    time = Math.round(time);
    let hours = Math.floor(time / 3600);
    let minutes = Math.floor((time - hours * 3600) / 60);
    let seconds = time - hours * 3600 - minutes * 60;

    return (
      (hours < 10 ? "0" + hours : hours) +
      ":" +
      (minutes < 10 ? "0" + minutes : minutes) +
      ":" +
      (seconds < 10 ? "0" + seconds : seconds)
    );
  };

  const closeEditor = () => {
    setEditorVisible(false);
    setTimeout(() => {
      setActiveFile(null);
      setUpdatedFile(null);
    }, 300); // Match the transition duration
  };

  const toggleActiveFile = (file: IFile | null) => {
    if (file && file.id !== activeFile?.id) {
      if (activeFile && updatedFile && !isEqual(activeFile, updatedFile)) {
        const alreadyAdded = updatingFiles.find(
          (item) => item.id === updatedFile.id
        );
        if (alreadyAdded) {
          setUpdatingFiles(
            updatingFiles.map((item) => {
              if (item.id === updatedFile.id) {
                return updatedFile;
              }
              return item;
            })
          );
        } else {
          setUpdatingFiles([...updatingFiles, updatedFile]);
        }
      }

      // Close current editor if open
      if (activeFile) {
        setEditorVisible(false);
        setTimeout(() => {
          setActiveFile(file);
          setUpdatedFile(file);
          setEditorVisible(true);
          setTimeout(() => scrollToElement(), 100);
        }, 300);
      } else {
        setActiveFile(file);
        setUpdatedFile(file);
        setEditorVisible(true);
        setTimeout(() => scrollToElement(), 100);
      }
    } else if (file && file.id === activeFile?.id) {
      if (activeFile && updatedFile && !isEqual(activeFile, updatedFile)) {
        const alreadyAdded = updatingFiles.find(
          (item) => item.id === updatedFile.id
        );
        if (alreadyAdded) {
          setUpdatingFiles(
            updatingFiles.map((item) => {
              if (item.id === updatedFile.id) {
                return updatedFile;
              }
              return item;
            })
          );
        } else {
          setUpdatingFiles([...updatingFiles, updatedFile]);
        }
      }

      closeEditor();
    } else {
      setActiveFile(file);
    }
  };

  const isEqual = (active: IFile, updated: IFile): boolean => {
    return (
      active.name === updated.name &&
      active.episode === updated.episode &&
      active.description === updated.description
    );
  };

  return (
    <>
      <div className="modal-body">
        {loading && (
          <div>
            <OrbitProgress color="#ffa500" size="small" />
          </div>
        )}
        {!loading && (
          <div className={"file-body"}>
            {activeFolder && (
              <div>
                <div className={"location-menu"}>
                  <Tooltip content={`Go to 'Home'`} placement="bottom">
                    <button
                      onClick={() => selectActiveFolder(activeFolder.path, "/")}
                      className="breadcrumb-item clickable"
                    >
                      <HiHome className="home-icon" />
                    </button>
                  </Tooltip>
                  {activeFolder.path.split("/").map((folder, index, arr) => {
                    if (index > 0 && !folder) return null;

                    const isRoot = index === 0 && !folder;
                    const isLast = index === arr.length - 1;

                    if (isRoot) return null;

                    return (
                      <Fragment key={index}>
                        {index > 1 && (
                          <HiChevronRight className="breadcrumb-separator" />
                        )}

                        {isLast ? (
                          <span className="breadcrumb-item current">
                            {folder || "Home"}
                          </span>
                        ) : (
                          <Tooltip
                            content={`Go to ${folder || "Home"}`}
                            placement="bottom"
                          >
                            <button
                              onClick={() =>
                                selectActiveFolder(activeFolder.path, folder)
                              }
                              className="breadcrumb-item clickable"
                            >
                              {folder || "Home"}
                            </button>
                          </Tooltip>
                        )}
                      </Fragment>
                    );
                  })}
                </div>
                {activeFolder.folders.map((folder) => (
                  <div
                    className={"select-folder"}
                    key={`folder-${folder.path}`}
                  >
                    <div
                      onClick={() => setActiveFolder(folder)}
                      className={"select-folder-name"}
                    >
                      <HiFolder />
                      <span>{folder.folder}</span>
                    </div>
                  </div>
                ))}
                {activeFolder.files.length > 0 && (
                  <Table
                    aria-label="Files table"
                    classNames={{
                      base: "table-container",
                      table: "w-full h-full",
                      thead: "table-header",
                      tbody: "table-body",
                      th: "table-header-cell",
                      td: "table-cell",
                    }}
                    shadow="none"
                    removeWrapper
                  >
                    <TableHeader>
                      <TableColumn style={{ width: "30%" }}>Name</TableColumn>
                      <TableColumn style={{ width: "20%" }}>
                        Duration
                      </TableColumn>
                      <TableColumn style={{ width: "10%" }}>
                        Episode
                      </TableColumn>
                      <TableColumn style={{ width: "40%" }}>
                        Description
                      </TableColumn>
                    </TableHeader>
                    <TableBody>
                      {activeFolder.files.map((file) => {
                        const hasFile = updatingFiles.find(
                          (uFile) => uFile.id === file.id
                        );
                        if (hasFile) {
                          file = hasFile;
                        }

                        const isActive = activeFile?.id === file.id;

                        return (
                          <>
                            <TableRow
                              key={"file-" + file.id}
                              className="table-row"
                              style={isActive ? styles.activeRow : undefined}
                              onClick={() => toggleActiveFile(file)}
                            >
                              <TableCell className="table-cell">
                                {file.name}
                              </TableCell>
                              <TableCell className="table-cell">
                                {durationToTime(file.duration)}
                              </TableCell>
                              <TableCell className="table-cell">
                                {file.episode.String}
                              </TableCell>
                              <TableCell className="table-cell">
                                {file.description.String}
                              </TableCell>
                            </TableRow>
                            {updatedFile && isActive && (
                              <TableRow
                                key={"editor-" + file.id}
                                style={{
                                  ...styles.editorRow,
                                  ...(editorVisible
                                    ? styles.editorVisible
                                    : styles.editorHidden),
                                }}
                              >
                                <TableCell style={{ padding: 0 }} colSpan={4}>
                                  <div
                                    ref={myRef}
                                    style={{
                                      ...styles.fileForm,
                                      ...(editorVisible
                                        ? styles.formVisible
                                        : styles.formHidden),
                                    }}
                                  >
                                    <h3 style={styles.fileTitle}>
                                      {file.fileName}
                                    </h3>
                                    <div style={styles.formField}>
                                      <label style={styles.formLabel}>
                                        Name
                                      </label>
                                      <input
                                        style={styles.formInput}
                                        onChange={(e) => {
                                          setUpdatedFile({
                                            ...updatedFile,
                                            name:
                                              e.target.value.length > 0
                                                ? e.target.value
                                                : path.parse(file.fileName)
                                                    .name,
                                          });
                                        }}
                                        defaultValue={file.name}
                                        placeholder={"Display Name"}
                                      />
                                    </div>
                                    <div style={styles.formField}>
                                      <label style={styles.formLabel}>
                                        Episode
                                      </label>
                                      <input
                                        style={styles.formInput}
                                        onChange={(e) => {
                                          setUpdatedFile({
                                            ...updatedFile,
                                            episode: {
                                              String: e.target.value,
                                            },
                                          });
                                        }}
                                        defaultValue={file.episode.String}
                                        placeholder={"S02E12"}
                                      />
                                    </div>
                                    <div style={styles.formField}>
                                      <label style={styles.formLabel}>
                                        Description
                                      </label>
                                      <textarea
                                        style={styles.formTextarea}
                                        onChange={(e) => {
                                          setUpdatedFile({
                                            ...updatedFile,
                                            description: {
                                              String: e.target.value,
                                            },
                                          });
                                        }}
                                        defaultValue={file.description.String}
                                        placeholder={"Description"}
                                      />
                                    </div>
                                    <div style={styles.formActions}>
                                      <button
                                        className="modal-button"
                                        style={styles.modalButton}
                                        onClick={closeEditor}
                                      >
                                        Close
                                      </button>
                                      <button
                                        className="modal-button confirm-button"
                                        style={styles.modalButton}
                                        onClick={() => toggleActiveFile(file)}
                                        disabled={isEqual(
                                          activeFile,
                                          updatedFile
                                        )}
                                      >
                                        Save
                                      </button>
                                    </div>
                                  </div>
                                </TableCell>
                                <TableCell style={styles.zeroWidthCell}>
                                  {" "}
                                </TableCell>
                                <TableCell style={styles.zeroWidthCell}>
                                  {" "}
                                </TableCell>
                                <TableCell style={styles.zeroWidthCell}>
                                  {" "}
                                </TableCell>
                              </TableRow>
                            )}
                          </>
                        );
                      })}
                    </TableBody>
                  </Table>
                )}
              </div>
            )}
            {!activeFolder && (
              <div className={"info-message"}>
                <HiOutlineArchiveBoxXMark />
                Folder is empty
              </div>
            )}
          </div>
        )}
      </div>
      <div className="modal-footer">
        <button className="modal-button cancel-button" onClick={close}>
          Cancel
        </button>
        <button
          onClick={handleSave}
          disabled={updatingFiles.length === 0}
          className="modal-button confirm-button"
        >
          Save ({updatingFiles.length})
        </button>
      </div>
    </>
  );
}
