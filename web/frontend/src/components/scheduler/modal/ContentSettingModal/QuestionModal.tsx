import { HiXMark } from "react-icons/hi2";

interface QuestionModalProps {
  approve: () => void;
  reject: () => void;
}

const QuestionModal = ({
  approve,
  reject,
}: QuestionModalProps) => {
  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h2>Regenerate Guide</h2>
          <button className="close-button" onClick={reject}>
            <HiXMark />
          </button>
        </div>
        <div className="modal-body">
          <h3 style={{ paddingBottom: "20px" }}>
            Looks like you’ve made some changes.
            <br/>
            Do you really want to close this without saving?
          </h3>
        </div>
        <div className="modal-footer">
          <button
            className="modal-button delete-button"
            onClick={approve}
          >
            Discard Changes
          </button>
          <button
            className="modal-button confirm-button"
            onClick={reject}
          >
            Keep Editing
          </button>
        </div>
      </div>
    </div>
  );
}

export default QuestionModal;
