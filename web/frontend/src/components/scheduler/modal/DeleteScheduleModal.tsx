import { HiXMark } from "react-icons/hi2";
import { deleteSchedule } from "@/api/schedulerApi.ts";
import { toast } from "react-toastify";

interface CreateScheduleModalProps {
  id: number;
  setIsLoading: (loading: boolean) => void;
  reloadPage: () => void;
  close: () => void;
}

const DeleteScheduleModal = ({
  id,
  close,
  setIsLoading,
  reloadPage,
}: CreateScheduleModalProps) => {
  const handleDelete = async () => {
    setIsLoading(true);
    try {
      await deleteSchedule(id);
      reloadPage();
    } catch (err) {
      console.error("Failed to delete schedule: ", err)
      toast.error("Failed to delete schedule")
    } finally {
      close();
      setIsLoading(false);
    }
  };

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h2>Delete Schedule</h2>
          <button className="close-button" onClick={close}>
            <HiXMark />
          </button>
        </div>
        <div className="modal-body">
          <div className="delete-message">
            <p className="delete-question">
              Are you sure you want to delete this schedule?
            </p>
          </div>
        </div>
        <div className="modal-footer">
          <button
            className="modal-button cancel-button"
            onClick={close}
          >
            Cancel
          </button>
          <button
            className="modal-button delete-button"
            onClick={handleDelete}
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  );
}

export default DeleteScheduleModal;
