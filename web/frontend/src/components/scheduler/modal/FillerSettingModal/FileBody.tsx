import { Fragment, useEffect, useState } from "react";
import { OrbitProgress } from "react-loading-indicators";
import {
  HiChevronRight,
  HiDocument,
  HiFolder,
  HiHome,
  HiInformationCircle,
  HiMiniPlusCircle,
  HiOutlineArchiveBoxXMark,
} from "react-icons/hi2";
import { HiDocumentRemove, HiFolderRemove } from "react-icons/hi";
import { useDispatch, useSelector } from "react-redux";
import { selectFillers, updateFillers } from "@/redux/schedulerSlice.ts";
import { FileInfo, IFile, IFolder } from "@/types/schedule.ts";
import { getAllFilesGroupedByFolders } from "@/api/schedulerApi.ts";
import { Tooltip } from "@nextui-org/react";
import { Tab, Tabs } from "@heroui/react";

interface FolderBodyProps {
  close: () => void;
  setChangeAction: (value: boolean) => void;
  setIsLoading: (value: boolean) => void;
}

const FileBody = ({
  close,
  setChangeAction,
  setIsLoading,
}: FolderBodyProps) => {
  const fillers = useSelector(selectFillers);
  const dispatch = useDispatch();

  const [files, setFiles] = useState<FileInfo[]>(fillers?.files ?? []);
  const [folders, setFolders] = useState<string[]>(fillers?.folders ?? []);
  const [preFiller, setPreFiller] = useState<FileInfo|null|undefined>(fillers?.pre_filler ?? null);
  const [loading, setLoading] = useState<boolean>(false);
  const [activeFolder, setActiveFolder] = useState<IFolder | null>(null);
  const [mainFolder, setMainFolder] = useState<IFolder | null>(null);
  const [activeTab, setActiveTab] = useState<string>("fillers");

  useEffect(() => {
    const loadFiles = async () => {
      setLoading(true);

      const folder = await getAllFilesGroupedByFolders();
      setActiveFolder(folder);
      setMainFolder(folder);

      setLoading(false);
    };

    loadFiles();
  }, []);

  const editItem = async () => {
    setIsLoading(true);
    dispatch(
      updateFillers({
        folders: folders,
        files: files,
        pre_filler: preFiller,
      })
    );
    setChangeAction(true);
    setIsLoading(false);
    close();
  };

  const addFolder = (addFolder: string | null | undefined) => {
    if (!addFolder) {
      return;
    }

    const alreadyAdded = folders.find((folder) => folder === addFolder);
    if (alreadyAdded) {
      return;
    }

    setFolders([...folders, addFolder]);
  };

  const addFile = (addFile: string | null | undefined, id: number) => {
    if (!addFile) {
      return;
    }

    const alreadyAdded = files.find((file) => file.id === id);
    if (alreadyAdded) {
      return;
    }

    setFiles([
      ...files,
      {
        id: Number(id),
        path: addFile,
      },
    ]);
  };

  const deleteFolder = (deleteFolder: string) => {
    setFolders(folders.filter((folder) => !(deleteFolder === folder)));
  };

  const deleteFile = (deleteFile: FileInfo) => {
    setFiles(
      files.filter((file) => !(Number(deleteFile.id) === Number(file.id)))
    );
  };

  const deletePreFiller = () => {
    setPreFiller(null);
  }

  const addPreFiller = (preFiller: IFile) => {
    if (preFiller.duration > 10) {
      return;
    }

    const filePath =
      (activeFolder?.path === "/" ? "" : activeFolder?.path) +
      "/" +
      preFiller.fileName;

    setPreFiller({
      id: Number(preFiller.id),
      path: filePath,
    });
  }

  const selectActiveFolder = (path: string, folder: string) => {
    const index = path.search(folder);
    if (index === -1 || !mainFolder) {
      return;
    }

    const searchPath = path.substring(0, index) + folder;
    const searchFolder = searchFolderByPath(mainFolder, searchPath);

    setActiveFolder(searchFolder);
  };

  const searchFolderByPath = (data: IFolder, path: string): IFolder | null => {
    if (data.path === path) {
      return data;
    }

    if (data.folders && data.folders.length > 0) {
      for (const folder of data.folders) {
        const result = searchFolderByPath(folder, path);
        if (result) {
          return result;
        }
      }
    }

    return null;
  };

  const durationToTime = (time: number): string => {
    time = Math.round(time);
    let hours = Math.floor(time / 3600);
    let minutes = Math.floor((time - hours * 3600) / 60);
    let seconds = time - hours * 3600 - minutes * 60;

    return (
      (hours < 10 ? "0" + hours : hours) +
      ":" +
      (minutes < 10 ? "0" + minutes : minutes) +
      ":" +
      (seconds < 10 ? "0" + seconds : seconds)
    );
  };

  return (
    <>
      <div className="modal-body">
        {loading && (
          <div>
            <OrbitProgress color="#ffa500" size="small" />
          </div>
        )}
        {!loading && (
          <div className={"filler-body"}>
            {activeFolder && (
              <div>
                <div className={"location-menu"}>
                  <Tooltip content={`Go to 'Home'`} placement="bottom">
                    <button
                      onClick={() => selectActiveFolder(activeFolder.path, "/")}
                      className="breadcrumb-item clickable"
                    >
                      <HiHome className="home-icon" />
                    </button>
                  </Tooltip>
                  {activeFolder.path.split("/").map((folder, index, arr) => {
                    if (index > 0 && !folder) return null;

                    const isRoot = index === 0 && !folder;
                    const isLast = index === arr.length - 1;

                    if (isRoot) return null;

                    return (
                      <Fragment key={index}>
                        {index > 1 && (
                          <HiChevronRight className="breadcrumb-separator" />
                        )}

                        {isLast ? (
                          <span className="breadcrumb-item current">
                            {folder || "Home"}
                          </span>
                        ) : (
                          <Tooltip
                            content={`Go to ${folder || "Home"}`}
                            placement="bottom"
                          >
                            <button
                              onClick={() =>
                                selectActiveFolder(activeFolder.path, folder)
                              }
                              className="breadcrumb-item clickable"
                            >
                              {folder || "Home"}
                            </button>
                          </Tooltip>
                        )}
                      </Fragment>
                    );
                  })}
                </div>
                {activeFolder.folders.map((folder) => (
                  <div
                    className={"select-folder"}
                    key={`folder-${folder.folder}`}
                  >
                    <div
                      className={"select-folder-name"}
                      onClick={() => setActiveFolder(folder)}
                    >
                      <HiFolder />
                      <span>{folder.folder}</span>
                    </div>
                    <div className={"select-folder-button"}>
                      {folder.files.length > 0 && activeTab === "fillers" && (
                        <button
                          onClick={() => {
                            addFolder(folder.path);
                          }}
                        >
                          <HiMiniPlusCircle />
                        </button>
                      )}
                    </div>
                  </div>
                ))}
                {activeFolder.files.map((file) => {
                  const filePath =
                    (activeFolder?.path === "/" ? "" : activeFolder?.path) +
                    "/" +
                    file.fileName;
                  return (
                    <div className={"select-folder"} key={`file-${file.name}`}>
                      <div
                        className={"select-folder-name"}
                        onClick={activeTab === "fillers" ?
                          () => addFile(filePath, file.id) :
                          () => addPreFiller(file)}
                      >
                        <HiDocument />
                        <span>
                          <span className={"file-duration"}>
                            {durationToTime(file.duration)}
                          </span>
                          {file.fileName}
                        </span>
                      </div>
                      <div className={"select-folder-button"}>
                        {
                          ((activeTab === "prefiller" && file.duration < 10) || activeTab === "fillers") && (
                            <button
                              onClick={activeTab === "fillers" ?
                                () => addFile(filePath, file.id) :
                                () => addPreFiller(file)}
                            >
                              <HiMiniPlusCircle />
                            </button>
                          )
                        }
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
            {!activeFolder && (
              <div className={"info-message"}>
                <HiOutlineArchiveBoxXMark />
                Folder is empty
              </div>
            )}
          </div>
        )}
        <div
          className={
            "w-full h-[100px] pt-2 gap-2 border-t-1 border-t-[#9e9e9e] overflow-auto filler-tabs"
          }
        >
          <Tabs aria-label="Options" onSelectionChange={(key) => setActiveTab(key.toString())} >
            <Tab key="fillers" title="Fillers" >
              <div className={"added-files"}>
                {folders.length === 0 && files.length === 0 && (
                  <div className={"info-message"}>
                    <HiInformationCircle
                      className={"h-[25px] w-[25px] min-w-[25px]"}
                    />
                    <p className={"flex items-center justify-center"}>
                      If no file/folder is selected, the default fillers will be
                      used. The filler may not be shown in full and will end at any
                      time if it is time for the main program guide.
                    </p>
                  </div>
                )}
                {folders.map((folder) => {
                  return (
                    <div className={"select-folder"} key={folder}>
                      <span>{folder}</span>
                      <div className={"select-folder-button remove-button"}>
                        <button onClick={() => deleteFolder(folder)}>
                          <HiFolderRemove />
                        </button>
                      </div>
                    </div>
                  );
                })}
                {files.map((file) => {
                  return (
                    <div className={"select-folder"} key={file.id}>
                      <span>{file.path}</span>
                      <div className={"select-folder-button remove-button"}>
                        <button onClick={() => deleteFile(file)}>
                          <HiDocumentRemove />
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>
            </Tab>
            <Tab key="prefiller" title="Pre-filler">
              <div className={"added-files"}>
              {
                preFiller && preFiller.path !== "" &&
                <div className={"select-folder"} key={preFiller.id}>
                  <span>{preFiller.path}</span>
                  <div className={"select-folder-button remove-button"}>
                    <button onClick={deletePreFiller}>
                      <HiDocumentRemove />
                    </button>
                  </div>
                </div>
              }
              {
                (!preFiller || preFiller.path === "") &&
                <div className={"info-message"}>
                  <HiInformationCircle
                    className={"h-[25px] w-[25px] min-w-[25px]"}
                  />
                  <p className={"flex items-center justify-center"}>
                    <p>You can specify a pre-filler of no more than <b>10 seconds</b> duration
                      that will be shown before each main element in the schedule.</p>
                  </p>
                </div>
              }
              </div>
            </Tab>
          </Tabs>
        </div>
      </div>
      <div className="modal-footer">
        <button className="modal-button" onClick={close}>
          Close
        </button>
        <button onClick={editItem} className="modal-button confirm-button">
          Save
        </button>
      </div>
    </>
  );
};

export default FileBody;
