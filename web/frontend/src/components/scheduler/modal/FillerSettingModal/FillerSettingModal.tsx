import { HiXMark } from "react-icons/hi2";
import FileBody from "@/components/scheduler/modal/FillerSettingModal/FileBody.tsx";
import './FillerSettingModal.css';

interface FillerSettingModalProps {
  close: () => void;
  setChangeAction: (value: boolean) => void;
  setIsLoading: (value: boolean) => void;
}

const FillerSettingModal = ({
  close,
  setChangeAction,
  setIsLoading,
}: FillerSettingModalProps) => {
  return (
    <div className="modal-overlay">
      <div className="modal-content files-modal">
        <div className="modal-header">
          <h2>Filler Setting</h2>
          <button className="close-button" onClick={close}>
            <HiXMark />
          </button>
        </div>
        <FileBody
          close={close}
          setChangeAction={setChangeAction}
          setIsLoading={setIsLoading}
        />
      </div>
    </div>
  );
}

export default FillerSettingModal;
