import { HiXMark } from "react-icons/hi2";
import { useSelector } from "react-redux";
import { selectActiveItem } from "@/redux/schedulerSlice.ts";
import FileBody from "@/components/scheduler/modal/EditItemModal/FileBody.tsx";

interface EditItemModalProps {
  close: () => void;
  type: string;
  setChangeAction: (value: boolean) => void;
}

const EditItemModal = ({
  close,
  type,
  setChangeAction,
}: EditItemModalProps) => {
  const activeItem = useSelector(selectActiveItem);

  return (
    <div className="modal-overlay">
      <div className="modal-content files-modal">
        <div className="modal-header">
          <h2>Edit Item</h2>
          <button className="close-button" onClick={close}>
            <HiXMark />
          </button>
        </div>
        {activeItem &&
          activeItem.folders &&
          activeItem.files && (
            <FileBody
              setChangeAction={setChangeAction}
              type={type}
              start={activeItem.start}
              close={close}
              addedFolders={activeItem.folders}
              addedFiles={activeItem.files}
              addedFillersFiles={activeItem.fillers.files ?? []}
              addedFillersFolders={activeItem.fillers.folders ?? []}
            />
          )}
      </div>
    </div>
  );
}

export default EditItemModal;
