import {
  HiDocument,
  HiFolderOpen,
} from "react-icons/hi2";
import { FileInfo } from "@/types/schedule.ts";
import "./FolderItem.css";

interface FolderItemProps {
  folders: string[];
  files: FileInfo[];
}

const FolderItem = ({ folders, files }: FolderItemProps) => {
  return (
    <div className={"folder-item"}>
      {folders.length > 0 && folders.map((folder: string) => {
        let paths = folder.split("/");
        let path = "/" + paths[paths.length - 1];
        if (paths.length > 2) {
          path = "/" + paths[paths.length - 2] + path;
        }

        return (
          <div
            title={folder}
            key={folder}
            className={"folder-item-row"}
          >
            <HiFolderOpen />
            {path}
          </div>
        );
      })}
      {files.length > 0 && files.map((file: FileInfo) => {
        let paths = file.path.split("/");
        let path = "/" + paths[paths.length - 1];
        if (paths.length > 2) {
          path = "/" + paths[paths.length - 2] + path;
        }

        return (
          <div
            title={file.path}
            key={file.path}
            className={"folder-item-row"}
          >
            <HiDocument/>
            {path.split("/").pop()}
          </div>
        );
      })}
    </div>
  );
}

export default FolderItem;
