.empty-schedules-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  background-color: #1a1a1a;
  border-radius: 12px;
  border: 1px solid #333;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  margin: 2rem auto;
  max-width: 600px;
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.empty-schedules-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, rgba(100, 108, 255, 0.1), rgba(255, 165, 0, 0.1));
  border-radius: 50%;
  margin-bottom: 1.5rem;
  position: relative;
  overflow: hidden;
}

.empty-schedules-icon-container::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    transparent, 
    rgba(100, 108, 255, 0.3), 
    transparent 30%
  );
  animation: rotate 4s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.empty-schedules-icon {
  font-size: 2.5rem;
  color: #2196f3;
  position: relative;
  z-index: 2;
}

.empty-schedules-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #fff;
  margin-bottom: 1rem;
  position: relative;
  display: inline-block;
}

.empty-schedules-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, #2196f3, #ffa500);
  border-radius: 3px;
}

.empty-schedules-description {
  color: #aaa;
  font-size: 1.1rem;
  margin-bottom: 2rem;
  max-width: 400px;
  line-height: 1.6;
}

.empty-schedules-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #2196f3, #535bf2);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.empty-schedules-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 10px rgba(0, 0, 0, 0.2);
  background: linear-gradient(135deg, #535bf2, #2196f3);
}

.button-icon {
  font-size: 1.2rem;
}
