import { <PERSON><PERSON><PERSON>, HiCalendar } from "react-icons/hi";
import "./EmptySchedulers.css";

const EmptySchedulers = ({handleCreate}: { handleCreate: () => void}) => {
  return (
    <div className="empty-schedules-container">
      <div className="empty-schedules-icon-container">
        <HiCalendar className="empty-schedules-icon" />
      </div>
      <h2 className="empty-schedules-title">No Schedules Found</h2>
      <p className="empty-schedules-description">Create your first schedule to get started with content planning.</p>
      <button className="empty-schedules-button" onClick={handleCreate}>
        <HiPlus className="button-icon" />
        <span>Create New Schedule</span>
      </button>
    </div>
  );
}

export default EmptySchedulers
