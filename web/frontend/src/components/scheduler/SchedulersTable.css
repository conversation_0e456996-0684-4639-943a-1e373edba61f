/* Modern Scheduler Table Styles */
.scheduler-table-container {
  border-radius: 12px;
  overflow-x: auto; /* Enable horizontal scrolling */
  overflow-y: hidden; /* Prevent vertical scrolling in the container */
  background-color: var(--color-bg-secondary);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  margin-top: 1rem;
  border: 1px solid var(--color-border-primary);
  transition: all var(--transition-medium);
  font-family: "Roboto", system-ui, sans-serif;
  /* Add scrollbar styling for better visibility */
  scrollbar-width: thin;
  scrollbar-color: var(--color-primary) var(--color-bg-tertiary);
  /* Improve scrolling behavior */
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on touch devices */
  max-width: 100%; /* Ensure container doesn't exceed viewport width */
  position: relative; /* Create stacking context for sticky elements */
}

/* Add scroll indicator when table is wider than container */
.scheduler-table-container::after {
  content: "";
  position: absolute;
  top: 50%;
  right: 10px;
  width: 30px;
  height: 50px;
  opacity: 0;
  z-index: 20;
}

/* Only show the indicator when scrolling is possible */
.scheduler-table-container:not(:hover):not([data-scrolled="true"])::after {
  opacity: 1;
}

@keyframes pulse-opacity {
  0% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 0.2;
  }
}

.scheduler-table {
  width: 100%;
  min-width: 1300px; /* Sum of all column widths (100+150+80+200+200+120+180+130+140=1300px) */
  border-collapse: separate;
  border-spacing: 0;
  table-layout: fixed; /* Improve table layout behavior */
  margin: 0; /* Remove any margin that might affect layout */
  /* Add subtle transition for smoother user experience */
  transition: all 0.2s ease;
}

.scheduler-table-header {
  background-color: var(--color-bg-tertiary);
  position: sticky;
  top: 0;
  z-index: 10;
  /* Ensure header cells stay in place during horizontal scrolling */
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Add shadow for visual separation */
  width: 100%; /* Ensure header spans full table width */
}

.scheduler-table-header-cell {
  color: var(--color-text-primary);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.85rem;
  letter-spacing: 0.5px;
  padding: 0.75rem 1.25rem;
  text-align: center;
  position: relative;
  height: 48px;
  font-family: "Roboto", system-ui, sans-serif;
}

.scheduler-table-header-cell::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(
    90deg,
    rgba(33, 150, 243, 0) 0%,
    rgba(33, 150, 243, 0.3) 50%,
    rgba(33, 150, 243, 0) 100%
  );
  transform: scaleX(0);
  transition: transform var(--transition-medium);
}

.scheduler-table-header-cell:hover::after {
  transform: scaleX(1);
}

.scheduler-table-body {
  background-color: transparent;
}

.scheduler-table-row {
  transition: all var(--transition-fast);
  height: 60px; /* Increased height for better readability */
  white-space: nowrap; /* Prevent row content from wrapping */
}

.scheduler-table-row:last-child {
  border-bottom: none;
}

.scheduler-table-row:hover {
  background-color: rgba(33, 150, 243, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.scheduler-table-cell {
  padding: 0.5rem 1rem;
  color: var(--color-text-primary);
  font-size: 1rem; /* Increased font size */
  vertical-align: middle;
  height: 60px; /* Increased height to match row */
  text-align: center;
  font-family: "Roboto", system-ui, sans-serif;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  box-sizing: border-box; /* Ensure padding is included in width calculation */
  border-bottom: 1px solid rgba(255, 255, 255, 0.03); /* Subtle separator */
  max-width: 0; /* Force respecting the column width */
}

/* Name cell styling */
.schedule-name-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  transition: all var(--transition-fast);
}

.schedule-name-cell:hover .schedule-name {
  color: var(--color-text-primary);
  text-shadow: 0 0 5px rgba(33, 150, 243, 0.4);
}

.schedule-name-cell:hover .schedule-icon {
  color: var(--color-primary-light);
}

.schedule-icon {
  color: var(--color-primary);
  font-size: 1.6rem;
  min-width: 1.6rem;
}

.schedule-name {
  font-weight: 600;
  color: var(--color-text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px; /* Limit to fit within the column */
  font-size: 1.1rem; /* Slightly smaller for better fit */
  text-align: center;
  letter-spacing: 0.3px;
  font-family: "Roboto", system-ui, sans-serif;
}

/* Date cell styling */
.schedule-date-cell {
  display: flex;
  align-items: center;
  justify-content: center; /* Center content horizontally */
  gap: 0.5rem;
  width: 100%; /* Ensure full width */
}

.date-icon {
  color: #ffa500;
  font-size: 1.1rem;
  flex-shrink: 0; /* Prevent icon from shrinking */
}

.date-text {
  color: #aaa;
  font-size: 0.9rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 140px; /* Limit to fit within the column */
}

/* Output URL cell styling */
.schedule-output-cell {
  display: flex;
  align-items: center;
  justify-content: center; /* Center content horizontally */
  gap: 0.5rem;
  width: 100%; /* Ensure full width */
}

.output-text {
  color: #aaa;
  font-size: 0.9rem;
  white-space: nowrap;
  max-width: 100px; /* Limit to fit within the column */
  text-align: center; /* Center text */
  display: block; /* Ensure it takes full width */
}

/* Current item cell styling */
.schedule-current-item-cell,
.schedule-next-item-cell {
  display: flex;
  flex-direction: column;
  gap: 2px;
  text-align: center; /* Center text */
  padding: 0.5rem;
  width: 100%; /* Ensure full width */
  align-items: center; /* Center content horizontally */
}

.item-name-row {
  font-weight: 500;
  font-size: 1rem; /* Slightly smaller for better fit */
  font-family: "Roboto", system-ui, sans-serif;
  color: var(--color-text-primary);
  white-space: nowrap;
  max-width: 180px; /* Limit to fit within the column */
  text-align: center; /* Center text */
  display: flex;
  justify-content: center; /* Center content */
  width: 100%; /* Full width */
}

/* Style for current-item-name and next-item-name to handle text overflow with ellipsis */
.current-item-name,
.next-item-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 160px; /* Fixed width to prevent x-axis overflow */
  display: inline-block; /* Required for text-overflow to work */
  vertical-align: middle;
}

.item-filename-row {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-top: 2px;
  font-size: 0.9em;
}

.item-filename-label {
  color: var(--color-text-secondary);
  font-weight: 500;
  font-family: "Roboto", system-ui, sans-serif;
}

.item-filename {
  color: var(--color-text-tertiary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
  font-family: "Roboto", system-ui, sans-serif;
}

.item-duration-row {
  color: var(--color-text-secondary);
  font-size: 0.95em;
  margin-top: 3px;
  font-family: "Roboto", system-ui, sans-serif;
}

.item-duration {
  color: var(--color-text-secondary);
  font-size: 1.05rem;
  font-weight: 500;
  font-family: "Roboto Mono", monospace;
  letter-spacing: 0.5px;
}

.item-remaining-time {
  color: var(--color-primary-light);
  font-size: 1.05rem;
  font-weight: 600;
  font-family: "Roboto Mono", monospace;
  letter-spacing: 0.5px;
  animation: pulse-text 1.5s infinite;
}

.item-total-duration {
  color: var(--color-text-secondary);
  font-size: 1.05rem;
  font-weight: 500;
  font-family: "Roboto Mono", monospace;
  letter-spacing: 0.5px;
}

@keyframes pulse-text {
  0% {
    text-shadow: 0 0 0 rgba(100, 181, 246, 0);
  }
  50% {
    text-shadow: 0 0 8px rgba(100, 181, 246, 0.7);
  }
  100% {
    text-shadow: 0 0 0 rgba(100, 181, 246, 0);
  }
}

.schedule-no-item-cell {
  display: flex;
  align-items: center;
  justify-content: center; /* Center content horizontally */
  width: 100%; /* Ensure full width */
}

.no-item-text {
  color: #666;
  font-size: 0.9rem;
  font-style: italic;
  text-align: center; /* Center text */
}

/* Network interface cell styling */
.schedule-network-cell {
  display: flex;
  align-items: center;
  justify-content: center; /* Center content horizontally */
  gap: 0.5rem;
  width: 100%; /* Ensure full width */
}

.network-icon {
  color: #2196f3;
  font-size: 1.6rem;
  flex-shrink: 0; /* Prevent icon from shrinking */
}

.network-text {
  color: #bbb;
  font-size: 0.95rem;
  font-weight: 500;
  letter-spacing: 0.3px;
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center; /* Center text */
  max-width: 90px; /* Limit to fit within the column */
  display: block; /* Ensure it takes full width */
}

.schedule-network-cell:hover .network-text {
  color: #fff;
  text-shadow: 0 0 5px rgba(33, 150, 243, 0.4);
}

/* Links cell styling */
.schedule-links-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.epg-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: transparent;
  border: none;
  padding: 0.5rem;
  color: #fff;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
  letter-spacing: 0.5px;
}

.epg-button:hover {
  transform: translateY(-2px) scale(1.1);
  color: #fff;
  filter: drop-shadow(0 0 5px rgba(33, 150, 243, 0.6));
}

.epg-button-icon {
  color: #2196f3;
  font-size: 1.4rem;
}

/* Actions cell styling */
.schedule-actions-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  right: 0;
  z-index: 5;
  padding: 0.5rem 1rem;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent; /* Remove background */
  border: 1px solid rgba(255, 255, 255, 0.15); /* Subtle border */
  border-radius: 6px; /* Rounded corners */
  color: var(--color-text-primary);
  transition: all var(--transition-fast);
  padding: 0.5rem;
  cursor: pointer;
  width: 36px; /* Fixed width */
  height: 36px; /* Fixed height */
  font-size: 1.4rem;
  font-weight: 500;
  letter-spacing: 0.2px;
  position: relative; /* For hover effects */
  z-index: 1; /* Ensure button is above other elements */
}

.edit-button {
  color: var(--color-primary);
  border-color: rgba(33, 150, 243, 0.3); /* Colored border */
}

.edit-button:hover {
  transform: translateY(-2px) scale(1.1);
  color: var(--color-primary);
  filter: drop-shadow(0 0 5px rgba(33, 150, 243, 0.6));
  border-color: var(--color-primary); /* Brighter border on hover */
}

.delete-button {
  color: var(--color-danger);
  border-color: rgba(244, 67, 54, 0.3); /* Colored border */
}

.delete-button:hover {
  transform: translateY(-2px) scale(1.1);
  color: var(--color-danger);
  filter: drop-shadow(0 0 5px rgba(244, 67, 54, 0.6));
  border-color: var(--color-danger); /* Brighter border on hover */
}

.action-icon {
  width: 1.4rem; /* Slightly smaller for better fit */
  height: 1.4rem;
}

/* Empty state styling */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.empty-icon {
  font-size: 3rem;
  color: #444;
  margin-bottom: 1rem;
}

.empty-text {
  color: #aaa;
  font-size: 1.1rem;
  margin-bottom: 1.5rem;
}

/* RTP Sync Status styling */
.sync-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
}

.sync-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  flex-shrink: 0; /* Prevent shrinking */
  min-width: 10px; /* Ensure minimum width */
  min-height: 10px; /* Ensure minimum height */
  display: block; /* Block display for better shape control */
}

.sync-dot.synced {
  background-color: var(--color-success);
  box-shadow: 0 0 5px var(--color-success);
  animation: pulse 1.5s infinite;
}

.sync-dot.unsynced {
  background-color: var(--color-warning);
}

.sync-dot.inactive {
  background-color: var(--color-danger);
}

.sync-text {
  font-weight: 600;
  font-family: "Roboto", system-ui, sans-serif;
  font-size: 0.9rem;
}

.sync-text.synced {
  color: var(--color-success);
}

.sync-text.unsynced {
  color: var(--color-warning);
}

.sync-text.inactive {
  color: var(--color-danger);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(76, 175, 80, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
  }
}

@keyframes pulse-text {
  0% {
    text-shadow: 0 0 0 rgba(33, 150, 243, 0);
  }
  50% {
    text-shadow: 0 0 8px rgba(33, 150, 243, 0.7);
  }
  100% {
    text-shadow: 0 0 0 rgba(33, 150, 243, 0);
  }
}

/* Pagination styling */
.pagination-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 1.5rem;
  gap: 0.5rem;
}

.pagination-button {
  background-color: var(--color-bg-secondary);
  border: none;
  border-radius: 8px;
  padding: 0.5rem;
  color: var(--color-text-primary);
  font-size: 0.9rem;
  transition: all var(--transition-fast);
  min-width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: "Roboto", system-ui, sans-serif;
}

.pagination-button:hover:not(.pagination-button-active) {
  background-color: var(--color-bg-tertiary);
  box-shadow: 0 0 5px rgba(33, 150, 243, 0.5);
  transform: translateY(-2px);
}

.pagination-button-active {
  background-color: var(--color-primary);
  color: white;
  box-shadow: 0 0 8px rgba(33, 150, 243, 0.7);
}

.pagination-info {
  color: var(--color-text-secondary);
  font-size: 0.9rem;
  margin: 0 1rem;
  font-family: "Roboto", system-ui, sans-serif;
}

/* Enhanced custom scrollbar styling for WebKit browsers */
.scheduler-table-container::-webkit-scrollbar {
  height: 12px; /* Taller for better usability */
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 0 0 12px 12px; /* Match container border radius */
}

.scheduler-table-container::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 0 0 12px 12px; /* Match container border radius */
  border-top: 1px solid rgba(255, 255, 255, 0.05); /* Subtle top border */
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
}

.scheduler-table-container::-webkit-scrollbar-thumb {
  background-color: var(--color-primary);
  border-radius: 6px;
  border: 3px solid rgba(0, 0, 0, 0.2); /* Create padding effect */
  background-clip: padding-box;
  min-width: 60px; /* Ensure minimum thumb size */
  box-shadow: 0 0 5px rgba(33, 150, 243, 0.5);
}

.scheduler-table-container::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-primary-light);
  box-shadow: 0 0 8px rgba(33, 150, 243, 0.7);
}

/* Add scrollbar button styling */
.scheduler-table-container::-webkit-scrollbar-button {
  display: none; /* Hide scrollbar buttons */
}

/* Firefox scrollbar styling */
.scheduler-table-container {
  scrollbar-width: thin;
  scrollbar-color: var(--color-primary) rgba(0, 0, 0, 0.2);
}

/* Add these styles to your existing CSS file */

.preview-button {
  color: var(--color-success);
  border-color: rgba(76, 175, 80, 0.3); /* Colored border */
}

.preview-button:hover {
  transform: translateY(-2px) scale(1.1);
  color: var(--color-success);
  filter: drop-shadow(0 0 5px rgba(76, 175, 80, 0.6));
  border-color: var(--color-success); /* Brighter border on hover */
}

.preview-button:disabled {
  color: var(--color-text-tertiary);
  cursor: not-allowed;
  transform: none;
  filter: none;
  opacity: 0.5;
  border-color: rgba(
    255,
    255,
    255,
    0.05
  ); /* Subtle border for disabled state */
  background-color: transparent; /* No background */
}

.preview-button:disabled:hover {
  background-color: transparent;
  transform: none;
  box-shadow: none;
  border-color: rgba(255, 255, 255, 0.05); /* Keep subtle border on hover */
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.75);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.preview-modal-content {
  width: 80%;
  max-width: 720px;
  background-color: #1e1e1e;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.preview-content {
  position: relative;
  width: 100%;
  padding-top: 56.25%; /* 16:9 aspect ratio */
}

.player-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 10;
}

.loading-text {
  margin-top: 16px;
  color: #fff;
  font-size: 16px;
}

.error-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #1e1e1e;
}

.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 24px;
  max-width: 80%;
}

.error-icon {
  font-size: 48px;
  color: #f44336;
  margin-bottom: 16px;
}

.close-button {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.5);
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 20;
  transition: background-color 0.2s;
}

.close-button:hover {
  background: rgba(255, 0, 0, 0.5);
}

.close-icon {
  color: white;
  font-size: 24px;
}
