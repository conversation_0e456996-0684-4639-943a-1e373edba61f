import moment from "moment/moment";
import {Hi<PERSON>iniArrowPath, HiMiniPlayCircle} from "react-icons/hi2";
import { Element as IElement } from '@/types/schedule'
import { getFileInfoById } from "@/api/schedulerApi.ts";
import { useEffect, useState } from "react";

interface ElementProps {
  element: IElement;
  setPreviewURL: (url: string | null) => void;
  setIsLoading: (isLoading: boolean) => void;
  canChange: boolean;
  setChangeFile: (file: IElement) => void;
}

export default function Element({
  element,
  setPreviewURL,
  setIsLoading,
  canChange,
  setChangeFile
}: ElementProps) {
  const episode = element.file.episode ?? null;
  const [isVisible, setIsVisible] = useState(false);

  // Add animation effect when component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  const getPreviewURL = async () => {
    setIsLoading(true);

    try {
      const fileInfo = await getFileInfoById(element.file.file_id);
      if (fileInfo) {
        const url = `${window.location.origin}/data${fileInfo.location + encodeURI(fileInfo.filename)}`;
        setPreviewURL(url);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={`guide-element ${isVisible ? 'visible' : ''}`} style={{ opacity: isVisible ? 1 : 0 }}>
      <div className="guide-time">
        <span className="time-display">{moment(element.start).utc().format("hh:mm A")}</span>
        {element.type === "file" && (
            <div className="icon-block">
              <span
                onClick={getPreviewURL}
                title="Preview"
                className="preview-button"
              >
                <HiMiniPlayCircle className="play-icon" />
              </span>
              {canChange && (
                  <span
                      onClick={ () => setChangeFile(element)}
                      title="Change"
                      className="change-button"
                  >
                    <HiMiniArrowPath className="change-icon" />
                  </span>
              )}
          </div>
        )}
      </div>
      <div className="guide-info">
        <p className="element-title">{element.title}</p>
        {element.type !== "filler" && (
          <p className="guide-description">
            {element.description}
          </p>
        )}
        {element.type === "file" && episode && (
          <p className="guide-episode">
            <span>Episode:</span> {episode}
          </p>
        )}
        {element.type === "filler" && (
          <div className="info-message">
            <p>
              This is a filler that will be automatically filled with some
              content. For some reason, content could not be generated for this
              time. Perhaps the time block was not filled or there was no
              element of the appropriate duration. Please check your scheduler
              again
            </p>
          </div>
        )}
      </div>


    </div>

  );
}
