import moment from "moment";
import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, HiChevronLeft, HiChevronRight } from "react-icons/hi2";
import { useDispatch, useSelector } from "react-redux";
import {
  openScheduler,
  selectGuide,
  selectSchedule,
} from "@/redux/schedulerSlice.ts";
import AttentionModal from "@/components/scheduler/modal/AttentionModal.tsx";
import {
  generateGuideByScheduleId,
  updateSchedule,
} from "@/api/schedulerApi.ts";
import { toast } from "react-toastify";
import Element from "@/components/scheduler/sidebar/Element.tsx";
import "./GuideSection.css";
import EpgChangeFileModal from "@/components/scheduler/modal/EpgChangeFileModal/EpgChangeFileModal.tsx";
import { Element as IElement } from '@/types/schedule'

interface GuideSectionProps {
  hide: () => void;
  setIsLoading: (loading: boolean) => void;
  setPreviewURL: (url: string | null) => void;
}

const GuideSection = ({
  hide,
  setPreviewURL,
  setIsLoading,
}: GuideSectionProps) => {
  const dispatch = useDispatch();
  const [showAttentionModal, setShowAttentionModal] = useState<boolean>(false);
  const guide = useSelector(selectGuide);
  const schedule = useSelector(selectSchedule);
  const [isNextWeek, setIsNextWeek] = useState<boolean>(false);
  const [changeFile, setChangeFile] = useState<IElement | null>(null)

  const [activeDate, setActiveDate] = useState<string>(
    moment().format("MM/DD")
  );
  const today = moment().utc().format("MM/DD");

  //To modify only those that are one hour later
  const currentDateWithOffset = moment.tz(schedule.timezone).add(1, 'hour').format('MM/DD HH:mm');

  // Generate all days of the week
  const generateWeekDays = () => {
    let startOfWeek;
    const days: { date: string; day: string }[] = [];

    if (isNextWeek) {
      startOfWeek = moment().utc().add(1, 'week').startOf("week");
    } else {
      startOfWeek = moment().utc().startOf("week");
    }

    for (let i = 0; i < 7; i++) {
      const day = moment(startOfWeek).add(i, "days");
      days.push({
        date: day.format("MM/DD"),
        day: day.format("ddd"),
      });
    }

    return days;
  };

  const days = generateWeekDays();

  // Create a map of dates with elements for quick lookup
  const datesWithElements = new Map();

  if (guide && guide.id) {
    guide.elements.forEach((element) => {
      const date = moment(element.start).utc().format("MM/DD");
      if (!datesWithElements.has(date)) {
        datesWithElements.set(date, []);
      }
      datesWithElements.get(date).push(element);
    });
  }

  const handleGenerateGuide = async () => {
    setIsLoading(true);
    try {
      const updatedSchedule = await updateSchedule(schedule.id, schedule);
      const updatedGuide = await generateGuideByScheduleId(schedule.id);

      dispatch(
        openScheduler({
          schedule: updatedSchedule,
          guide: updatedGuide,
        })
      );
    } catch (err) {
      console.error("Failed generate guide: ", err);
      toast.error("Failed generate guide");
    } finally {
      setShowAttentionModal(false);
      setIsLoading(false);
    }
  };

  if (!guide || !guide.id) {
    return (
      <div>
        <div className="guide-overflow" onClick={hide} />
        <div className="guide-section">
          <h2 style={{
            color: '#2196f3',
            marginBottom: '16px',
            fontFamily: "'Roboto', system-ui, sans-serif",
            borderBottom: '1px solid #2a2a2a',
            paddingBottom: '10px',
            width: '100%',
            textAlign: 'center'
          }}>
            Program Guide
          </h2>
          <div className="guide-empty">
            <p>The guide has not been created yet</p>
            <button onClick={handleGenerateGuide}>
              Create a guide now
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      {showAttentionModal && (
        <AttentionModal
          close={() => setShowAttentionModal(false)}
          setIsLoading={setIsLoading}
        />
      )}
      <div className="guide-overflow" onClick={hide} />
      <div className="guide-section">
        <h2 style={{
          color: '#2196f3',
          marginBottom: '16px',
          fontFamily: "'Roboto', system-ui, sans-serif",
          borderBottom: '1px solid #2a2a2a',
          paddingBottom: '10px',
          width: '100%',
          textAlign: 'center'
        }}>
          Program Guide
        </h2>

        <div className="guide-days">
          <div
            className={`guide-day guide-change-week ${!isNextWeek ? 'disabled' : ''}`}
            onClick={() => setIsNextWeek(false)}
            title={"Current week"}
          >
            <HiChevronLeft />
          </div>
          {days.map((date, index) => (
            <div
              key={index}
              onClick={() => {
                setActiveDate(date.date);
              }}
              className={`guide-day ${
                datesWithElements.has(date.date) ? "has-elements" : ""
              }`}
              style={{
                color: `${activeDate === date.date ? "#2196f3" : "#ffffff"}`,
              }}
            >
              {today === date.date && (
                <span className="font-bold text-[#ffa500]">Today</span>
              )}
              {today !== date.date && (
                <span className="font-bold">{date.day}</span>
              )}
              <span className="text-small text-[#9e9e9e]">{date.date}</span>
            </div>
          ))}
          <div
            className={`guide-day guide-change-week ${isNextWeek ? 'disabled' : ''}`}
            onClick={() => setIsNextWeek(true)}
            title={"Next week"}
          >
            <HiChevronRight />
          </div>
          <div
            onClick={() => setShowAttentionModal(true)}
            title="Force update the guide"
            className="guide-day guide-reload"
          >
            <HiArrowPath className="w-[20px] h-[20px]" />
          </div>
        </div>

        <div className="guide-details">
          {guide.elements.map((element, index) => {
            if (activeDate === moment(element.start).utc().format("MM/DD")) {
              return (
                <Element
                  key={index}
                  setIsLoading={setIsLoading}
                  setPreviewURL={setPreviewURL}
                  element={element}
                  canChange={moment(element.start).utc().format("MM/DD HH:mm") > currentDateWithOffset  }
                  setChangeFile={setChangeFile}
                />
              );
            }
            return null;
          })}
          {datesWithElements.has(activeDate) === false && (
            <div className="empty-day-message">
              <p>No scheduled content for this day</p>
            </div>
          )}
        </div>
      </div>

      {changeFile !== null && (
          <EpgChangeFileModal
              close={() => setChangeFile(null)}
              setIsLoading={setIsLoading}
              file={changeFile}
              schedule={schedule}
          />
      )}
    </>
  );
};

export default GuideSection;
