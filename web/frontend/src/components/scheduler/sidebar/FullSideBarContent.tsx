import { AiFillSave, AiOutlineVideoCameraAdd } from "react-icons/ai";
import {
  Hi<PERSON>rrowPath,
  HiCalendarDays,
  HiCheckCircle,
  HiChevronLeft,
  HiChevronRight,
  HiMiniListBullet,
  HiMiniTableCells,
  HiSignal,
} from "react-icons/hi2";
import { useDispatch, useSelector } from "react-redux";
import moment from "moment";
import Loading from "@/components/common/Loading";
import { useState } from "react";
import { HiGlobeAlt } from "react-icons/hi";
import Select from "react-select";
import momentTimezone from "moment-timezone";
import {
  changeSchedulerDetail,
  selectGuide,
  selectSchedule,
} from "@/redux/schedulerSlice.ts";
import { Schedule } from "@/types/schedule.ts";
import { getTimezones } from "@/utils/formatters.ts";
import AttentionModal from "@/components/scheduler/modal/AttentionModal.tsx";
import "./ToggleSwitch.css";

interface FullSideBarContentProps {
  changeView: () => void;
  setHideCalendarSection: (hidden: boolean) => void;
  setChangeAction: (value: boolean) => void;
  hideCalendarSection: boolean;
  setHideGuideSection: (hidden: boolean) => void;
  hideGuideSection: boolean;
  isSaveAction: boolean;
  isChangeAction?: boolean;
  handleSave: () => void;
  openInNewTab: (url: string) => void;
  openFillerModal: () => void;
  openContentSettingModal: () => void;
  openRtpOutputSettingModal: () => void;
}

const FullSideBarContent = ({
  openFillerModal,
  openInNewTab,
  isSaveAction,
  setChangeAction,
  handleSave,
  changeView,
  setHideCalendarSection,
  setHideGuideSection,
  hideGuideSection,
  hideCalendarSection,
  openContentSettingModal,
  openRtpOutputSettingModal,
  isChangeAction,
}: FullSideBarContentProps) => {
  const dispatch = useDispatch();
  const schedule = useSelector(selectSchedule);
  const guide = useSelector(selectGuide);
  const timezones = getTimezones();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [showAttentionModal, setShowAttentionModal] = useState<boolean>(false);

  const changeValue = (field: string, value: boolean | string | null) => {
    setChangeAction(true);
    dispatch(changeSchedulerDetail({ field, value }));
  };

  const toggleCalendar = () => {
    setHideCalendarSection(!hideCalendarSection);
    setHideGuideSection(true);
  };

  const toggleGuide = () => {
    setHideGuideSection(!hideGuideSection);
    setHideCalendarSection(true);
  };

  const getEPGLink = (schedule: Schedule): string => {
    return `${window.location.origin}/data/epg/${schedule.short_id}.xml`;
  };

  return (
    <>
      {showAttentionModal && (
        <AttentionModal
          close={() => setShowAttentionModal(false)}
          setIsLoading={setIsLoading}
        />
      )}
      <div className={`sidebar full-sidebar`}>
        <div className={"sidebar-content"}>
          <div className={"sidebar-row"}>
            <label>Title</label>
            <input
              type={"text"}
              placeholder={"Channel name"}
              value={schedule.name}
              onChange={(event) => changeValue("name", event.target.value)}
            />
          </div>
          <div className={"sidebar-row"}>
            <label>Timezone</label>
            <Select
              options={timezones}
              defaultValue={{
                value: schedule.timezone,
                label:
                  " (GMT" +
                  momentTimezone.tz(schedule.timezone).format("Z") +
                  ") " +
                  schedule.timezone,
              }}
              onChange={(data) =>
                data?.value ? changeValue("timezone", data.value) : ""
              }
              onMenuClose={() => {}}
              onMenuOpen={() => {}}
              className={"react-select-container"}
              classNamePrefix="react-select"
            />
          </div>
          <div className={"sidebar-row"}>
            <button onClick={toggleCalendar}>
              <HiCalendarDays />
              Calendar
              <HiChevronRight />
            </button>
          </div>
          <div className={"sidebar-row sidebar-row-multiple"}>
            <button
              onClick={() => setShowAttentionModal(true)}
              disabled={guide === null}
              title={"Force update the guide"}
              style={{ width: "40px" }}
            >
              <HiArrowPath />
            </button>
            <button onClick={toggleGuide}>
              <HiMiniTableCells />
              Guide
              <HiChevronRight />
            </button>
          </div>
          <div className={"sidebar-row"}>
            <button
              disabled={guide === null}
              onClick={() => openInNewTab(getEPGLink(schedule))}
            >
              <HiGlobeAlt />
              EPG
            </button>
          </div>
          <div className={"sidebar-row"}>
            <button onClick={openFillerModal}>
              <AiOutlineVideoCameraAdd />
              Filler setting
            </button>
          </div>
          <div className={"sidebar-row"}>
            <button onClick={openContentSettingModal}>
              <HiMiniListBullet />
              Metadata edit
            </button>
          </div>
          <div className={"sidebar-row"}>
            <button onClick={openRtpOutputSettingModal}>
              <HiSignal />
              RTP Output
            </button>
          </div>
        </div>
        <div className={"sidebar-content"}>
          {!schedule.autosave && (
            <div className={"sidebar-row"}>
              <button
                onClick={handleSave}
                className={isChangeAction ? "save-button-highlight" : ""}
                title="Save your changes"
              >
                <AiFillSave />
                <span>Save Changes</span>
              </button>
            </div>
          )}
          <div className={"sidebar-row"}>
            <div className="toggle-switch-container">
              <input
                type="checkbox"
                id="autosave"
                name="autosave"
                className="toggle-switch-checkbox"
                checked={schedule.autosave}
                onChange={() => changeValue("autosave", !schedule.autosave)}
              />
              <label htmlFor="autosave" className="toggle-switch-label">
                <span className="toggle-switch-inner"></span>
                <span className="toggle-switch-switch"></span>
              </label>
              <span className="toggle-switch-text">Autosave</span>
            </div>
          </div>

          <div className={"sidebar-row sidebar-row-multiple"}>
            {!isSaveAction && schedule.updated_at && (
              <HiCheckCircle className={"text-emerald-500"} />
            )}
            {isSaveAction && <HiArrowPath className={"text-emerald-500"} />}
            <span className={"sidebar-updated"}>
              {isSaveAction
                ? "Saving..."
                : schedule.updated_at
                ? moment(schedule.updated_at).format("L LTS")
                : ""}
            </span>
          </div>

          <div className={"sidebar-row"}>
            <button onClick={changeView}>
              <HiChevronLeft />
            </button>
          </div>
        </div>
      </div>
      {isLoading && <Loading />}
    </>
  );
};

export default FullSideBarContent;
