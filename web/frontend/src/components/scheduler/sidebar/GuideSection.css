/* Guide Section Container */
.guide-section {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 850px;
  z-index: 20;
  display: flex;
  flex-direction: column;
  background: #1a1a1a;
  border-left: 2px solid #2a2a2a;
  padding: 16px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
  animation: slideIn 0.3s ease-out;
  font-family: 'Roboto', system-ui, sans-serif;
}

@keyframes slideIn {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.guide-overflow {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100%;
  background: rgba(18, 18, 18, 0.8);
  backdrop-filter: blur(2px);
  z-index: 10;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Empty Guide State */
.guide-empty {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.5s ease-out;
}

.guide-empty p {
  font-size: 1.1rem;
  color: #e0e0e0;
  text-align: center;
}

.guide-empty button {
  height: 40px;
  padding: 0.5rem 2rem;
  border: 1px solid #333;
  border-radius: 6px;
  background-color: #2a2a2a;
  color: #fff;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.guide-empty button:hover {
  background-color: #2196f3;
  border-color: #2196f3;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(33, 150, 243, 0.3);
}

/* Days Navigation */
.guide-days {
  display: flex;
  justify-content: space-between;
  gap: 8px;
  width: 100%;
  margin-bottom: 16px;
  animation: fadeInDown 0.4s ease-out;
}

@keyframes fadeInDown {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.guide-day {
  display: flex;
  flex-direction: column;
  padding: 0.6rem 0.4rem;
  width: 100%;
  justify-content: center;
  align-items: center;
  border: 1px solid #2a2a2a;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: rgba(26, 26, 26, 0.8);
}

.guide-day:hover {
  border: 1px solid #2196f3;
  background-color: rgba(33, 150, 243, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
}

.guide-day.has-elements {
  background-color: rgba(33, 150, 243, 0.15);
  border-color: rgba(33, 150, 243, 0.3);
}

.guide-change-week {
  width: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.guide-change-week.disabled {
  color: #666;
}

.guide-change-week.disabled:hover {
  cursor: default;
  border: 1px solid #2a2a2a;
  background-color: rgba(26, 26, 26, 0.8);
  transform: none;
}

.guide-reload {
  width: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.guide-reload:hover svg {
  animation: spin 1s ease;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Guide Content */
.guide-details {
  height: calc(100% - 60px);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 12px 8px;
  scrollbar-width: thin;
  scrollbar-color: #2196f3 #1a1a1a;
  animation: fadeIn 0.5s ease-out;
}

.guide-details::-webkit-scrollbar {
  width: 6px;
}

.guide-details::-webkit-scrollbar-track {
  background: #1a1a1a;
  border-radius: 10px;
}

.guide-details::-webkit-scrollbar-thumb {
  background-color: #2196f3;
  border-radius: 10px;
}

/* Guide Elements */
.guide-element {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  background-color: rgba(26, 26, 26, 0.8);
  border: 1px solid #2a2a2a;
  transition: all 0.2s ease;
  animation: fadeInUp 0.4s ease-out;
}

@keyframes fadeInUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.guide-element:hover {
  border-color: #2196f3;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}

.guide-element.visible {
  transition: opacity 0.4s ease-out, transform 0.3s ease-out;
}

.guide-time {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  font-size: 0.9rem;
  padding-top: 2px;
  min-width: 80px;
  color: #e0e0e0;
}

.guide-time span {
  cursor: pointer;
  color: #2196f3;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.guide-time .time-display {
  width: auto;
  height: auto;
  font-family: 'Courier New', Consolas, monospace;
  font-weight: 600;
  color: #e0e0e0;
}

.guide-time .preview-button, .guide-time .change-button {
  background-color: rgba(33, 150, 243, 0.1);
  border: 1px solid transparent;
}

.guide-time .icon-block {
  display: flex;
  align-items: center;
  gap: 8px;
}

.guide-time span:hover {
  background-color: rgba(33, 150, 243, 0.2);
  transform: scale(1.1);
  box-shadow: 0 0 8px rgba(33, 150, 243, 0.5);
}

.guide-time span svg {
  width: 24px;
  height: 24px;
  transition: all 0.2s ease;
}

.guide-time span:hover svg {
  color: #64b5f6;
}

.play-icon, .change-icon {
  width: 24px !important;
  height: 24px !important;
  color: #2196f3;
}

.guide-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: calc(100% - 100px);
  overflow: hidden;
}

.guide-info p {
  margin: 0;
  color: #e0e0e0;
  font-size: 1rem;
  line-height: 1.4;
  overflow-wrap: break-word;
  word-break: break-word;
}

.element-title {
  font-family: 'Courier New', Consolas, monospace;
  font-weight: 600;
  font-size: 1.05rem !important;
  color: #ffffff !important;
  margin-bottom: 4px !important;
  transition: color 0.2s ease;
}

.guide-element:hover .element-title {
  color: #2196f3 !important;
}

.guide-description {
  font-size: 0.9rem;
  color: #9e9e9e;
  line-height: 1.4;
  max-height: 100px;
  overflow-y: auto;
}

.guide-episode {
  font-size: 0.9rem;
  color: #9e9e9e;
  display: flex;
  align-items: center;
  gap: 5px;
}

.guide-episode span {
  color: #e0e0e0;
  font-weight: bold;
}

.info-message {
  background-color: rgba(33, 150, 243, 0.1);
  border-left: 3px solid #2196f3;
  padding: 10px 12px;
  border-radius: 0 4px 4px 0;
  font-size: 0.9rem;
  color: #9e9e9e;
  transition: all 0.2s ease;
  margin-top: 4px;
}

.info-message p {
  font-family: 'Courier New', Consolas, monospace;
  font-size: 0.9rem !important;
  color: #9e9e9e !important;
  line-height: 1.5 !important;
}

.guide-element:hover .info-message {
  background-color: rgba(33, 150, 243, 0.15);
  box-shadow: 0 2px 6px rgba(33, 150, 243, 0.1);
}

.empty-day-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 120px;
  color: #9e9e9e;
  font-style: italic;
  background-color: rgba(26, 26, 26, 0.5);
  border-radius: 8px;
  border: 1px dashed #2a2a2a;
  animation: fadeIn 0.5s ease-out;
}


