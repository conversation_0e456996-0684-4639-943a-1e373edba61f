import { I18n<PERSON><PERSON><PERSON> } from "@react-aria/i18n";
import { Calendar } from "react-multi-date-picker";
import { HiChevronLeft, HiTrash } from "react-icons/hi2";
import { useEffect, useRef, useState } from "react";
import interactionPlugin from "@fullcalendar/interaction";
import timeGridPlugin from "@fullcalendar/timegrid";
import FullCalendar from "@fullcalendar/react";
import {
  DateSelectArg,
  EventChangeArg,
  EventClickArg,
  EventInput,
} from "@fullcalendar/core";
import {useDispatch, useSelector} from "react-redux";
import { deleteSpecialDay, selectSpecialDays, SPECIAL } from "@/redux/schedulerSlice.ts";
import FolderItem from "@/components/scheduler/FolderItem.tsx";
import "react-multi-date-picker/styles/backgrounds/bg-dark.css"
import './CalendarSection.css';

interface CalendarSectionProps {
  editItem: (arg: EventChangeArg, type: string) => void;
  editEvent: (arg: EventClickArg, type: string) => void;
  selectSection: (day: string, info: DateSelectArg, type: string) => void;
  changeView: () => void;
}

const CalendarSection = ({
  selectSection,
  editItem,
  editEvent,
  changeView,
}: CalendarSectionProps) => {
  let events: EventInput[] = [];
  const calendarRef = useRef<FullCalendar | null>(null);
  const dispatch = useDispatch();

  const [day, setDay] = useState<string>("");
  const [dates, setDates] = useState<string[]>([]);

  const specialDays = useSelector(selectSpecialDays) ?? [];

  useEffect(() => {
    const initDates: string[] = [];
    specialDays.forEach((iDay) => {
      initDates.push(iDay.name);
    });

    setDates(initDates);
  }, []);

  const selectDay = (newDay: string) => {
    setDay(newDay);
    if (null !== calendarRef.current && newDay) {
      const calendarApi = calendarRef.current.getApi();
      calendarApi.gotoDate(newDay);
    }
  };

  const hasItems = (day: string): boolean => {
    let has = false;
    specialDays.forEach((iDay) => {
      if (iDay.name === day) {
        has = iDay.items.length > 0;
      }
    });

    return has;
  };

  if (day) {
    events = [];

    specialDays.forEach((iDay) => {
      if (iDay.name === day) {
        iDay.items.forEach((item) => {
          events = [...events, item];
        });
      }
    });
  }

  const deleteDay = (day: string) => {
    dispatch(deleteSpecialDay(day));
    setDay("");
  };

  return (
    <>
      <div className={`calendar-overflow`} onClick={changeView} />
      <div className={"calendar-section"}>
        <I18nProvider locale="en">
          <Calendar
            format="YYYY-MM-DD"
            multiple={true}
            className={"bg-dark"}
            onFocusedDateChange={(_, dateClicked) => {
              if (dateClicked) {
                selectDay(dateClicked.toString());
                if (!dates.includes(dateClicked.toString())) {
                  setDates([
                    ...dates.filter((date) => hasItems(date)),
                    dateClicked.toString(),
                  ]);
                } else {
                  setDates(dates.filter((date) => hasItems(date)));
                }
              }
            }}
            value={dates}
            onChange={(value) => {
              if (value.length <= dates.length) {
                return false;
              }
            }}
          />
        </I18nProvider>
        <button onClick={changeView}>
          <HiChevronLeft />
        </button>
      </div>
      {day && events && (
        <div className={"calendar-day"}>
          <FullCalendar
            ref={calendarRef}
            timeZone={"UTC"}
            editable={true}
            eventOverlap={false}
            initialDate={day}
            dayHeaderFormat={{
              weekday: "long",
              month: "numeric",
              day: "numeric",
            }}
            headerToolbar={{
              left: "",
              center: "",
              right: "",
            }}
            slotLabelFormat={{
              hour: "2-digit",
              minute: "2-digit",
            }}
            selectable={true}
            initialView={"timeGridWeek"}
            views={{
              timeGridWeek: {
                type: "timeGrid",
                duration: {
                  days: 1,
                },
              },
            }}
            allDaySlot={false}
            scrollTime={"00:00:00"}
            plugins={[interactionPlugin, timeGridPlugin]}
            select={(info) => selectSection(day, info, SPECIAL)}
            eventClick={(arg) => editEvent(arg, SPECIAL)}
            eventDrop={(arg) => editItem(arg, SPECIAL)}
            eventResize={(arg) => editItem(arg, SPECIAL)}
            height={"100%"}
            locale={"UTC"}
            events={events}
            eventColor={"#1E1E1E"}
            eventContent={(arg) => {
              const props = arg.event.extendedProps;

              return <FolderItem folders={props.folders} files={props.files} />;
            }}
          />
          <button
            onClick={() => deleteDay(day)}
            className={"delete"}
          >
            <HiTrash />
          </button>
        </div>
      )}
    </>
  );
}

export default CalendarSection;
