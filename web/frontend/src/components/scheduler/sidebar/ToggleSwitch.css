.toggle-switch-container {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  width: 100%;
}

.toggle-switch-checkbox {
  height: 0;
  width: 0;
  visibility: hidden;
  position: absolute;
}

.toggle-switch-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  width: 40px;
  height: 20px;
  margin-top: 6px;
  background: #ddd;
  border-radius: 100px;
  position: relative;
  transition: background-color 0.2s;
}

.toggle-switch-label .toggle-switch-inner {
  width: 100%;
  height: 100%;
  border-radius: 100px;
  transition: 0.3s;
}

.toggle-switch-label .toggle-switch-switch {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 16px;
  height: 16px;
  border-radius: 45px;
  transition: 0.3s;
  background: #fff;
  box-shadow: 0 0 2px 0 rgba(10, 10, 10, 0.29);
}

.toggle-switch-checkbox:checked + .toggle-switch-label {
  background: var(--color-primary, #2196f3);
}

.toggle-switch-checkbox:checked + .toggle-switch-label .toggle-switch-switch {
  left: calc(100% - 2px);
  transform: translateX(-100%);
}

.toggle-switch-label:active .toggle-switch-switch {
  width: 25px;
}

.toggle-switch-text {
  margin-left: 10px;
  font-size: 14px;
  font-weight: 500;
}

/* Animation effects */
.toggle-switch-label {
  overflow: hidden;
}

.toggle-switch-label::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 100px;
  transform: scale(0);
  transition: 0.3s;
}

.toggle-switch-label:hover::before {
  transform: scale(1);
}

.toggle-switch-switch {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.toggle-switch-checkbox:checked + .toggle-switch-label .toggle-switch-switch {
  transform: translateX(-100%);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
}

@keyframes ripple {
  0% {
    transform: scale(1);
    opacity: 0.4;
  }
  100% {
    transform: scale(2.5);
    opacity: 0;
  }
}

.toggle-switch-label::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 100px;
  opacity: 0;
}

.toggle-switch-checkbox:checked + .toggle-switch-label::after {
  animation: ripple 0.6s ease-out;
}
