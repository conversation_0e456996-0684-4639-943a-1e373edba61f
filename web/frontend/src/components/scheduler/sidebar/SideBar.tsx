import FullSideBarContent from "@/components/scheduler/sidebar/FullSideBarContent.tsx";
import ShortSideBarContent from "@/components/scheduler/sidebar/ShortSideBarContent.tsx";

interface SideBarProps {
  hidden: boolean;
  setHidden: (value: boolean) => void;
  setChangeAction: (value: boolean) => void;
  setHideCalendarSection: (hidden: boolean) => void;
  hideCalendarSection: boolean;
  setHideGuideSection: (hidden: boolean) => void;
  hideGuideSection: boolean;
  isSaveAction: boolean;
  handleSave: () => void;
  openFillerModal: () => void;
  openContentSettingModal: () => void;
  openRtpOutputSettingModal: () => void;
  isChangeAction?: boolean;
}

const SideBar = ({
  openFillerModal,
  hidden,
  setHidden,
  setChangeAction,
  isSaveAction,
  setHideCalendarSection,
  setHideGuideSection,
  handleSave,
  hideGuideSection,
  hideCalendarSection,
  openContentSettingModal,
  openRtpOutputSettingModal,
  isChangeAction,
}: SideBarProps) => {


  const openInNewTab = (url: string) => {
    window.open(url, '_blank');
  };

  return hidden ? (
    <ShortSideBarContent
      openFillerModal={openFillerModal}
      handleSave={handleSave}
      setHideCalendarSection={setHideCalendarSection}
      setHideGuideSection={setHideGuideSection}
      changeView={() => setHidden(false)}
      hideGuideSection={hideGuideSection}
      hideCalendarSection={hideCalendarSection}
      openInNewTab={openInNewTab}
      isSaveAction={isSaveAction}
      openContentSettingModal={openContentSettingModal}
      openRtpOutputSettingModal={openRtpOutputSettingModal}
      isChangeAction={isChangeAction}
    />
  ) : (
    <FullSideBarContent
      openFillerModal={openFillerModal}
      handleSave={handleSave}
      setChangeAction={setChangeAction}
      setHideCalendarSection={setHideCalendarSection}
      setHideGuideSection={setHideGuideSection}
      changeView={() => setHidden(true)}
      hideGuideSection={hideGuideSection}
      hideCalendarSection={hideCalendarSection}
      isSaveAction={isSaveAction}
      openContentSettingModal={openContentSettingModal}
      openRtpOutputSettingModal={openRtpOutputSettingModal}
      openInNewTab={openInNewTab}
      isChangeAction={isChangeAction}
    />
  );
};

export default SideBar;
