.autosave-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  margin: 8px 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.autosave-checkbox-wrapper {
  display: flex;
  align-items: center;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 46px;
  height: 22px;
  vertical-align: middle;
  margin-right: 8px;
}

.toggle-input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 34px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

.toggle-slider.active {
  background-color: #4ade80;
}

.toggle-slider.active:before {
  transform: translateX(24px);
}

.toggle-label {
  margin-left: 56px;
  cursor: pointer;
  font-weight: 500;
}

.save-status-container {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
}

.status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-updated {
  font-size: 0.8rem;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

.pulse {
  animation: pulse 1s ease-in-out;
}

@keyframes pulseOpacity {
  0% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.7;
  }
}

.pulse-opacity {
  animation: pulseOpacity 1s ease-in-out;
}

/* Enhanced UI elements and animations */
.save-button-highlight {
  background-color: rgba(74, 222, 128, 0.2);
  box-shadow: 0 0 8px rgba(74, 222, 128, 0.4);
  transition: all 0.3s ease;
}

.sidebar-row motion.button {
  transition: transform 0.3s ease, background-color 0.3s ease;
}

.sidebar-row motion.button:hover {
  background-color: rgba(0, 0, 0, 0.1);
}
