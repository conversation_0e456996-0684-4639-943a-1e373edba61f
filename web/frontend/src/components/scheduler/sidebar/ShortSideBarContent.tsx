import { AiFillSave, AiOutlineVideoCameraAdd } from "react-icons/ai";
import {
  HiCalendarDays,
  HiChevronRight,
  HiMiniListBullet,
  HiMiniTableCells,
  HiSignal,
} from "react-icons/hi2";
import { useSelector } from "react-redux";
import moment from "moment/moment";
import { selectGuide, selectSchedule } from "@/redux/schedulerSlice.ts";
import { Schedule } from "@/types/schedule.ts";

interface ShortSideBarContentProps {
  changeView: () => void;
  setHideCalendarSection: (hidden: boolean) => void;
  hideCalendarSection: boolean;
  setHideGuideSection: (hidden: boolean) => void;
  hideGuideSection: boolean;
  isSaveAction: boolean;
  isChangeAction?: boolean;
  handleSave: () => void;
  openInNewTab: (url: string) => void;
  openFillerModal: () => void;
  openContentSettingModal: () => void;
  openRtpOutputSettingModal: () => void;
}

const ShortSideBarContent = ({
  openFillerModal,
  openInNewTab,
  handleSave,
  isSaveAction,
  isChangeAction,
  changeView,
  setHideCalendarSection,
  setHideGuideSection,
  hideGuideSection,
  hideCalendarSection,
  openContentSettingModal,
  openRtpOutputSettingModal,
}: ShortSideBarContentProps) => {
  const schedule = useSelector(selectSchedule);
  const guide = useSelector(selectGuide);

  const toggleCalendar = () => {
    setHideCalendarSection(!hideCalendarSection);
    setHideGuideSection(true);
  };

  const toggleGuide = () => {
    setHideGuideSection(!hideGuideSection);
    setHideCalendarSection(true);
  };

  const getEPGLink = (schedule: Schedule): string => {
    return `${window.location.origin}/data/epg/${schedule.short_id}.xml`;
  };

  return (
    <div className={`sidebar short-sidebar`}>
      <div className={"sidebar-row"}>
        <button title={"Calendar"} onClick={toggleCalendar}>
          <HiCalendarDays />
        </button>
        <button title={"Guide"} onClick={toggleGuide}>
          <HiMiniTableCells />
        </button>
        <button
          title={"Open EPG link in new tab"}
          disabled={guide === null}
          onClick={() => openInNewTab(getEPGLink(schedule))}
        >
          EPG
        </button>
        <button title={"Filler setting"} onClick={openFillerModal}>
          <AiOutlineVideoCameraAdd />
        </button>
        <button title={"Metadata edit"} onClick={openContentSettingModal}>
          <HiMiniListBullet />
        </button>
        <button title={"RTP Output"} onClick={openRtpOutputSettingModal}>
          <HiSignal />
        </button>
      </div>
      <div className={"sidebar-row"}>
        {!schedule.autosave && (
          <button
            title={"Save"}
            onClick={handleSave}
            className={isChangeAction ? "save-button-highlight" : ""}
          >
            <AiFillSave />
          </button>
        )}
        <span className={"text-small text-center"}>
          {isSaveAction ? "Save.." : moment(schedule.updated_at).format("LT")}
        </span>
        <button
          onClick={changeView}
          className={
            "bg-[#151515A3] text-zinc-50 flex justify-center items-center h-[40px] gap-3 rounded-md w-full hover:border-[#ffa500] focus-visible:outline-0 border border-[#4D4D4D]"
          }
        >
          <HiChevronRight />
        </button>
      </div>
    </div>
  );
};

export default ShortSideBarContent;
