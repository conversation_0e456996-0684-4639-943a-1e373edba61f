import { HiChevronLeft, HiChevronRight } from "react-icons/hi";

interface PaginationProps {
  pagination: {
    page: number;
    limit: number;
    totalPages: number;
  };
  setPagination: (pagination: {
    page: number;
    limit: number;
    totalPages: number;
  }) => void;
}

const Pagination = (props: PaginationProps) => {
  const setCurrentPage = (page: number) => {
    props.setPagination({
      ...props.pagination,
      page,
    });
  };

  // Generate page numbers
  const getPageNumbers = () => {
    const totalPages = props.pagination.totalPages;
    const currentPage = props.pagination.page;
    const pageNumbers = [];

    // Always show first page
    pageNumbers.push(1);

    // Calculate range to show around current page
    let startPage = Math.max(2, currentPage - 1);
    let endPage = Math.min(totalPages - 1, currentPage + 1);

    // Add ellipsis if needed
    if (startPage > 2) {
      pageNumbers.push('...');
    }

    // Add pages around current page
    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }

    // Add ellipsis if needed
    if (endPage < totalPages - 1) {
      pageNumbers.push('...');
    }

    // Always show last page if more than 1 page
    if (totalPages > 1) {
      pageNumbers.push(totalPages);
    }

    return pageNumbers;
  };

  return (
    <div className="pagination-container">
      <button
        className="pagination-button"
        disabled={props.pagination.page === 1}
        onClick={() => setCurrentPage(props.pagination.page - 1)}
        title="Previous Page"
      >
        <HiChevronLeft />
      </button>

      {getPageNumbers().map((page, index) =>
        typeof page === 'number' ? (
          <button
            key={index}
            className={`pagination-button ${props.pagination.page === page ? 'pagination-button-active' : ''}`}
            onClick={() => setCurrentPage(page)}
          >
            {page}
          </button>
        ) : (
          <span key={index} className="pagination-ellipsis">...</span>
        )
      )}

      <button
        className="pagination-button"
        disabled={props.pagination.page === props.pagination.totalPages}
        onClick={() => setCurrentPage(props.pagination.page + 1)}
        title="Next Page"
      >
        <HiChevronRight />
      </button>

      <span className="pagination-info">
        Page {props.pagination.page} of {props.pagination.totalPages}
      </span>
    </div>
  );
};

export default Pagination;