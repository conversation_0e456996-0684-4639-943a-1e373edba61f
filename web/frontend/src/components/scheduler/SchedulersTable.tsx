import Pagination from "./Pagination";
import { Tooltip } from "@nextui-org/react";
import {
  Hi<PERSON>alendar,
  HiPencil,
  HiTrash,
  HiOutlineDocumentText,
  HiGlobeAlt,
  HiPlay,
} from "react-icons/hi";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { selectSchedulers } from "@/redux/schedulerSlice.ts";
import { Schedule, SchedulerStatus } from "@/types/schedule.ts";
import { HiOutlineClock } from "react-icons/hi2";
import { formatSchedulerDate } from "@/utils/fileUtils.ts";
import {
  getSchedulePlayingInfo,
  SchedulePlayingInfo,
} from "@/utils/scheduleUtils";
import { getSchedulersStatus } from "@/api/schedulerApi";
import PreviewSchedulerModal from "./modal/PreviewSchedulerModal";
import "./SchedulersTable.css";

interface TableProps {
  handleDelete: (id: number | null) => void;
  pagination: {
    page: number;
    limit: number;
    totalPages: number;
  };
  setPagination: (pagination: {
    page: number;
    limit: number;
    totalPages: number;
  }) => void;
}

const SchedulersTable = ({
  handleDelete,
  pagination,
  setPagination,
}: TableProps) => {
  const navigate = useNavigate();
  const schedulers = useSelector(selectSchedulers);
  const [playingInfo, setPlayingInfo] = useState<{
    [key: number]: SchedulePlayingInfo;
  }>({});
  const [schedulerStatus, setSchedulerStatus] = useState<
    Record<string, SchedulerStatus>
  >({});
  const [remainingTimes, setRemainingTimes] = useState<{
    [key: number]: string;
  }>({});
  const [previewShortId, setPreviewShortId] = useState<string | null>(null);
  const [, setIsScrolled] = useState<boolean>(false);

  // Update playing info every 10 seconds for full data refresh
  useEffect(() => {
    // Function to fetch playing info for all schedules
    const fetchPlayingInfo = async () => {
      const updatedPlayingInfo: { [key: number]: SchedulePlayingInfo } = {};

      // Create an array of promises for all schedules
      const promises = schedulers.map(async (schedule) => {
        if (schedule && schedule.id) {
          try {
            const info = await getSchedulePlayingInfo(schedule);
            updatedPlayingInfo[schedule.id] = info;
          } catch (error) {
            console.error(
              `Error fetching playing info for schedule ${schedule.id}:`,
              error
            );
            // Use empty info on error
            updatedPlayingInfo[schedule.id] = {
              currentItem: null,
              nextItem: null,
              currentItemDuration: "",
              nextItemDuration: "",
              currentItemRemainingTime: "",
              currentItemEndTime: null,
            };
          }
        }
      });

      // Wait for all promises to resolve
      await Promise.all(promises);
      setPlayingInfo(updatedPlayingInfo);
    };

    // Initial fetch
    fetchPlayingInfo();

    // Set up interval to update
    const intervalId = setInterval(fetchPlayingInfo, 10000);

    return () => clearInterval(intervalId);
  }, [schedulers]);

  // Update remaining time every second for countdown effect
  useEffect(() => {
    // Skip if no playing info or no scheduler status
    if (
      Object.keys(playingInfo).length === 0 ||
      Object.keys(schedulerStatus).length === 0
    )
      return;

    // Function to update remaining time
    const updateRemainingTime = () => {
      const newRemainingTimes: { [key: number]: string } = {};

      // Update remaining time only for schedulers with "Playing" status
      Object.keys(playingInfo).forEach((scheduleIdStr) => {
        const scheduleId = Number(scheduleIdStr);
        const info = playingInfo[scheduleId];
        const schedule = schedulers.find((s) => s.id === scheduleId);

        // Skip if schedule not found or no end time
        if (!schedule || !info.currentItemEndTime) {
          newRemainingTimes[scheduleId] =
            info.currentItemRemainingTime || "00:00:00";
          return;
        }

        // Only update countdown for schedulers with "Playing" status (active with sync)
        const isPlaying =
          schedulerStatus[schedule.short_id] &&
          schedulerStatus[schedule.short_id].is_active &&
          schedulerStatus[schedule.short_id].rtp_synced;

        if (isPlaying && info.currentItemEndTime) {
          // Calculate new remaining time
          const now = new Date();
          const endTime = info.currentItemEndTime;

          // If end time is in the past, set to "00:00:00"
          if (endTime.getTime() <= now.getTime()) {
            newRemainingTimes[scheduleId] = "00:00:00";
            return;
          }

          // Calculate remaining time in seconds
          const remainingSeconds = Math.floor(
            (endTime.getTime() - now.getTime()) / 1000
          );

          // Convert to hours, minutes, seconds
          const hours = Math.floor(remainingSeconds / 3600);
          const minutes = Math.floor((remainingSeconds % 3600) / 60);
          const seconds = remainingSeconds % 60;

          // Format with leading zeros
          newRemainingTimes[scheduleId] = `${hours
            .toString()
            .padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${seconds
            .toString()
            .padStart(2, "0")}`;
        } else {
          // For non-playing items, use the stored value or default
          newRemainingTimes[scheduleId] =
            info.currentItemRemainingTime || "00:00:00";
        }
      });

      setRemainingTimes(newRemainingTimes);
    };

    // Update immediately
    updateRemainingTime();

    // Set up interval to update every second
    const intervalId = setInterval(updateRemainingTime, 1000);

    return () => clearInterval(intervalId);
  }, [schedulerStatus, schedulers, playingInfo]);

  // Fetch scheduler status
  useEffect(() => {
    const fetchSchedulerStatus = async () => {
      try {
        const status = await getSchedulersStatus();
        setSchedulerStatus(status);
      } catch (error) {
        console.error("Failed to fetch scheduler status:", error);
        // Set empty status object on error to prevent undefined access
        setSchedulerStatus({});
      }
    };

    // Initial fetch
    fetchSchedulerStatus();

    // Set up interval to update status
    const intervalId = setInterval(fetchSchedulerStatus, 10000);

    return () => clearInterval(intervalId);
  }, []);

  // Handle scroll events for the table container
  useEffect(() => {
    const handleScroll = (event: Event) => {
      const container = event.target as HTMLElement;
      if (container.scrollLeft > 10) {
        setIsScrolled(true);
        container.setAttribute("data-scrolled", "true");
      } else {
        setIsScrolled(false);
        container.removeAttribute("data-scrolled");
      }
    };

    // Add scroll event listener to table container
    const tableContainer = document.querySelector(".scheduler-table-container");
    if (tableContainer) {
      tableContainer.addEventListener("scroll", handleScroll);
    }

    return () => {
      // Clean up event listener
      if (tableContainer) {
        tableContainer.removeEventListener("scroll", handleScroll);
      }
    };
  }, []);

  const openInNewTab = (url: string) => {
    window.open(url, "_blank");
  };

  const getEPGLink = (schedule: Schedule): string => {
    return `${window.location.origin}/data/epg/${schedule.short_id}.xml`;
  };

  // Helper function to get status display for a scheduler
  const getSchedulerStatusDisplay = (shortId: string) => {
    // Check if we have valid status data and the short_id exists
    if (!schedulerStatus || !shortId || !schedulerStatus[shortId]) {
      return {
        dotClass: "inactive",
        statusText: "Unknown",
      };
    }

    const status = schedulerStatus[shortId];

    // Validate the status object has the expected properties
    if (
      typeof status.is_active !== "boolean" ||
      typeof status.rtp_synced !== "boolean"
    ) {
      return {
        dotClass: "inactive",
        statusText: "Unknown",
      };
    }

    if (status.is_active) {
      if (status.rtp_synced) {
        return {
          dotClass: "synced",
          statusText: "Playing",
        };
      } else {
        return {
          dotClass: "unsynced",
          statusText: "No config",
        };
      }
    } else {
      return {
        dotClass: "inactive",
        statusText: "Stopped",
      };
    }
  };

  return (
    <>
      {previewShortId && (
        <PreviewSchedulerModal
          onClose={() => setPreviewShortId(null)}
          shortId={previewShortId}
        />
      )}
      <div className="scheduler-table-container">
        <table className="scheduler-table">
          <thead className="scheduler-table-header">
            <tr>
              <th
                className="scheduler-table-header-cell"
                style={{ width: "100px", textAlign: "center" }}
              >
                Status
              </th>
              <th
                className="scheduler-table-header-cell"
                style={{ width: "150px", textAlign: "center" }}
              >
                Name
              </th>
              <th
                className="scheduler-table-header-cell"
                style={{ width: "100px", textAlign: "center" }}
              >
                Links
              </th>
              <th
                className="scheduler-table-header-cell"
                style={{ width: "180px", textAlign: "center" }}
              >
                Current
              </th>
              <th
                className="scheduler-table-header-cell"
                style={{ width: "180px", textAlign: "center" }}
              >
                Next
              </th>
              <th
                className="scheduler-table-header-cell"
                style={{ width: "160px", textAlign: "center" }}
              >
                Output
              </th>
              <th
                className="scheduler-table-header-cell"
                style={{ width: "180px", textAlign: "center" }}
              >
                Last Updated
              </th>
              <th
                className="scheduler-table-header-cell"
                style={{ width: "130px", textAlign: "center" }}
              >
                NIC
              </th>
              <th
                className="scheduler-table-header-cell"
                style={{ width: "200px", textAlign: "center" }}
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="scheduler-table-body">
            {schedulers.map((item, index) => {
              const info = playingInfo[item.id] || {
                currentItem: null,
                nextItem: null,
                currentItemDuration: "",
                nextItemDuration: "",
              };

              return (
                item && (
                  <tr key={index} className="scheduler-table-row">
                    <td className="scheduler-table-cell">
                      <div className="sync-status">
                        {(() => {
                          const statusDisplay = getSchedulerStatusDisplay(
                            item.short_id
                          );

                          return (
                            <>
                              <div
                                className={`sync-dot ${statusDisplay.dotClass}`}
                              ></div>
                              <span
                                className={`sync-text ${statusDisplay.dotClass}`}
                              >
                                {statusDisplay.statusText}
                              </span>
                            </>
                          );
                        })()}
                      </div>
                    </td>
                    <td className="scheduler-table-cell">
                      <div className="schedule-name-cell">
                        <HiOutlineDocumentText className="schedule-icon" />
                        <Tooltip content={item.name} placement="top">
                          <span className="schedule-name">{item.name}</span>
                        </Tooltip>
                      </div>
                    </td>
                    <td className="scheduler-table-cell">
                      <div className="schedule-links-cell">
                        <button
                          className="epg-button"
                          onClick={() => openInNewTab(getEPGLink(item))}
                          title="Open EPG Link in New Tab"
                        >
                          <HiGlobeAlt className="epg-button-icon" />
                          <span>EPG</span>
                        </button>
                      </div>
                    </td>
                    <td className="scheduler-table-cell">
                      {info.currentItem ? (
                        <div className="schedule-current-item-cell">
                          <div className="item-name-row">
                            <Tooltip
                              content={info.currentItem.fileName}
                              placement="top"
                            >
                              <span className="current-item-name">
                                {info.currentItem.fileName}
                              </span>
                            </Tooltip>
                          </div>
                          <div className="item-duration-row">
                            {schedulerStatus[item.short_id] &&
                            schedulerStatus[item.short_id].is_active &&
                            schedulerStatus[item.short_id].rtp_synced ? (
                              <span className="item-remaining-time">
                                {remainingTimes[item.id] ||
                                  info.currentItemRemainingTime}
                              </span>
                            ) : schedulerStatus[item.short_id] &&
                              schedulerStatus[item.short_id].is_active ? (
                              <span className="item-total-duration">
                                {info.currentItemDuration}
                              </span>
                            ) : (
                              <span className="item-total-duration">
                                {info.currentItemDuration}
                              </span>
                            )}
                          </div>
                        </div>
                      ) : (
                        <div className="schedule-no-item-cell">
                          <span className="no-item-text">No current item</span>
                        </div>
                      )}
                    </td>
                    <td className="scheduler-table-cell">
                      {info.nextItem ? (
                        <div className="schedule-next-item-cell">
                          <div className="item-name-row">
                            <Tooltip
                              content={info.nextItem.fileName}
                              placement="top"
                            >
                              <span className="next-item-name">
                                {info.nextItem.fileName}
                              </span>
                            </Tooltip>
                          </div>
                          <div className="item-duration-row">
                            <span className="item-duration">
                              {info.nextItemDuration}
                            </span>
                          </div>
                        </div>
                      ) : (
                        <div className="schedule-no-item-cell">
                          <span className="no-item-text">No next item</span>
                        </div>
                      )}
                    </td>
                    <td className="scheduler-table-cell">
                      <div className="schedule-output-cell">
                        <span className="output-text">
                          {item.output_url || "Not set"}
                        </span>
                      </div>
                    </td>
                    <td className="scheduler-table-cell">
                      <div className="schedule-date-cell">
                        <HiOutlineClock className="date-icon" />
                        <span className="date-text">
                          {formatSchedulerDate(item.updated_at)}
                        </span>
                      </div>
                    </td>
                    <td className="scheduler-table-cell">
                      <div className="schedule-network-cell">
                        <HiGlobeAlt className="network-icon" />
                        <Tooltip
                          content={
                            item.network_interface
                              ? item.network_interface
                              : "Auto (System Default)"
                          }
                          placement="top"
                        >
                          <span className="network-text">
                            {item.network_interface
                              ? item.network_interface
                              : "Auto (System Default)"}
                          </span>
                        </Tooltip>
                      </div>
                    </td>
                    <td className="scheduler-table-cell">
                      <div className="schedule-actions-cell">
                        <button
                          className="action-button preview-button"
                          onClick={() => setPreviewShortId(item.short_id)}
                          title="Preview Stream"
                          disabled={!schedulerStatus[item.short_id]?.is_active}
                        >
                          <HiPlay className="action-icon" />
                        </button>
                        <button
                          className="action-button edit-button"
                          onClick={() => {
                            // Use navigate with state to prevent page reload
                            navigate(`/scheduler/${item.id}`, {
                              state: { fromSchedulersTable: true },
                            });
                          }}
                          title="Edit Schedule"
                        >
                          <HiPencil className="action-icon" />
                        </button>
                        <button
                          className="action-button delete-button"
                          onClick={() => handleDelete(item.id)}
                          title="Delete Schedule"
                        >
                          <HiTrash className="action-icon" />
                        </button>
                      </div>
                    </td>
                  </tr>
                )
              );
            })}
            {(!schedulers || schedulers.length === 0) && (
              <tr>
                <td colSpan={9}>
                  <div className="empty-state">
                    <HiCalendar className="empty-icon" />
                    <p className="empty-text">No schedules found</p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      <div className="pagination-container">
        <Pagination pagination={pagination} setPagination={setPagination} />
      </div>
    </>
  );
};

export default SchedulersTable;
