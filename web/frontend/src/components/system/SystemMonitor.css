.system-monitor {
  padding: 28px;
  background-color: var(--color-bg-primary);
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  position: relative;
  font-family: 'Roboto', system-ui, sans-serif;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.system-monitor::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, 
    var(--color-primary-dark), 
    var(--color-primary)
  );
  z-index: 1;
}

.system-monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.system-monitor-header h2 {
  margin: 0;
  font-size: 26px;
  font-weight: 600;
  color: var(--color-text-primary);
  display: flex;
  align-items: center;
  letter-spacing: 0.3px;
}

.system-monitor-header h2::before {
  content: '';
  display: inline-block;
  width: 12px;
  height: 12px;
  background-color: #10b981;
  border-radius: 50%;
  margin-right: 12px;
}

.system-alarm-banner {
  background: rgba(239, 68, 68, 0.8);
  color: white;
  padding: 10px 18px;
  border-radius: 8px;
  font-weight: 600;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.15);
  font-size: 0.85rem;
}

.system-alarm-banner::before {
  content: '⚠️';
  margin-right: 10px;
  font-size: 16px;
}

.system-monitor-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 260px;
  color: var(--color-text-secondary);
  font-size: 16px;
  position: relative;
  flex-direction: column;
  gap: 24px;
}

.system-monitor-loading::after {
  content: '';
  display: block;
  width: 40px;
  height: 40px;
  border: 3px solid rgba(59, 130, 246, 0.15);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.system-monitor-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 260px;
  color: var(--color-danger);
  font-size: 16px;
  text-align: center;
  background-color: rgba(239, 68, 68, 0.05);
  border-radius: 12px;
  padding: 24px;
  border: 1px solid rgba(239, 68, 68, 0.1);
}

.system-monitor-error::before {
  content: '⚠️';
  font-size: 40px;
  margin-bottom: 16px;
}

.system-monitor-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 20px;
  animation: fadeIn 0.5s ease-out;
}

/* Special layout when GPU is available */
.system-monitor-grid.with-gpu {
  grid-template-columns: repeat(2, 1fr);
}

.system-monitor-card {
  background-color: var(--color-bg-secondary);
  border-radius: 12px;
  padding: 22px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
  transition: transform 0.25s ease, box-shadow 0.25s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.system-monitor-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.system-monitor-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 14px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  position: relative;
}

.system-monitor-card-header svg {
  font-size: 24px;
  margin-right: 16px;
  color: var(--color-primary);
  padding: 8px;
  border-radius: 10px;
  background-color: rgba(33, 150, 243, 0.08);
}

.system-monitor-card-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--color-text-primary);
  letter-spacing: 0.3px;
}

.system-monitor-details {
  margin-top: 18px;
  display: grid;
  gap: 12px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  padding: 16px;
  font-family: 'Roboto', system-ui, sans-serif;
  overflow: hidden;
  position: relative;
}

.system-monitor-detail {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  padding: 6px 0;
  border-bottom: 1px dashed rgba(255, 255, 255, 0.06);
  align-items: center;
  overflow: hidden;
  width: 100%;
}

.system-monitor-detail:last-child,
.system-monitor-detail:nth-last-child(2) {
  border-bottom: none;
}

.system-monitor-detail span:first-child {
  color: var(--color-text-secondary);
  font-weight: 500;
  font-family: 'Roboto', system-ui, sans-serif;
  flex-shrink: 0;
  margin-right: 12px;
  min-width: 80px;
}

/* GPU specific styles */
.gpu-model-name {
  font-family: 'Roboto Mono', monospace;
  font-weight: 500;
  color: var(--color-text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  display: inline-block;
}

.gpu-memory {
  display: flex;
  align-items: center;
  gap: 4px;
  max-width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.gpu-memory-used {
  color: var(--color-text-primary);
  overflow: hidden;
  text-overflow: ellipsis;
}

.gpu-memory-separator {
  color: var(--color-text-secondary);
  margin: 0 2px;
  flex-shrink: 0;
}

.gpu-memory-total {
  color: var(--color-text-secondary);
  overflow: hidden;
  text-overflow: ellipsis;
}

.gpu-memory-percentage {
  color: var(--color-text-secondary);
  font-size: 12px;
  margin-left: 4px;
  opacity: 0.7;
}

.system-monitor-detail span:last-child {
  font-family: 'Roboto Mono', monospace;
  text-align: right;
  font-weight: 500;
  color: var(--color-text-primary);
  padding: 4px 8px;
  border-radius: 4px;
  background-color: rgba(33, 150, 243, 0.08);
  min-width: 60px;
  max-width: calc(100% - 90px);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

/* Media queries for better responsiveness */
@media (max-width: 1280px) {
  .system-monitor-grid.with-gpu {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 992px) {
  .system-monitor-grid.with-gpu {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .system-monitor {
    padding: 20px;
  }

  .system-monitor-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .system-monitor-header {
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 12px;
  }

  .system-monitor-header h2 {
    font-size: 22px;
    margin-bottom: 12px;
  }

  .system-alarm-banner {
    margin-top: 12px;
    width: 100%;
    text-align: center;
    padding: 8px 12px;
    font-size: 13px;
  }

  .system-monitor-card {
    padding: 16px;
  }

  .system-monitor-card-header svg {
    font-size: 20px;
    padding: 6px;
  }

  .system-monitor-card-header h3 {
    font-size: 18px;
  }

  .system-monitor-details {
    padding: 12px;
    margin-top: 14px;
  }

  .system-monitor-detail span:first-child {
    min-width: 70px;
  }

  .system-monitor-detail span:last-child {
    max-width: calc(100% - 80px);
  }
}

/* Add animation for fadeIn */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
