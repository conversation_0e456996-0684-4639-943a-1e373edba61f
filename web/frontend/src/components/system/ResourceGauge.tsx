import React from 'react';
import { HiExclamation } from 'react-icons/hi';
import './ResourceGauge.css';

interface ResourceGaugeProps {
  title: string;
  value: number;
  total: number;
  unit: string;
  percentage: number;
  alarm: boolean;
  threshold: number;
}

const ResourceGauge: React.FC<ResourceGaugeProps> = ({
  title,
  value,
  total,
  unit,
  percentage,
  alarm,
  threshold
}) => {
  // Format values for display
  const formatValue = (val: number): string => {
    if (unit === 'B') {
      // Format bytes to appropriate unit
      const units = ['B', 'KiB', 'MiB', 'GiB', 'TiB'];
      let formattedValue = val;
      let unitIndex = 0;

      while (formattedValue >= 1024 && unitIndex < units.length - 1) {
        formattedValue /= 1024;
        unitIndex++;
      }

      return `${formattedValue.toFixed(1)} ${units[unitIndex]}`;
    }

    // For other units, just format with 1 decimal place
    return `${val.toFixed(1)} ${unit}`;
  };

  // Determine color based on percentage and threshold
  const getColor = (): string => {
    if (percentage >= threshold) {
      return 'var(--color-danger)';
    } else if (percentage >= threshold * 0.8) {
      return 'var(--color-warning)';
    }
    return 'var(--color-success)';
  };

  const color = getColor();

  return (
    <div className={`resource-gauge ${alarm ? 'alarm' : ''}`}>
      <div className="resource-gauge-header">
        <h3 className="resource-gauge-title">{title}</h3>
        {alarm && (
          <div className="resource-gauge-alarm">
            <HiExclamation />
            <span>Alarm</span>
          </div>
        )}
      </div>

      <div className="resource-gauge-body">
        <div className="resource-gauge-meter">
          <div
            className={`resource-gauge-fill ${alarm ? 'alarm-fill' : ''}`}
            style={{
              width: `${Math.min(percentage, 100)}%`,
              backgroundColor: alarm ? undefined : color
            }}
          ></div>
        </div>

        <div className="resource-gauge-info">
          <div className="resource-gauge-values">
            <span>{formatValue(value)}</span>
            <span className="resource-gauge-total">/ {formatValue(total)}</span>
          </div>
          <div className="resource-gauge-percentage">
            {percentage.toFixed(1)}%
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResourceGauge;
