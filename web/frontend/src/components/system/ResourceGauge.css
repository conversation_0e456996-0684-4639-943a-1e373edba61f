.resource-gauge {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  padding: 18px;
  margin-bottom: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.25s ease;
  border: 1px solid rgba(255, 255, 255, 0.05);
  position: relative;
  overflow: hidden;
  font-family: 'Roboto', system-ui, sans-serif;
}

.resource-gauge::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(33, 150, 243, 0.15), transparent 70%);
  pointer-events: none;
  z-index: 0;
}

.resource-gauge:hover {
  box-shadow: 0 6px 14px rgba(0, 0, 0, 0.15);
}

.resource-gauge.alarm {
  border-color: rgba(239, 68, 68, 0.2);
}

.resource-gauge.alarm::before {
  background: radial-gradient(circle at top right, rgba(239, 68, 68, 0.15), transparent 70%);
}

@keyframes alarmPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    border-color: rgba(239, 68, 68, 0.4);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(239, 68, 68, 0);
    border-color: rgba(239, 68, 68, 0.6);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
    border-color: rgba(239, 68, 68, 0.4);
  }
}

.resource-gauge-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 14px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  position: relative;
}

.resource-gauge-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary);
  letter-spacing: 0.3px;
  position: relative;
  padding-left: 12px;
}

.resource-gauge-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 14px;
  background-color: var(--color-primary);
  border-radius: 2px;
}

.resource-gauge-alarm {
  display: flex;
  align-items: center;
  color: white;
  font-weight: 600;
  font-size: 12px;
  background-color: rgba(239, 68, 68, 0.8);
  padding: 4px 10px;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.resource-gauge-alarm::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shine 3s infinite;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
  20%, 40%, 60%, 80% { transform: translateX(2px); }
}

@keyframes shine {
  0% { left: -100%; }
  20% { left: 100%; }
  100% { left: 100%; }
}

.resource-gauge-alarm svg {
  margin-right: 6px;
  font-size: 14px;
}

.resource-gauge-body {
  display: flex;
  flex-direction: column;
  position: relative;
}

.resource-gauge-meter {
  height: 8px;
  background-color: rgba(33, 150, 243, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 12px;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  position: relative;
}

.resource-gauge-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.6s ease;
  background-color: var(--color-primary);
}

.resource-gauge-fill.alarm-fill {
  background-color: var(--color-danger);
}

.resource-gauge-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  padding: 0 2px;
}

.resource-gauge-values {
  color: var(--color-text-primary);
  font-weight: 500;
  display: flex;
  align-items: center;
  font-family: 'Roboto Mono', monospace;
}

.resource-gauge-values::before {
  content: '📊';
  margin-right: 8px;
  font-size: 14px;
  opacity: 0.8;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.resource-gauge:hover .resource-gauge-values::before {
  transform: scale(1.2);
  opacity: 1;
}

.resource-gauge-total {
  color: var(--color-text-secondary);
  margin-left: 5px;
  opacity: 0.8;
  font-family: 'Roboto Mono', monospace;
}

.resource-gauge-percentage {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-secondary);
  opacity: 0.85;
  font-family: 'Roboto Mono', monospace;
}

/* Media queries for better responsiveness */
@media (max-width: 768px) {
  .resource-gauge {
    padding: 14px;
    margin-bottom: 14px;
  }

  .resource-gauge-title {
    font-size: 15px;
  }

  .resource-gauge-alarm {
    font-size: 11px;
    padding: 3px 8px;
  }

  .resource-gauge-meter {
    height: 6px;
    margin-bottom: 10px;
  }

  .resource-gauge-info {
    font-size: 13px;
  }
}
