import React, { useState, useEffect } from 'react';
import { HiServer, HiChip, HiDatabase, HiCube } from 'react-icons/hi';
import { getSystemStats, getAlarmThresholds } from '../../api/systemApi';
import webSocketService from '../../api/websocket';
import { SystemStats, AlarmThresholds } from '../../types/system';
import ResourceGauge from './ResourceGauge';
import './SystemMonitor.css';

const SystemMonitor: React.FC = () => {
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [thresholds, setThresholds] = useState<AlarmThresholds>({
    disk_threshold: 80,
    cpu_threshold: 80,
    memory_threshold: 80,
    gpu_threshold: 80
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch initial data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch system stats
        const statsData = await getSystemStats();
        setStats(statsData);

        // Fetch alarm thresholds
        const thresholdsData = await getAlarmThresholds();
        setThresholds(thresholdsData);
      } catch (err) {
        console.error('Failed to fetch system data:', err);
        setError('Failed to fetch system data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Subscribe to WebSocket updates
  useEffect(() => {
    const unsubscribe = webSocketService.subscribeToSystemStats((newStats) => {
      setStats(newStats);
    });

    return () => {
      unsubscribe();
    };
  }, []);

  // Check if any alarms are active
  const hasAlarms = stats?.disk_alarm || stats?.cpu_alarm || stats?.memory_alarm || stats?.gpu_alarm;

  return (
    <div className="system-monitor">
      <div className="system-monitor-header">
        <h2>System Resources</h2>
        {hasAlarms && (
          <div className="system-alarm-banner">
            System resources exceeding thresholds!
          </div>
        )}
      </div>

      {loading ? (
        <div className="system-monitor-loading">Loading system statistics...</div>
      ) : error ? (
        <div className="system-monitor-error">{error}</div>
      ) : stats ? (
        <div className={`system-monitor-grid ${stats.gpu_available ? 'with-gpu' : ''}`}>
          {/* GPU section - only displayed when GPU is available */}
          {stats.gpu_available && (
            <div className="system-monitor-card">
              <div className="system-monitor-card-header">
                <HiCube />
                <h3>GPU Usage</h3>
              </div>
              <ResourceGauge
                title="GPU"
                value={stats.gpu_usage}
                total={100}
                unit="%"
                percentage={stats.gpu_usage}
                alarm={stats.gpu_alarm}
                threshold={thresholds.gpu_threshold}
              />
              <div className="system-monitor-details">
                <div className="system-monitor-detail">
                  <span>Model:</span>
                  <span className="gpu-model-name">{stats.gpu_model}</span>
                </div>
                <div className="system-monitor-detail">
                  <span>Usage:</span>
                  <span>{stats.gpu_usage.toFixed(1)}%</span>
                </div>
                <div className="system-monitor-detail">
                  <span>Temperature:</span>
                  <span>{stats.gpu_temp.toFixed(1)}°C</span>
                </div>
                <div className="system-monitor-detail">
                  <span>Memory:</span>
                  <span className="gpu-memory">
                    <span className="gpu-memory-used">{formatBytes(stats.gpu_mem_used)}</span>
                    <span className="gpu-memory-separator">/</span>
                    <span className="gpu-memory-total">{formatBytes(stats.gpu_mem_total)}</span>
                    <span className="gpu-memory-percentage">
                      {stats.gpu_mem_total > 0
                        ? ((stats.gpu_mem_used / stats.gpu_mem_total) * 100).toFixed(1)
                        : "0.0"}%
                    </span>
                  </span>
                </div>
                <div className="system-monitor-detail">
                  <span>Threshold:</span>
                  <span>{thresholds.gpu_threshold}%</span>
                </div>
              </div>
            </div>
          )}
          
          <div className="system-monitor-card">
            <div className="system-monitor-card-header">
              <HiChip />
              <h3>CPU Usage</h3>
            </div>
            <ResourceGauge
              title="CPU"
              value={stats.cpu_usage}
              total={100}
              unit="%"
              percentage={stats.cpu_usage}
              alarm={stats.cpu_alarm}
              threshold={thresholds.cpu_threshold}
            />
            <div className="system-monitor-details">
              <div className="system-monitor-detail">
                <span>Current:</span>
                <span>{stats.cpu_usage.toFixed(1)}%</span>
              </div>
              <div className="system-monitor-detail">
                <span>Threshold:</span>
                <span>{thresholds.cpu_threshold}%</span>
              </div>
            </div>
          </div>

          <div className="system-monitor-card">
            <div className="system-monitor-card-header">
              <HiServer />
              <h3>Memory Usage</h3>
            </div>
            <ResourceGauge
              title="Memory"
              value={stats.memory_used}
              total={stats.memory_total}
              unit="B"
              percentage={stats.memory_usage}
              alarm={stats.memory_alarm}
              threshold={thresholds.memory_threshold}
            />
            <div className="system-monitor-details">
              <div className="system-monitor-detail">
                <span>Free:</span>
                <span>{formatBytes(stats.memory_free)}</span>
              </div>
              <div className="system-monitor-detail">
                <span>Used:</span>
                <span>{formatBytes(stats.memory_used)}</span>
              </div>
              <div className="system-monitor-detail">
                <span>Total:</span>
                <span>{formatBytes(stats.memory_total)}</span>
              </div>
              <div className="system-monitor-detail">
                <span>Threshold:</span>
                <span>{thresholds.memory_threshold}%</span>
              </div>
            </div>
          </div>

          <div className="system-monitor-card">
            <div className="system-monitor-card-header">
              <HiDatabase />
              <h3>Disk Usage</h3>
            </div>
            <ResourceGauge
              title="Disk Space"
              value={stats.disk_used}
              total={stats.disk_total}
              unit="B"
              percentage={stats.disk_usage}
              alarm={stats.disk_alarm}
              threshold={thresholds.disk_threshold}
            />
            <div className="system-monitor-details">
              <div className="system-monitor-detail">
                <span>Free:</span>
                <span>{formatBytes(stats.disk_free)}</span>
              </div>
              <div className="system-monitor-detail">
                <span>Used:</span>
                <span>{formatBytes(stats.disk_used)}</span>
              </div>
              <div className="system-monitor-detail">
                <span>Total:</span>
                <span>{formatBytes(stats.disk_total)}</span>
              </div>
              <div className="system-monitor-detail">
                <span>Threshold:</span>
                <span>{thresholds.disk_threshold}%</span>
              </div>
            </div>
          </div>


        </div>
      ) : (
        <div className="system-monitor-error">No system data available</div>
      )}
    </div>
  );
};

// Helper function to format bytes
const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B';

  const units = ['B', 'KiB', 'MiB', 'GiB', 'TiB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));

  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${units[i]}`;
};

export default SystemMonitor;
