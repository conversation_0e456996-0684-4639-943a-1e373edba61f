import React, { useRef, useEffect } from "react";
import moment from "moment";
import "./GuideViewTimeline.css";

interface CurrentTimeIndicatorProps {
  currentTime: Date;
  startHour: number;
}

const CurrentTimeIndicator: React.FC<CurrentTimeIndicatorProps> = ({
  currentTime,
  startHour,
}) => {
  const indicatorRef = useRef<HTMLDivElement>(null);

  // Format current time for display
  const formattedTime = moment(currentTime).format("h:mm A");

  // Position the indicator
  useEffect(() => {
    if (indicatorRef.current) {
      try {
        // Calculate position based on current time and startHour
        const hours = currentTime.getHours();
        const minutes = currentTime.getMinutes();

        // Handle negative startHour (when we're displaying a window that starts in the past)
        let hoursSinceStart = 0;

        // Simple approach: treat this as a linear scale
        // If startHour is -12 and current hour is 6, then we're 18 hours in
        hoursSinceStart = hours - startHour + minutes / 60;

        const position = hoursSinceStart * 240; // 240px per hour (4px per minute)

        console.log("Current time indicator position:", {
          position,
          startHour,
          currentHour: hours,
          minutes,
          hoursSinceStart,
        });

        if (!isNaN(position) && position >= 0) {
          indicatorRef.current.style.left = `${position}px`;
          // Make sure the indicator is displayed
          indicatorRef.current.style.display = "block";
          indicatorRef.current.style.opacity = "1";
        } else {
          console.warn(
            "Invalid position calculated for current time indicator",
            { position, startHour, hours, minutes }
          );
          // Since we can't position it correctly, hide it
          indicatorRef.current.style.display = "none";
        }
      } catch (err) {
        console.error("Error positioning time indicator:", err);
      }
    }
  }, [currentTime, startHour]);

  return (
    <div
      ref={indicatorRef}
      className="guide-view-current-time-indicator"
      data-testid="current-time-indicator"
      style={{
        opacity: 0, // Start hidden, will be shown by the useEffect
        position: "absolute",
        top: 2,
        height: "538px",
        zIndex: 25,
      }}
    >
      <div className="current-time-label">{formattedTime}</div>
      <div
        className="current-time-line"
        style={{
          position: "absolute",
          top: "38px",
          bottom: 0,
          left: 0,
          width: "4px",
        }}
      ></div>
      <div
        className="current-time-pulse"
        style={{
          position: "absolute",
          top: "38px",
          left: "-2px",
          width: "8px",
          height: "8px",
          borderRadius: "50%",
          backgroundColor: "#2196f3",
          boxShadow: "0 0 10px rgba(33, 150, 243, 0.8)",
        }}
      ></div>
    </div>
  );
};

export default CurrentTimeIndicator;
