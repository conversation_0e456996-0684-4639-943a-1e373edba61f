import React from "react";
import { Element } from "@/types/schedule";
import ProgramBlock from "./ProgramBlock";

interface Channel {
  id: number | string;
  name: string;
  elements: Element[];
}

interface ChannelRowProps {
  channel: Channel;
  currentTime: Date;
  startHour: number;
  endHour: number;
}

const ChannelRow: React.FC<ChannelRowProps> = ({
  channel,
  currentTime,
  startHour,
  endHour,
}) => {
  // Create reference date with only the date part from currentTime
  const referenceDate = new Date(currentTime);
  referenceDate.setHours(0, 0, 0, 0);

  // Calculate timeline start based on startHour (handling negative hours)
  const timelineStart = new Date(referenceDate);
  if (startHour < 0) {
    // If startHour is negative, it's the previous day
    timelineStart.setDate(timelineStart.getDate() - 1);
    timelineStart.setHours(startHour + 24, 0, 0, 0);
  } else {
    timelineStart.setHours(startHour, 0, 0, 0);
  }

  // Calculate timeline end - using the full hour span
  const timelineEnd = new Date(timelineStart);
  timelineEnd.setTime(
    timelineStart.getTime() + (endHour - startHour) * 60 * 60 * 1000
  );

  // Filter elements to only include those within the visible time range
  const visibleElements = channel.elements.filter((element) => {
    const startTime = new Date(element.start);
    const endTime = new Date(element.end);

    // Check if element overlaps with the visible time range
    return startTime <= timelineEnd && endTime >= timelineStart;
  });

  // Sort elements by start time
  const sortedElements = [...visibleElements].sort((a, b) => {
    return new Date(a.start).getTime() - new Date(b.start).getTime();
  });

  return (
    <div className="guide-view-channel-row">
      {sortedElements.map((element, index) => (
        <ProgramBlock
          key={`${channel.id}-${index}`}
          element={element}
          currentTime={currentTime}
          startHour={startHour}
          endHour={endHour}
          animationOrder={index}
        />
      ))}
    </div>
  );
};

export default ChannelRow;
