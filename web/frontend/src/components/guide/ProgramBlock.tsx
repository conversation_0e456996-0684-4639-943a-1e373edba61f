import React, { useState } from "react";
import { Element } from "@/types/schedule";
import moment from "moment";

interface ProgramBlockProps {
  element: Element;
  currentTime: Date;
  startHour: number;
  endHour: number;
  animationOrder?: number;
}

const ProgramBlock: React.FC<ProgramBlockProps> = ({
  element,
  currentTime,
  startHour,
  endHour,
  animationOrder = 0,
}) => {
  const [showTooltip, setShowTooltip] = useState(false);

  // Calculate start and end times
  const startTime = new Date(element.start);
  const endTime = new Date(element.end);

  // Create reference date with only the date part from currentTime (remove time)
  const referenceDate = new Date(currentTime);
  referenceDate.setHours(0, 0, 0, 0);

  // Calculate position and width
  // Handle negative startHour (for display window that starts before current time)
  const timelineStartHour = startHour < 0 ? startHour + 24 : startHour;

  // Calculate the timeline start time
  const timelineStart = new Date(referenceDate);
  if (startHour < 0) {
    // If startHour is negative, it's the previous day
    timelineStart.setDate(timelineStart.getDate() - 1);
    timelineStart.setHours(timelineStartHour, 0, 0, 0);
  } else {
    timelineStart.setHours(timelineStartHour, 0, 0, 0);
  }

  // Calculate timeline end using the actual endHour from the timeline range
  const timelineEnd = new Date(timelineStart);
  const hoursSpan = endHour - startHour;
  timelineEnd.setTime(timelineStart.getTime() + hoursSpan * 60 * 60 * 1000);

  // For programs that started before the timeline, adjust the start time
  const adjustedStartTime =
    startTime < timelineStart ? timelineStart : startTime;

  // For programs that end after the timeline, adjust the end time
  // Special handling for filler programs - extend them to the timeline end
  let adjustedEndTime;
  if (element.type === "filler") {
    // For filler programs, always extend to the timeline end if they're active in the timeline
    adjustedEndTime = endTime > timelineEnd ? timelineEnd : endTime;
    // If filler starts within timeline but ends before timeline end, extend it to timeline end
    if (startTime >= timelineStart && endTime < timelineEnd) {
      adjustedEndTime = timelineEnd;
    }
  } else {
    // For non-filler programs, clip normally
    adjustedEndTime = endTime > timelineEnd ? timelineEnd : endTime;
  }

  // Calculate position (in minutes from timeline start)
  // Handle day crossings by using getTime() to get milliseconds
  const startMinutesFromTimelineStart =
    (adjustedStartTime.getTime() - timelineStart.getTime()) / (1000 * 60);

  // Calculate width (in minutes) - ensure it accurately reflects the real content duration
  const durationMinutes =
    (adjustedEndTime.getTime() - adjustedStartTime.getTime()) / (1000 * 60);

  // Calculate actual duration for tooltip (not adjusted for timeline)
  const actualDurationMinutes =
    (endTime.getTime() - startTime.getTime()) / (1000 * 60);

  // Convert to pixels (4px per minute for wider blocks)
  const leftPosition = startMinutesFromTimelineStart * 4;
  const width = Math.max(durationMinutes * 4, 60); // Ensure minimum width of 60px for very short programs

  // Check if program is currently playing
  // For extended filler programs, use adjusted end time for visual consistency
  let isCurrentlyPlaying;
  if (
    element.type === "filler" &&
    adjustedEndTime.getTime() !== endTime.getTime()
  ) {
    // For extended filler programs, consider them playing until the adjusted end time
    isCurrentlyPlaying =
      currentTime >= startTime && currentTime < adjustedEndTime;
  } else {
    // For regular programs, use original logic
    isCurrentlyPlaying = currentTime >= startTime && currentTime < endTime;
  }

  // Check if program has already played (is in the past)
  // For extended filler programs, use adjusted end time
  let isPlayed;
  if (
    element.type === "filler" &&
    adjustedEndTime.getTime() !== endTime.getTime()
  ) {
    isPlayed = currentTime > adjustedEndTime;
  } else {
    isPlayed = currentTime > endTime;
  }

  // Check if program is upcoming (in the future)
  const isUpcoming = currentTime < startTime;

  // Calculate progress percentage for currently playing programs
  // For visual consistency, use adjusted times for the progress bar display
  let progressPercentage = 0;
  if (isCurrentlyPlaying) {
    if (
      element.type === "filler" &&
      adjustedEndTime.getTime() !== endTime.getTime()
    ) {
      // For extended filler programs, calculate progress based on current time position within the timeline window
      // This ensures the progress bar matches the timeline header position
      const timelineStartTime = timelineStart.getTime();
      const timelineEndTime = timelineEnd.getTime();
      const currentTimeMs = currentTime.getTime();

      // Calculate where current time falls within the timeline window
      const timelineProgress =
        (currentTimeMs - timelineStartTime) /
        (timelineEndTime - timelineStartTime);
      progressPercentage = Math.max(0, Math.min(timelineProgress * 100, 100));
    } else {
      // For regular programs and non-extended fillers, use original calculation
      progressPercentage =
        ((currentTime.getTime() - startTime.getTime()) /
          (endTime.getTime() - startTime.getTime())) *
        100;
      // Ensure progress doesn't exceed 100%
      progressPercentage = Math.min(progressPercentage, 100);
    }
  }

  // Format times for display using the device's local timezone
  const formatTime = (date: Date) => moment(date).format("h:mm A");

  // Determine program type for styling
  const getProgramTypeClass = () => {
    switch (element.type) {
      case "file":
        return "program-file";
      case "connection":
        return "program-connection";
      case "filler":
        return "program-filler";
      default:
        return "";
    }
  };

  // Calculate animation order based on start time and provided animationOrder prop
  const calculatedAnimationOrder =
    Math.floor(startMinutesFromTimelineStart / 30) + animationOrder;

  // Determine program state class
  const getProgramStateClass = () => {
    if (isCurrentlyPlaying) return "currently-playing";
    if (isPlayed) return "played-program";
    if (isUpcoming) return "upcoming-program";
    return "";
  };

  // Debug logging for filler programs
  if (element.type === "filler" || isCurrentlyPlaying) {
    console.log(
      `Element: "${element.title}", type: "${
        element.type
      }", isCurrentlyPlaying: ${isCurrentlyPlaying}, shouldShowProgress: ${
        isCurrentlyPlaying && element.type !== "filler"
      }`
    );
  }

  return (
    <div
      className={`guide-view-program-block ${getProgramTypeClass()} ${getProgramStateClass()}`}
      style={
        {
          left: `${leftPosition}px`,
          width: `${width}px`,
          "--animation-order": calculatedAnimationOrder,
        } as React.CSSProperties
      }
      onMouseEnter={() => setShowTooltip(true)}
      onMouseLeave={() => setShowTooltip(false)}
    >
      <div className="program-title" title={element.title}>
        {element.title}
      </div>

      {isCurrentlyPlaying &&
        element.title?.toLowerCase?.()?.trim?.() !== "filler" && (
          <div className="program-progress-bar">
            <div
              className="program-progress"
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
        )}

      {showTooltip && (
        <div className="program-tooltip">
          <h4>{element.title}</h4>
          {element.description && <p>{element.description}</p>}
          <div className="tooltip-time">
            {formatTime(startTime)} - {formatTime(endTime)}
            <span className="tooltip-duration">
              ({Math.round(actualDurationMinutes)} min)
            </span>
            {element.type === "filler" &&
              adjustedEndTime.getTime() === timelineEnd.getTime() &&
              endTime < timelineEnd && (
                <span className="tooltip-extended">
                  {" "}
                  - Extended to timeline end
                </span>
              )}
          </div>
          {element.type === "file" && element.file && (
            <div className="tooltip-file-info">
              {element.file.filename || `File ID: ${element.file.file_id}`}
              {element.file.episode && (
                <span> | Episode: {element.file.episode}</span>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ProgramBlock;
