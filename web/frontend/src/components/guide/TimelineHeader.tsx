import React from "react";
import moment from "moment";

interface TimelineHeaderProps {
  timeSlots: string[];
}

const TimelineHeader: React.FC<TimelineHeaderProps> = ({ timeSlots }) => {
  // Format time for display - handling negative hours
  const formatTimeSlot = (timeSlot: string) => {
    const [hourStr, minute] = timeSlot.split(":");
    let hour = parseInt(hourStr);

    // Create a base date for today
    const baseDate = moment();

    // If hour is negative or >= 24, adjust the date accordingly
    let dayOffset = 0;
    if (hour < 0) {
      dayOffset = -1;
      hour += 24;
    } else if (hour >= 24) {
      dayOffset = Math.floor(hour / 24);
      hour = hour % 24;
    }

    // Set the correct date and time
    const time = baseDate.add(dayOffset, "days").set({
      hour: hour,
      minute: parseInt(minute) || 0,
      second: 0,
      millisecond: 0,
    });

    return time.format("h:mm A");
  };

  return (
    <div className="guide-view-timeline-header">
      {timeSlots.map((timeSlot, index) => {
        const isFullHour = timeSlot.endsWith(":00");
        const isHalfHour = timeSlot.endsWith(":30");

        // Calculate if this is the last time slot
        const isLastTimeSlot = index === timeSlots.length - 1;

        return (
          <div
            key={index}
            className={`guide-view-timeline-header-item ${
              isFullHour ? "hour-marker" : "half-hour-marker"
            } ${isLastTimeSlot ? "last-time-slot" : ""}`}
            style={{ "--header-index": index } as React.CSSProperties}
          >
            {isFullHour && (
              <div className="time-marker">{formatTimeSlot(timeSlot)}</div>
            )}
            {isHalfHour && <div className="half-hour-marker-line"></div>}
          </div>
        );
      })}
    </div>
  );
};

export default TimelineHeader;
