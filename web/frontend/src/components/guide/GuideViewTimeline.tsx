import React, { useEffect, useState, useRef } from "react";
import { Guide, Element, SchedulerStatus } from "@/types/schedule";
import "./GuideViewTimeline.css";
import TimelineHeader from "./TimelineHeader";
import ChannelRow from "./ChannelRow";
import CurrentTimeIndicator from "./CurrentTimeIndicator";
import {
  loadEPGData,
  convertEPGDataToGuides,
  scanEPGDirectory,
  EPGData,
} from "@/utils/epgUtils";
import { getSchedulersStatus, getSchedules } from "@/api/schedulerApi";
import { useSelector, useDispatch } from "react-redux";
import { selectSchedulers, setSchedulers } from "@/redux/schedulerSlice";
import { HiPlay } from "react-icons/hi";

interface GuideViewTimelineProps {
  className?: string;
  onPreview?: (shortId: string) => void;
}

interface Channel {
  id: number | string;
  name: string;
  elements: Element[];
  short_id: string;
}

const GuideViewTimeline = ({
  className = "",
  onPreview,
}: GuideViewTimelineProps) => {
  const [, setGuides] = useState<Guide[]>([]);
  const [channels, setChannels] = useState<Channel[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [currentTime, setCurrentTime] = useState<Date>(new Date());
  const [timeRange, setTimeRange] = useState({ startHour: 0, endHour: 24 });
  const timelineRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [schedulerStatus, setSchedulerStatus] = useState<
    Record<string, SchedulerStatus>
  >({});

  // Get actual schedulers data from Redux, same as SchedulersTable
  const dispatch = useDispatch();
  const schedulers = useSelector(selectSchedulers);

  // Fetch schedulers data if not already available in Redux
  useEffect(() => {
    const fetchSchedulersData = async () => {
      // Only fetch if we don't have schedulers data yet
      if (!schedulers || schedulers.length === 0) {
        try {
          console.log("Fetching schedulers data for GuideViewTimeline...");
          const response = await getSchedules(1, 100); // Fetch first page with high limit to get all schedules
          dispatch(setSchedulers({ schedulers: response.items }));
          console.log(
            `Fetched ${response.items.length} schedulers for GuideViewTimeline`
          );
        } catch (error) {
          console.error("Failed to fetch schedulers data:", error);
          setError(
            "Failed to load schedulers data: " +
              (error instanceof Error ? error.message : String(error))
          );
        }
      }
    };

    fetchSchedulersData();
  }, [dispatch]); // Removed schedulers from dependency to avoid loop

  // Create channels whenever schedulers data changes
  useEffect(() => {
    if (schedulers && schedulers.length > 0) {
      console.log("Creating channels from schedulers data...");
      // Calculate time range when schedulers are available
      calculateTimeRange();
      // Create channels with empty guides initially
      processGuidesIntoChannels([]);
      // If we have schedulers but no EPG data, we can still show the timeline
      setIsLoading(false);

      // Now that we have schedulers data, fetch EPG data
      fetchEPGData();
    }
  }, [schedulers]);

  // Generate time slots for the timeline (30-minute intervals)
  const timeSlots = [];
  for (let hour = timeRange.startHour; hour < timeRange.endHour; hour++) {
    // Format hour value as string, even if negative or >24
    const hourStr = `${hour}`;

    // Add full hour marker
    timeSlots.push(`${hourStr}:00`);

    // Add half-hour marker for all hours except the last one
    if (hour < timeRange.endHour - 1) {
      timeSlots.push(`${hourStr}:30`);
    }
  }

  // Add the final full hour
  timeSlots.push(`${timeRange.endHour}:00`);

  // Update current time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  // Fetch scheduler status
  useEffect(() => {
    const fetchSchedulerStatus = async () => {
      try {
        const status = await getSchedulersStatus();
        setSchedulerStatus(status);
      } catch (error) {
        console.error("Failed to fetch scheduler status:", error);
        // Set empty status object on error to prevent undefined access
        setSchedulerStatus({});
      }
    };

    // Initial fetch
    fetchSchedulerStatus();

    // Set up interval to update status more frequently (every 2 seconds instead of 10)
    // This makes the status update more responsive and in sync with schedulersTable
    const intervalId = setInterval(fetchSchedulerStatus, 2000);

    return () => clearInterval(intervalId);
  }, []);

  // Process guides into channels - simplified to use actual scheduler data
  const processGuidesIntoChannels = (guides: Guide[]) => {
    const channelsMap = new Map<number, Channel>();

    // Use actual schedulers data instead of trying to match EPG data
    if (!schedulers || schedulers.length === 0) {
      console.log("No schedulers available");
      return;
    }

    const now = new Date();

    // Calculate time range - 1 hour before and 12 hours after current time
    const oneHourAgo = new Date(now);
    oneHourAgo.setHours(now.getHours() - 1);

    const twelveHoursAhead = new Date(now);
    twelveHoursAhead.setHours(now.getHours() + 12);

    // Create channels from actual scheduler data
    schedulers.forEach((scheduler) => {
      // Find matching guide for this scheduler (if any)
      const matchingGuide = guides.find(
        (guide) =>
          guide.scheduleName === scheduler.name ||
          guide.schedule_id === scheduler.id
      );

      // Use guide elements if available, otherwise create empty channel
      const elements = matchingGuide
        ? matchingGuide.elements.filter((element) => {
            const startTime = new Date(element.start);
            const endTime = new Date(element.end);

            return (
              (startTime >= oneHourAgo && startTime <= twelveHoursAhead) ||
              (endTime >= oneHourAgo && endTime <= twelveHoursAhead) ||
              (startTime <= oneHourAgo && endTime >= twelveHoursAhead)
            );
          })
        : [];

      // Create channel using actual scheduler data
      const channel: Channel = {
        id: scheduler.id,
        name: scheduler.name,
        elements: elements,
        short_id: scheduler.short_id, // Use the actual short_id from scheduler
      };

      channelsMap.set(scheduler.id, channel);
    });

    setChannels(Array.from(channelsMap.values()));
  };

  // Fetch EPG data from XML files - now called after schedulers are loaded
  const fetchEPGData = async () => {
    try {
      console.log("Loading EPG data from XML files...");

      // Scan for available EPG files based on schedule short_ids
      const availableFiles = await scanEPGDirectory();
      console.log("Available EPG files from schedules:", availableFiles);

      if (availableFiles.length === 0) {
        console.log("No EPG data found in any of the files");
        // Don't set error or loading to false - we can still show channels without EPG data
        console.warn(
          "No valid EPG data found. Channels will be displayed without program information."
        );
        return;
      } else {
        // Load data from available schedule files
        const epgDataPromises = availableFiles.map((file) => {
          const shortId = file.replace(".xml", "");
          console.log(`Loading EPG data for schedule with shortId: ${shortId}`);
          return loadEPGData(shortId);
        });

        const epgDataResults = await Promise.allSettled(epgDataPromises);
        const successfulEpgData = epgDataResults
          .filter(
            (result): result is PromiseFulfilledResult<EPGData> =>
              result.status === "fulfilled" && result.value !== null
          )
          .map((result) => result.value as EPGData);

        if (successfulEpgData.length === 0) {
          console.log(
            "No EPG data found in any of the available schedule files"
          );
          // Don't set error or loading to false - we can still show channels without EPG data
          console.warn(
            "No valid EPG data found. Channels will be displayed without program information."
          );
          return;
        }

        console.log(
          `Successfully loaded EPG data from ${successfulEpgData.length} schedule files`
        );

        // Process the loaded EPG data
        processEPGData(successfulEpgData);
      }
    } catch (error) {
      console.error("Error loading EPG data:", error);
      // Don't set error state that would block the display - log the warning instead
      console.warn(
        "Failed to load EPG data, but channels can still be displayed:",
        error instanceof Error ? error.message : String(error)
      );
    }
  };

  // Process EPG data
  const processEPGData = (epgDataArray: EPGData[]) => {
    try {
      // Merge all EPG data into a single object
      const mergedEpgData: EPGData = {
        channels: [],
        programs: [],
      };

      // Merge channels and programs from all EPG data
      epgDataArray.forEach((epgData) => {
        if (!epgData) return;

        // Add channels that don't already exist (based on id)
        epgData.channels.forEach(
          (channel: { id: string; displayName: string; icon?: string }) => {
            if (
              !mergedEpgData.channels.some(
                (c: { id: string }) => c.id === channel.id
              )
            ) {
              mergedEpgData.channels.push(channel);
            }
          }
        );

        // Add all programs
        mergedEpgData.programs.push(...epgData.programs);
      });

      console.log("Merged EPG data:", mergedEpgData);

      // Check if we have valid data
      if (
        mergedEpgData.channels.length === 0 ||
        mergedEpgData.programs.length === 0
      ) {
        console.warn(
          "No valid channels or programs found in EPG data - using channels without program information"
        );
        return;
      }

      // Convert EPG data to guides (one guide per channel)
      const guides = convertEPGDataToGuides(mergedEpgData);
      console.log(`Created ${guides.length} guides from EPG data`);
      setGuides(guides);

      // Re-process channels with the new EPG data
      processGuidesIntoChannels(guides);
    } catch (err) {
      console.error("Error processing EPG data:", err);
      console.warn(
        "Failed to process EPG data, channels will be displayed without program information:",
        err instanceof Error ? err.message : String(err)
      );
    }
  };

  // Calculate the time range based on program data
  const calculateTimeRange = () => {
    // Set time range to show past 1 hour and next 12 hours based on current time
    const now = new Date();
    const currentHour = now.getHours();

    // Calculate start hour (1 hour back) and end hour (12 hours forward)
    // We'll display a 13-hour window
    const startHour = currentHour - 1;
    const endHour = currentHour + 12;

    console.log(
      `Setting time range: ${startHour} to ${endHour} (past 1 hour to next 12 hours, current hour: ${currentHour})`
    );

    setTimeRange({
      startHour,
      endHour,
    });
  };

  // Calculate the total width of the timeline grid based on time range
  const calculateGridWidth = () => {
    const hoursCount = timeRange.endHour - timeRange.startHour;
    return hoursCount * 240; // 240px per hour
  };

  // Scroll to current time when component mounts or when time range changes
  useEffect(() => {
    if (!isLoading) {
      // Use a timeout to ensure DOM is fully rendered
      setTimeout(() => {
        centerTimelineOnCurrentTime();
      }, 300);
    }
  }, [isLoading, timeRange, currentTime]);

  // Center the timeline on the current time
  const centerTimelineOnCurrentTime = () => {
    const scrollContainer = scrollContainerRef.current;

    if (!scrollContainer) return;

    const now = new Date();
    const currentHour = now.getHours() + now.getMinutes() / 60;

    // With our 13-hour window (past 1 hour, next 12 hours), calculating position is simpler
    // The current time should be 1 hour from the left edge of the timeline
    const hoursSinceStart = currentHour - timeRange.startHour;

    const currentTimePosition = hoursSinceStart * 240; // 240px per hour (4px per minute)

    // Validate position is a number
    if (isNaN(currentTimePosition)) {
      console.warn("Invalid time position calculated, cannot center timeline");
      return;
    }

    const scrollContainerWidth = scrollContainer.clientWidth;

    // Calculate the scroll position that will center the current time
    const scrollPosition = Math.max(
      0,
      currentTimePosition - scrollContainerWidth / 2
    );

    // Apply the scroll position to the scroll container
    scrollContainer.scrollLeft = scrollPosition;

    console.log(
      `Centered timeline at position ${scrollPosition}px (current hour: ${currentHour}, hours since start: ${hoursSinceStart})`
    );
  };

  // Get channel status details for tooltip and display
  const getChannelStatus = (shortId: string) => {
    // Check if we have valid status data and the short_id exists
    if (!schedulerStatus || !shortId || !schedulerStatus[shortId]) {
      return {
        status: "unknown",
        className: "inactive",
        title: "Unknown",
        tooltip: "Status information unavailable",
      };
    }

    const status = schedulerStatus[shortId];

    // Validate the status object has the expected properties
    if (
      typeof status.is_active !== "boolean" ||
      typeof status.rtp_synced !== "boolean"
    ) {
      return {
        status: "unknown",
        className: "inactive",
        title: "Unknown",
        tooltip: "Invalid status data",
      };
    }

    if (status.is_active) {
      if (status.rtp_synced) {
        return {
          status: "online",
          className: "synced",
          title: "Online",
          tooltip: "Channel is online and streaming",
        };
      } else {
        return {
          status: "no-config",
          className: "unsynced",
          title: "No Config",
          tooltip: "Channel is active but not configured properly",
        };
      }
    } else {
      return {
        status: "offline",
        className: "inactive",
        title: "Offline",
        tooltip: "Channel is currently offline",
      };
    }
  };

  if (isLoading) {
    return (
      <div className="guide-view-timeline-loading">
        Loading Program Guide Timeline
        <br />
        Fetching schedule and program data...
      </div>
    );
  }

  if (error) {
    return <div className="guide-view-timeline-error">{error}</div>;
  }

  const gridWidth = calculateGridWidth();

  return (
    <div className={`guide-view-timeline ${className}`}>
      <div className="guide-view-container" ref={timelineRef}>
        <div className="guide-view-sidebar">
          <div className="guide-view-sidebar-header">Channels</div>
          <div className="guide-view-sidebar-channels">
            {channels.map((channel: Channel, index: number) => {
              const channelStatus = getChannelStatus(channel.short_id);

              return (
                <div
                  key={channel.id}
                  className="guide-view-sidebar-item"
                  style={{ "--item-index": index } as React.CSSProperties}
                >
                  <div className="channel-info">
                    <div className="channel-name-section">
                      <div
                        className={`sync-dot ${channelStatus.className}`}
                        title={channelStatus.title}
                      ></div>
                      <span className="channel-name">{channel.name}</span>
                    </div>
                    <div className="channel-status">
                      <button
                        className="channel-preview-button"
                        onClick={() => onPreview?.(channel.short_id)}
                        title="Preview Stream"
                        disabled={!schedulerStatus[channel.short_id]?.is_active}
                      >
                        <HiPlay />
                      </button>
                    </div>
                  </div>
                  <div className="channel-tooltip">
                    <div className="channel-tooltip-name">{channel.name}</div>
                    <div className="channel-tooltip-status">
                      Status: {channelStatus.title}
                    </div>
                    <div className="channel-tooltip-details">
                      {channelStatus.tooltip}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        <div className="guide-view-timeline-content">
          <div
            className="guide-view-timeline-scroll-container"
            ref={scrollContainerRef}
            onScroll={(e) => {
              // Sync vertical scroll with the sidebar
              const sidebarChannels = document.querySelector(
                ".guide-view-sidebar-channels"
              );
              if (sidebarChannels) {
                sidebarChannels.scrollTop = e.currentTarget.scrollTop;
              }
            }}
          >
            <div
              className="guide-view-content-wrapper"
              style={{ width: `${gridWidth}px` }}
            >
              <div className="guide-view-timeline-header-container">
                <TimelineHeader timeSlots={timeSlots} />
                <CurrentTimeIndicator
                  currentTime={currentTime}
                  startHour={timeRange.startHour}
                />
              </div>

              <div className="guide-view-timeline-grid">
                {channels.map((channel: Channel) => (
                  <ChannelRow
                    key={channel.id}
                    channel={channel}
                    currentTime={currentTime}
                    startHour={timeRange.startHour}
                    endHour={timeRange.endHour}
                  />
                ))}
              </div>
            </div>
          </div>

          {/* Button to return to current time */}
          <button
            className="return-to-now-button"
            onClick={centerTimelineOnCurrentTime}
            title="Return to current time"
          >
            Now
          </button>
        </div>
      </div>
    </div>
  );
};

export default GuideViewTimeline;
