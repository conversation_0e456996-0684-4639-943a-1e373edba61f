/* GuideViewTimeline.css */

.guide-view-timeline {
  width: 100%;
  background-color: var(--color-bg-secondary);
  background-image: linear-gradient(
    to bottom,
    rgba(33, 150, 243, 0.05),
    rgba(108, 92, 231, 0.05)
  );
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--shadow-md);
  margin-top: 2rem;
  position: relative;
  animation: fadeInUp 0.8s cubic-bezier(0.22, 1, 0.36, 1) forwards;
  border: 1px solid rgba(33, 150, 243, 0.1);
  transition: all 0.3s ease-in-out;
}

.guide-view-timeline:hover {
  box-shadow: var(--shadow-lg), 0 0 20px rgba(33, 150, 243, 0.1);
  border: 1px solid rgba(33, 150, 243, 0.2);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.guide-view-title {
  padding: 1rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-text-primary);
  border-bottom: 1px solid var(--color-border-primary);
  background-color: var(--color-bg-tertiary);
}

.guide-view-container {
  display: flex;
  height: 550px;
  max-height: 75vh;
  overflow: hidden;
  position: relative;
  border-radius: 0 0 12px 12px;
  box-shadow: inset 0 0 30px rgba(0, 0, 0, 0.2);
  background-image: linear-gradient(
    to bottom,
    rgba(30, 30, 30, 0.5),
    rgba(18, 18, 18, 0.5)
  );
  will-change: transform; /* Optimize for performance */
}

.guide-view-sidebar {
  width: 200px;
  flex-shrink: 0;
  background-color: var(--color-bg-tertiary);
  background-image: linear-gradient(
    to right,
    rgba(33, 150, 243, 0.05),
    rgba(33, 150, 243, 0.01)
  );
  border-right: 1px solid rgba(33, 150, 243, 0.2);
  z-index: 10;
  position: sticky;
  left: 0;
  display: flex;
  flex-direction: column;
  box-shadow: 5px 0 15px rgba(0, 0, 0, 0.1);
}

.guide-view-sidebar-header {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  border-bottom: 1px solid rgba(33, 150, 243, 0.2);
  background: linear-gradient(
    90deg,
    rgba(33, 150, 243, 0.1),
    rgba(108, 92, 231, 0.1)
  );
  color: var(--color-primary);
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: 0.9rem;
  position: sticky;
  top: 0;
  z-index: 20;
  overflow: hidden;
}

.guide-view-sidebar-header::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, #2196f3, #6c5ce7);
}

.guide-view-sidebar-channels {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: none; /* Hide scrollbar for Firefox */
  -ms-overflow-style: none; /* Hide scrollbar for IE/Edge */
}

.guide-view-sidebar-channels::-webkit-scrollbar {
  display: none; /* Hide scrollbar for Chrome/Safari/Opera */
}

.guide-view-sidebar-item {
  height: 55px;
  display: flex;
  align-items: center;
  padding: 0 1rem;
  border-bottom: 1px solid rgba(33, 150, 243, 0.1);
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  animation: fadeInLeft 0.5s ease-out forwards;
  opacity: 0;
  animation-delay: calc(var(--item-index, 0) * 0.1s);
  white-space: nowrap;
  text-overflow: ellipsis;
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.guide-view-sidebar-item {
  position: relative;
}

.guide-view-sidebar-item:hover {
  background-color: rgba(33, 150, 243, 0.1);
  padding-left: 1rem;
}

.guide-view-sidebar-item::before {
  display: none;
}

.channel-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 0.5rem;
}

.channel-name-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
  min-width: 0;
}

.channel-preview-button {
  background: transparent;
  border: 1px solid rgba(76, 175, 80, 0.3);
  border-radius: 4px;
  color: var(--color-success);
  padding: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.channel-preview-button:hover:not(:disabled) {
  transform: translateY(-2px) scale(1.1);
  color: var(--color-success);
  filter: drop-shadow(0 0 5px rgba(76, 175, 80, 0.6));
  border-color: var(--color-success);
  background-color: rgba(76, 175, 80, 0.1);
}

.channel-preview-button:disabled {
  color: var(--color-text-tertiary);
  cursor: not-allowed;
  opacity: 0.5;
  border-color: rgba(255, 255, 255, 0.05);
}

.channel-preview-button:disabled:hover {
  transform: none;
  filter: none;
  background-color: transparent;
}

.guide-view-sidebar-item .channel-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: calc(100% - 20px);
}

.channel-status {
  display: flex;
  align-items: center;
  margin-left: 8px;
}

/* Style for status dots */
.sync-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 8px;
  position: relative;
  z-index: 2;
}

.sync-dot.synced {
  background: linear-gradient(to right, #4caf50, #66bb6a);
  box-shadow: 0 0 10px #4caf50;
  animation: pulse-dot 1.5s infinite ease-in-out alternate;
}

.sync-dot.synced::before {
  content: "";
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border-radius: 50%;
  background: transparent;
  border: 2px solid rgba(76, 175, 80, 0.5);
  opacity: 0;
  z-index: -1;
  animation: ripple 2s infinite cubic-bezier(0.65, 0, 0.34, 1);
}

.sync-dot.synced::after {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  background: transparent;
  border: 1px solid rgba(76, 175, 80, 0.8);
  z-index: -1;
  animation: ripple 2s infinite 0.5s cubic-bezier(0.65, 0, 0.34, 1);
}

.sync-dot.unsynced {
  background: linear-gradient(to right, var(--color-warning), #ffa000);
  box-shadow: 0 0 5px var(--color-warning);
  animation: pulse-warning 3s infinite ease-in-out alternate;
}

.sync-dot.inactive {
  background: linear-gradient(to right, var(--color-danger), #d32f2f);
  box-shadow: 0 0 5px var(--color-danger);
}

@keyframes pulse-dot {
  0% {
    transform: scale(0.9);
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.8);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 8px 3px rgba(76, 175, 80, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 12px 5px rgba(76, 175, 80, 0.3);
  }
}

@keyframes ripple {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }
  100% {
    transform: scale(1.6);
    opacity: 0;
  }
}

@keyframes pulse-warning {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(255, 152, 0, 0.5);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 5px 3px rgba(255, 152, 0, 0.2);
  }
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(255, 152, 0, 0);
  }
}

.guide-view-sidebar-item .channel-tooltip {
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background: linear-gradient(
    135deg,
    rgba(33, 150, 243, 0.95),
    rgba(108, 92, 231, 0.95)
  );
  color: white;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s ease;
  white-space: nowrap;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3), 0 0 10px rgba(33, 150, 243, 0.5);
  z-index: 50;
  margin-left: 10px;
  visibility: hidden;
  min-width: 180px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(8px);
}

.guide-view-sidebar-item:hover .channel-tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateY(-50%) translateX(5px);
}

.channel-tooltip-name {
  font-weight: 700;
  margin-bottom: 8px;
  color: white;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  padding-bottom: 5px;
  font-size: 1rem;
}

.channel-tooltip-status {
  font-weight: 600;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
}

.channel-tooltip-details {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.85);
  font-style: italic;
}

.guide-view-timeline-content {
  flex-grow: 1;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.guide-view-timeline-scroll-container {
  flex-grow: 1;
  position: relative;
  width: 100%;
  height: 100%;
  overflow-x: auto;
  overflow-y: auto;
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: rgba(33, 150, 243, 0.4) rgba(0, 0, 0, 0.1);
  will-change: scroll-position;
  -webkit-overflow-scrolling: touch;
}

.guide-view-content-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}

/* Style the scrollbar for the scroll container */
.guide-view-timeline-scroll-container::-webkit-scrollbar {
  height: 14px;
  width: 14px;
  display: block !important;
  visibility: visible !important;
}

.guide-view-timeline-scroll-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  margin: 0 10px;
}

.guide-view-timeline-scroll-container::-webkit-scrollbar-thumb {
  background: rgba(33, 150, 243, 0.7);
  border-radius: 10px;
  border: 2px solid rgba(0, 0, 0, 0.2);
}

.guide-view-timeline-scroll-container::-webkit-scrollbar-thumb:hover {
  background: rgba(33, 150, 243, 0.9);
}

.guide-view-timeline-header-container {
  position: sticky;
  top: 0;
  z-index: 20;
  width: 100%;
  overflow: visible;
  background: linear-gradient(
    to bottom,
    rgba(42, 42, 42, 0.98),
    rgba(30, 30, 30, 0.98)
  );
}

.guide-view-timeline-header-wrapper {
  overflow-x: hidden;
}

.guide-view-timeline-header-wrapper::-webkit-scrollbar {
  height: 14px; /* Match the height of the grid scrollbar */
  display: block;
}

.guide-view-timeline-header-wrapper::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  margin: 0 10px;
}

.guide-view-timeline-header-wrapper::-webkit-scrollbar-thumb {
  background: rgba(33, 150, 243, 0.7);
  border-radius: 10px;
  border: 2px solid rgba(0, 0, 0, 0.2);
}

.guide-view-timeline-header-wrapper::-webkit-scrollbar-thumb:hover {
  background: rgba(33, 150, 243, 0.9);
}

.guide-view-timeline-grid {
  position: relative;
  will-change: transform;
  flex: 1;
  overflow: visible;
  padding-bottom: 15px;
  width: max-content;
}

.guide-view-timeline-header {
  height: 40px;
  display: flex;
  position: sticky;
  top: 0;
  left: 0;
  z-index: 20;
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;
  will-change: transform;
}

.guide-view-timeline-header-item {
  width: 120px; /* 30 minutes = 120px (4px per minute) */
  height: 100%;
  position: relative;
  flex-shrink: 0;
  animation: fadeInDown 0.5s cubic-bezier(0.22, 1, 0.36, 1) forwards;
  opacity: 0;
  animation-delay: calc(var(--header-index, 0) * 0.05s);
  transition: background-color 0.3s ease;
  background: linear-gradient(
    to bottom,
    rgba(42, 42, 42, 0.98),
    rgba(30, 30, 30, 0.98)
  );
  border-bottom: 1px solid rgba(33, 150, 243, 0.2);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.guide-view-timeline-header-item:hover {
  background: linear-gradient(
    to bottom,
    rgba(45, 45, 45, 0.98),
    rgba(35, 35, 35, 0.98)
  );
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
  border-bottom: 1px solid rgba(33, 150, 243, 0.3);
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hour-marker {
  border-left: 1px solid rgba(33, 150, 243, 0.4);
}

.last-time-slot {
  border-right: 1px solid rgba(33, 150, 243, 0.4);
}

.half-hour-marker-line {
  position: absolute;
  left: 0;
  top: 50%;
  height: 50%;
  border-left: 1px dashed rgba(33, 150, 243, 0.15);
  transition: border-color 0.3s ease;
}

.half-hour-marker:hover .half-hour-marker-line {
  border-left: 1px dashed rgba(33, 150, 243, 0.4);
}

.time-marker {
  position: absolute;
  left: 5px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.85rem;
  color: var(--color-primary);
  white-space: nowrap;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.22, 1, 0.36, 1);
  padding: 2px 6px;
  border-radius: 10px;
}

.hour-marker:hover .time-marker {
  transform: translateY(-50%) scale(1.1);
  color: #fff;
  background: linear-gradient(
    90deg,
    rgba(33, 150, 243, 0.7),
    rgba(108, 92, 231, 0.7)
  );
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.guide-view-channel-row {
  height: 55px;
  position: relative;
  border-bottom: 1px solid var(--color-border-secondary);
}

.guide-view-program-block {
  position: absolute;
  height: 45px;
  top: 5px;
  background-color: var(--color-bg-elevated);
  background-image: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.05) 0%,
    rgba(255, 255, 255, 0.01) 100%
  );
  border-radius: 8px;
  padding: 0.4rem 0.8rem;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.22, 1, 0.36, 1);
  border: 1px solid var(--color-border-primary);
  box-shadow: var(--shadow-sm), 0 0 0 rgba(33, 150, 243, 0);
  animation: fadeInScale 0.7s cubic-bezier(0.22, 1, 0.36, 1) forwards;
  animation-delay: calc(var(--animation-order, 0) * 0.08s);
  opacity: 0;
  transform: scale(0.95) translateY(10px);
  backdrop-filter: blur(5px);
  will-change: transform, box-shadow, border-color;
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.95) translateY(10px);
  }
  60% {
    opacity: 1;
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.guide-view-program-block:hover {
  transform: translateY(-3px) scale(1.03);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15), 0 0 15px rgba(33, 150, 243, 0.3);
  z-index: 20;
  background-color: var(--color-bg-tertiary);
  border-color: var(--color-primary);
}

.guide-view-program-block:active {
  transform: translateY(-1px) scale(1.01);
  transition-duration: 0.1s;
}

.guide-view-program-block::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.08),
    transparent
  );
  transform: translateX(-100%);
  pointer-events: none;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  60% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.program-title {
  font-weight: 600;
  font-size: 0.9rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--color-text-primary);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  line-height: 1.2;
  max-width: 100%;
  position: relative;
  top: 50%;
  transform: translateY(-50%);
  letter-spacing: 0.2px;
}

.guide-view-program-block:hover .program-title {
  color: var(--color-primary);
  letter-spacing: 0.3px;
}

/* Hide time display in blocks to make them thinner */
.program-time {
  display: none;
}

.program-description {
  display: none;
}

.program-progress-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 0 0 8px 8px;
  overflow: hidden;
}

.program-progress {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), #6c5ce7);
  box-shadow: 0 0 8px rgba(33, 150, 243, 0.5);
  position: relative;
  animation: pulse-glow 2s infinite alternate;
}

@keyframes pulse-glow {
  from {
    box-shadow: 0 0 4px rgba(33, 150, 243, 0.3);
  }
  to {
    box-shadow: 0 0 12px rgba(33, 150, 243, 0.7);
  }
}

.program-progress::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 5px;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.7);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  animation: blink 1s infinite;
}

@keyframes blink {
  0%,
  100% {
    opacity: 0.7;
  }
  50% {
    opacity: 0.3;
  }
}

.program-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-5px);
  background-color: var(--color-bg-elevated);
  background-image: linear-gradient(
    135deg,
    rgba(33, 150, 243, 0.08) 0%,
    rgba(108, 92, 231, 0.08) 100%
  );
  border-radius: 12px;
  padding: 1.4rem;
  width: 340px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.25), 0 0 20px rgba(33, 150, 243, 0.15);
  z-index: 30;
  border: 1px solid rgba(33, 150, 243, 0.25);
  margin-bottom: 15px;
  pointer-events: none;
  backdrop-filter: blur(12px);
  animation: tooltipFadeIn 0.3s cubic-bezier(0.22, 1, 0.36, 1);
  opacity: 0;
  animation-fill-mode: forwards;
  will-change: transform, opacity;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(-5px) scale(1);
  }
}

.program-tooltip:after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-width: 10px;
  border-style: solid;
  border-color: rgba(33, 150, 243, 0.25) transparent transparent transparent;
  filter: drop-shadow(0 4px 4px rgba(0, 0, 0, 0.15));
}

.program-tooltip h4 {
  margin: 0 0 0.85rem;
  font-size: 1.25rem;
  color: var(--color-primary);
  border-bottom: 1px solid rgba(33, 150, 243, 0.15);
  padding-bottom: 0.6rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
  line-height: 1.3;
  letter-spacing: 0.3px;
  font-weight: 700;
}

.program-tooltip p {
  margin: 0 0 0.85rem;
  font-size: 0.95rem;
  color: var(--color-text-secondary);
  max-height: 120px;
  overflow-y: auto;
  line-height: 1.6;
  padding-right: 8px;
  scrollbar-width: thin;
  scrollbar-color: rgba(33, 150, 243, 0.4) transparent;
  letter-spacing: 0.2px;
}

.program-tooltip p::-webkit-scrollbar {
  width: 5px;
}

.program-tooltip p::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 5px;
}

.program-tooltip p::-webkit-scrollbar-thumb {
  background-color: rgba(33, 150, 243, 0.4);
  border-radius: 5px;
  transition: background-color 0.3s ease;
}

.program-tooltip p::-webkit-scrollbar-thumb:hover {
  background-color: rgba(33, 150, 243, 0.6);
}

.tooltip-time {
  font-size: 1rem;
  color: var(--color-primary);
  margin-bottom: 0.6rem;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  border-top: 1px solid rgba(33, 150, 243, 0.15);
  padding-top: 0.6rem;
  letter-spacing: 0.2px;
}

.tooltip-time::before {
  content: "🕒";
  font-size: 1rem;
}

.tooltip-duration {
  margin-left: 8px;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.85);
  font-style: italic;
  background-color: rgba(33, 150, 243, 0.15);
  padding: 2px 8px;
  border-radius: 12px;
}

.tooltip-file-info {
  font-size: 0.9rem;
  color: var(--color-text-tertiary);
  background-color: rgba(0, 0, 0, 0.15);
  padding: 0.6rem 0.8rem;
  border-radius: 8px;
  margin-top: 0.6rem;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.3s ease;
  border: 1px solid rgba(33, 150, 243, 0.1);
}

.tooltip-file-info:hover {
  background-color: rgba(0, 0, 0, 0.2);
}

.tooltip-file-info::before {
  content: "📁";
  font-size: 1rem;
}

.guide-view-current-time-indicator {
  position: absolute;
  top: 0;
  height: 100%;
  z-index: 25; /* Increased z-index to ensure it's above other elements */
  pointer-events: none;
  animation: fadeIn 1.2s cubic-bezier(0.22, 1, 0.36, 1) forwards;
  animation-delay: 0.5s;
  opacity: 0;
  filter: drop-shadow(
    0 0 20px rgba(33, 150, 243, 0.8)
  ); /* Increased glow effect */
  will-change: opacity, filter;
}

/* Style for the center fixed time indicator */
.center-fixed-indicator {
  position: fixed !important; /* Force fixed positioning */
  left: 50% !important; /* Position at the center of the view */
  transform: translateX(-50%) !important; /* Ensure perfect centering */
  height: 100% !important; /* Full height */
  z-index: 999 !important; /* Highest z-index to ensure it's above other elements */
  pointer-events: none !important; /* Allow clicking through the indicator */
  width: 40px !important; /* Set a width to make positioning more predictable */
  margin: 0 !important; /* Reset any margin */
  padding: 0 !important; /* Reset any padding */
  top: 0 !important; /* Ensure it starts from the top */
  bottom: 0 !important; /* Ensure it extends to the bottom */
}

.center-fixed-indicator .current-time-line {
  position: absolute;
  top: 40px !important; /* Start below the header */
  bottom: 0 !important;
  left: 50% !important; /* Center the line within the indicator */
  transform: translateX(-50%) !important;
  width: 4px !important; /* Width of the line */
  background: linear-gradient(
    to bottom,
    rgb(33, 150, 243),
    rgba(33, 150, 243, 0.7)
  ) !important;
  box-shadow: 0 0 20px rgba(33, 150, 243, 0.9),
    0 0 10px rgba(255, 255, 255, 0.5) !important; /* Enhanced glow effect */
  border-radius: 2px !important;
  height: calc(100% - 40px) !important; /* Full height minus header */
}

/* Add highlight effect for the center current time area */
.center-fixed-indicator::before {
  content: "";
  position: absolute;
  top: 40px; /* Start below the header */
  left: 50%;
  transform: translateX(-50%);
  width: 60px; /* Increased width of highlight area */
  height: calc(100% - 40px); /* Full height minus header */
  background: linear-gradient(
    to right,
    rgba(33, 150, 243, 0) 0%,
    rgba(33, 150, 243, 0.12) 50%,
    rgba(33, 150, 243, 0) 100%
  );
  pointer-events: none;
  z-index: 5;
}

.current-time-label {
  position: absolute;
  top: 0px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  background: linear-gradient(135deg, #2196f3, #6c5ce7) !important;
  color: white !important;
  padding: 5px 12px !important;
  border-radius: 20px !important;
  font-size: 0.9rem !important;
  font-weight: 700 !important;
  white-space: nowrap !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.25), 0 0 10px rgba(33, 150, 243, 0.5) !important;
  border: 2px solid rgba(255, 255, 255, 0.25) !important;
  letter-spacing: 0.5px !important;
  z-index: 1000 !important;
}

@keyframes pulseScale {
  from {
    transform: translateX(-50%) scale(1);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.25), 0 0 10px rgba(33, 150, 243, 0.5);
  }
  to {
    transform: translateX(-50%) scale(1.1);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.35), 0 0 20px rgba(33, 150, 243, 0.7);
  }
}

.current-time-line {
  position: absolute;
  top: 24px;
  bottom: 0;
  left: 0;
  width: 4px; /* Increased width for better visibility */
  background: linear-gradient(to bottom, #2196f3, rgba(33, 150, 243, 0.5));
  box-shadow: 0 0 20px rgba(33, 150, 243, 0.9); /* Increased glow effect */
  animation: glowLine 2.5s infinite alternate
    cubic-bezier(0.455, 0.03, 0.515, 0.955);
  will-change: box-shadow;
  z-index: 30; /* Increased z-index to ensure it's above other elements */
  border-radius: 2px;
}

/* Add a highlight effect to the current time line */
.center-fixed-indicator .current-time-line::after {
  content: "";
  position: absolute;
  top: 0;
  left: -2px;
  width: 8px;
  height: 100%;
  background: rgba(33, 150, 243, 0.1);
  filter: blur(4px);
  border-radius: 4px;
}

@keyframes glowLine {
  from {
    box-shadow: 0 0 10px rgba(33, 150, 243, 0.6);
  }
  to {
    box-shadow: 0 0 25px rgba(33, 150, 243, 0.9);
  }
}

.current-time-line::before {
  content: "";
  position: absolute;
  top: 0;
  left: -3.5px;
  width: 10px;
  height: 10px;
  background-color: #2196f3;
  border-radius: 50%;
  box-shadow: 0 0 15px rgba(33, 150, 243, 0.8);
  animation: pulseDot 2.5s infinite alternate
    cubic-bezier(0.455, 0.03, 0.515, 0.955);
  will-change: transform, box-shadow;
}

@keyframes pulseDot {
  from {
    transform: scale(1);
    box-shadow: 0 0 10px rgba(33, 150, 243, 0.6);
  }
  to {
    transform: scale(1.4);
    box-shadow: 0 0 25px rgba(33, 150, 243, 0.9);
  }
}

.current-time-pulse {
  position: absolute;
  top: 24px;
  left: -10px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: rgba(33, 150, 243, 0.2);
  animation: pulse 2s infinite;
  z-index: 10;
}

@keyframes pulse {
  0% {
    transform: scale(0.5);
    opacity: 1;
  }
  100% {
    transform: scale(5);
    opacity: 0;
  }
}

/* Program type styling */
.program-file {
  background-color: rgba(33, 150, 243, 0.1);
  border-color: rgba(33, 150, 243, 0.3);
  background-image: linear-gradient(
    135deg,
    rgba(33, 150, 243, 0.1) 0%,
    rgba(33, 150, 243, 0.05) 100%
  );
}

.program-file:hover::after {
  opacity: 0.8;
}

.program-connection {
  background-color: rgba(156, 39, 176, 0.1);
  border-color: rgba(156, 39, 176, 0.3);
  background-image: linear-gradient(
    135deg,
    rgba(156, 39, 176, 0.1) 0%,
    rgba(156, 39, 176, 0.05) 100%
  );
}

.program-connection::after {
  content: "🔌";
  position: absolute;
  top: 8px;
  right: 8px;
  font-size: 0.9rem;
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.program-connection:hover::after {
  opacity: 0.8;
}

.program-filler {
  background-color: rgba(76, 175, 80, 0.1);
  border-color: rgba(76, 175, 80, 0.3);
  background-image: linear-gradient(
    135deg,
    rgba(76, 175, 80, 0.1) 0%,
    rgba(76, 175, 80, 0.05) 100%
  );
}

.program-filler::after {
  content: "🔄";
  position: absolute;
  top: 8px;
  right: 8px;
  font-size: 0.9rem;
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.program-filler:hover::after {
  opacity: 0.8;
}

/* Loading and error states */
.guide-view-timeline-loading,
.guide-view-timeline-error {
  padding: 3rem;
  text-align: center;
  color: var(--color-text-secondary);
  font-size: 1.1rem;
  background-color: var(--color-bg-secondary);
  border-radius: 12px;
  box-shadow: var(--shadow-md);
}

.guide-view-timeline-loading {
  position: relative;
}

.guide-view-timeline-loading:after {
  content: "";
  position: absolute;
  width: 40px;
  height: 40px;
  top: calc(50% + 20px);
  left: 50%;
  margin-left: -20px;
  border-radius: 50%;
  border: 3px solid rgba(33, 150, 243, 0.3);
  border-top-color: var(--color-primary);
  animation: spin 1s infinite linear;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.guide-view-timeline-error {
  color: var(--color-danger);
  border: 1px solid rgba(255, 71, 87, 0.3);
}

/* Return to current time button */
.return-to-now-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: linear-gradient(135deg, #2196f3, #6c5ce7);
  color: white;
  border: none;
  border-radius: 50px;
  padding: 10px 20px;
  font-weight: bold;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3), 0 0 10px rgba(33, 150, 243, 0.5);
  cursor: pointer;
  z-index: 100;
  transition: all 0.3s cubic-bezier(0.22, 1, 0.36, 1);
  animation: pulseButton 2s infinite alternate;
  font-size: 0.9rem;
  letter-spacing: 0.5px;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.return-to-now-button:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4), 0 0 15px rgba(33, 150, 243, 0.7);
}

.return-to-now-button:active {
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3), 0 0 8px rgba(33, 150, 243, 0.4);
}

@keyframes pulseButton {
  from {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3), 0 0 10px rgba(33, 150, 243, 0.5);
  }
  to {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4), 0 0 15px rgba(33, 150, 243, 0.7);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .guide-view-container {
    height: 350px;
  }

  .guide-view-sidebar {
    width: 120px;
  }

  .guide-view-sidebar-item {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .guide-view-container {
    height: 300px;
    flex-direction: column;
  }

  .guide-view-sidebar {
    width: 100%;
    height: auto;
    display: flex;
    overflow-x: auto;
  }

  .guide-view-sidebar-header {
    min-width: 100px;
  }

  .guide-view-sidebar-item {
    height: 40px;
    min-width: 120px;
    border-right: 1px solid var(--color-border-secondary);
    border-bottom: none;
  }
}

/* Program state styling */
.played-program {
  background-color: rgba(120, 120, 120, 0.1) !important;
  border-color: rgba(120, 120, 120, 0.3) !important;
  background-image: linear-gradient(
    135deg,
    rgba(120, 120, 120, 0.1) 0%,
    rgba(120, 120, 120, 0.05) 100%
  ) !important;
  opacity: 0.7;
}

.played-program .program-title {
  color: var(--color-text-secondary);
}

.upcoming-program {
  background-color: var(--color-bg-elevated);
  border-color: var(--color-border-primary);
  background-image: linear-gradient(
    135deg,
    rgba(33, 150, 243, 0.1) 0%,
    rgba(33, 150, 243, 0.05) 100%
  );
}

.currently-playing {
  border-color: var(--color-primary) !important;
  background-color: rgba(226, 33, 243, 0.15) !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1), 0 0 10px rgba(33, 150, 243, 0.2) !important;
  border-width: 2px;
}

@keyframes currentlyPlayingPulse {
  from {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1), 0 0 10px rgba(33, 150, 243, 0.2);
  }
  to {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15), 0 0 15px rgba(33, 150, 243, 0.4);
  }
}

.currently-playing::before {
  content: "▶️ NOW";
  position: absolute;
  top: -10px;
  left: 10px;
  background: linear-gradient(90deg, #2196f3, #6c5ce7);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: bold;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  z-index: 5;
}

/* Program type styling */
.program-file {
  background-color: rgba(33, 150, 243, 0.1);
  border-color: rgba(33, 150, 243, 0.3);
  background-image: linear-gradient(
    135deg,
    rgba(33, 150, 243, 0.1) 0%,
    rgba(33, 150, 243, 0.05) 100%
  );
}

.program-file:hover::after {
  opacity: 0.8;
}
