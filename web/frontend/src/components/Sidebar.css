.sidebar {
  display: flex;
  flex-direction: column;
  width: 250px;
  height: 100vh;
  background-color: #1a1a1a;
  color: white;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow-x: hidden;
  z-index: 100;
  position: relative;
}

.sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0) 100%);
  pointer-events: none;
}

.sidebar-collapsed {
  width: 70px;
}

.sidebar-collapsed .sidebar-nav-link {
  justify-content: center;
  padding: 0.9rem 0;
  position: relative;
}

.sidebar-collapsed .sidebar-nav-item:not(:last-child) .sidebar-nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 20%;
  width: 60%;
  height: 1px;
  background-color: rgba(255, 255, 255, 0.05);
}

.sidebar-collapsed .sidebar-icon {
  margin: 0;
}

.sidebar-collapsed .sidebar-user-logout-inline {
  justify-content: center;
  padding: 0;
  width: 80%;
  margin: 0 auto;
  border-radius: 8px;
  background-color: #121212;
  text-align: center;
  border: 1px solid #333;
  height: auto;
}

.sidebar-collapsed .sidebar-user-logout-inline:hover {
  background-color: #1a1a1a;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  border-color: #2196f3;
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #333;
  background-color: transparent;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 10%;
  width: 80%;
  height: 1px;
  /* background: linear-gradient(90deg,
    rgba(33, 150, 243, 0) 0%,
    rgba(33, 150, 243, 0.3) 50%,
    rgba(33, 150, 243, 0) 100%
  ); */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-collapsed .sidebar-header {
  flex-direction: column;
  padding: 1rem 0;
  gap: 0.5rem;
}

.sidebar-collapsed .sidebar-header::after {
  left: 20%;
  width: 60%;
}

.sidebar-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-logo-image {
  height: 40px;
  width: auto;
}

.sidebar-collapsed .sidebar-logo-image {
  height: 30px;
  width: 30px;
  object-fit: contain;
  animation: pulse 2s infinite alternate;
}

@keyframes pulse {
  0% {
    filter: drop-shadow(0 0 2px rgba(33, 150, 243, 0.3));
  }
  100% {
    filter: drop-shadow(0 0 5px rgba(33, 150, 243, 0.6));
  }
}

.sidebar-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: #aaa;
  cursor: pointer;
  font-size: 1.2rem;
  padding: 0.25rem;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  width: 28px;
  height: 28px;
  position: relative;
  overflow: hidden;
}

.sidebar-toggle::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(33, 150, 243, 0.3) 0%, rgba(33, 150, 243, 0) 70%);
  transform: scale(0);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 50%;
}

.sidebar-toggle:hover {
  color: #2196F3;
}

.sidebar-toggle:hover::before {
  transform: scale(1.5);
}

.sidebar-collapsed .sidebar-toggle {
  margin-left: auto;
  margin-right: auto;
  animation: rotate 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.sidebar-content {
  /* flex: 1; */
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  padding: 0.5rem 0;
  scrollbar-width: thin;
  scrollbar-color: #333 #1a1a1a;
}

.sidebar-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: #1a1a1a;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background-color: #333;
  border-radius: 6px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background-color: #444;
}

.sidebar-nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
  width: 100%;
}

.sidebar-nav-item {
  width: 100%;
}

.sidebar-collapsed .sidebar-nav-item {
  display: flex;
  justify-content: center;
}

.sidebar-collapsed .sidebar-nav-list {
  margin: 0.5rem 0;
}

.sidebar-nav-link {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.9rem 1.5rem;
  color: #ddd;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-left: 3px solid transparent;
  width: 100%;
  position: relative;
  overflow: hidden;
}

.sidebar-nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background-color: #2196F3;
  transform: translateX(-100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
}

.sidebar-nav-link::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(33, 150, 243, 0.1);
  transform: translateX(-100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 0;
}

.sidebar-nav-link:hover {
  color: #2196F3;
}

.sidebar-nav-link:hover::after {
  transform: translateX(0);
}

.sidebar-collapsed .sidebar-nav-link:hover {
  background-color: transparent;
}

.sidebar-collapsed .sidebar-nav-link:hover::after {
  transform: translateX(0);
}

.sidebar-nav-link.active {
  color: #2196F3;
}

.sidebar-nav-link.active::before {
  transform: translateX(0);
}

.sidebar-nav-link.active::after {
  transform: translateX(0);
  background-color: rgba(33, 150, 243, 0.15);
}

.sidebar-collapsed .sidebar-nav-link.active {
  border-left: none;
}

.sidebar-icon, .sidebar-link-text {
  position: relative;
  z-index: 2;
}

.sidebar-icon {
  font-size: 1.4rem;
  min-width: 1.4rem;
}

.sidebar-link-text {
  white-space: nowrap;
  font-size: 1rem;
  display: block;
  text-align: center;
}

.sidebar-footer {
  padding: 1rem;
  border-top: 1px solid #333;
  background-color: rgba(26, 26, 26, 0.9);
  margin-top: auto;
  width: 100%;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  /* box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.15); */
}

.sidebar-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 10%;
  width: 80%;
  height: 1px;
  /* background: linear-gradient(90deg,
    rgba(255, 165, 0, 0) 0%,
    rgba(255, 165, 0, 0.3) 50%,
    rgba(255, 165, 0, 0) 100%
  ); */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-collapsed .sidebar-footer {
  padding: 1rem 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

.sidebar-collapsed .sidebar-footer::before {
  left: 20%;
  width: 60%;
}

.sidebar-user-logout-inline {
  display: flex;
  width: 100%;
  padding: 0;
  background-color: #191919;
  border: 1px solid #333;
  color: white;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 8px;
  position: relative;
  overflow: hidden;
  font-weight: 500;
  text-align: center;
}

.sidebar-user-logout-inline::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
    rgba(100, 108, 255, 0.05) 0%,
    rgba(100, 108, 255, 0) 50%,
    rgba(244, 67, 54, 0.05) 100%
  );
  opacity: 0;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-user-logout-inline:hover {
  background-color: #1a1a1a;
  border-color: #2196f3;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.sidebar-user-logout-inline:hover::before {
  opacity: 1;
}

.sidebar-user-logout-inline:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.sidebar-user-logout-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  width: 100%;
  position: relative;
  text-align: left;
}

.logout-divider {
  display: none;
}

.sidebar-user-logout-collapsed {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 0;
}

.user-icon {
  color: #2196f3;
  position: relative;
  z-index: 1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 1.3rem;
}

.sidebar-collapsed .user-icon {
  animation: pulse-purple 2s infinite alternate;
  font-size: 1.4rem;
}

.logout-icon-small {
  color: #f44336;
  position: relative;
  z-index: 1;
  font-size: 0.9rem;
  position: absolute;
  bottom: -0.25rem;
  right: -0.25rem;
  background-color: #121212;
  border-radius: 50%;
  padding: 0.1rem;
  border: 1px solid #333;
}

@keyframes pulse-purple {
  0% {
    filter: drop-shadow(0 0 2px rgba(100, 108, 255, 0.3));
  }
  100% {
    filter: drop-shadow(0 0 5px rgba(100, 108, 255, 0.6));
  }
}

.user-info-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sidebar-user-name {
  font-weight: 600;
  color: #2196f3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
  z-index: 1;
  font-size: 0.9rem;
  letter-spacing: 0.5px;
  text-align: left;
  max-width: 120px;
}

.text-center {
  text-align: center;
}

.w-full {
  width: 100%;
  max-height: 530px;
}

.logout-icon {
  color: #f44336;
  position: relative;
  z-index: 1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 1.3rem;
}

.sidebar-collapsed .logout-icon {
  animation: pulse-red 2s infinite alternate;
  font-size: 1.4rem;
}

.logout-icon:hover {
  background-color: rgba(244, 67, 54, 0.15);
  border-color: rgba(244, 67, 54, 0.3);
  transform: scale(1.1);
}

.sidebar-logout-text {
  font-weight: 500;
  color: #f44336;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
  z-index: 1;
  font-size: 0.85rem;
  text-align: center;
  display: inline-block;
}

@keyframes pulse-red {
  0% {
    filter: drop-shadow(0 0 2px rgba(244, 67, 54, 0.3));
  }
  100% {
    filter: drop-shadow(0 0 5px rgba(244, 67, 54, 0.6));
  }
}

.mobile-menu-toggle {
  display: none;
  position: fixed;
  top: 70px;
  left: 0;
  background-color: #1a1a1a;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  padding: 0.5rem;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.3);
  z-index: 99;
  cursor: pointer;
  font-size: 1.2rem;
}

.sidebar-backdrop {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 90;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Mobile styles */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 250px;
    height: 100vh;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    z-index: 1000;
  }

  .sidebar:not(.sidebar-collapsed) {
    transform: translateX(0);
  }

  .sidebar-collapsed {
    transform: translateX(-100%);
  }

  .sidebar-backdrop {
    display: block;
  }

  .mobile-menu-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .sidebar-toggle {
    display: none;
  }

  .content {
    margin-left: 0 !important;
  }

  .sidebar-header {
    padding: 1rem;
  }

  .sidebar-logo-image {
    height: 30px;
  }

  .sidebar-content {
    display: block;
  }

  .sidebar-footer {
    display: block;
  }
}
