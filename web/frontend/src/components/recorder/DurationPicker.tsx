import * as React from "react";

export interface DurationPickerProps {
  value: number;
  setValue: (value: number) => void;
  onSubmit: () => void;
}

// Utility functions
function secondsToHms(seconds: number, sep = ':'): string {
  const h = Math.floor(seconds / 3600);
  const m = Math.floor((seconds % 3600) / 60);
  const s = Math.floor((seconds % 3600) % 60);
  return [h, m, s].map(v => v.toString().padStart(2, '0')).join(sep);
}

function hmsToSeconds(hms: string): number {
  const match = hms.match(/(\d\d?):(\d\d?):(\d\d?)/);
  if (!match) {
    return 0;
  }
  const [h, m, s] = match.slice(1);
  return Number(h) * 3600 + Number(m) * 60 + Number(s);
}

const DurationPicker: React.FC<DurationPickerProps> = ({value, setValue, onSubmit}) => {
  // Use refs to track last selected values and prevent circular updates
  const lastSetValueRef = React.useRef<number>(value);
  const isInternalChangeRef = React.useRef<boolean>(false);

  // Initialize with the current value, ensuring 2-digit format
  const [hours, setHours] = React.useState<string>(Math.floor(value / 3600).toString().padStart(2, '0'));
  const [minutes, setMinutes] = React.useState<string>(Math.floor((value % 3600) / 60).toString().padStart(2, '0'));
  const [seconds, setSeconds] = React.useState<string>(Math.floor((value % 3600) % 60).toString().padStart(2, '0'));
  
  const [hoursOpen, setHoursOpen] = React.useState(false);
  const [minutesOpen, setMinutesOpen] = React.useState(false);
  const [secondsOpen, setSecondsOpen] = React.useState(false);
  
  const hoursRef = React.useRef<HTMLDivElement>(null);
  const minutesRef = React.useRef<HTMLDivElement>(null);
  const secondsRef = React.useRef<HTMLDivElement>(null);
  
  // Generate options
  const hoursOptions = Array.from({ length: 24 }, (_, i) => i.toString().padStart(2, '0'));
  const minutesOptions = Array.from({ length: 60 }, (_, i) => i.toString().padStart(2, '0'));
  const secondsOptions = Array.from({ length: 60 }, (_, i) => i.toString().padStart(2, '0'));

  // This effect handles updating the parent when our internal state changes
  React.useEffect(() => {
    // Skip this effect if the change is triggered by the parent value changing
    if (!isInternalChangeRef.current) return;
    
    const totalSeconds = (Number(hours) * 3600) + (Number(minutes) * 60) + Number(seconds);
    
    // Only update if the value is different to avoid loops
    if (totalSeconds !== lastSetValueRef.current) {
      lastSetValueRef.current = totalSeconds;
      setValue(totalSeconds);
    }
    
    // Reset the flag
    isInternalChangeRef.current = false;
  }, [hours, minutes, seconds, setValue]);
  
  // This effect handles updating our internal state when the parent value changes
  React.useEffect(() => {
    // Skip if this is an internal change
    if (isInternalChangeRef.current) return;
    
    // Check if the parent value is different from our last known value
    if (value !== lastSetValueRef.current) {
      lastSetValueRef.current = value;
      
      const h = Math.floor(value / 3600).toString().padStart(2, '0');
      const m = Math.floor((value % 3600) / 60).toString().padStart(2, '0');
      const s = Math.floor((value % 3600) % 60).toString().padStart(2, '0');
      
      setHours(h);
      setMinutes(m);
      setSeconds(s);
    }
  }, [value]);
  
  React.useEffect(() => {
    // Handle clicks outside the dropdowns
    const handleClickOutside = (event: MouseEvent) => {
      if (hoursRef.current && !hoursRef.current.contains(event.target as Node)) {
        setHoursOpen(false);
      }
      if (minutesRef.current && !minutesRef.current.contains(event.target as Node)) {
        setMinutesOpen(false);
      }
      if (secondsRef.current && !secondsRef.current.contains(event.target as Node)) {
        setSecondsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Apply changes and notify parent
  const applyChanges = () => {
    // Set flag to indicate this is an internal change
    isInternalChangeRef.current = true;
    
    // Format all values to ensure they're valid
    const h = parseInt(hours || "0", 10).toString().padStart(2, '0');
    const m = parseInt(minutes || "0", 10).toString().padStart(2, '0');
    const s = parseInt(seconds || "0", 10).toString().padStart(2, '0');
    
    setHours(h);
    setMinutes(m);
    setSeconds(s);
    
    // Calculate the total seconds
    const totalSeconds = (Number(h) * 3600) + (Number(m) * 60) + Number(s);
    
    // Update our ref to track the last value we set
    lastSetValueRef.current = totalSeconds;
    
    // Update parent
    setValue(totalSeconds);
    
    // Call the parent's onSubmit
    onSubmit();
  };

  const validateAndUpdateHours = (value: string) => {
    if (value === "") {
      isInternalChangeRef.current = true;
      setHours("00");
      return;
    }
    
    // Only allow digits
    if (!/^\d+$/.test(value)) {
      return;
    }
    
    const numValue = parseInt(value, 10);
    // Ensure it's within valid range (0-23)
    if (numValue >= 0 && numValue <= 23) {
      isInternalChangeRef.current = true;
      setHours(numValue.toString().padStart(2, '0'));
    }
  };

  const validateAndUpdateMinutes = (value: string) => {
    if (value === "") {
      isInternalChangeRef.current = true;
      setMinutes("00");
      return;
    }
    
    // Only allow digits
    if (!/^\d+$/.test(value)) {
      return;
    }
    
    const numValue = parseInt(value, 10);
    // Ensure it's within valid range (0-59)
    if (numValue >= 0 && numValue <= 59) {
      isInternalChangeRef.current = true;
      setMinutes(numValue.toString().padStart(2, '0'));
    }
  };

  const validateAndUpdateSeconds = (value: string) => {
    if (value === "") {
      isInternalChangeRef.current = true;
      setSeconds("00");
      return;
    }
    
    // Only allow digits
    if (!/^\d+$/.test(value)) {
      return;
    }
    
    const numValue = parseInt(value, 10);
    // Ensure it's within valid range (0-59)
    if (numValue >= 0 && numValue <= 59) {
      isInternalChangeRef.current = true;
      setSeconds(numValue.toString().padStart(2, '0'));
    }
  };

  const handleHoursChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    validateAndUpdateHours(e.target.value);
  };

  const handleMinutesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    validateAndUpdateMinutes(e.target.value);
  };

  const handleSecondsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    validateAndUpdateSeconds(e.target.value);
  };

  const handleBlur = () => {
    applyChanges();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      applyChanges();
    }
  };

  const handleSelectHours = (selectedValue: string) => {
    // Set flag to indicate this is an internal change
    isInternalChangeRef.current = true;
    
    // Update state directly
    setHours(selectedValue);
    
    // Close dropdown
    setHoursOpen(false);
    
    // Apply changes synchronously to prevent reverting
    const m = parseInt(minutes || "0", 10).toString().padStart(2, '0');
    const s = parseInt(seconds || "0", 10).toString().padStart(2, '0');
    
    // Calculate total seconds
    const totalSeconds = (Number(selectedValue) * 3600) + (Number(m) * 60) + Number(s);
    
    // Update ref to track last value
    lastSetValueRef.current = totalSeconds;
    
    // Update parent immediately
    setValue(totalSeconds);
    onSubmit();
  };

  const handleSelectMinutes = (selectedValue: string) => {
    // Set flag to indicate this is an internal change
    isInternalChangeRef.current = true;
    
    // Update state directly
    setMinutes(selectedValue);
    
    // Close dropdown
    setMinutesOpen(false);
    
    // Apply changes synchronously to prevent reverting
    const h = parseInt(hours || "0", 10).toString().padStart(2, '0');
    const s = parseInt(seconds || "0", 10).toString().padStart(2, '0');
    
    // Calculate total seconds
    const totalSeconds = (Number(h) * 3600) + (Number(selectedValue) * 60) + Number(s);
    
    // Update ref to track last value
    lastSetValueRef.current = totalSeconds;
    
    // Update parent immediately
    setValue(totalSeconds);
    onSubmit();
  };

  const handleSelectSeconds = (selectedValue: string) => {
    // Set flag to indicate this is an internal change
    isInternalChangeRef.current = true;
    
    // Update state directly
    setSeconds(selectedValue);
    
    // Close dropdown
    setSecondsOpen(false);
    
    // Apply changes synchronously to prevent reverting
    const h = parseInt(hours || "0", 10).toString().padStart(2, '0');
    const m = parseInt(minutes || "0", 10).toString().padStart(2, '0');
    
    // Calculate total seconds
    const totalSeconds = (Number(h) * 3600) + (Number(m) * 60) + Number(selectedValue);
    
    // Update ref to track last value
    lastSetValueRef.current = totalSeconds;
    
    // Update parent immediately
    setValue(totalSeconds);
    onSubmit();
  };

  const toggleHoursDropdown = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent event bubbling
    setHoursOpen(!hoursOpen);
    setMinutesOpen(false);
    setSecondsOpen(false);
  };

  const toggleMinutesDropdown = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent event bubbling
    setMinutesOpen(!minutesOpen);
    setHoursOpen(false);
    setSecondsOpen(false);
  };

  const toggleSecondsDropdown = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent event bubbling
    setSecondsOpen(!secondsOpen);
    setHoursOpen(false);
    setMinutesOpen(false);
  };

  return (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center',
      justifyContent: 'center',
      gap: '8px',
      width: '100%',
      padding: '2px 0',
    }}>
      {/* Hours */}
      <div 
        ref={hoursRef}
        style={{ 
          position: 'relative',
          width: '60px',
        }}
      >
        <div 
          style={{
            position: 'relative',
            width: '100%',
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <input
            type="text"
            value={hours}
            onChange={handleHoursChange}
            onBlur={handleBlur}
            onKeyDown={handleKeyDown}
            onClick={toggleHoursDropdown}
            style={{
              width: '100%',
              padding: '6px 20px 6px 6px',
              backgroundColor: 'rgba(40, 40, 40, 0.8)',
              color: '#e0e0e0',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              borderRadius: '6px',
              textAlign: 'center',
              fontSize: '14px',
              outline: 'none',
              cursor: 'pointer',
            }}
          />
          <button 
            type="button"
            onClick={toggleHoursDropdown}
            style={{
              position: 'absolute',
              right: '2px',
              padding: '0',
              backgroundColor: 'transparent',
              border: 'none',
              color: '#9e9e9e',
              fontSize: '12px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: '16px',
              height: '16px',
            }}
          >
            ▼
          </button>
        </div>
        
        {hoursOpen && (
          <div
            style={{
              position: 'absolute',
              top: '100%',
              left: '0',
              width: '100%',
              maxHeight: '200px',
              overflowY: 'auto',
              backgroundColor: '#2a2b31',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              borderRadius: '6px',
              zIndex: 10,
              marginTop: '4px',
              scrollbarWidth: 'thin',
              scrollbarColor: 'rgba(255, 255, 255, 0.1) transparent',
            }}
          >
            {hoursOptions.map((hour) => (
              <div
                key={hour}
                onClick={() => handleSelectHours(hour)}
                style={{
                  padding: '4px 8px',
                  color: '#e0e0e0',
                  cursor: 'pointer',
                  textAlign: 'center',
                  backgroundColor: hour === hours ? 'rgba(32, 126, 200, 0.2)' : 'transparent',
                  fontSize: '14px',
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgba(32, 126, 200, 0.1)';
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.backgroundColor = hour === hours ? 'rgba(32, 126, 200, 0.2)' : 'transparent';
                }}
              >
                {hour}
              </div>
            ))}
          </div>
        )}
      </div>
      
      <div style={{ color: '#9e9e9e', fontWeight: 'bold', fontSize: '18px' }}>:</div>
      
      {/* Minutes */}
      <div 
        ref={minutesRef}
        style={{ 
          position: 'relative',
          width: '60px',
        }}
      >
        <div 
          style={{
            position: 'relative',
            width: '100%',
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <input
            type="text"
            value={minutes}
            onChange={handleMinutesChange}
            onBlur={handleBlur}
            onKeyDown={handleKeyDown}
            onClick={toggleMinutesDropdown}
            style={{
              width: '100%',
              padding: '6px 20px 6px 6px',
              backgroundColor: 'rgba(40, 40, 40, 0.8)',
              color: '#e0e0e0',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              borderRadius: '6px',
              textAlign: 'center',
              fontSize: '14px',
              outline: 'none',
              cursor: 'pointer',
            }}
          />
          <button 
            type="button"
            onClick={toggleMinutesDropdown}
            style={{
              position: 'absolute',
              right: '2px',
              padding: '0',
              backgroundColor: 'transparent',
              border: 'none',
              color: '#9e9e9e',
              fontSize: '12px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: '16px',
              height: '16px',
            }}
          >
            ▼
          </button>
        </div>
        
        {minutesOpen && (
          <div
            style={{
              position: 'absolute',
              top: '100%',
              left: '0',
              width: '100%',
              maxHeight: '200px',
              overflowY: 'auto',
              backgroundColor: '#2a2b31',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              borderRadius: '6px',
              zIndex: 10,
              marginTop: '4px',
              scrollbarWidth: 'thin',
              scrollbarColor: 'rgba(255, 255, 255, 0.1) transparent',
            }}
          >
            {minutesOptions.map((minute) => (
              <div
                key={minute}
                onClick={() => handleSelectMinutes(minute)}
                style={{
                  padding: '4px 8px',
                  color: '#e0e0e0',
                  cursor: 'pointer',
                  textAlign: 'center',
                  backgroundColor: minute === minutes ? 'rgba(32, 126, 200, 0.2)' : 'transparent',
                  fontSize: '14px',
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgba(32, 126, 200, 0.1)';
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.backgroundColor = minute === minutes ? 'rgba(32, 126, 200, 0.2)' : 'transparent';
                }}
              >
                {minute}
              </div>
            ))}
          </div>
        )}
      </div>
      
      <div style={{ color: '#9e9e9e', fontWeight: 'bold', fontSize: '18px' }}>:</div>
      
      {/* Seconds */}
      <div 
        ref={secondsRef}
        style={{ 
          position: 'relative',
          width: '60px',
        }}
      >
        <div 
          style={{
            position: 'relative',
            width: '100%',
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <input
            type="text"
            value={seconds}
            onChange={handleSecondsChange}
            onBlur={handleBlur}
            onKeyDown={handleKeyDown}
            onClick={toggleSecondsDropdown}
            style={{
              width: '100%',
              padding: '6px 20px 6px 6px',
              backgroundColor: 'rgba(40, 40, 40, 0.8)',
              color: '#e0e0e0',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              borderRadius: '6px',
              textAlign: 'center',
              fontSize: '14px',
              outline: 'none',
              cursor: 'pointer',
            }}
          />
          <button 
            type="button"
            onClick={toggleSecondsDropdown}
            style={{
              position: 'absolute',
              right: '2px',
              padding: '0',
              backgroundColor: 'transparent',
              border: 'none',
              color: '#9e9e9e',
              fontSize: '12px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: '16px',
              height: '16px',
            }}
          >
            ▼
          </button>
        </div>
        
        {secondsOpen && (
          <div
            style={{
              position: 'absolute',
              top: '100%',
              left: '0',
              width: '100%',
              maxHeight: '200px',
              overflowY: 'auto',
              backgroundColor: '#2a2b31',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              borderRadius: '6px',
              zIndex: 10,
              marginTop: '4px',
              scrollbarWidth: 'thin',
              scrollbarColor: 'rgba(255, 255, 255, 0.1) transparent',
            }}
          >
            {secondsOptions.map((second) => (
              <div
                key={second}
                onClick={() => handleSelectSeconds(second)}
                style={{
                  padding: '4px 8px',
                  color: '#e0e0e0',
                  cursor: 'pointer',
                  textAlign: 'center',
                  backgroundColor: second === seconds ? 'rgba(32, 126, 200, 0.2)' : 'transparent',
                  fontSize: '14px',
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgba(32, 126, 200, 0.1)';
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.backgroundColor = second === seconds ? 'rgba(32, 126, 200, 0.2)' : 'transparent';
                }}
              >
                {second}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

// Also export the utility functions for use elsewhere
export { secondsToHms, hmsToSeconds };

export default DurationPicker;