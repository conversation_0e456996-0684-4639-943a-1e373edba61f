import { useState, useEffect } from "react";
import {
  getRecorderServices,
  startRecorder,
  getRtpSenders,
} from "../../api/recorderApi";
import { ServiceInfo, RtpSender, Recorder } from "../../types/recorder";
import { toast } from "react-toastify";
import {
  HiInformationCircle,
  HiCollection, // Used for Available Services section header
  HiPlay,
  HiX,
  HiOutlineStatusOnline,
  HiServer,
  HiGlobeAlt,
  HiRefresh,
} from "react-icons/hi";
import "./SelectServiceModal.css";

interface SelectServiceModalProps {
  recorder: Recorder;
  onClose: () => void;
  onSuccess: () => void;
}

const SelectServiceModal = ({
  recorder,
  onClose,
  onSuccess,
}: SelectServiceModalProps) => {
  const [services, setServices] = useState<ServiceInfo[]>([]);
  const [selectedServiceId, setSelectedServiceId] = useState<number | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [tsSync, setTsSync] = useState(false);
  const [rtpSenders, setRtpSenders] = useState<RtpSender[]>([]);
  const [selectedSender, setSelectedSender] = useState<string | null>(null);
  const [loadingRtpSenders, setLoadingRtpSenders] = useState(false);

  // Function to parse multicast address and port from RTP URL
  const parseRtpUrl = (
    url: string
  ): { address: string; port: number } | null => {
    try {
      // Remove rtp:// prefix
      const urlWithoutPrefix = url.replace(/^rtp:\/\//, "");

      // Extract address and port part (before any query parameters)
      const addressPart = urlWithoutPrefix.split("?")[0];

      // Split address and port
      const lastColonIndex = addressPart.lastIndexOf(":");
      if (lastColonIndex === -1) return null;

      const address = addressPart.substring(0, lastColonIndex);
      const port = parseInt(addressPart.substring(lastColonIndex + 1), 10);

      if (isNaN(port)) return null;

      return { address, port };
    } catch (error) {
      console.error("Failed to parse RTP URL:", error);
      return null;
    }
  };

  // Function to fetch RTP senders
  const fetchRtpSenders = async () => {
    if (!recorder?.input) {
      console.error("No recorder input available");
      return;
    }

    const urlInfo = parseRtpUrl(recorder.input);
    if (!urlInfo) {
      console.error("Could not parse RTP URL:", recorder.input);
      return;
    }

    try {
      setLoadingRtpSenders(true);
      // Pass the recorder's network interface if available
      const result = await getRtpSenders(
        urlInfo.address,
        urlInfo.port,
        recorder.network_interface
      );
      setRtpSenders(result.senders || []);
    } catch (error) {
      console.error("Failed to fetch RTP senders:", error);
      // Don't show toast error here as it might be expected if no senders exist
      setRtpSenders([]);
    } finally {
      setLoadingRtpSenders(false);
    }
  };

  useEffect(() => {
    const fetchServices = async () => {
      if (!recorder?.id) {
        setError("Invalid recorder ID");
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        const result = await getRecorderServices(recorder.id);
        console.log("API Response:", result); // Debug log

        setServices(result.services || []);
        setTsSync(result.ts_sync || false);

        // Auto-select the first service if available
        if (result.services && result.services.length > 0) {
          setSelectedServiceId(result.services[0].service_id);
        }

        setIsLoading(false);
      } catch (error) {
        console.error("Failed to fetch services:", error);
        setError("Failed to fetch services. Please try again.");
        setIsLoading(false);
      }
    };

    fetchServices();
  }, [recorder?.id]);

  // Fetch RTP senders when the modal opens
  useEffect(() => {
    // Only try to fetch if we have a valid recorder input
    if (recorder?.input) {
      try {
        fetchRtpSenders();
      } catch (error) {
        console.error("Error in fetchRtpSenders useEffect:", error);
      }
    }
  }, [recorder?.input]);

  // Auto-select first RTP sender when rtpSenders are loaded
  useEffect(() => {
    if (rtpSenders.length > 0 && !selectedSender) {
      setSelectedSender(rtpSenders[0].ip_address);
    }
  }, [rtpSenders, selectedSender]);

  const handleServiceSelect = (serviceId: number) => {
    setSelectedServiceId(serviceId);
  };

  const handleRecordSelected = async () => {
    if (selectedServiceId === null) {
      toast.error("Please select a service first");
      return;
    }

    try {
      setIsSubmitting(true);

      // Start the recorder with the selected service and optional source IP
      await startRecorder(
        recorder.id,
        selectedServiceId,
        selectedSender || undefined
      );

      // Update the UI to show success
      toast.success("Recording started successfully");

      // Call onSuccess to close the modal and update the parent component
      // This will trigger fetchRecorderStatus in the parent component
      onSuccess();
    } catch (error) {
      console.error("Failed to start recording:", error);
      toast.error("Failed to start recording");
      setIsSubmitting(false);
    }
  };

  // Safety check - don't render if recorder is invalid
  if (!recorder || !recorder.input) {
    return (
      <div className="modal-overlay">
        <div className="modal-content service-modal">
          <div className="modal-header">
            <h2>
              <HiServer className="section-icon" /> Select Service
            </h2>
            <button
              className="close-button"
              onClick={onClose}
              aria-label="Close"
            >
              <HiX />
            </button>
          </div>
          <div className="modal-body">
            <div className="error-message">
              <HiInformationCircle className="error-icon" />
              <p>Invalid recorder data. Please try again.</p>
              <button className="retry-button" onClick={onClose}>
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Wrap the entire component in error handling
  try {
    return (
      <div className="modal-overlay">
        <div className="modal-content service-modal">
          <div className="modal-header">
            <h2>
              <HiServer className="section-icon" /> Select Service
            </h2>
            <button
              className="close-button"
              onClick={onClose}
              aria-label="Close"
            >
              <HiX />
            </button>
          </div>

          <div className="modal-body">
            {isLoading ? (
              <div className="loading">
                <div className="loading-spinner"></div>
                <p>Detecting services...</p>
                <span className="loading-subtext">Analyzing stream data</span>
              </div>
            ) : error ? (
              <div className="error-message">
                <HiInformationCircle className="error-icon" />
                <p>{error}</p>
                <button
                  className="retry-button"
                  onClick={() => window.location.reload()}
                >
                  Retry
                </button>
              </div>
            ) : !tsSync ? (
              <div className="error-message">
                <HiOutlineStatusOnline className="error-icon pulse-icon" />
                <p>Waiting for stream synchronization</p>
                <div className="sync-status-indicator">
                  <div className="sync-pulse"></div>
                  <span>Stream not yet stable</span>
                </div>
              </div>
            ) : (
              <div className="service-selection-container">
                {/* RTP Senders Section */}
                <div className="rtp-senders-section">
                  <div className="rtp-senders-header">
                    <h3>
                      <HiGlobeAlt className="section-icon" /> Source IP
                      Addresses
                    </h3>
                    <button
                      className="refresh-senders-button"
                      onClick={fetchRtpSenders}
                      disabled={loadingRtpSenders}
                      title="Refresh RTP senders"
                    >
                      <HiRefresh
                        className={`refresh-icon ${
                          loadingRtpSenders ? "spinning" : ""
                        }`}
                      />
                      <span>Refresh</span>
                    </button>
                  </div>

                  {loadingRtpSenders ? (
                    <div className="loading-senders">
                      <div className="loading-spinner-small"></div>
                      <span>Detecting RTP senders...</span>
                    </div>
                  ) : rtpSenders.length === 0 ? (
                    <div className="no-senders">
                      <HiInformationCircle className="info-icon" />
                      <p>No RTP senders detected on this multicast address</p>
                      <span className="info-subtext">
                        Click refresh to scan for active senders
                      </span>
                    </div>
                  ) : (
                    <div className="senders-list">
                      {rtpSenders.map((sender) => (
                        <div
                          key={sender.ip_address}
                          className={`sender-item ${
                            selectedSender === sender.ip_address
                              ? "selected"
                              : ""
                          }`}
                          onClick={() => setSelectedSender(sender.ip_address)}
                        >
                          <div className="sender-ip">
                            <HiServer className="sender-icon" />
                            <span className="ip-address">
                              {sender.ip_address}
                            </span>
                          </div>
                          <div className="sender-stats">
                            <span className="packet-count">
                              {sender.packet_count} packets
                            </span>
                            <span className="last-seen">
                              Last seen:{" "}
                              {new Date(sender.last_seen).toLocaleTimeString()}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {selectedSender && (
                    <div className="sender-confirmation">
                      <HiGlobeAlt className="confirmation-icon" />
                      <span>
                        Selected source: <strong>{selectedSender}</strong>
                      </span>
                    </div>
                  )}
                </div>

                {/* Service Selection */}
                <div className="service-selection">
                  <div className="service-selection-header">
                    <h3>
                      <HiCollection className="section-icon" /> Select Service
                    </h3>
                    <div
                      className={`sync-status ${
                        tsSync ? "synced" : "not-synced"
                      }`}
                    >
                      <HiOutlineStatusOnline className="status-icon" />
                      <span>{tsSync ? "TS Synced" : "TS Not Synced"}</span>
                    </div>
                  </div>

                  {services.length === 0 ? (
                    <div className="no-services">
                      <HiInformationCircle className="info-icon" />
                      <p>No services detected</p>
                    </div>
                  ) : (
                    <div className="service-table-container">
                      <table className="service-table">
                        <thead>
                          <tr>
                            <th className="service-id-column">ID</th>
                            <th className="service-video-column">Video</th>
                            <th className="service-audio-column">AUDIO 1</th>
                            <th className="service-audio-column">AUDIO 2</th>
                          </tr>
                        </thead>
                        <tbody>
                          {services.map((service) => (
                            <tr
                              key={service.service_id}
                              className={`service-row ${
                                selectedServiceId === service.service_id
                                  ? "selected"
                                  : ""
                              }`}
                              onClick={() =>
                                handleServiceSelect(service.service_id)
                              }
                            >
                              <td className="service-id-cell">
                                {service.service_id}
                              </td>
                              <td className="service-video-cell">
                                {service.video_codec &&
                                service.video_resolution ? (
                                  <div className="video-info">
                                    <div className="codec">
                                      {service.video_codec}
                                    </div>
                                    <div className="resolution">
                                      {service.video_resolution}
                                    </div>
                                  </div>
                                ) : (
                                  <span className="no-info">-</span>
                                )}
                              </td>
                              <td className="service-audio-cell">
                                {service.audio_tracks &&
                                service.audio_tracks.length > 0 ? (
                                  <div className="audio-info">
                                    <div className="codec">
                                      {service.audio_tracks[0].codec}
                                    </div>
                                    <div className="channels">
                                      {service.audio_tracks[0].channels}ch
                                    </div>
                                  </div>
                                ) : service.audio_codec ? (
                                  <div className="audio-info">
                                    <div className="codec">
                                      {service.audio_codec}
                                    </div>
                                    {service.audio_channels && (
                                      <div className="channels">
                                        {service.audio_channels}ch
                                      </div>
                                    )}
                                  </div>
                                ) : (
                                  <span className="no-info">-</span>
                                )}
                              </td>
                              <td className="service-audio-cell">
                                {service.audio_tracks &&
                                service.audio_tracks.length > 1 ? (
                                  <div className="audio-info">
                                    <div className="codec">
                                      {service.audio_tracks[1].codec}
                                    </div>
                                    <div className="channels">
                                      {service.audio_tracks[1].channels}ch
                                    </div>
                                  </div>
                                ) : (
                                  <span className="no-info">NONE</span>
                                )}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}

                  {selectedServiceId && (
                    <div className="selection-confirmation">
                      <HiServer className="confirmation-icon" />
                      <span>
                        Selected:{" "}
                        <strong>
                          {services.find(
                            (s) => s.service_id === selectedServiceId
                          )?.service_name || `Service ${selectedServiceId}`}
                        </strong>
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          <div className="modal-footer">
            <div className="modal-footer-right">
              <button
                className="cancel-button"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Cancel
              </button>

              <button
                className="record-selected-button"
                onClick={handleRecordSelected}
                disabled={
                  isSubmitting ||
                  !tsSync ||
                  selectedServiceId === null ||
                  services.length === 0
                }
              >
                {isSubmitting ? (
                  <>
                    <div className="loading-spinner-small"></div>
                    <span>Processing...</span>
                  </>
                ) : (
                  <>
                    <HiPlay className="button-icon" />
                    <span>Record</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error("Error rendering SelectServiceModal:", error);
    return (
      <div className="modal-overlay">
        <div className="modal-content service-modal">
          <div className="modal-header">
            <h2>
              <HiServer className="section-icon" /> Select Service
            </h2>
            <button
              className="close-button"
              onClick={onClose}
              aria-label="Close"
            >
              <HiX />
            </button>
          </div>
          <div className="modal-body">
            <div className="error-message">
              <HiInformationCircle className="error-icon" />
              <p>An unexpected error occurred while loading the modal.</p>
              <button className="retry-button" onClick={onClose}>
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }
};

export default SelectServiceModal;
