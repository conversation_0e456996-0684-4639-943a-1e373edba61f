.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: #121212;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 85vh;
  overflow-y: auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  border: 1px solid #333;
  animation: modalFadeIn 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  display: flex;
  flex-direction: column;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1.25rem;
  border-bottom: 1px solid #333;
  background-color: #151515;
}

.modal-header h2 {
  margin: 0;
  color: #2196f3;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.close-button {
  background: none;
  border: none;
  color: #888;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0;
  line-height: 1;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.close-button:hover {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.1);
}

form {
  padding: 1.25rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.form-group {
  margin-bottom: 1.25rem;
}

.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-row .form-group {
  flex: 1;
  margin-bottom: 0;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
  font-size: 0.9rem;
}

input,
select {
  width: 100%;
  padding: 0.6rem 0.75rem;
  border: 1px solid #333;
  border-radius: 6px;
  background-color: #1e1e1e;
  color: #fff;
  font-size: 0.95rem;
  transition: all 0.2s ease;
}

input:focus,
select:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

/* Network interface select styling */
.network-select-container {
  position: relative;
  overflow: hidden;
}

.network-select-container {
  position: relative;
  overflow: visible;
}

/* Custom Network Interface Dropdown */
.network-display {
  width: 100%;
  padding: 0.75rem 3rem 0.75rem 1rem;
  background-color: rgba(40, 40, 40, 0.8);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  user-select: none;
}

.network-display:hover {
  background-color: #1e1e1e;
  border-color: rgba(33, 150, 243, 0.3);
}

.network-display:focus {
  outline: none;
  border-color: #ffa500;
  box-shadow: 0 0 0 3px rgba(255, 165, 0, 0.2),
    inset 0 1px 3px rgba(0, 0, 0, 0.2);
  background-color: rgba(50, 50, 50, 0.8);
}

.network-display-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.network-display-icon {
  position: absolute;
  right: 1rem;
  color: #2196f3;
  font-size: 1.25rem;
  opacity: 0.8;
  transition: all 0.2s ease;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.network-display:hover .network-display-icon {
  opacity: 1;
  transform: scale(1.1);
}

.network-dropdown {
  margin-top: 1px;
  max-height: 180px; /* Match the dropdown menu height */
}

/* For backwards compatibility */
.network-select {
  width: 100%;
  padding: 0.75rem 3rem 0.75rem 1rem;
  background-color: #1a1a1a;
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  font-size: 0.95rem;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), inset 0 1px 2px rgba(0, 0, 0, 0.2);
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%232196f3' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1rem;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: auto;
  padding-top: 1.25rem;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  position: relative;
}

.form-actions::before {
  content: "";
  position: absolute;
  top: -2px;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(33, 150, 243, 0.2),
    transparent
  );
}

.cancel-button {
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  padding: 0.7rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
  font-size: 0.9rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.cancel-button:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.submit-button {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  color: white;
  border: none;
  padding: 0.7rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 600;
  font-size: 0.9rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(33, 150, 243, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 120px;
  position: relative;
  overflow: hidden;
}

.submit-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: all 0.6s;
}

.submit-button:hover {
  background: linear-gradient(135deg, #1976d2 0%, #0d47a1 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4),
    0 0 0 1px rgba(33, 150, 243, 0.5);
}

.submit-button:hover::before {
  left: 100%;
}

.submit-button:active {
  transform: translateY(1px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(33, 150, 243, 0.5);
}

.submit-button:disabled {
  background: linear-gradient(135deg, #3a3a3a 0%, #2a2a2a 100%);
  color: rgba(255, 255, 255, 0.4);
  border: none;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

/* Modern URL input with dropdown */
.input-with-dropdown {
  position: relative;
}

/* Custom URL input field */
.url-input-container {
  position: relative;
  overflow: hidden;
}

.url-input-container {
  position: relative;
  overflow: visible;
}

.url-input {
  width: 100%;
  padding: 0.75rem 3rem 0.75rem 1rem;
  background-color: #1a1a1a;
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  font-size: 0.95rem;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), inset 0 1px 2px rgba(0, 0, 0, 0.2);
}

.url-input:focus {
  border-color: rgba(33, 150, 243, 0.5);
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2),
    inset 0 1px 2px rgba(0, 0, 0, 0.1);
  background-color: #1e1e1e;
}

.url-input.error {
  border-color: rgba(255, 77, 79, 0.5);
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2),
    inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.url-input-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #2196f3;
  font-size: 1.25rem;
  pointer-events: none;
  opacity: 0.8;
  transition: all 0.2s ease;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.url-input:focus + .url-input-icon {
  color: #1976d2;
  opacity: 1;
  transform: translateY(-50%) scale(1.1);
}

/* Modern Dropdown Menu Styling */
.dropdown-menu {
  position: absolute;
  top: calc(100% + 8px);
  left: 0;
  right: 0;
  background-color: #1a1a1a;
  border-radius: 12px;
  max-height: 180px; /* Reduced height */
  overflow-y: auto;
  z-index: 20;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  animation: dropdownSlideIn 0.25s cubic-bezier(0.25, 1, 0.5, 1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  scrollbar-width: thin;
  scrollbar-color: #2196f3 #1a1a1a;
  padding: 6px;
}

@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.98);
    transform-origin: top center;
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    transform-origin: top center;
  }
}

.dropdown-menu::before {
  content: "";
  position: absolute;
  top: -6px;
  left: 20px;
  width: 12px;
  height: 12px;
  background-color: #1a1a1a;
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  transform: rotate(45deg);
  z-index: -1;
}

.dropdown-menu::-webkit-scrollbar {
  width: 4px;
}

.dropdown-menu::-webkit-scrollbar-track {
  background: transparent;
  margin: 6px;
}

.dropdown-menu::-webkit-scrollbar-thumb {
  background-color: rgba(33, 150, 243, 0.5);
  border-radius: 10px;
}

.dropdown-menu::-webkit-scrollbar-thumb:hover {
  background-color: rgba(33, 150, 243, 0.8);
}

.dropdown-header {
  padding: 8px 12px;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  margin-bottom: 6px;
}

.dropdown-items-container {
  display: flex;
  flex-direction: column;
  gap: 2px;
  max-height: 140px; /* Limit height */
  overflow-y: auto; /* Add scrollbar when needed */
}

.dropdown-item {
  padding: 10px 12px;
  cursor: pointer;
  transition: all 0.15s ease;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  color: rgba(255, 255, 255, 0.85);
  border-radius: 8px;
  position: relative;
  overflow: hidden;
}

.dropdown-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  color: #2196f3;
  font-size: 1rem;
  opacity: 0.9;
  transition: all 0.15s ease;
}

.dropdown-item-text {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dropdown-item:hover {
  background-color: rgba(33, 150, 243, 0.15);
  color: #fff;
}

.dropdown-item:hover .dropdown-item-icon {
  opacity: 1;
  transform: scale(1.1);
}

.dropdown-item:active {
  background-color: rgba(100, 108, 255, 0.25);
  transform: scale(0.98);
}

.dropdown-item::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at center,
    rgba(100, 108, 255, 0.4) 0%,
    transparent 70%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.dropdown-item:active::after {
  opacity: 1;
}

.advanced-settings-toggle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.75rem;
  margin: 0.75rem 0;
  cursor: pointer;
  transition: all 0.2s;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.85rem;
  border: 1px solid #333;
  border-radius: 6px;
  background-color: #1e1e1e;
  width: fit-content;
}

.advanced-settings-toggle:hover {
  color: #fff;
  background-color: #2a2a2a;
  border-color: #444;
}

.toggle-icon {
  font-size: 0.7rem;
  margin-left: 0.75rem;
  color: #2196f3;
}

.advanced-settings {
  background-color: #151515;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
  margin-bottom: 1rem;
  border: 1px solid #222;
}

/* Duration input styling */
.duration-input-container {
  position: relative;
  overflow: hidden;
}

.duration-input-container {
  position: relative;
  overflow: visible;
}

.duration-input {
  width: 100%;
  padding: 0.75rem 3rem 0.75rem 1rem;
  background-color: #1a1a1a;
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  font-size: 0.95rem;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), inset 0 1px 2px rgba(0, 0, 0, 0.2);
  font-family: "Courier New", monospace;
  letter-spacing: 1px;
}

.duration-input:focus {
  border-color: rgba(33, 150, 243, 0.5);
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2),
    inset 0 1px 2px rgba(0, 0, 0, 0.1);
  background-color: #1e1e1e;
}

.duration-input.error {
  border-color: rgba(255, 77, 79, 0.5);
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2),
    inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.duration-input-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #2196f3;
  font-size: 1.25rem;
  pointer-events: none;
  opacity: 0.8;
  transition: all 0.2s ease;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.duration-input:focus + .duration-input-icon {
  color: #1976d2;
  opacity: 1;
  transform: translateY(-50%) scale(1.1);
}

/* Error message styling */
.error-message {
  color: #ff4d4f;
  font-size: 0.8rem;
  margin-top: 0.75rem;
  padding: 0.5rem 0.75rem;
  background-color: rgba(255, 77, 79, 0.08);
  border-radius: 8px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  animation: errorPulse 2s infinite;
  border: 1px solid rgba(255, 77, 79, 0.2);
}

@keyframes errorPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(255, 77, 79, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0);
  }
}

.error-message::before {
  content: "⚠️";
  margin-right: 0.75rem;
  font-size: 1rem;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.help-text {
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.8rem;
  margin-top: 0.75rem;
  font-style: italic;
  line-height: 1.4;
  padding: 0.5rem 0.75rem;
  background-color: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.help-text-icon {
  color: #2196f3;
  opacity: 0.7;
  font-size: 1rem;
  margin-top: 0.1rem;
  flex-shrink: 0;
}

/* Network option styling */
.network-option {
  padding: 8px 12px;
  background-color: #1a1a1a;
  color: #fff;
}

.network-option:hover {
  background-color: #2a2a2a;
}

/* Custom select styling for Firefox */
@-moz-document url-prefix() {
  .network-select {
    color: #fff;
    background-color: #1a1a1a;
    border-radius: 10px;
  }
}

/* Loading spinner */
.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Name input styling */
.name-input-container {
  position: relative;
  overflow: hidden;
}

.name-input-container {
  position: relative;
  overflow: visible;
}

.name-input {
  width: 100%;
  padding: 0.75rem 3rem 0.75rem 1rem;
  background-color: #1a1a1a;
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  font-size: 0.95rem;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2), inset 0 1px 2px rgba(0, 0, 0, 0.2);
}

.name-input:focus {
  border-color: rgba(33, 150, 243, 0.5);
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2),
    inset 0 1px 2px rgba(0, 0, 0, 0.1);
  background-color: #1e1e1e;
}

.name-input-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #2196f3;
  font-size: 1.25rem;
  pointer-events: none;
  opacity: 0.8;
  transition: all 0.2s ease;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.name-input:focus + .name-input-icon {
  color: #1976d2;
  opacity: 1;
  transform: translateY(-50%) scale(1.1);
}

/* Tailwind-like utility classes */
.mr-1 {
  margin-right: 0.25rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.right-3 {
  right: 0.75rem;
}

.top-1\/2 {
  top: 50%;
}

.transform {
  transform: translateY(-50%);
}

.text-gray-400 {
  color: #9ca3af;
}

.text-\[\#2196f3\] {
  color: #2196f3;
}

/* Scheduling Section Styles */
.schedule-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.radio-group {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 0.5rem;
}

.radio-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.95rem;
  transition: color 0.2s ease;
}

.radio-option:hover {
  color: #fff;
}

.radio-option input[type="radio"] {
  width: auto;
  margin: 0;
  accent-color: #2196f3;
  cursor: pointer;
}

.radio-label {
  cursor: pointer;
  user-select: none;
}

.scheduled-time-input {
  margin-top: 1rem;
  padding: 1rem;
  background-color: rgba(40, 40, 40, 0.5);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.scheduled-time-input label {
  margin-bottom: 0.75rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.datetime-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #333;
  border-radius: 6px;
  background-color: #1e1e1e;
  color: #fff;
  font-size: 0.95rem;
  transition: all 0.2s ease;
}

.datetime-input:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

.datetime-input.error {
  border-color: #f44336;
  box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.2);
}

/* Color scheme for datetime input */
.datetime-input::-webkit-calendar-picker-indicator {
  filter: invert(1);
  cursor: pointer;
}
