/* Service Modal Styles - Dark Theme */
.service-modal {
  max-width: 500px;
  width: 90%;
  background-color: #121212;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  animation: modalFadeIn 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #333;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-overlay {
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-header {
  background-color: #151515;
  border-bottom: 1px solid #333;
  padding: 0.75rem 1.25rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-header h2 {
  color: #2196f3;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  margin: 0;
}

.close-button {
  color: #888;
  font-size: 1.25rem;
  transition: all 0.2s;
  background: none;
  border: none;
  cursor: pointer;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.close-button:hover {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.1);
}

.modal-body {
  padding: 0.75rem;
  background-color: #121212;
  max-height: 60vh;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #444 #121212;
}

.modal-body::-webkit-scrollbar {
  width: 6px;
}

.modal-body::-webkit-scrollbar-track {
  background: #121212;
}

.modal-body::-webkit-scrollbar-thumb {
  background-color: #444;
  border-radius: 6px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 0.75rem 1.25rem;
  border-top: 1px solid #333;
  background-color: #151515;
  gap: 0.75rem;
}

/* Service Selection Container */
.service-selection-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Removed grid layout - service selection now uses full width */

/* Info Panel (contains both Stream and Service Info) */
.info-panel {
  background-color: #1a1a1a;
  border-radius: 8px;
  padding: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: 1px solid #333;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* Stream Info Section */
.stream-info-section {
  display: flex;
  flex-direction: column;
}

/* Service Info Section */
.service-info-section {
  display: flex;
  flex-direction: column;
  border-top: 1px solid #333;
  padding-top: 0.75rem;
  margin-top: 0.25rem;
}

/* Common styles for section headers */
.stream-info-section h3,
.service-info-section h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 0.8rem;
  color: #2196f3;
  display: flex;
  align-items: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Info grid for both stream and service info */
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

/* Info items (used in both stream and service info) */
.info-item {
  display: flex;
  flex-direction: column;
  padding: 0.4rem 0.6rem;
  background-color: #151515;
  border-radius: 6px;
  border: 1px solid #222;
  transition: all 0.2s;
}

.info-item:hover {
  border-color: #333;
}

.info-label {
  font-weight: 500;
  color: #777;
  font-size: 0.7rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.25rem;
}

.info-value {
  font-size: 0.8rem;
  color: #fff;
  font-weight: 500;
  text-align: center;
}

.info-value.synced {
  color: #4caf50;
  font-weight: 600;
}

.info-value.not-synced {
  color: #f44336;
  font-weight: 600;
}

/* Service info header */
.service-info-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.info-value.highlight {
  color: #2196f3;
  font-weight: 600;
}

.detail-value.highlight-id {
  color: #2196f3;
  font-weight: 600;
  font-size: 0.9rem;
}

.detail-value.highlight-ready {
  color: #4caf50;
  font-weight: 600;
}

.section-icon {
  margin-right: 0.5rem;
  font-size: 1rem;
  color: #2196f3;
}

.detail-label-with-icon {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.detail-icon {
  color: #aaa;
  font-size: 1rem;
}

.service-id-label {
  color: #aaa;
  margin-right: 0.25rem;
  font-size: 0.85rem;
}

.service-id-value {
  font-weight: 600;
  color: #ff9800;
}

.button-icon {
  /* margin-right: 0.5rem; */
  font-size: 1.1rem;
}

/* Service Selection */
.service-selection {
  background-color: #1a1a1a;
  border-radius: 8px;
  padding: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: 1px solid #333;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Service Selection Header */
.service-selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #333;
}

.service-selection h3 {
  margin: 0;
  font-size: 0.85rem;
  color: #2196f3;
  display: flex;
  align-items: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Sync Status Indicator */
.sync-status {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  padding: 0.3rem 0.6rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.sync-status.synced {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
  border: 1px solid rgba(76, 175, 80, 0.2);
}

.sync-status.not-synced {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
  border: 1px solid rgba(244, 67, 54, 0.2);
}

.sync-status .status-icon {
  font-size: 0.9rem;
}

/* Selection Confirmation */
.selection-confirmation {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
  padding: 0.75rem 1rem;
  background-color: rgba(33, 150, 243, 0.1);
  border: 1px solid rgba(33, 150, 243, 0.2);
  border-radius: 6px;
  color: #2196f3;
  font-size: 0.9rem;
}

.confirmation-icon {
  font-size: 1.1rem;
  color: #2196f3;
}

.selection-confirmation strong {
  color: #fff;
  font-weight: 600;
}

.service-table-container {
  border: none;
  border-radius: 4px;
  overflow: hidden;
  background-color: transparent;
  max-height: 200px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #444 #1a1a1a;
  flex: 1;
}

.service-table-container::-webkit-scrollbar {
  width: 4px;
}

.service-table-container::-webkit-scrollbar-track {
  background: #1a1a1a;
  border-radius: 4px;
}

.service-table-container::-webkit-scrollbar-thumb {
  background-color: #444;
  border-radius: 4px;
}

.service-table {
  width: 100%;
  border-collapse: collapse;
  text-align: center;
}

.service-table thead {
  background-color: #151515;
  position: sticky;
  top: 0;
  z-index: 10;
}

.service-table th {
  padding: 0.4rem 0.5rem;
  font-weight: 500;
  color: #2196f3;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 1px solid #222;
  text-align: center;
}

.service-table tbody tr {
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid #222;
  height: 32px;
}

.service-table tbody tr:last-child {
  border-bottom: none;
}

.service-table tbody tr:hover {
  background-color: #222;
}

.service-table tbody tr.selected {
  background-color: rgba(33, 150, 243, 0.1);
  border-left: 3px solid #2196f3;
}

.service-table td {
  padding: 0.3rem 0.5rem;
  vertical-align: middle;
  font-size: 0.8rem;
}

.service-name-column {
  width: 30%;
  min-width: 120px;
}

.service-id-column {
  width: 10%;
  min-width: 60px;
}

.service-video-column {
  width: 30%;
  min-width: 120px;
}

.service-audio-column {
  width: 30%;
  min-width: 120px;
}

.service-action-column {
  width: 5%;
  text-align: center;
}

.service-name-cell {
  font-weight: 500;
  color: #fff;
  text-align: center;
}

.service-id-cell {
  color: #2196f3;
  font-weight: 500;
  text-align: center;
}

.service-video-cell {
  text-align: center;
  color: #fff;
}

.service-audio-cell {
  text-align: center;
  color: #fff;
}

.video-info,
.audio-info {
  display: flex;
  flex-direction: column;
  gap: 0.1rem;
}

.video-info .codec,
.audio-info .codec {
  font-weight: 500;
  color: #4caf50;
  font-size: 0.75rem;
}

.video-info .resolution {
  color: #aaa;
  font-size: 0.7rem;
}

.audio-info .channels {
  color: #aaa;
  font-size: 0.7rem;
}

.no-info {
  color: #666;
  font-style: italic;
  font-size: 0.75rem;
}

.service-action-cell {
  text-align: center;
}

.service-table tfoot {
  background-color: #151515;
}

.service-table-footer {
  padding: 0.5rem 0.75rem;
  color: #777;
  font-size: 0.75rem;
  text-align: center;
  border-top: 1px solid #333;
}

.service-list {
  max-height: 250px;
  overflow-y: auto;
  background-color: #2a2a2a;
  scrollbar-width: thin;
  scrollbar-color: #444 #2a2a2a;
}

.service-list::-webkit-scrollbar {
  width: 8px;
}

.service-list::-webkit-scrollbar-track {
  background: #2a2a2a;
  border-radius: 8px;
}

.service-list::-webkit-scrollbar-thumb {
  background-color: #444;
  border-radius: 8px;
}

.service-item {
  padding: 1rem;
  border-bottom: 1px solid #333;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #2a2a2a;
}

.service-item:last-child {
  border-bottom: none;
}

.service-item:hover {
  background-color: #333;
  transform: translateY(-2px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
}

.service-item.selected {
  background-color: rgba(33, 150, 243, 0.15);
  border-left: 4px solid #2196f3;
}

.service-item-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
}

.service-item-action {
  margin-left: 1rem;
}

.service-name {
  font-weight: 600;
  font-size: 1rem;
  color: #fff;
}

.service-id {
  color: #aaa;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
}

.selected-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: #2196f3;
  font-weight: 500;
}

.select-button {
  padding: 0.4rem 0.8rem;
  background-color: rgba(33, 150, 243, 0.1);
  color: #2196f3;
  border: 1px solid #2196f3;
  border-radius: 4px;
  font-size: 0.85rem;
  transition: all 0.2s;
}

.service-item:hover .select-button {
  background-color: rgba(33, 150, 243, 0.2);
}

.check-icon {
  font-size: 1.25rem;
}

/* Selected Service Details */
.selected-service-details {
  background-color: #1a1a1a;
  border-radius: 8px;
  margin-top: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: 1px solid #333;
  transition: all 0.2s ease;
  overflow: hidden;
}

.selected-service-details:hover {
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
  border-color: #444;
}

.service-detail-header {
  padding: 0.6rem 0.75rem;
  background-color: rgba(100, 108, 255, 0.05);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.service-detail-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.service-name-large {
  font-size: 0.85rem;
  font-weight: 600;
  color: #fff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.service-id-badge {
  display: flex;
  align-items: center;
  background-color: rgba(100, 108, 255, 0.1);
  border: 1px solid rgba(100, 108, 255, 0.2);
  color: #2196f3;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

.badge-icon {
  margin-right: 0.4rem;
  font-size: 0.9rem;
}

.service-detail-tabs {
  display: flex;
  border-bottom: 1px solid #333;
  background-color: #151515;
}

.tab {
  padding: 0.5rem 1rem;
  color: #777;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.8rem;
  font-weight: 500;
}

.tab.active {
  color: #2196f3;
  border-bottom: 2px solid #2196f3;
  background-color: rgba(100, 108, 255, 0.05);
}

.service-detail-content {
  padding: 0.75rem 1rem;
}

.detail-section {
  margin-bottom: 1rem;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-section-title {
  display: flex;
  align-items: center;
  margin: 0 0 0.75rem 0;
  font-size: 0.9rem;
  color: #fff;
  padding-bottom: 0.4rem;
  border-bottom: 1px solid #222;
}

.detail-section-icon {
  margin-right: 0.4rem;
  color: #2196f3;
  font-size: 0.9rem;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 0.5rem;
}

.detail-item {
  background-color: #151515;
  border: 1px solid #222;
  border-radius: 6px;
  padding: 0.5rem 0.75rem;
  transition: all 0.2s;
}

.detail-item:hover {
  background-color: #1d1d1d;
  transform: translateY(-1px);
  border-color: #333;
}

.detail-item-label {
  color: #777;
  font-size: 0.7rem;
  margin-bottom: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-item-value {
  color: #fff;
  font-weight: 500;
  font-size: 0.85rem;
  text-align: center;
}

.detail-item-value.highlight-id {
  color: #2196f3;
  font-weight: 600;
}

.detail-item-value.highlight-codec {
  color: #2196f3;
  font-weight: 600;
}

.detail-item-value.highlight-resolution {
  color: #2196f3;
  font-weight: 600;
}

.status-card {
  background-color: #151515;
  border: 1px solid #222;
  border-radius: 6px;
  padding: 0.75rem;
}

.status-indicator {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.status-dot.success {
  background-color: #4caf50;
  box-shadow: 0 0 4px #4caf50;
}

.status-text {
  color: #4caf50;
  font-weight: 500;
  font-size: 0.8rem;
}

.status-description {
  color: #777;
  font-size: 0.8rem;
  line-height: 1.4;
}

/* Loading, Error, and No Services States */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  text-align: center;
  color: #888;
}

.loading-spinner {
  width: 36px;
  height: 36px;
  border: 3px solid rgba(100, 108, 255, 0.1);
  border-radius: 50%;
  border-top: 3px solid #2196f3;
  animation: spin 0.8s linear infinite;
  margin-bottom: 1rem;
}

.loading-spinner-small {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(100, 108, 255, 0.1);
  border-radius: 50%;
  border-top: 2px solid #2196f3;
  animation: spin 0.8s linear infinite;
  margin-right: 0.5rem;
}

.loading-subtext {
  color: #666;
  font-size: 0.8rem;
  margin-top: 0.5rem;
  opacity: 0.7;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.pulse-icon {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.sync-status-indicator {
  display: flex;
  align-items: center;
  margin-top: 0.75rem;
  padding: 0.5rem 0.75rem;
  background-color: rgba(244, 67, 54, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(244, 67, 54, 0.2);
}

.sync-pulse {
  width: 8px;
  height: 8px;
  background-color: #f44336;
  border-radius: 50%;
  margin-right: 0.5rem;
  animation: pulse 1.5s infinite;
}

.error-message {
  display: flex;
  flex-direction: column;
}

.error-icon,
.info-icon {
  font-size: 2rem;
  /* margin-bottom: 0.75rem; */
  opacity: 0.9;
}

.retry-button {
  margin-top: 0.75rem;
  padding: 0.4rem 0.75rem;
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
  border: 1px solid rgba(244, 67, 54, 0.2);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.85rem;
}

.retry-button:hover {
  background-color: rgba(244, 67, 54, 0.15);
  transform: translateY(-1px);
}

.retry-button:active {
  transform: translateY(0);
}

.no-services {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem 1rem;
  text-align: center;
  color: #888;
  background-color: rgba(26, 26, 26, 0.7);
  border-radius: 8px;
  border: 1px solid #333;
}

/* No Service Selected State */
.no-service-selected {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1.5rem;
  text-align: center;
  height: 100%;
  min-height: 200px;
}

.no-service-selected .info-icon {
  font-size: 2.5rem;
  color: #555;
  margin-bottom: 1rem;
  opacity: 0.8;
}

.no-service-selected h3 {
  margin: 0 0 0.75rem 0;
  font-size: 1.1rem;
  color: #888;
  font-weight: 600;
}

.no-service-selected p {
  margin: 0 0 1.5rem 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
  max-width: 280px;
}

.selection-hint {
  background-color: rgba(33, 150, 243, 0.05);
  border: 1px solid rgba(33, 150, 243, 0.1);
  border-radius: 6px;
  padding: 0.75rem 1rem;
  margin-top: 0.5rem;
}

.selection-hint span {
  color: #2196f3;
  font-size: 0.85rem;
  font-weight: 500;
}

.empty-services-action {
  margin-top: 1.5rem;
}

.service-list-container {
  display: flex;
  flex-direction: column;
  border: 1px solid #333;
  border-radius: 8px;
  overflow: hidden;
  background-color: #2a2a2a;
}

.service-list-header {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  background-color: #222;
  border-bottom: 1px solid #333;
  font-weight: 600;
  color: #2196f3;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.service-header-name {
  flex: 1;
}

.service-header-id {
  width: 100px;
  text-align: right;
}

.service-list-footer {
  padding: 0.75rem 1rem;
  background-color: #222;
  border-top: 1px solid #333;
  color: #aaa;
  font-size: 0.85rem;
}

.service-count {
  font-style: italic;
}

.select-prompt {
  color: #555;
  font-size: 0.75rem;
  font-style: italic;
  opacity: 0;
  transition: opacity 0.2s;
}

.service-table tbody tr:hover .select-prompt {
  opacity: 1;
  color: #2196f3;
}

.detail-value-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-icon {
  font-size: 1.1rem;
}

.status-icon.success {
  color: #4caf50;
}

.status-with-icon {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.selected-service-action {
  padding: 1.25rem;
  background-color: #222;
  border-top: 1px solid #333;
}

.full-width {
  width: 100%;
}

.modal-footer-left {
  display: flex;
  align-items: center;
}

.modal-footer-right {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.submitting-indicator {
  display: flex;
  align-items: center;
  color: #777;
  font-size: 0.8rem;
}

/* Footer Buttons */
.cancel-button {
  background-color: transparent;
  color: #888;
  border: 1px solid #333;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-width: 100px;
  font-size: 0.85rem;
}

.cancel-button:hover {
  background-color: rgba(255, 255, 255, 0.05);
  color: #fff;
  border-color: #444;
}

.cancel-button:active {
  transform: translateY(1px);
}

.cancel-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.record-all-button,
.record-selected-button {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.85rem;
}

.record-all-button {
  background-color: #1a1a1a;
  color: #4caf50;
  border: 1px solid #4caf50;
}

.record-all-button:hover:not(:disabled) {
  background-color: rgba(76, 175, 80, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.record-all-button:active:not(:disabled) {
  transform: translateY(0);
}

.record-all-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.record-selected-button {
  background-color: #2196f3;
  color: white;
  border: none;
  font-weight: 500;
  min-width: 100px;
  box-shadow: 0 2px 4px rgba(100, 108, 255, 0.2);
}

.record-selected-button:hover:not(:disabled) {
  background-color: #535bf2;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(100, 108, 255, 0.3);
}

.record-selected-button:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(100, 108, 255, 0.3);
}

.record-selected-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  box-shadow: none;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .service-modal {
    max-width: 95%;
    width: 95%;
    margin: 1rem;
  }

  .service-selection-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .sync-status {
    align-self: flex-end;
  }

  .modal-footer {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .modal-footer-left,
  .modal-footer-right {
    width: 100%;
  }

  .modal-footer-right {
    flex-direction: column;
    gap: 0.75rem;
  }

  .modal-footer button {
    width: 100%;
    justify-content: center;
  }

  .service-table th,
  .service-table td {
    padding: 0.3rem 0.25rem;
    font-size: 0.75rem;
  }

  .service-name-column {
    width: 40%;
  }

  .service-id-column {
    width: 15%;
  }

  .service-video-column,
  .service-audio-column {
    width: 22.5%;
  }
}

/* RTP Senders Section */
.rtp-senders-section {
  background-color: #1a1a1a;
  border-radius: 8px;
  padding: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: 1px solid #333;
  margin-bottom: 0.75rem;
}

.rtp-senders-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.rtp-senders-header h3 {
  margin: 0;
  font-size: 0.8rem;
  color: #2196f3;
  display: flex;
  align-items: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.refresh-senders-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #333;
  color: #fff;
  border: 1px solid #444;
  border-radius: 6px;
  padding: 0.4rem 0.75rem;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s;
}

.refresh-senders-button:hover:not(:disabled) {
  background-color: #444;
  border-color: #555;
}

.refresh-senders-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.refresh-icon {
  width: 14px;
  height: 14px;
  transition: transform 0.5s linear;
}

.refresh-icon.spinning {
  animation: spin 1s linear infinite;
}

.loading-senders {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #888;
  font-size: 0.8rem;
  padding: 1rem;
  justify-content: center;
}

.no-senders {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: #888;
  padding: 1rem;
  text-align: center;
}

.no-senders .info-icon {
  font-size: 1.5rem;
  color: #666;
}

.no-senders p {
  margin: 0;
  font-size: 0.85rem;
  color: #ccc;
}

.info-subtext {
  font-size: 0.75rem;
  color: #777;
}

.senders-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.sender-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.6rem 0.75rem;
  background-color: #151515;
  border: 1px solid #333;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.sender-item:hover {
  border-color: #444;
  background-color: #1a1a1a;
}

.sender-item.selected {
  border-color: #2196f3;
  background-color: rgba(33, 150, 243, 0.1);
}

.sender-ip {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sender-icon {
  color: #2196f3;
  font-size: 1rem;
}

.ip-address {
  font-weight: 500;
  color: #fff;
  font-size: 0.9rem;
}

.sender-stats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.packet-count {
  font-size: 0.75rem;
  color: #4caf50;
  font-weight: 500;
}

.last-seen {
  font-size: 0.7rem;
  color: #888;
}

.sender-confirmation {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.75rem;
  padding: 0.5rem 0.75rem;
  background-color: rgba(33, 150, 243, 0.1);
  border: 1px solid #2196f3;
  border-radius: 6px;
  color: #2196f3;
  font-size: 0.8rem;
}

.sender-confirmation .confirmation-icon {
  font-size: 1rem;
}

.sender-confirmation strong {
  color: #fff;
}
