import { useState, useEffect, useRef } from "react";
import {
  createRecorder,
  updateRecorder,
  getRtpUrls,
  getNetworkInterfaces,
} from "../../api/recorderApi";
import { getCodecSettings } from "../../api/adminApi";
import { Recorder, RtpUrl } from "../../types/recorder";
import { NetworkInterface } from "../../types/network";
import {
  HiVideoCamera,
  HiClock,
  HiGlobeAlt,
  HiX,
  HiSearch,
  HiCheck,
} from "react-icons/hi";
import "./CreateRecorderModal.css";
import DurationPicker from "./DurationPicker";
import { hmsToSeconds, secondsToHms } from "./DurationPicker";

interface CreateRecorderModalProps {
  recorder?: Recorder | null;
  onClose: () => void;
  onSuccess: () => void;
}

// Utility functions for datetime handling
const formatDateTimeForInput = (utcDateString: string): string => {
  // Convert UTC datetime string to local datetime string for datetime-local input
  // Ensure we're parsing as UTC by adding 'Z' if not present
  let utcString = utcDateString;
  if (
    !utcString.endsWith("Z") &&
    !utcString.includes("+") &&
    !utcString.includes("-", 10)
  ) {
    utcString = utcString.replace("T", "T").replace(/\.\d{3}$/, "") + "Z";
  }

  const utcDate = new Date(utcString);

  // Check if the date is valid
  if (isNaN(utcDate.getTime())) {
    console.error("Invalid date string provided:", utcDateString);
    return "";
  }

  // Format as local time for datetime-local input (YYYY-MM-DDTHH:mm)
  const year = utcDate.getFullYear();
  const month = String(utcDate.getMonth() + 1).padStart(2, "0");
  const day = String(utcDate.getDate()).padStart(2, "0");
  const hours = String(utcDate.getHours()).padStart(2, "0");
  const minutes = String(utcDate.getMinutes()).padStart(2, "0");
  return `${year}-${month}-${day}T${hours}:${minutes}`;
};

const formatDateTimeForServer = (localDateString: string): string => {
  // Convert local datetime string to UTC ISO string for server
  // The datetime-local input gives us a string in format "YYYY-MM-DDTHH:mm"
  // This represents local time, so we need to convert it to UTC

  if (!localDateString) {
    return "";
  }

  // Create a Date object from the local datetime string
  // JavaScript treats this as local time (which is what we want)
  const localDate = new Date(localDateString);

  // Check if the date is valid
  if (isNaN(localDate.getTime())) {
    console.error("Invalid local date string provided:", localDateString);
    return "";
  }

  // Convert to UTC ISO string for the server
  return localDate.toISOString();
};

const getCurrentLocalDateTimeString = (): string => {
  // Get current local time in format suitable for datetime-local input
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  const hours = String(now.getHours()).padStart(2, "0");
  const minutes = String(now.getMinutes()).padStart(2, "0");
  return `${year}-${month}-${day}T${hours}:${minutes}`;
};

const CreateRecorderModal = ({
  recorder,
  onClose,
  onSuccess,
}: CreateRecorderModalProps) => {
  const [name, setName] = useState("");
  const [input, setInput] = useState("");
  const [, setDuration] = useState("00:30:00");
  const [durationInSeconds, setDurationInSeconds] = useState(1800);
  const [vcodec, setVcodec] = useState("h264");
  const [acodec, setAcodec] = useState("aac_downmix");
  const [resolution, setResolution] = useState("1920x1080i");
  const [fps, setFps] = useState(29.97);
  const [sampleRate, setSampleRate] = useState(48000);
  const [vbitrate, setVbitrate] = useState(6000);
  const [abitrate, setAbitrate] = useState(192);
  const [maxVbitrate, setMaxVbitrate] = useState(10000);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [rtpUrls, setRtpUrls] = useState<RtpUrl[]>([]);
  const [showRtpUrlDropdown, setShowRtpUrlDropdown] = useState(false);
  const [durationError, setDurationError] = useState("");
  const [rtpUrlError, setRtpUrlError] = useState("");
  const [filteredRtpUrls, setFilteredRtpUrls] = useState<RtpUrl[]>([]);
  const [networkInterfaces, setNetworkInterfaces] = useState<
    NetworkInterface[]
  >([]);
  const [selectedInterface, setSelectedInterface] = useState<string>("");
  const [showNetworkDropdown, setShowNetworkDropdown] = useState(false);

  // New scheduling state variables
  const [scheduleMode, setScheduleMode] = useState<"immediate" | "scheduled">(
    "immediate"
  );
  const [scheduledStartTime, setScheduledStartTime] = useState("");
  const [scheduleError, setScheduleError] = useState("");

  const rtpInputRef = useRef<HTMLInputElement>(null);
  const networkSelectRef = useRef<HTMLDivElement>(null);
  const isEditMode = !!recorder;

  // Load default codec settings
  const loadDefaultCodecSettings = async () => {
    try {
      // Only load defaults if we're not in edit mode
      if (!isEditMode) {
        const settings = await getCodecSettings();
        setVcodec(settings.vcodec);
        setAcodec(settings.acodec);
        setResolution(settings.resolution);
        setFps(settings.fps);
        setSampleRate(settings.sample_rate);
        setVbitrate(settings.vbitrate);
        setAbitrate(settings.abitrate);
        setMaxVbitrate(settings.max_vbitrate);
      }
    } catch (error) {
      console.error("Failed to load default codec settings:", error);
      // Keep the hardcoded defaults if we can't load from server
    }
  };

  useEffect(() => {
    // If we're in edit mode, populate the form with the recorder data
    if (recorder) {
      setName(recorder.name);
      setInput(recorder.input);
      setDuration(recorder.duration);
      setDurationInSeconds(hmsToSeconds(recorder.duration));
      setVcodec(recorder.VCodec);
      setAcodec(recorder.ACodec);
      setResolution(recorder.resolution);
      setFps(recorder.FPS);
      setSampleRate(recorder.sampleRate);
      setVbitrate(recorder.VBitrate);
      setAbitrate(recorder.ABitrate);
      setMaxVbitrate(recorder.MaxVBitrate);

      // Set the network interface from the recorder object
      if (recorder.network_interface) {
        setSelectedInterface(recorder.network_interface);
      }

      // Set scheduling fields
      if (recorder.is_scheduled && recorder.scheduled_start_time) {
        setScheduleMode("scheduled");
        // Convert UTC datetime from server to local datetime for input
        const localTime = formatDateTimeForInput(recorder.scheduled_start_time);
        console.log("Loading existing scheduled time:", {
          serverTime: recorder.scheduled_start_time,
          localTime: localTime,
          userTimezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        });
        setScheduledStartTime(localTime);
      } else {
        setScheduleMode("immediate");
        setScheduledStartTime("");
      }
    } else {
      // Load default codec settings if we're creating a new recorder
      loadDefaultCodecSettings();
    }

    // Load RTP URLs and network interfaces
    loadRtpUrls();
    loadNetworkInterfaces();
  }, [recorder]);

  // Update duration string whenever durationInSeconds changes
  useEffect(() => {
    setDuration(secondsToHms(durationInSeconds));
  }, [durationInSeconds]);

  // Filter RTP URLs based on input
  useEffect(() => {
    if (rtpUrls.length > 0) {
      const filtered = input
        ? rtpUrls.filter((url) =>
            url.url.toLowerCase().includes(input.toLowerCase())
          )
        : rtpUrls;

      setFilteredRtpUrls(filtered);
    }
  }, [input, rtpUrls]);

  const loadRtpUrls = async () => {
    try {
      console.log("Loading RTP URLs...");
      const urls = await getRtpUrls();
      console.log("RTP URLs loaded:", urls);
      setRtpUrls(urls || []); // Ensure we always set an array, even if the response is null
      setFilteredRtpUrls(urls || []);
    } catch (error) {
      console.error("Failed to load RTP URLs:", error);
      setRtpUrls([]); // Set to empty array on error
      setFilteredRtpUrls([]);
    }
  };

  const loadNetworkInterfaces = async () => {
    try {
      console.log("Loading network interfaces...");
      const interfaces = await getNetworkInterfaces();
      console.log("Network interfaces loaded:", interfaces);
      setNetworkInterfaces(interfaces || []);
    } catch (error) {
      console.error("Failed to load network interfaces:", error);
      setNetworkInterfaces([]);
    }
  };

  // Handler for duration picker value changes
  const handleDurationChange = (newDurationInSeconds: number) => {
    setDurationInSeconds(newDurationInSeconds);
  };

  // Handler called when duration picker completes editing
  const handleDurationSubmit = () => {
    // This function is called when the duration picker completes an edit
    // We've already updated durationInSeconds, so we just need to
    // ensure duration error is cleared
    setDurationError("");
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Reset validation errors
    setDurationError("");
    setRtpUrlError("");
    setScheduleError("");

    // Validate form
    let hasError = false;

    // Validate name
    if (!name.trim()) {
      console.error("Validation Error: Name is required");
      hasError = true;
    }

    // Validate RTP URL
    if (!input.trim()) {
      setRtpUrlError("RTP URL is required");
      hasError = true;
    } else if (
      !input.match(/^rtp:\/\/(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}):(\d{1,5})$/)
    ) {
      setRtpUrlError("Invalid RTP URL format. Should be rtp://IP:PORT");
      hasError = true;
    }

    // Validate duration - make sure it's not zero
    if (durationInSeconds <= 0) {
      setDurationError("Duration must be greater than zero");
      hasError = true;
    }

    // Validate scheduling
    if (scheduleMode === "scheduled") {
      if (!scheduledStartTime.trim()) {
        setScheduleError("Start time is required for scheduled recordings");
        hasError = true;
      } else {
        // Validate that the scheduled time is in the future
        // Note: scheduledStartTime is in local time format from datetime-local input
        const scheduledDate = new Date(scheduledStartTime);

        // Check if the date is valid
        if (isNaN(scheduledDate.getTime())) {
          setScheduleError("Invalid start time format");
          hasError = true;
        } else {
          const now = new Date();
          // Add a small buffer (1 minute) to account for form submission time
          const minTime = new Date(now.getTime() + 60 * 1000);

          if (scheduledDate <= minTime) {
            setScheduleError(
              "Start time must be at least 1 minute in the future"
            );
            hasError = true;
          }
        }
      }
    }

    if (hasError) {
      return;
    }

    try {
      setIsSubmitting(true);

      // Use the formatted duration string from our state
      const durationToSend = secondsToHms(durationInSeconds);

      const recorderData = {
        name,
        input,
        duration: durationToSend, // Use the formatted duration string
        vcodec,
        acodec,
        resolution,
        fps,
        sample_rate: sampleRate,
        vbitrate,
        abitrate,
        max_vbitrate: maxVbitrate,
        network_interface: selectedInterface,
        is_scheduled: scheduleMode === "scheduled",
        scheduled_start_time:
          scheduleMode === "scheduled"
            ? (() => {
                const utcTime = formatDateTimeForServer(scheduledStartTime);
                console.log("Timezone conversion debug:", {
                  localTime: scheduledStartTime,
                  utcTime: utcTime,
                  userTimezone:
                    Intl.DateTimeFormat().resolvedOptions().timeZone,
                });
                return utcTime;
              })()
            : undefined,
      };

      if (isEditMode && recorder) {
        await updateRecorder(recorder.id, recorderData);
        console.log("Input feed updated successfully");
      } else {
        await createRecorder(recorderData);
        console.log("Input feed created successfully");
      }

      onSuccess();
    } catch (error) {
      console.error("Failed to save recorder:", error);
      console.error(
        `Failed to ${isEditMode ? "update" : "create"} input feed: ${error}`
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRtpUrlSelect = (url: string) => {
    setInput(url);
    setShowRtpUrlDropdown(false);
  };

  const handleNetworkInterfaceSelect = (name: string) => {
    setSelectedInterface(name);
    setShowNetworkDropdown(false);
  };

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h2>
            <HiVideoCamera className="mr-2" />
            {isEditMode ? "Edit Input Feed" : "Create New Input Feed"}
          </h2>
          <button className="close-button" onClick={onClose}>
            <HiX />
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          {/* Basic Settings */}
          <div className="form-group" style={{ zIndex: 50 }}>
            <label htmlFor="name">
              <span className="flex items-center">
                <HiVideoCamera className="mr-2 text-[#2196f3]" />
                Input Feed Name
              </span>
            </label>
            <div className="name-input-container">
              <input
                type="text"
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Enter a name for this input feed"
                required
                className="name-input"
              />
              <HiVideoCamera className="name-input-icon" />
            </div>
          </div>

          <div className="form-group" style={{ zIndex: 40 }}>
            <label htmlFor="input">
              <span className="flex items-center">
                <HiGlobeAlt className="mr-2 text-[#2196f3]" />
                RTP URL
              </span>
            </label>
            <div className="input-with-dropdown">
              <div className="url-input-container">
                <input
                  type="text"
                  id="input"
                  ref={rtpInputRef}
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  onFocus={() => setShowRtpUrlDropdown(true)}
                  onBlur={() =>
                    setTimeout(() => setShowRtpUrlDropdown(false), 200)
                  }
                  placeholder="rtp://239.0.0.1:5000"
                  required
                  className={`url-input ${rtpUrlError ? "error" : ""}`}
                />
                <HiSearch className="url-input-icon" />
              </div>
              {rtpUrlError && (
                <div className="error-message">{rtpUrlError}</div>
              )}
              {showRtpUrlDropdown && filteredRtpUrls.length > 0 && (
                <div className="dropdown-menu">
                  <div className="dropdown-header">Available RTP URLs</div>
                  <div className="dropdown-items-container">
                    {filteredRtpUrls.map((rtpUrl) => (
                      <div
                        key={rtpUrl.id}
                        className="dropdown-item"
                        onClick={() => handleRtpUrlSelect(rtpUrl.url)}
                      >
                        <div className="dropdown-item-icon">
                          <HiGlobeAlt />
                        </div>
                        <div className="dropdown-item-text">{rtpUrl.url}</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="form-group" style={{ zIndex: 30 }}>
            <label htmlFor="duration">
              <span className="flex items-center">
                <HiClock className="mr-2 text-[#2196f3]" />
                Duration (HH:MM:SS)
              </span>
            </label>
            <div className="duration-input-container">
              <DurationPicker
                value={durationInSeconds}
                setValue={handleDurationChange}
                onSubmit={handleDurationSubmit}
              />
              {durationError && (
                <div className="error-message">{durationError}</div>
              )}
            </div>
          </div>

          {/* Scheduling Section */}
          <div className="form-group" style={{ zIndex: 25 }}>
            <label>
              <span className="flex items-center">
                <HiClock className="mr-2 text-[#2196f3]" />
                Schedule
              </span>
            </label>
            <div className="schedule-options">
              <div className="radio-group">
                <label className="radio-option">
                  <input
                    type="radio"
                    name="scheduleMode"
                    value="immediate"
                    checked={scheduleMode === "immediate"}
                    onChange={(e) =>
                      setScheduleMode(
                        e.target.value as "immediate" | "scheduled"
                      )
                    }
                  />
                  <span className="radio-label">Start Immediately</span>
                </label>
                <label className="radio-option">
                  <input
                    type="radio"
                    name="scheduleMode"
                    value="scheduled"
                    checked={scheduleMode === "scheduled"}
                    onChange={(e) =>
                      setScheduleMode(
                        e.target.value as "immediate" | "scheduled"
                      )
                    }
                  />
                  <span className="radio-label">Schedule for Later</span>
                </label>
              </div>

              {scheduleMode === "scheduled" && (
                <div className="scheduled-time-input">
                  <label htmlFor="scheduledStartTime">
                    <span className="flex items-center">
                      <HiClock className="mr-2 text-[#2196f3]" />
                      Start Time
                    </span>
                  </label>
                  <input
                    type="datetime-local"
                    id="scheduledStartTime"
                    value={scheduledStartTime}
                    onChange={(e) => setScheduledStartTime(e.target.value)}
                    className={`datetime-input ${scheduleError ? "error" : ""}`}
                    min={getCurrentLocalDateTimeString()}
                  />
                  {scheduleError && (
                    <div className="error-message">{scheduleError}</div>
                  )}
                </div>
              )}
            </div>
          </div>

          <div className="form-group" style={{ zIndex: 20 }}>
            <label htmlFor="networkInterface">
              <span className="flex items-center">
                <HiGlobeAlt className="mr-2 text-[#2196f3]" />
                Network Interface (Optional)
              </span>
            </label>
            <div className="network-select-container" ref={networkSelectRef}>
              <div
                className="network-display"
                onClick={() => setShowNetworkDropdown(!showNetworkDropdown)}
                onBlur={() =>
                  setTimeout(() => setShowNetworkDropdown(false), 200)
                }
                tabIndex={0}
              >
                <span className="network-display-text">
                  {selectedInterface
                    ? networkInterfaces.find(
                        (iface) => iface.name === selectedInterface
                      )?.name +
                      " - " +
                      networkInterfaces.find(
                        (iface) => iface.name === selectedInterface
                      )?.ip_address
                    : "Auto (System Default)"}
                </span>
                <HiGlobeAlt className="network-display-icon" />
              </div>

              {showNetworkDropdown && (
                <div className="dropdown-menu network-dropdown">
                  <div className="dropdown-header">
                    Available Network Interfaces
                  </div>
                  <div className="dropdown-items-container">
                    <div
                      className="dropdown-item"
                      onClick={() => handleNetworkInterfaceSelect("")}
                    >
                      <div className="dropdown-item-icon">
                        <HiGlobeAlt />
                      </div>
                      <div className="dropdown-item-text">
                        Auto (System Default)
                      </div>
                    </div>

                    {networkInterfaces.map((iface) => (
                      <div
                        key={iface.name}
                        className="dropdown-item"
                        onClick={() => handleNetworkInterfaceSelect(iface.name)}
                      >
                        <div className="dropdown-item-icon">
                          <HiGlobeAlt />
                        </div>
                        <div className="dropdown-item-text">
                          {iface.name} - {iface.ip_address}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
            <div className="help-text">
              <HiGlobeAlt className="help-text-icon" />
              <span>
                Select a network interface to receive the RTP stream. Leave
                empty to use the system default.
              </span>
            </div>
          </div>

          <div className="form-actions" style={{ zIndex: 10 }}>
            <button type="button" className="cancel-button" onClick={onClose}>
              <HiX className="mr-1" />
              Cancel
            </button>
            <button
              type="submit"
              className="submit-button"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <span className="loading-spinner mr-2"></span>
                  Saving...
                </>
              ) : (
                <>
                  <HiCheck className="mr-1" />
                  {isEditMode ? "Update" : "Create"}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateRecorderModal;
