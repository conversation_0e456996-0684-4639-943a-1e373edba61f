.footer {
  position: fixed;
  top: 5px;
  right: 20px;
  z-index: 100;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  pointer-events: none; /* Allows clicking through the footer */
}

.powered-by {
  display: flex;
  align-items: center;
  gap: 5px;
  background-color: rgba(18, 18, 18, 0.7);
  padding: 6px 10px;
  border-radius: 6px;
  backdrop-filter: blur(4px);
}

.powered-by-text {
  font-size: 10px;
  color: #888;
  font-weight: 300;
}

.powered-by-logo {
  height: 50px;
  width: auto;
  opacity: 0.9;
}

@media (max-width: 768px) {
  .footer {
    top: 5px;
    right: 15px;
  }

  .powered-by-logo {
    height: 40px;
  }

  .powered-by-text {
    font-size: 9px;
  }
}
