.navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 2rem;
  background-color: #1a1a1a;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid #333;
}

.nav-logo {
  display: flex;
  align-items: center;
}

.nav-logo-image {
  height: 40px;
  width: auto;
}

.nav-links {
  display: flex;
  list-style: none;
  gap: 0.5rem;
  margin: 0;
  padding: 0;
  align-items: center;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  transition: all 0.3s;
  border-radius: 4px;
  font-size: 0.9rem;
}

.nav-icon {
  font-size: 1.2rem;
}

.nav-link:hover {
  color: #2196F3;
  background-color: rgba(33, 150, 243, 0.05);
}

.nav-link.active {
  color: #2196F3;
  background-color: rgba(33, 150, 243, 0.1);
  border-radius: 4px;
  font-weight: 500;
}

.user-info .nav-link {
  color: #ffa500;
  background-color: rgba(255, 165, 0, 0.1);
  border-radius: 4px;
  margin-left: 1rem;
}

.logout-button {
  background: none;
  border: none;
  cursor: pointer;
  color: #ff6b6b;
  transition: all 0.3s;
}

.logout-button:hover {
  background-color: rgba(255, 107, 107, 0.1);
}

@media (max-width: 768px) {
  .navigation {
    flex-direction: column;
    padding: 0.5rem;
  }

  .nav-logo {
    margin-bottom: 0.5rem;
  }

  .nav-logo-image {
    height: 30px;
  }

  .nav-links {
    width: 100%;
    justify-content: space-around;
    flex-wrap: wrap;
  }

  .nav-link {
    flex-direction: column;
    font-size: 0.8rem;
    padding: 0.5rem;
  }

  .nav-icon {
    font-size: 1.5rem;
  }

  .user-info .nav-link {
    margin-left: 0;
  }

  .user-info, .logout-button {
    margin-top: 0.5rem;
  }
}
