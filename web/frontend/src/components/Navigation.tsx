import { NavLink, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import { toast } from "react-toastify";
import "./Navigation.css";
import logoImage from "../assets/images/logo.png";
import {
  HiHome,
  HiLogout,
  HiCalendar,
  HiUser,
  HiVideoCamera,
  HiFolderOpen,
  HiCog,
  HiServer,
  HiChartBar,
} from "react-icons/hi";

const Navigation = () => {
  const navigate = useNavigate();
  const [username, setUsername] = useState<string>("");
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    // Get user info from localStorage
    const userJson = localStorage.getItem("user");
    if (userJson) {
      try {
        const user = JSON.parse(userJson);
        setUsername(user.username);
        setIsAdmin(user.role === "admin");
      } catch (error) {
        console.error("Failed to parse user data:", error);
      }
    }
  }, []);

  const handleLogout = () => {
    // Clear authentication data
    localStorage.removeItem("token");
    localStorage.removeItem("user");

    // Show success message
    toast.success("Logged out successfully");

    // Redirect to login page
    navigate("/login");
  };

  return (
    <nav className="navigation">
      <div className="nav-logo">
        <img
          src={logoImage}
          alt="Showfer Media Logo"
          className="nav-logo-image"
        />
      </div>
      <ul className="nav-links">
        <li>
          <NavLink to="/" end className="nav-link">
            <HiHome className="nav-icon" />
            <span>Home</span>
          </NavLink>
        </li>
        <li>
          <NavLink to="/scheduler" className="nav-link">
            <HiCalendar className="nav-icon" />
            <span>Scheduler</span>
          </NavLink>
        </li>
        <li>
          <NavLink to="/file_manager" className="nav-link">
            <HiFolderOpen className="nav-icon" />
            <span>File Manager</span>
          </NavLink>
        </li>
        <li>
          <NavLink to="/recorder" className="nav-link">
            <HiVideoCamera className="nav-icon" />
            <span>Input Feeds</span>
          </NavLink>
        </li>
        <li>
          <NavLink to="/analytics" className="nav-link">
            <HiChartBar className="nav-icon" />
            <span>Analytics</span>
          </NavLink>
        </li>
        <li>
          <NavLink to="/system" className="nav-link">
            <HiServer className="nav-icon" />
            <span>System</span>
          </NavLink>
        </li>
        {isAdmin && (
          <li>
            <NavLink to="/admin" className="nav-link">
              <HiCog className="nav-icon" />
              <span>Admin</span>
            </NavLink>
          </li>
        )}
        <li className="user-info">
          <div className="nav-link">
            <HiUser className="nav-icon" />
            <span>{username}</span>
          </div>
        </li>
        <li>
          <button onClick={handleLogout} className="nav-link logout-button">
            <HiLogout className="nav-icon" />
            <span>Logout</span>
          </button>
        </li>
      </ul>
    </nav>
  );
};

export default Navigation;
