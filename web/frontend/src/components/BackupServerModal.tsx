import React, { useEffect, useState } from 'react';
import { HiS<PERSON>r, HiStatusOnline, HiDatabase, HiRefresh } from 'react-icons/hi';
import { Spinner } from '@nextui-org/react';
import './BackupServerModal.css';

interface BackupServerModalProps {
  isVisible: boolean;
  onClose?: () => void;
}

interface ServerInfo {
  serverType: string;
  backupIP: string;
  primaryIP: string;
}

interface RestoreStatus {
  server_type: string;
  dump_file_exists: boolean;
  dump_file_size: number;
  dump_file_modified: string;
  last_checked: string;
}

const BackupServerModal: React.FC<BackupServerModalProps> = ({ isVisible }) => {
  const [serverInfo, setServerInfo] = useState<ServerInfo | null>(null);
  const [restoreStatus, setRestoreStatus] = useState<RestoreStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [, setLastUpdate] = useState<Date>(new Date());

  const fetchServerInfo = async () => {
    try {
      // Fetch server type
      const serverTypeResponse = await fetch('/api/server-type');
      const serverTypeData = await serverTypeResponse.json();

      // Fetch backup IP
      const backupIPResponse = await fetch('/api/backup-ip');
      const backupIPData = await backupIPResponse.json();

      // Fetch primary IP (if available)
      const primaryIPResponse = await fetch('/api/primary-ip');
      const primaryIPData = await primaryIPResponse.json();

      // Fetch restore status
      const restoreStatusResponse = await fetch('/api/restore-status');
      const restoreStatusData = await restoreStatusResponse.json();

      setServerInfo({
        serverType: serverTypeData.server_type,
        backupIP: backupIPData.backup_ip || '',
        primaryIP: primaryIPData.primary_ip || ''
      });

      setRestoreStatus(restoreStatusData);
      setLastUpdate(new Date());
    } catch (error) {
      console.error('Failed to fetch server info:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isVisible) {
      fetchServerInfo();
      
      // Refresh status every 10 seconds
      const interval = setInterval(fetchServerInfo, 10000);
      return () => clearInterval(interval);
    }
  }, [isVisible]);

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Never';
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return 'Invalid date';
    }
  };

  if (!isVisible) return null;

  return (
    <div className="backup-modal-overlay">
      <div className="backup-modal-content">
        <div className="backup-modal-header">
          <div className="backup-icon">
            <HiServer size={48} />
          </div>
          <h1>Backup Server Mode</h1>
          <p>This server is currently operating as a backup server</p>
        </div>

        <div className="backup-modal-body">
          {loading ? (
            <div className="loading-container">
              <Spinner size="lg" color="primary" />
              <p>Loading server information...</p>
            </div>
          ) : (
            <>
              <div className="server-info-grid">
                <div className="info-card">
                  <div className="info-icon">
                    <HiStatusOnline size={24} />
                  </div>
                  <div className="info-content">
                    <h3>Server Status</h3>
                    <p className="status-active">Active Backup Server</p>
                    <small>Monitoring primary server for changes</small>
                  </div>
                </div>

                <div className="info-card">
                  <div className="info-icon">
                    <HiServer size={24} />
                  </div>
                  <div className="info-content">
                    <h3>Primary Server</h3>
                    <p>{serverInfo?.primaryIP || 'Not configured'}</p>
                    <small>Source server for backup sync</small>
                  </div>
                </div>

                <div className="info-card">
                  <div className="info-icon">
                    <HiDatabase size={24} />
                  </div>
                  <div className="info-content">
                    <h3>Database Sync</h3>
                    <p className={restoreStatus?.dump_file_exists ? 'status-active' : 'status-inactive'}>
                      {restoreStatus?.dump_file_exists ? 'Synchronized' : 'No backup data'}
                    </p>
                    {restoreStatus?.dump_file_exists && (
                      <small>
                        Size: {formatFileSize(restoreStatus.dump_file_size)} | 
                        Updated: {formatDate(restoreStatus.dump_file_modified)}
                      </small>
                    )}
                  </div>
                </div>
              </div>

              <div className="backup-actions">
                <button 
                  className="refresh-button"
                  onClick={fetchServerInfo}
                  disabled={loading}
                >
                  <HiRefresh />
                  <span>Refresh Status</span>
                </button>
              </div>

              <div className="backup-note">
                <p>
                  <strong>Note:</strong> This server is in backup mode and the main application interface is disabled. 
                  To access the full interface, change the server type to "Primary" in the administration settings.
                </p>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default BackupServerModal; 