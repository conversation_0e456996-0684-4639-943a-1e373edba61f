.upload-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.upload-modal {
  background: rgba(31, 41, 55, 0.95);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  width: 90%;
  max-width: 500px;
  max-height: 70vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
  animation: slideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, sans-serif;
}

@keyframes slideUp {
  from {
    transform: translateY(20px) scale(0.95);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

.upload-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  background: rgba(255, 255, 255, 0.05);
}

.upload-modal-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.upload-modal-icon {
  width: 24px;
  height: 24px;
  color: #3b82f6;
}

.upload-modal-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
}

.upload-modal-close {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: #9ca3af;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 16px;
}

.upload-modal-close:hover {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.upload-modal-content {
  padding: 20px;
  max-height: calc(70vh - 100px);
  overflow-y: auto;
}

.upload-summary {
  margin-bottom: 20px;
}

.upload-summary-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
}

.upload-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.upload-stat-label {
  font-size: 11px;
  color: #6b7280;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.upload-stat-value {
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
}

.upload-stat-value.success {
  color: #10b981;
}

.upload-stat-value.error {
  color: #ef4444;
}

.upload-overall-progress {
  margin-bottom: 16px;
}

.upload-progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.upload-progress-text {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
}

.upload-progress-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.upload-progress-percentage {
  font-size: 14px;
  font-weight: 700;
  color: #ffffff;
}

.upload-speed-display {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #9ca3af;
}

.cloud-icon {
  width: 14px;
  height: 14px;
}

.upload-progress-bar-modal {
  height: 6px;
}

.upload-progress-track-modal {
  background: rgba(55, 65, 81, 0.8);
  border-radius: 3px;
}

.upload-progress-indicator-modal {
  background: linear-gradient(90deg, #3b82f6, #10b981);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.upload-files-list {
  margin-top: 16px;
}

.upload-files-title {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 12px;
}

.upload-files-container {
  max-height: 200px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.upload-files-container::-webkit-scrollbar {
  width: 4px;
}

.upload-files-container::-webkit-scrollbar-track {
  background: rgba(55, 65, 81, 0.5);
  border-radius: 2px;
}

.upload-files-container::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 2px;
}

.upload-files-container::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

.upload-file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.upload-file-item:hover {
  background: rgba(255, 255, 255, 0.08);
}

.upload-file-info {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
  min-width: 0;
}

.file-status-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.file-status-icon.uploading {
  color: #3b82f6;
}

.file-status-icon.success {
  color: #10b981;
}

.file-status-icon.error {
  color: #ef4444;
}

.file-status-icon.queued {
  color: #6b7280;
}

.upload-file-details {
  flex: 1;
  min-width: 0;
}

.upload-file-name {
  font-size: 13px;
  font-weight: 500;
  color: #ffffff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
}

.upload-file-size {
  font-size: 11px;
  color: #9ca3af;
}

.upload-file-progress {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  min-width: 60px;
}

.upload-file-percentage {
  font-size: 11px;
  font-weight: 600;
  color: #ffffff;
}

.upload-file-progress-bar {
  width: 60px;
  height: 4px;
}

.upload-file-progress-track {
  background: rgba(55, 65, 81, 0.8);
  border-radius: 2px;
}

.upload-file-progress-indicator {
  border-radius: 2px;
}

.upload-modal-animation {
  padding: 0 20px 16px 20px;
  display: flex;
  justify-content: center;
}

.upload-modal-dots {
  display: flex;
  gap: 4px;
}

.upload-modal-dot {
  width: 6px;
  height: 6px;
  background: #3b82f6;
  border-radius: 50%;
  animation: dotPulse 1.4s infinite ease-in-out;
}

.upload-modal-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.upload-modal-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes dotPulse {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Mobile responsive */
@media (max-width: 640px) {
  .upload-modal {
    width: 95%;
    max-height: 80vh;
  }

  .upload-modal-header {
    padding: 16px;
  }

  .upload-modal-content {
    padding: 16px;
  }

  .upload-summary-stats {
    gap: 16px;
  }

  .upload-progress-info {
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
  }

  .upload-file-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .upload-file-progress {
    align-items: stretch;
  }

  .upload-file-progress-bar {
    width: 100%;
  }
}
