.global-upload-status {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 360px;
  background: linear-gradient(
    135deg,
    rgba(15, 23, 42, 0.95) 0%,
    rgba(30, 41, 59, 0.95) 50%,
    rgba(15, 23, 42, 0.95) 100%
  );
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 20px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(148, 163, 184, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  z-index: 1000;
  animation: slideInGlow 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  overflow: hidden;
}

.global-upload-status::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(
    90deg,
    #06b6d4 0%,
    #3b82f6 25%,
    #8b5cf6 50%,
    #ec4899 75%,
    #f59e0b 100%
  );
  background-size: 200% 100%;
  animation: borderFlow 3s ease-in-out infinite;
}

@keyframes slideInGlow {
  0% {
    transform: translateX(100%) translateY(-10px) scale(0.95);
    opacity: 0;
  }
  60% {
    transform: translateX(-5px) translateY(0) scale(1.02);
    opacity: 0.9;
  }
  100% {
    transform: translateX(0) translateY(0) scale(1);
    opacity: 1;
  }
}

@keyframes borderFlow {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.upload-status-header {
  display: flex;
  align-items: center;
  padding: 24px;
  gap: 16px;
}

.status-icon {
  width: 32px;
  height: 32px;
  flex-shrink: 0;
  padding: 6px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.status-icon svg {
  width: 20px;
  height: 20px;
}

.status-icon.uploading {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.2),
    rgba(147, 197, 253, 0.1)
  );
  color: #60a5fa;
  animation: iconPulse 2s ease-in-out infinite;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.status-icon.success {
  background: linear-gradient(
    135deg,
    rgba(16, 185, 129, 0.2),
    rgba(110, 231, 183, 0.1)
  );
  color: #34d399;
  animation: successGlow 0.6s ease-out;
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
}

.status-icon.error {
  background: linear-gradient(
    135deg,
    rgba(239, 68, 68, 0.2),
    rgba(252, 165, 165, 0.1)
  );
  color: #f87171;
  animation: errorShake 0.5s ease-out;
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
}

.status-icon.restored {
  background: linear-gradient(
    135deg,
    rgba(245, 158, 11, 0.2),
    rgba(251, 191, 36, 0.1)
  );
  color: #fbbf24;
  animation: restoredGlow 1s ease-out;
  box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
}

@keyframes iconPulse {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
  }
}

@keyframes successGlow {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 30px rgba(16, 185, 129, 0.6);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes errorShake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-3px);
  }
  75% {
    transform: translateX(3px);
  }
}

@keyframes restoredGlow {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 30px rgba(245, 158, 11, 0.6);
  }
  100% {
    transform: scale(1);
  }
}

.upload-status-info {
  flex: 1;
  min-width: 0;
}

.upload-status-text {
  font-size: 16px;
  font-weight: 700;
  color: #f8fafc;
  margin-bottom: 6px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  letter-spacing: 0.01em;
}

.current-file-name {
  font-size: 13px;
  color: rgba(148, 163, 184, 0.9);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.05);
  padding: 4px 8px;
  border-radius: 6px;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.upload-status-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.action-button {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  position: relative;
  overflow: hidden;
  font-size: 16px;
}

.action-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.15),
    transparent
  );
  transition: left 0.6s;
}

.action-button:hover::before {
  left: 100%;
}

.view-button {
  color: #94a3b8;
}

.view-button:hover {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.25) 0%,
    rgba(59, 130, 246, 0.1) 100%
  );
  color: #60a5fa;
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4),
    0 0 0 1px rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.3);
}

.close-button {
  color: #94a3b8;
}

.close-button:hover {
  background: linear-gradient(
    135deg,
    rgba(239, 68, 68, 0.25) 0%,
    rgba(239, 68, 68, 0.1) 100%
  );
  color: #f87171;
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4),
    0 0 0 1px rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.3);
}

.action-button:active {
  transform: translateY(-1px) scale(1.02);
}

.upload-progress-section {
  padding: 0 24px 20px 24px;
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.02) 0%,
    rgba(255, 255, 255, 0.01) 100%
  );
  border-top: 1px solid rgba(148, 163, 184, 0.1);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-top: 20px;
}

.progress-percentage {
  font-size: 18px;
  font-weight: 800;
  color: #f8fafc;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, #60a5fa, #a855f7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.upload-speed {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: rgba(148, 163, 184, 0.9);
  font-weight: 600;
  background: rgba(255, 255, 255, 0.05);
  padding: 6px 12px;
  border-radius: 20px;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.cloud-icon {
  width: 16px;
  height: 16px;
  animation: float 3s ease-in-out infinite;
  color: #60a5fa;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-3px);
  }
}

.upload-progress-bar-mini {
  width: 100%;
  height: 8px;
  background: rgba(71, 85, 105, 0.3);
  border-radius: 6px;
  overflow: hidden;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
  position: relative;
}

.upload-progress-track-mini {
  background: rgba(71, 85, 105, 0.3);
  height: 8px;
  border-radius: 6px;
}

.upload-progress-indicator-mini {
  background: linear-gradient(
    90deg,
    #06b6d4 0%,
    #3b82f6 25%,
    #8b5cf6 50%,
    #ec4899 75%,
    #f59e0b 100%
  );
  background-size: 200% 100%;
  height: 8px;
  border-radius: 6px;
  transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation: progressFlow 2s ease-in-out infinite;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
  position: relative;
}

.upload-progress-indicator-mini::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  animation: shimmer 1.5s infinite;
  border-radius: 6px;
}

@keyframes progressFlow {
  0%,
  100% {
    background-position: 0% 50%;
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
  }
  50% {
    background-position: 100% 50%;
    box-shadow: 0 0 30px rgba(139, 92, 246, 0.6);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.upload-animation {
  padding: 0 12px 12px 12px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.upload-dots {
  display: flex;
  gap: 8px;
  align-items: center;
}

.upload-dot {
  width: 8px;
  height: 8px;
  background: linear-gradient(135deg, #60a5fa, #a855f7);
  border-radius: 50%;
  animation: dotDance 1.4s infinite ease-in-out;
  box-shadow: 0 0 15px rgba(96, 165, 250, 0.5);
}

.upload-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.upload-dot:nth-child(2) {
  animation-delay: -0.16s;
}

.upload-dot:nth-child(3) {
  animation-delay: 0s;
}

@keyframes dotDance {
  0%,
  80%,
  100% {
    transform: scale(0.8) translateY(0);
    opacity: 0.6;
    box-shadow: 0 0 15px rgba(96, 165, 250, 0.3);
  }
  40% {
    transform: scale(1.2) translateY(-8px);
    opacity: 1;
    box-shadow: 0 0 25px rgba(168, 85, 247, 0.7);
  }
}

/* Mobile responsive */
@media (max-width: 640px) {
  .global-upload-status {
    top: 10px;
    right: 10px;
    left: 10px;
    width: auto;
    max-width: none;
    border-radius: 16px;
  }

  .upload-status-header {
    padding: 18px;
  }

  .upload-progress-section {
    padding: 0 18px 16px 18px;
  }

  .upload-animation {
    padding: 0 18px 18px 18px;
  }

  .action-button {
    width: 36px;
    height: 36px;
  }

  .progress-info {
    padding-top: 16px;
  }
}

.noti-close-button {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  position: relative;
  overflow: hidden;
  font-size: 16px;
  color: #94a3b8;
}

.noti-close-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.15),
    transparent
  );
  transition: left 0.6s;
}

.noti-close-button:hover::before {
  left: 100%;
}

.noti-close-button:hover {
  background: linear-gradient(
    135deg,
    rgba(239, 68, 68, 0.25) 0%,
    rgba(239, 68, 68, 0.1) 100%
  );
  color: #f87171;
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4),
    0 0 0 1px rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.3);
}

.noti-close-button:active {
  transform: translateY(-1px) scale(1.02);
}
