import React, { useState, useEffect, useCallback, useMemo } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Progress } from "@nextui-org/react";
import {
  HiDocumentArrowUp,
  HiCloud,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>gle,
  HiArrowUturnLeft,
} from "react-icons/hi2";
import {
  selectShowStatusNotification,
  selectCurrentFile,
  selectTotalProgress,
  selectUploadSpeed,
  selectIsUploading,
  selectUploadFiles,
  hideStatusNotification,
  setShowModal,
} from "../../redux/uploadSlice";
import { useNavigate, useLocation } from "react-router-dom";
import "./GlobalUploadStatus.css";

const GlobalUploadStatus: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const [showRestored, setShowRestored] = useState(false);
  const [restorationTimer, setRestorationTimer] =
    useState<NodeJS.Timeout | null>(null);

  const showNotification = useSelector(selectShowStatusNotification);
  const currentFile = useSelector(selectCurrentFile);
  const totalProgress = useSelector(selectTotalProgress);
  const uploadSpeed = useSelector(selectUploadSpeed);
  const isUploading = useSelector(selectIsUploading);
  const files = useSelector(selectUploadFiles);

  // Memoized calculations for better performance
  const stats = useMemo(() => {
    const completed = files.filter(
      (file) => file.status === "completed"
    ).length;
    const failed = files.filter((file) => file.status === "failed").length;
    const total = files.length;

    return { completed, failed, total };
  }, [files]);

  // Show restoration indicator when files are present but not uploading (restored state)
  useEffect(() => {
    // Clear existing timer to prevent memory leaks
    if (restorationTimer) {
      clearTimeout(restorationTimer);
      setRestorationTimer(null);
    }

    if (stats.total > 0 && !isUploading && showNotification) {
      setShowRestored(true);

      // Set new timer
      const timer = setTimeout(() => {
        setShowRestored(false);
        setRestorationTimer(null);
      }, 3000);

      setRestorationTimer(timer);
    } else {
      setShowRestored(false);
    }

    // Cleanup function
    return () => {
      if (restorationTimer) {
        clearTimeout(restorationTimer);
        setRestorationTimer(null);
      }
    };
  }, [stats.total, isUploading, showNotification]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (restorationTimer) {
        clearTimeout(restorationTimer);
      }
    };
  }, []);

  const handleViewDetails = useCallback(() => {
    try {
      if (location.pathname !== "/file_manager") {
        navigate("/file_manager");
      }
      dispatch(setShowModal(true));
    } catch (error) {
      console.error("Error navigating to file manager:", error);
      // Fallback: just show modal without navigation
      dispatch(setShowModal(true));
    }
  }, [location.pathname, navigate, dispatch]);

  const handleClose = useCallback(() => {
    try {
      dispatch(hideStatusNotification());
    } catch (error) {
      console.error("Error hiding status notification:", error);
    }
  }, [dispatch]);

  const getStatusIcon = useCallback(() => {
    if (showRestored) {
      return <HiArrowUturnLeft className="status-icon restored" />;
    } else if (stats.failed > 0) {
      return <HiExclamationTriangle className="status-icon error" />;
    } else if (stats.completed === stats.total && stats.total > 0) {
      return <HiCheck className="status-icon success" />;
    } else {
      return <HiDocumentArrowUp className="status-icon uploading" />;
    }
  }, [showRestored, stats.failed, stats.completed, stats.total]);

  const getStatusText = useCallback(() => {
    if (showRestored) {
      return `Session restored (${stats.total} file${
        stats.total !== 1 ? "s" : ""
      })`;
    } else if (stats.failed > 0) {
      return `Upload failed (${stats.failed}/${stats.total} file${
        stats.total !== 1 ? "s" : ""
      })`;
    } else if (stats.completed === stats.total && stats.total > 0) {
      return `Upload completed (${stats.total} file${
        stats.total !== 1 ? "s" : ""
      })`;
    } else {
      return `Uploading ${stats.total} file${stats.total !== 1 ? "s" : ""}`;
    }
  }, [showRestored, stats.failed, stats.completed, stats.total]);

  const getCurrentFileName = useCallback(() => {
    if (!currentFile || !isUploading) return null;

    const maxLength = 25;
    return currentFile.name.length > maxLength
      ? currentFile.name.substring(0, maxLength - 3) + "..."
      : currentFile.name;
  }, [currentFile, isUploading]);

  const getProgressColor = useCallback(() => {
    if (stats.failed > 0) return "danger";
    if (stats.completed === stats.total && stats.total > 0) return "success";
    return "primary";
  }, [stats.failed, stats.completed, stats.total]);

  // Don't render if no notification should be shown or no files
  if (!showNotification || stats.total === 0) {
    return null;
  }

  const currentFileName = getCurrentFileName();
  const progressColor = getProgressColor();
  const roundedProgress = Math.round(totalProgress);

  return (
    <div className="global-upload-status">
      <div className="upload-status-header">
        {getStatusIcon()}
        <div className="upload-status-info">
          <div className="upload-status-text">{getStatusText()}</div>
          {currentFileName && (
            <div className="current-file-name">{currentFileName}</div>
          )}
        </div>
        <div className="upload-status-actions">
          <button
            onClick={handleViewDetails}
            className="action-button view-button"
            title="View upload details"
            aria-label="View upload details"
          >
            <HiEye />
          </button>
          <button
            onClick={handleClose}
            className="noti-close-button"
            title="Close notification"
            aria-label="Close notification"
          >
            ×
          </button>
        </div>
      </div>

      <div className="upload-progress-section">
        <div className="progress-info">
          <span className="progress-percentage">{roundedProgress}%</span>
          {isUploading && uploadSpeed && (
            <div className="upload-speed">
              <HiCloud className="cloud-icon" />
              <span>{uploadSpeed}</span>
            </div>
          )}
        </div>

        <Progress
          value={totalProgress}
          color={progressColor}
          size="sm"
          radius="none"
          classNames={{
            base: "upload-progress-bar-mini",
            track: "upload-progress-track-mini",
            indicator: "upload-progress-indicator-mini",
          }}
          aria-label={`Upload progress: ${roundedProgress}%`}
          showValueLabel={false}
        />
      </div>

      {isUploading && (
        <div className="upload-animation">
          <div className="upload-dots">
            <div className="upload-dot"></div>
            <div className="upload-dot"></div>
            <div className="upload-dot"></div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GlobalUploadStatus;
