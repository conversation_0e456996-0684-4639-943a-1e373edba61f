import React from "react";
import { useSelector, useDispatch } from "react-redux";
import { Progress } from "@nextui-org/react";
import {
  HiDocumentArrowUp,
  HiCloud,
  HiCheck,
  HiExclamationTriangle,
} from "react-icons/hi2";
import {
  selectShowModal,
  selectUploadFiles,
  selectTotalProgress,
  selectUploadSpeed,
  selectIsUploading,
  setShowModal,
  hideStatusNotification,
} from "../../redux/uploadSlice";
import "./UploadModal.css";

const UploadModal: React.FC = () => {
  const dispatch = useDispatch();

  const showModal = useSelector(selectShowModal);
  const files = useSelector(selectUploadFiles);
  const totalProgress = useSelector(selectTotalProgress);
  const uploadSpeed = useSelector(selectUploadSpeed);
  const isUploading = useSelector(selectIsUploading);

  if (!showModal || files.length === 0) {
    return null;
  }

  const completedFiles = files.filter(
    (file) => file.status === "completed"
  ).length;
  const failedFiles = files.filter((file) => file.status === "failed").length;
  const totalFiles = files.length;

  const handleClose = () => {
    dispatch(setShowModal(false));
    if (!isUploading) {
      dispatch(hideStatusNotification());
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <HiCheck className="file-status-icon success" />;
      case "failed":
        return <HiExclamationTriangle className="file-status-icon error" />;
      case "uploading":
        return <HiDocumentArrowUp className="file-status-icon uploading" />;
      default:
        return <HiDocumentArrowUp className="file-status-icon queued" />;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
  };

  return (
    <div className="upload-modal-overlay">
      <div className="upload-modal">
        <div className="upload-modal-header">
          <div className="upload-modal-title">
            <HiDocumentArrowUp className="upload-modal-icon" />
            <h3>Upload Progress</h3>
          </div>
          <button
            onClick={handleClose}
            className="upload-modal-close"
            title="Close"
          >
            ×
          </button>
        </div>

        <div className="upload-modal-content">
          <div className="upload-summary">
            <div className="upload-summary-stats">
              <div className="upload-stat">
                <span className="upload-stat-label">Total Files</span>
                <span className="upload-stat-value">{totalFiles}</span>
              </div>
              <div className="upload-stat">
                <span className="upload-stat-label">Completed</span>
                <span className="upload-stat-value success">
                  {completedFiles}
                </span>
              </div>
              {failedFiles > 0 && (
                <div className="upload-stat">
                  <span className="upload-stat-label">Failed</span>
                  <span className="upload-stat-value error">{failedFiles}</span>
                </div>
              )}
            </div>

            <div className="upload-overall-progress">
              <div className="upload-progress-header">
                <span className="upload-progress-text">Overall Progress</span>
                <div className="upload-progress-info">
                  <span className="upload-progress-percentage">
                    {Math.round(totalProgress)}%
                  </span>
                  {isUploading && (
                    <div className="upload-speed-display">
                      <HiCloud className="cloud-icon" />
                      <span>{uploadSpeed}</span>
                    </div>
                  )}
                </div>
              </div>

              <Progress
                value={totalProgress}
                color={failedFiles > 0 ? "danger" : "primary"}
                size="lg"
                radius="sm"
                classNames={{
                  base: "upload-progress-bar-modal",
                  track: "upload-progress-track-modal",
                  indicator: "upload-progress-indicator-modal",
                }}
                aria-label="Overall upload progress"
                showValueLabel={false}
              />
            </div>
          </div>

          <div className="upload-files-list">
            <h4 className="upload-files-title">Files</h4>
            <div className="upload-files-container">
              {files.map((file) => (
                <div key={file.id} className="upload-file-item">
                  <div className="upload-file-info">
                    {getStatusIcon(file.status)}
                    <div className="upload-file-details">
                      <div className="upload-file-name">{file.name}</div>
                      <div className="upload-file-size">
                        {formatFileSize(file.size)}
                      </div>
                    </div>
                  </div>

                  <div className="upload-file-progress">
                    <span className="upload-file-percentage">
                      {Math.round(file.progress)}%
                    </span>
                    <Progress
                      value={file.progress}
                      color={
                        file.status === "failed"
                          ? "danger"
                          : file.status === "completed"
                          ? "success"
                          : "primary"
                      }
                      size="sm"
                      radius="sm"
                      classNames={{
                        base: "upload-file-progress-bar",
                        track: "upload-file-progress-track",
                        indicator: "upload-file-progress-indicator",
                      }}
                      aria-label={`Upload progress for ${file.name}`}
                      showValueLabel={false}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {isUploading && (
          <div className="upload-modal-animation">
            <div className="upload-modal-dots">
              <div className="upload-modal-dot"></div>
              <div className="upload-modal-dot"></div>
              <div className="upload-modal-dot"></div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default UploadModal;
