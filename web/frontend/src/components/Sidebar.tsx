import { NavLink, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import { toast } from "react-toastify";
import "./Sidebar.css";
import logoImage from "../assets/images/logo.png";
import logoIcon from "../assets/images/logo_icon.png";
import {
  HiHome,
  HiLogout,
  HiCalendar,
  HiUser,
  HiVideoCamera,
  HiFolderOpen,
  HiCog,
  HiServer,
  HiChevronLeft,
  HiChevronRight,
  HiChartBar,
  HiDocumentText,
} from "react-icons/hi";

interface SidebarProps {
  collapsed: boolean;
  toggleSidebar: () => void;
}

const Sidebar = ({ collapsed, toggleSidebar }: SidebarProps) => {
  const navigate = useNavigate();
  const [username, setUsername] = useState<string>("");
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    // Get user info from localStorage
    const userJson = localStorage.getItem("user");
    if (userJson) {
      try {
        const user = JSON.parse(userJson);
        setUsername(user.username);
        setIsAdmin(user.role === "admin");
      } catch (error) {
        console.error("Failed to parse user data:", error);
      }
    }
  }, []);

  const handleLogout = () => {
    // Clear authentication data
    localStorage.removeItem("token");
    localStorage.removeItem("user");

    // Show success message
    toast.success("Logged out successfully");

    // Redirect to login page
    navigate("/login");
  };

  return (
    <>
      {!collapsed && (
        <div className="sidebar-backdrop" onClick={toggleSidebar}></div>
      )}
      <aside className={`sidebar ${collapsed ? "sidebar-collapsed" : ""}`}>
        <div className="sidebar-header">
          <div className="sidebar-logo">
            <img
              src={collapsed ? logoIcon : logoImage}
              alt="Showfer Media Logo"
              className="sidebar-logo-image"
            />
          </div>
          <button
            className="sidebar-toggle"
            onClick={toggleSidebar}
            aria-label="Toggle Sidebar"
          >
            {collapsed ? <HiChevronRight /> : <HiChevronLeft />}
          </button>
        </div>

        <button
          className="mobile-menu-toggle"
          onClick={toggleSidebar}
          aria-label="Toggle Mobile Menu"
        >
          {collapsed ? <HiChevronRight /> : <HiChevronLeft />}
        </button>

        <div className="sidebar-content">
          <nav className="sidebar-nav">
            <ul className="sidebar-nav-list">
              <li className="sidebar-nav-item">
                <NavLink
                  to="/"
                  end
                  className={({ isActive }) =>
                    `sidebar-nav-link ${isActive ? "active" : ""}`
                  }
                >
                  <HiHome className="sidebar-icon" />
                  {!collapsed && (
                    <span className="sidebar-link-text">Home</span>
                  )}
                </NavLink>
              </li>
              <li className="sidebar-nav-item">
                <NavLink
                  to="/scheduler"
                  className={({ isActive }) =>
                    `sidebar-nav-link ${isActive ? "active" : ""}`
                  }
                >
                  <HiCalendar className="sidebar-icon" />
                  {!collapsed && (
                    <span className="sidebar-link-text">Scheduler</span>
                  )}
                </NavLink>
              </li>
              <li className="sidebar-nav-item">
                <NavLink
                  to="/file_manager"
                  className={({ isActive }) =>
                    `sidebar-nav-link ${isActive ? "active" : ""}`
                  }
                >
                  <HiFolderOpen className="sidebar-icon" />
                  {!collapsed && (
                    <span className="sidebar-link-text">File Manager</span>
                  )}
                </NavLink>
              </li>
              <li className="sidebar-nav-item">
                <NavLink
                  to="/recorder"
                  className={({ isActive }) =>
                    `sidebar-nav-link ${isActive ? "active" : ""}`
                  }
                >
                  <HiVideoCamera className="sidebar-icon" />
                  {!collapsed && (
                    <span className="sidebar-link-text">Input Feeds</span>
                  )}
                </NavLink>
              </li>
              <li className="sidebar-nav-item">
                <NavLink
                  to="/analytics"
                  className={({ isActive }) =>
                    `sidebar-nav-link ${isActive ? "active" : ""}`
                  }
                >
                  <HiChartBar className="sidebar-icon" />
                  {!collapsed && (
                    <span className="sidebar-link-text">Analytics</span>
                  )}
                </NavLink>
              </li>
              <li className="sidebar-nav-item">
                <NavLink
                  to="/system"
                  className={({ isActive }) =>
                    `sidebar-nav-link ${isActive ? "active" : ""}`
                  }
                >
                  <HiServer className="sidebar-icon" />
                  {!collapsed && (
                    <span className="sidebar-link-text">System</span>
                  )}
                </NavLink>
              </li>
              <li className="sidebar-nav-item">
                <NavLink
                  to="/logs"
                  className={({ isActive }) =>
                    `sidebar-nav-link ${isActive ? "active" : ""}`
                  }
                >
                  <HiDocumentText className="sidebar-icon" />
                  {!collapsed && (
                    <span className="sidebar-link-text">Logs</span>
                  )}
                </NavLink>
              </li>
              {isAdmin && (
                <li className="sidebar-nav-item">
                  <NavLink
                    to="/admin"
                    className={({ isActive }) =>
                      `sidebar-nav-link ${isActive ? "active" : ""}`
                    }
                  >
                    <HiCog className="sidebar-icon" />
                    {!collapsed && (
                      <span className="sidebar-link-text">Admin</span>
                    )}
                  </NavLink>
                </li>
              )}
            </ul>
          </nav>
        </div>

        <div className="sidebar-footer">
          <button
            onClick={handleLogout}
            className="sidebar-user-logout-inline"
            title={`Logout ${username}`}
            aria-label="Logout from application"
          >
            {collapsed ? (
              <div className="sidebar-user-logout-collapsed">
                <HiLogout className="sidebar-icon logout-icon" />
              </div>
            ) : (
              <div className="sidebar-user-logout-content">
                <div className="user-info-section">
                  <HiUser className="sidebar-icon user-icon" />
                  <span className="sidebar-user-name">{username}</span>
                </div>
                <HiLogout className="sidebar-icon logout-icon" title="Logout" />
              </div>
            )}
          </button>
        </div>
      </aside>
    </>
  );
};

export default Sidebar;
