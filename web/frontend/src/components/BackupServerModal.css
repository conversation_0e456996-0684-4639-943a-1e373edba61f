.backup-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  backdrop-filter: blur(10px);
}

.backup-modal-content {
  background: white;
  border-radius: 16px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
  max-width: 800px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.backup-modal-header {
  text-align: center;
  padding: 40px 40px 20px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-radius: 16px 16px 0 0;
}

.backup-icon {
  display: inline-block;
  padding: 16px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  margin-bottom: 16px;
}

.backup-modal-header h1 {
  margin: 0 0 8px;
  font-size: 24px;
  font-weight: 600;
}

.backup-modal-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 16px;
}

.backup-modal-body {
  padding: 40px;
}

.loading-container {
  text-align: center;
  padding: 40px;
}

.loading-container p {
  margin-top: 16px;
  color: #6b7280;
  font-size: 16px;
}

.server-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.info-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: flex-start;
  gap: 16px;
  transition: all 0.2s ease;
}

.info-card:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.info-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: #3b82f6;
  color: white;
  border-radius: 8px;
  flex-shrink: 0;
}

.info-content {
  flex: 1;
}

.info-content h3 {
  margin: 0 0 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.info-content p {
  margin: 0 0 4px;
  font-size: 14px;
  font-weight: 500;
}

.info-content small {
  color: #6b7280;
  font-size: 12px;
}

.status-active {
  color: #059669 !important;
}

.status-inactive {
  color: #dc2626 !important;
}

.backup-details {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
}

.backup-details h3 {
  margin: 0 0 16px;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e2e8f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: 500;
  color: #4b5563;
  font-size: 14px;
}

.detail-value {
  font-weight: 600;
  color: #1f2937;
  font-size: 14px;
  text-align: right;
}

.backup-actions {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
}

.refresh-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.refresh-button:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.refresh-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.backup-note {
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
}

.backup-note p {
  margin: 0;
  color: #92400e;
  font-size: 14px;
  line-height: 1.5;
}

.backup-note strong {
  color: #b45309;
}

/* Responsive design */
@media (max-width: 768px) {
  .backup-modal-content {
    width: 95%;
    margin: 20px;
  }
  
  .backup-modal-header,
  .backup-modal-body {
    padding: 24px;
  }
  
  .server-info-grid {
    grid-template-columns: 1fr;
  }
  
  .details-grid {
    grid-template-columns: 1fr;
  }
  
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .detail-value {
    text-align: left;
  }
} 