/* Network Settings specific styles */
.admin-card-header {
  padding: 0.75rem 1rem !important;
}

.admin-card-header .admin-card-title {
  font-size: 1rem !important;
}

.admin-card-body {
  padding: 0.75rem !important;
}

/* Network card styles */
.network-card {
  margin-bottom: 0.75rem !important;
}

.network-card-header {
  padding: 0.75rem 1rem !important;
}

.network-card-title {
  font-size: 1rem !important;
}

.network-card-subtitle {
  font-size: 0.8rem !important;
  margin-top: 0.15rem !important;
}

.network-card-body {
  padding: 0.75rem !important;
}

/* Form elements */
.network-form-grid {
  gap: 0.75rem !important;
}

.form-group {
  margin-bottom: 0.5rem !important;
}

.form-label {
  margin-bottom: 0.25rem !important;
  font-size: 0.8rem !important;
}

.form-input {
  padding: 0.5rem 0.75rem !important;
  font-size: 0.85rem !important;
  height: 2.25rem !important;
}

.compact-input {
  height: 2rem !important;
  padding: 0.35rem 0.5rem !important;
  font-size: 0.8rem !important;
}

/* Status chips */
.status-chip {
  padding: 0.25rem 0.5rem !important;
  font-size: 0.7rem !important;
}

/* Loading and empty states */
.loading-container {
  padding: 1rem !important;
}

.empty-state {
  padding: 1.5rem !important;
  margin: 1rem 0 !important;
}

.empty-state-icon {
  font-size: 2rem !important;
  margin-bottom: 0.5rem !important;
}

.empty-state-text {
  font-size: 0.9rem !important;
}

/* Space between network cards */
.space-y-6 > * + * {
  margin-top: 0.75rem !important;
}

/* Error state styles */
.border-red-500 {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2) !important;
}

.text-red-500 {
  color: #ef4444 !important;
}

/* Update button styles */
.update-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  min-width: 120px;
}

.update-button:hover:not(:disabled) {
  background-color: #1976d2;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(33, 150, 243, 0.3);
}

.update-button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.update-button:active {
  transform: translateY(0);
}
