import { useState, useEffect } from "react";
import { Spinner } from "@nextui-org/react";
import { toast } from "react-toastify";
import { getUsers } from "@/api/authApi";
import { getPendingUsers, approveUser, rejectUser } from "@/api/adminApi";
import { User } from "@/types/auth";
import CreateUserModal from "./CreateUserModal";
import EditUserModal from "./EditUserModal";
import DeleteUserModal from "./DeleteUserModal";
import {
  HiCheck,
  HiX,
  HiRefresh,
  HiUserGroup,
  HiExclamation,
  HiOutlineUserAdd,
  HiShieldCheck,
  HiPencil,
  HiTrash,
} from "react-icons/hi";

const UserManagement = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [pendingUsers, setPendingUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const data = await getUsers();
      setUsers(data);
    } catch (error) {
      console.error("Failed to fetch users:", error);
      toast.error("Failed to fetch users");
    } finally {
      setLoading(false);
    }
  };

  const fetchPendingUsers = async () => {
    setLoading(true);
    try {
      const data = await getPendingUsers();
      setPendingUsers(data);
    } catch (error) {
      console.error("Failed to fetch pending users:", error);
      toast.error("Failed to fetch pending users");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
    fetchPendingUsers();
  }, []);

  const handleApproveUser = async (id: number) => {
    try {
      await approveUser(id);
      toast.success("User approved successfully");
      fetchUsers();
      fetchPendingUsers();
    } catch (error) {
      console.error("Failed to approve user:", error);
      toast.error("Failed to approve user");
    }
  };

  const handleRejectUser = async (id: number) => {
    try {
      await rejectUser(id);
      toast.success("User rejected successfully");
      fetchUsers();
      fetchPendingUsers();
    } catch (error) {
      console.error("Failed to reject user:", error);
      toast.error("Failed to reject user");
    }
  };

  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setIsEditModalOpen(true);
  };

  const handleDeleteUser = (user: User) => {
    setSelectedUser(user);
    setIsDeleteModalOpen(true);
  };

  return (
    <div>
      {/* All Users Card */}
      <div className="admin-card">
        <div className="admin-card-header">
          <h2 className="admin-card-title">
            <HiUserGroup className="admin-card-icon" />
            <span>User Management</span>
          </h2>
          <button
            className="create-user-button"
            onClick={() => setIsCreateModalOpen(true)}
          >
            <HiOutlineUserAdd className="button-icon" />
            <span>Create User</span>
          </button>
        </div>
        <div className="admin-card-body">
          {loading ? (
            <div className="loading-container">
              <div className="loading-text">
                <Spinner size="sm" color="primary" />
                <span>Loading users...</span>
              </div>
            </div>
          ) : users.length === 0 ? (
            <div className="empty-state">
              <div className="empty-state-icon">
                <HiUserGroup />
              </div>
              <p className="empty-state-text">No users found in the system.</p>
            </div>
          ) : (
            <div className="admin-table-container">
              <table className="admin-table">
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Username</th>
                    <th>Email</th>
                    <th>Role</th>
                    <th>Status</th>
                    <th>Created At</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {users.map((user) => (
                    <tr key={user.id}>
                      <td>{user.id}</td>
                      <td>{user.username}</td>
                      <td>{user.email}</td>
                      <td>
                        <div className={`role-chip ${user.role}`}>
                          {user.role === "admin" ? (
                            <>
                              <HiShieldCheck />
                              Admin
                            </>
                          ) : (
                            <>
                              <HiUserGroup />
                              User
                            </>
                          )}
                        </div>
                      </td>
                      <td>
                        <div className={`status-chip ${user.status}`}>
                          {user.status === "approved" ? (
                            <>
                              <HiCheck />
                              Approved
                            </>
                          ) : user.status === "pending" ? (
                            <>
                              <HiExclamation />
                              Pending
                            </>
                          ) : (
                            <>
                              <HiX />
                              Rejected
                            </>
                          )}
                        </div>
                      </td>
                      <td>{new Date(user.created_at).toLocaleString()}</td>
                      <td>
                        <div className="action-buttons">
                          {user.status === "pending" && (
                            <>
                              <button
                                className="action-button approve"
                                onClick={() => handleApproveUser(user.id)}
                              >
                                <HiCheck />
                                Approve
                              </button>
                              <button
                                className="action-button reject"
                                onClick={() => handleRejectUser(user.id)}
                              >
                                <HiX />
                                Reject
                              </button>
                            </>
                          )}
                          <button
                            className="action-button edit"
                            onClick={() => handleEditUser(user)}
                            title={
                              user.username === "admin"
                                ? "Admin user can only change email and password"
                                : "Edit user"
                            }
                            disabled={false}
                          >
                            <HiPencil />
                          </button>
                          <button
                            className="action-button delete"
                            onClick={() => handleDeleteUser(user)}
                            title={
                              user.username === "admin"
                                ? "Cannot delete admin user"
                                : "Delete user"
                            }
                            disabled={user.username === "admin"}
                          >
                            <HiTrash />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Pending Approvals Card */}
      {pendingUsers.length > 0 && (
        <div className="admin-card">
          <div className="admin-card-header">
            <h2 className="admin-card-title">
              <HiExclamation />
              Pending Approvals
            </h2>
            <div className="flex flex-col items-end gap-2">
              <button
                className="refresh-button"
                onClick={fetchPendingUsers}
                title="Refresh pending users"
              >
                <HiRefresh />
              </button>
            </div>
          </div>
          <div className="admin-card-body">
            {loading ? (
              <div className="loading-container">
                <div className="loading-text">
                  <Spinner size="sm" color="primary" />
                  <span>Loading pending users...</span>
                </div>
              </div>
            ) : (
              <div className="admin-table-container">
                <table className="admin-table">
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>Username</th>
                      <th>Email</th>
                      <th>Created At</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {pendingUsers.map((user) => (
                      <tr key={user.id}>
                        <td>{user.id}</td>
                        <td>{user.username}</td>
                        <td>{user.email}</td>
                        <td>{new Date(user.created_at).toLocaleString()}</td>
                        <td>
                          <div className="action-buttons">
                            <button
                              className="action-button approve"
                              onClick={() => handleApproveUser(user.id)}
                            >
                              <HiCheck />
                              Approve
                            </button>
                            <button
                              className="action-button reject"
                              onClick={() => handleRejectUser(user.id)}
                            >
                              <HiX />
                              Reject
                            </button>
                            <button
                              className="action-button edit"
                              onClick={() => handleEditUser(user)}
                              title={
                                user.username === "admin"
                                  ? "Admin user can only change email and password"
                                  : "Edit user"
                              }
                              disabled={false}
                            >
                              <HiPencil />
                              Edit
                            </button>
                            <button
                              className="action-button delete"
                              onClick={() => handleDeleteUser(user)}
                              title={
                                user.username === "admin"
                                  ? "Cannot delete admin user"
                                  : "Delete user"
                              }
                              disabled={user.username === "admin"}
                            >
                              <HiTrash />
                              Delete
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      )}

      <CreateUserModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onUserCreated={fetchUsers}
      />

      <EditUserModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onUserUpdated={fetchUsers}
        user={selectedUser}
      />

      <DeleteUserModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onUserDeleted={fetchUsers}
        user={selectedUser}
      />
    </div>
  );
};

export default UserManagement;
