import { useState, useEffect } from "react";
import { Spin<PERSON> } from "@nextui-org/react";
import { toast } from "react-toastify";
import { getCodecSettings, updateCodecSettings } from "@/api/adminApi";
import { startRetranscoding } from "@/api/retranscodeApi";
import {
  CodecSettings as CodecSettingsType,
  CodecSettingsInput,
  VIDEO_CODEC_OPTIONS,
  AUDIO_CODEC_OPTIONS,
  AUDIO1_CODEC_OPTIONS,
  AUDIO2_CODEC_OPTIONS,
  AUDIO_CHANNEL_OPTIONS,
  RESOLUTION_OPTIONS,
  FPS_OPTIONS,
  AUDIO_BITRATE_OPTIONS,
  SAMPLE_RATE_OPTIONS,
} from "@/types/codec";
import {
  HiRefresh,
  HiSave,
  HiVideoCamera,
  HiMicrophone,
  HiCog,
  HiPlay,
} from "react-icons/hi";
import RetranscodeStatus from "./RetranscodeStatus";
import "./CodecSettings.css";

const CodecSettings = () => {
  const [, setSettings] = useState<CodecSettingsType | null>(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [isValid, setIsValid] = useState(true);
  const [isRetranscoding, setIsRetranscoding] = useState(false);
  const [showRetranscodePrompt, setShowRetranscodePrompt] = useState(false);
  const [formData, setFormData] = useState<CodecSettingsInput>({
    vcodec: "h264",
    acodec: "aac_downmix",
    resolution: "1920x1080i",
    fps: 29.97,
    sample_rate: 48000,
    vbitrate: 6000,
    abitrate: 192,
    max_vbitrate: 10000,
    // New dual audio fields with defaults
    dual_audio_mode: false,
    audio1_codec: "ac3_passthrough",
    audio1_bitrate: 448,
    audio1_channels: 6,
    audio2_codec: "aac_downmix",
    audio2_bitrate: 192,
    audio2_channels: 2,
  });

  // Fetch codec settings
  const fetchSettings = async () => {
    try {
      setLoading(true);
      const data = await getCodecSettings();
      setSettings(data);

      const formDataFromServer: CodecSettingsInput = {
        vcodec: data.vcodec,
        acodec: data.acodec,
        resolution: data.resolution,
        fps: data.fps,
        sample_rate: data.sample_rate,
        vbitrate: data.vbitrate,
        abitrate: data.abitrate,
        max_vbitrate: data.max_vbitrate,
        // Handle dual audio fields with fallbacks for backward compatibility
        dual_audio_mode: data.dual_audio_mode || false,
        audio1_codec: data.audio1_codec || "ac3_passthrough",
        audio1_bitrate: data.audio1_bitrate || 448,
        audio1_channels: data.audio1_channels || 6,
        audio2_codec: data.audio2_codec || "aac_downmix",
        audio2_bitrate: data.audio2_bitrate || 192,
        audio2_channels: data.audio2_channels || 2,
      };

      setFormData(formDataFromServer);

      // If the loaded data has validation issues, show a warning
      // (validation state will be updated by the useEffect hook)
      if (!checkSettingsValidity(formDataFromServer)) {
        toast.warn(
          "The current codec settings have validation issues. Please correct them before saving."
        );
      }
    } catch (error) {
      console.error("Failed to fetch codec settings:", error);
      toast.error("Failed to load codec settings");
    } finally {
      setLoading(false);
    }
  };

  // Check if settings are valid without showing toast messages
  const checkSettingsValidity = (data: CodecSettingsInput): boolean => {
    // Check max output bitrate doesn't exceed 20,000 kbps
    if (data.max_vbitrate > 20000) {
      return false;
    }

    // Check video bitrate doesn't exceed max output bitrate
    if (data.vbitrate > data.max_vbitrate) {
      return false;
    }

    // Check video bitrate is at least 1,000 kbps
    if (data.vbitrate < 1000) {
      return false;
    }

    // For dual audio mode, include both audio tracks in validation
    if (data.dual_audio_mode) {
      const totalAudioBitrate = data.audio1_bitrate + data.audio2_bitrate;
      if (data.max_vbitrate <= data.vbitrate + totalAudioBitrate) {
        return false;
      }
    } else {
      // Single audio mode validation
      if (data.max_vbitrate <= data.vbitrate + data.abitrate) {
        return false;
      }
    }

    return true;
  };

  // Check if a specific field has validation errors
  const isFieldInvalid = (field: string): boolean => {
    const totalAudioBitrate = formData.dual_audio_mode
      ? formData.audio1_bitrate + formData.audio2_bitrate
      : formData.abitrate;

    if (field === "max_vbitrate") {
      return (
        formData.max_vbitrate > 20000 ||
        formData.max_vbitrate <= formData.vbitrate + totalAudioBitrate
      );
    }
    if (field === "vbitrate") {
      return (
        formData.vbitrate < 1000 || formData.vbitrate > formData.max_vbitrate
      );
    }
    if (field === "abitrate") {
      return (
        !formData.dual_audio_mode &&
        formData.vbitrate + formData.abitrate >= formData.max_vbitrate
      );
    }
    if (field === "audio1_bitrate" || field === "audio2_bitrate") {
      return (
        formData.dual_audio_mode &&
        formData.vbitrate + totalAudioBitrate >= formData.max_vbitrate
      );
    }
    return false;
  };

  // Validate codec settings (with toast messages for form submission)
  const validateSettings = (): boolean => {
    // Validate max output bitrate doesn't exceed 20,000 kbps
    if (formData.max_vbitrate > 20000) {
      toast.error("Max Output Bitrate cannot exceed 20,000 kbps");
      return false;
    }

    // Validate video bitrate doesn't exceed max output bitrate
    if (formData.vbitrate > formData.max_vbitrate) {
      toast.error("Video Bitrate cannot exceed Max Output Bitrate");
      return false;
    }

    // Validate video bitrate is at least 1,000 kbps
    if (formData.vbitrate < 1000) {
      toast.error("Video Bitrate must be at least 1,000 kbps");
      return false;
    }

    // Validate bitrate combinations based on audio mode
    if (formData.dual_audio_mode) {
      const totalAudioBitrate =
        formData.audio1_bitrate + formData.audio2_bitrate;
      if (formData.max_vbitrate <= formData.vbitrate + totalAudioBitrate) {
        toast.error(
          "Max Output Bitrate must be greater than the sum of Video Bitrate, Audio1 Bitrate, and Audio2 Bitrate"
        );
        return false;
      }
    } else {
      if (formData.max_vbitrate <= formData.vbitrate + formData.abitrate) {
        toast.error(
          "Max Output Bitrate must be greater than the sum of Video Bitrate and Audio Bitrate"
        );
        return false;
      }
    }

    return true;
  };

  // Start retranscoding process
  const handleStartRetranscoding = async () => {
    try {
      console.log(
        "-------------- Debug: Starting retranscoding process -----------------"
      );

      // Reset states before starting
      setIsRetranscoding(false);
      setShowRetranscodePrompt(false);

      const result = await startRetranscoding();
      toast.success(`Started retranscoding ${result.jobs_created} files`);
      setIsRetranscoding(true);
      setShowRetranscodePrompt(false);
    } catch (error) {
      console.error("Failed to start retranscoding:", error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to start retranscoding process";
      toast.error(errorMessage);

      // Reset states on error
      setIsRetranscoding(false);
      setShowRetranscodePrompt(false);
    }
  };

  // Handle retranscoding completion
  const handleRetranscodeComplete = () => {
    console.log(
      "-------------- Debug: Retranscoding completed -----------------"
    );
    setIsRetranscoding(false);
    // Don't automatically hide the prompt - let the status check determine if it should show
    checkRetranscodeStatusOnLoad(); // Re-check status to determine proper state
  };

  // Save codec settings
  const saveSettings = async () => {
    try {
      setSaving(true);

      // Validate settings before saving
      if (!validateSettings()) {
        setSaving(false);
        return;
      }

      await updateCodecSettings(formData);
      toast.success("Codec settings saved successfully");
      await fetchSettings(); // Refresh settings

      // Reset retranscode states to allow fresh attempts
      setIsRetranscoding(false);
      setShowRetranscodePrompt(false);

      // Check status to determine if we should show the retranscode prompt
      setTimeout(() => {
        checkRetranscodeStatusOnLoad();
      }, 500); // Small delay to ensure backend state is consistent

      // Show retranscoding prompt after successful save if no active retranscoding
      setTimeout(() => {
        if (!isRetranscoding) {
          setShowRetranscodePrompt(true);
        }
      }, 1000);
    } catch (error) {
      console.error("Failed to save codec settings:", error);
      toast.error("Failed to save codec settings");
    } finally {
      setSaving(false);
    }
  };

  // Handle form input changes
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = e.target;
    let newValue: string | number | boolean = value;
    let updatedFormData = { ...formData };

    // Handle checkbox for dual audio mode
    if (type === "checkbox") {
      newValue = (e.target as HTMLInputElement).checked;
      updatedFormData = {
        ...formData,
        [name]: newValue,
      };
    }
    // Convert numeric values
    else if (
      [
        "fps",
        "sample_rate",
        "vbitrate",
        "abitrate",
        "max_vbitrate",
        "audio1_bitrate",
        "audio2_bitrate",
        "audio1_channels",
        "audio2_channels",
      ].includes(name)
    ) {
      newValue = parseFloat(value);
      updatedFormData = {
        ...formData,
        [name]: newValue,
      };

      // Real-time validation for bitrate fields
      if (name === "max_vbitrate") {
        // Check if max_vbitrate exceeds 20,000 kbps
        if (newValue > 20000) {
          toast.warn("Max Output Bitrate cannot exceed 20,000 kbps");
        }

        // Check bitrate constraints based on audio mode
        const totalAudioBitrate = formData.dual_audio_mode
          ? formData.audio1_bitrate + formData.audio2_bitrate
          : formData.abitrate;

        if (newValue <= formData.vbitrate + totalAudioBitrate) {
          if (formData.dual_audio_mode) {
            toast.warn(
              "Max Output Bitrate must be greater than the sum of Video, Audio1, and Audio2 Bitrates"
            );
          } else {
            toast.warn(
              "Max Output Bitrate must be greater than the sum of Video and Audio Bitrates"
            );
          }
        }
      } else if (name === "vbitrate") {
        // Check if vbitrate is less than 1,000 kbps
        if (newValue < 1000) {
          toast.warn("Video Bitrate must be at least 1,000 kbps");
        }

        // Check if vbitrate exceeds max_vbitrate
        if (newValue > formData.max_vbitrate) {
          toast.warn("Video Bitrate cannot exceed Max Output Bitrate");
        }

        // Check sum constraints based on audio mode
        const totalAudioBitrate = formData.dual_audio_mode
          ? formData.audio1_bitrate + formData.audio2_bitrate
          : formData.abitrate;

        if (newValue + totalAudioBitrate >= formData.max_vbitrate) {
          if (formData.dual_audio_mode) {
            toast.warn(
              "Sum of Video, Audio1, and Audio2 Bitrates must be less than Max Output Bitrate"
            );
          } else {
            toast.warn(
              "Sum of Video and Audio Bitrates must be less than Max Output Bitrate"
            );
          }
        }
      } else if (name === "abitrate" && !formData.dual_audio_mode) {
        // Single audio mode validation
        if (formData.vbitrate + newValue >= formData.max_vbitrate) {
          toast.warn(
            "Sum of Video and Audio Bitrates must be less than Max Output Bitrate"
          );
        }
      } else if (
        (name === "audio1_bitrate" || name === "audio2_bitrate") &&
        formData.dual_audio_mode
      ) {
        // Dual audio mode validation
        const audio1Bitrate =
          name === "audio1_bitrate" ? newValue : formData.audio1_bitrate;
        const audio2Bitrate =
          name === "audio2_bitrate" ? newValue : formData.audio2_bitrate;
        const totalAudioBitrate = audio1Bitrate + audio2Bitrate;

        if (formData.vbitrate + totalAudioBitrate >= formData.max_vbitrate) {
          toast.warn(
            "Sum of Video, Audio1, and Audio2 Bitrates must be less than Max Output Bitrate"
          );
        }
      }
    } else {
      updatedFormData = {
        ...formData,
        [name]: value,
      };
    }

    // Update form data (validation will be handled by the useEffect hook)
    setFormData(updatedFormData);
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    saveSettings();
  };

  // Load settings on component mount and check retranscode status
  useEffect(() => {
    fetchSettings();
    checkRetranscodeStatusOnLoad();
  }, []);

  // Check retranscode status on component load to set proper states
  const checkRetranscodeStatusOnLoad = async () => {
    try {
      const { getRetranscodeStatus } = await import("@/api/retranscodeApi");
      const status = await getRetranscodeStatus();

      console.log(
        "-------------- Debug: Retranscode status check:",
        status,
        "-----------------"
      );

      if (status) {
        // If retranscoding is currently active, set the retranscoding state
        if (status.is_active && status.total_jobs > 0) {
          setIsRetranscoding(true);
          setShowRetranscodePrompt(false); // Hide prompt while actively retranscoding
        }
        // If there are completed/failed jobs that haven't been activated, show the prompt
        else if (
          !status.is_active &&
          status.total_jobs > 0 &&
          (status.completed_jobs > 0 || status.failed_jobs > 0)
        ) {
          setIsRetranscoding(false);
          setShowRetranscodePrompt(false); // Don't show start prompt when completed jobs exist
        }
        // No retranscode jobs or all have been processed and activated
        else {
          setIsRetranscoding(false);
          setShowRetranscodePrompt(false);
        }
      } else {
        // No status means no active retranscoding
        setIsRetranscoding(false);
        setShowRetranscodePrompt(false);
      }
    } catch (error) {
      console.error("Failed to check retranscode status:", error);
      setIsRetranscoding(false);
      setShowRetranscodePrompt(false);
    }
  };

  // Update validation state whenever form data changes
  useEffect(() => {
    const valid = checkSettingsValidity(formData);
    setIsValid(valid);
  }, [formData]);

  return (
    <div>
      {/* Retranscoding Status */}
      <RetranscodeStatus
        isRetranscoding={isRetranscoding}
        onRetranscodeComplete={handleRetranscodeComplete}
      />

      {/* Retranscoding Prompt */}
      {showRetranscodePrompt && !isRetranscoding && (
        <div className="retranscode-prompt-card">
          <div className="retranscode-prompt-header">
            <h3 className="retranscode-prompt-title">
              <HiPlay className="prompt-icon" />
              Apply New Codec Settings to Existing Files?
            </h3>
          </div>
          <div className="retranscode-prompt-body">
            <p className="prompt-description">
              Your codec settings have been saved successfully. Would you like
              to retranscode all existing files in the File Manager with these
              new settings?
            </p>
            <div className="prompt-buttons">
              <button
                onClick={handleStartRetranscoding}
                className="retranscode-start-button"
              >
                <HiPlay />
                <span>Start Retranscoding</span>
              </button>
              <button
                onClick={() => setShowRetranscodePrompt(false)}
                className="retranscode-skip-button"
              >
                Skip for Now
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="admin-card">
        <div className="admin-card-header">
          <h2 className="admin-card-title">
            <HiCog />
            Default Codec Settings
          </h2>
          <div className="flex items-center gap-3">
            <button
              className="refresh-button"
              onClick={fetchSettings}
              disabled={loading || saving}
            >
              <HiRefresh />
            </button>
          </div>
        </div>
        <div className="admin-card-body">
          {loading ? (
            <div className="loading-container">
              <div className="loading-text">
                <Spinner size="sm" color="primary" />
                <span>Loading codec settings...</span>
              </div>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-3">
              {/* Row 3: Max Video Bitrate and empty space */}
              <div className="codec-settings-section">
                <h3 className="section-title">
                  <HiVideoCamera />
                  Output Settings
                </h3>
                <div className="space-y-1">
                  <div className="flex gap-2">
                    <div className="codec-settings-content">
                      <div className="form-group compact flex-1">
                        <label htmlFor="max_vbitrate">
                          Max Output Bitrate (kbps)
                        </label>
                        <input
                          type="number"
                          id="max_vbitrate"
                          name="max_vbitrate"
                          value={formData.max_vbitrate}
                          onChange={handleChange}
                          min={formData.vbitrate + formData.abitrate + 1}
                          max="20000"
                          title="Max Output Bitrate must be greater than the sum of Video and Audio Bitrates, and cannot exceed 20,000 kbps"
                          className={`form-input compact ${
                            isFieldInvalid("max_vbitrate") ? "invalid" : ""
                          }`}
                        />
                      </div>
                      <div className="form-group compact flex-1">
                        {/* Empty space to maintain layout */}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="codec-settings-section">
                <h3 className="section-title">
                  <HiVideoCamera />
                  Video Settings
                </h3>
                <div className="codec-settings-content">
                  <div className="space-y-1">
                    {/* Row 1: Video Codec and Resolution */}
                    <div className="flex gap-2">
                      <div className="form-group compact flex-1">
                        <label htmlFor="vcodec">Video Codec</label>
                        <select
                          id="vcodec"
                          name="vcodec"
                          value={formData.vcodec}
                          onChange={handleChange}
                          className="form-select compact"
                        >
                          {VIDEO_CODEC_OPTIONS.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div className="form-group compact flex-1">
                        <label htmlFor="resolution">Resolution</label>
                        <select
                          id="resolution"
                          name="resolution"
                          value={formData.resolution}
                          onChange={handleChange}
                          className="form-select compact"
                        >
                          {RESOLUTION_OPTIONS.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>

                    {/* Row 2: FPS and Video Bitrate */}
                    <div className="flex gap-2">
                      <div className="form-group compact flex-1">
                        <label htmlFor="fps">FPS</label>
                        <select
                          id="fps"
                          name="fps"
                          value={formData.fps}
                          onChange={handleChange}
                          className="form-select compact"
                        >
                          {FPS_OPTIONS.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div className="form-group compact flex-1">
                        <label htmlFor="vbitrate">Video Bitrate (kbps)</label>
                        <input
                          type="number"
                          id="vbitrate"
                          name="vbitrate"
                          value={formData.vbitrate}
                          onChange={handleChange}
                          min="1000"
                          max={formData.max_vbitrate - formData.abitrate}
                          title="Video Bitrate must be at least 1,000 kbps and less than Max Output Bitrate minus Audio Bitrate"
                          className={`form-input compact ${
                            isFieldInvalid("vbitrate") ? "invalid" : ""
                          }`}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="codec-settings-section">
                <h3 className="section-title">
                  <HiMicrophone />
                  Audio Settings
                </h3>
                <div className="codec-settings-content">
                  <div className="space-y-1">
                    {/* Dual Audio Mode Toggle */}
                    <div className="dual-audio-toggle-row">
                      <label className="checkbox-label">
                        <input
                          type="checkbox"
                          name="dual_audio_mode"
                          checked={formData.dual_audio_mode}
                          onChange={handleChange}
                          className="form-checkbox"
                        />
                        <span className="checkbox-text">
                          Enable Dual Audio Mode (5.1 Surround + Stereo)
                        </span>
                        <span className="field-description">
                          {formData.dual_audio_mode
                            ? "Two audio tracks: 5.1 surround and stereo downmix"
                            : "Single audio track only"}
                        </span>
                      </label>
                      <div
                        className={`dual-audio-status ${
                          formData.dual_audio_mode ? "active" : ""
                        }`}
                      >
                        {formData.dual_audio_mode
                          ? "✓ Dual Audio"
                          : "Single Audio"}
                      </div>
                    </div>

                    {/* Sample Rate (shared for both modes) */}
                    <div className="flex gap-2">
                      <div className="form-group compact flex-1">
                        <label htmlFor="sample_rate">Sample Rate (Hz)</label>
                        <select
                          id="sample_rate"
                          name="sample_rate"
                          value={formData.sample_rate}
                          onChange={handleChange}
                          className="form-select compact"
                        >
                          {SAMPLE_RATE_OPTIONS.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div className="form-group compact flex-1">
                        {/* Empty space to maintain layout */}
                      </div>
                    </div>

                    {!formData.dual_audio_mode ? (
                      // Single Audio Mode
                      <>
                        <div className="flex gap-2">
                          <div className="form-group compact flex-1">
                            <label htmlFor="acodec">Audio Codec</label>
                            <select
                              id="acodec"
                              name="acodec"
                              value={formData.acodec}
                              onChange={handleChange}
                              className="form-select compact"
                            >
                              {AUDIO_CODEC_OPTIONS.map((option) => (
                                <option key={option.value} value={option.value}>
                                  {option.label}
                                </option>
                              ))}
                            </select>
                          </div>
                          <div className="form-group compact flex-1">
                            <label htmlFor="abitrate">
                              Audio Bitrate (kbps)
                            </label>
                            <select
                              id="abitrate"
                              name="abitrate"
                              value={formData.abitrate}
                              onChange={handleChange}
                              title={
                                isFieldInvalid("abitrate")
                                  ? "Sum of Video and Audio Bitrates must be less than Max Output Bitrate"
                                  : ""
                              }
                              className={`form-select compact ${
                                isFieldInvalid("abitrate") ? "invalid" : ""
                              }`}
                            >
                              {AUDIO_BITRATE_OPTIONS.map((option) => (
                                <option key={option.value} value={option.value}>
                                  {option.label}
                                </option>
                              ))}
                            </select>
                          </div>
                        </div>
                      </>
                    ) : (
                      // Dual Audio Mode
                      <>
                        {/* Audio Track 1 (5.1 Surround) */}
                        <div className="audio-track-section">
                          <h4 className="audio-track-title">
                            🔊 Audio Track 1 (5.1 Surround)
                          </h4>
                          <div className="flex gap-2">
                            <div className="form-group compact flex-1">
                              <label htmlFor="audio1_codec">Codec</label>
                              <select
                                id="audio1_codec"
                                name="audio1_codec"
                                value={formData.audio1_codec}
                                onChange={handleChange}
                                className="form-select compact"
                              >
                                {AUDIO1_CODEC_OPTIONS.map((option) => (
                                  <option
                                    key={option.value}
                                    value={option.value}
                                  >
                                    {option.label}
                                  </option>
                                ))}
                              </select>
                            </div>
                            <div className="form-group compact flex-1">
                              <label htmlFor="audio1_bitrate">
                                Bitrate (kbps)
                              </label>
                              <select
                                id="audio1_bitrate"
                                name="audio1_bitrate"
                                value={formData.audio1_bitrate}
                                onChange={handleChange}
                                title={
                                  isFieldInvalid("audio1_bitrate")
                                    ? "Sum of Video, Audio1, and Audio2 Bitrates must be less than Max Output Bitrate"
                                    : ""
                                }
                                className={`form-select compact ${
                                  isFieldInvalid("audio1_bitrate")
                                    ? "invalid"
                                    : ""
                                }`}
                              >
                                {AUDIO_BITRATE_OPTIONS.map((option) => (
                                  <option
                                    key={option.value}
                                    value={option.value}
                                  >
                                    {option.label}
                                  </option>
                                ))}
                              </select>
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <div className="form-group compact flex-1">
                              <label htmlFor="audio1_channels">Channels</label>
                              <select
                                id="audio1_channels"
                                name="audio1_channels"
                                value={formData.audio1_channels}
                                onChange={handleChange}
                                className="form-select compact"
                              >
                                {AUDIO_CHANNEL_OPTIONS.map((option) => (
                                  <option
                                    key={option.value}
                                    value={option.value}
                                  >
                                    {option.label}
                                  </option>
                                ))}
                              </select>
                            </div>
                            <div className="form-group compact flex-1">
                              {/* Empty space to maintain layout */}
                            </div>
                          </div>
                        </div>

                        {/* Audio Track 2 (Stereo) */}
                        <div className="audio-track-section">
                          <h4 className="audio-track-title">
                            🔊 Audio Track 2 (Stereo)
                          </h4>
                          <div className="flex gap-2">
                            <div className="form-group compact flex-1">
                              <label htmlFor="audio2_codec">Codec</label>
                              <select
                                id="audio2_codec"
                                name="audio2_codec"
                                value={formData.audio2_codec}
                                onChange={handleChange}
                                className="form-select compact"
                              >
                                {AUDIO2_CODEC_OPTIONS.map((option) => (
                                  <option
                                    key={option.value}
                                    value={option.value}
                                  >
                                    {option.label}
                                  </option>
                                ))}
                              </select>
                            </div>
                            <div className="form-group compact flex-1">
                              <label htmlFor="audio2_bitrate">
                                Bitrate (kbps)
                              </label>
                              <select
                                id="audio2_bitrate"
                                name="audio2_bitrate"
                                value={formData.audio2_bitrate}
                                onChange={handleChange}
                                title={
                                  isFieldInvalid("audio2_bitrate")
                                    ? "Sum of Video, Audio1, and Audio2 Bitrates must be less than Max Output Bitrate"
                                    : ""
                                }
                                className={`form-select compact ${
                                  isFieldInvalid("audio2_bitrate")
                                    ? "invalid"
                                    : ""
                                }`}
                              >
                                {AUDIO_BITRATE_OPTIONS.map((option) => (
                                  <option
                                    key={option.value}
                                    value={option.value}
                                  >
                                    {option.label}
                                  </option>
                                ))}
                              </select>
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <div className="form-group compact flex-1">
                              <label htmlFor="audio2_channels">Channels</label>
                              <select
                                id="audio2_channels"
                                name="audio2_channels"
                                value={formData.audio2_channels}
                                onChange={handleChange}
                                className="form-select compact"
                              >
                                {AUDIO_CHANNEL_OPTIONS.map((option) => (
                                  <option
                                    key={option.value}
                                    value={option.value}
                                  >
                                    {option.label}
                                  </option>
                                ))}
                              </select>
                            </div>
                            <div className="form-group compact flex-1">
                              {/* Empty space to maintain layout */}
                            </div>
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>

              {!isValid && (
                <div className="validation-error-summary">
                  <p>Please correct the following validation errors:</p>
                  <ul>
                    {formData.max_vbitrate > 20000 && (
                      <li>Max Output Bitrate cannot exceed 20,000 kbps</li>
                    )}
                    {formData.vbitrate < 1000 && (
                      <li>Video Bitrate must be at least 1,000 kbps</li>
                    )}
                    {formData.vbitrate > formData.max_vbitrate && (
                      <li>Video Bitrate cannot exceed Max Output Bitrate</li>
                    )}
                    {formData.dual_audio_mode
                      ? // Dual audio mode validation errors
                        formData.max_vbitrate <=
                          formData.vbitrate +
                            formData.audio1_bitrate +
                            formData.audio2_bitrate && (
                          <li>
                            Max Output Bitrate must be greater than the sum of
                            Video, Audio1, and Audio2 Bitrates
                          </li>
                        )
                      : // Single audio mode validation errors
                        formData.max_vbitrate <=
                          formData.vbitrate + formData.abitrate && (
                          <li>
                            Max Output Bitrate must be greater than the sum of
                            Video and Audio Bitrates
                          </li>
                        )}
                  </ul>
                </div>
              )}

              <div className="seting-buttons">
                <button
                  type="submit"
                  className={`save-button-primary ${
                    !isValid ? "invalid-settings" : ""
                  }`}
                  disabled={loading || saving || !isValid || isRetranscoding}
                  title={
                    isRetranscoding
                      ? "Cannot save settings while retranscoding is in progress"
                      : !isValid
                      ? "Please correct validation errors before saving"
                      : "Save codec settings"
                  }
                >
                  {saving ? (
                    <>
                      <Spinner size="sm" color="white" />
                      <span>Saving...</span>
                    </>
                  ) : (
                    <>
                      <HiSave />
                      <span>Save Settings</span>
                    </>
                  )}
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default CodecSettings;
