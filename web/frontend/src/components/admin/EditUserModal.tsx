import { useState, useEffect } from 'react';
import { Spin<PERSON> } from '@nextui-org/react';
import { toast } from 'react-toastify';
import { updateUser } from '@/api/authApi';
import { User, UserInput } from '@/types/auth';
import { Hi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, HiShi<PERSON>Check, HiUserGroup, HiExclamation } from 'react-icons/hi';
import './CreateUserModal.css'; // Reuse the same styles

// Email validation function
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

interface EditUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUserUpdated: () => void;
  user: User | null;
}

const EditUserModal = ({ isOpen, onClose, onUserUpdated, user }: EditUserModalProps) => {
  const [formData, setFormData] = useState<UserInput>({
    username: '',
    email: '',
    password: '',
    role: 'user',
    status: 'pending'
  });
  const [loading, setLoading] = useState(false);

  // Initialize form data when user changes
  useEffect(() => {
    if (user) {
      setFormData({
        username: user.username,
        email: user.email,
        password: '', // Password is not included in the user data
        role: user.role,
        status: user.status
      });
    }
  }, [user]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleRoleChange = (value: string) => {
    setFormData(prev => ({ ...prev, role: value }));
  };

  const handleStatusChange = (value: string) => {
    setFormData(prev => ({ ...prev, status: value }));
  };

  const handleSubmit = async () => {
    if (!formData.username || !formData.email) {
      toast.error('Please fill in all required fields');
      return;
    }

    if (!isValidEmail(formData.email)) {
      toast.error('Please enter a valid email address');
      return;
    }

    // If password is provided, check its length
    if (formData.password && formData.password.length < 6) {
      toast.error('Password must be at least 6 characters long');
      return;
    }

    // Special handling for admin user
    if (user && user.username === 'admin') {
      // Admin username cannot be changed
      if (formData.username !== 'admin') {
        toast.error('Cannot change admin username');
        return;
      }

      // Admin role and status cannot be changed
      if (formData.role !== 'admin' || formData.status !== 'approved') {
        toast.error('Cannot change admin role or status');
        return;
      }
    }

    // If password is empty, remove it from the request
    const dataToSubmit = { ...formData };
    if (!dataToSubmit.password) {
      delete dataToSubmit.password;
    }

    setLoading(true);
    try {
      if (user) {
        await updateUser(user.id, dataToSubmit);
        toast.success('User updated successfully');
        onUserUpdated();
        onClose();
      }
    } catch (error: any) {
      console.error('Failed to update user:', error);

      if (error.response && error.response.data && error.response.data.error) {
        toast.error(error.response.data.error);
      } else {
        toast.error('Failed to update user. Please try again later.');
      }
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen || !user) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h2>Edit User</h2>
          <button className="close-button" onClick={onClose}>
            <HiX />
          </button>
        </div>
        <div className="modal-body">
          <div className="form-group">
            <label>Username</label>
            <input
              type="text"
              className={`modal-input ${user?.username === 'admin' ? 'bg-gray-700 cursor-not-allowed' : ''}`}
              name="username"
              value={formData.username}
              onChange={handleChange}
              placeholder="Enter username"
              required
              disabled={user?.username === 'admin'}
              title={user?.username === 'admin' ? 'Admin username cannot be changed' : ''}
            />
          </div>

          <div className="form-group">
            <label>Email</label>
            <input
              type="email"
              className="modal-input"
              name="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="Enter email"
              required
            />
          </div>

          <div className="form-group">
            <label>Password (leave empty to keep current)</label>
            <input
              type="password"
              className="modal-input"
              name="password"
              value={formData.password}
              onChange={handleChange}
              placeholder="Enter new password"
            />
          </div>

          <div className="form-group">
            <label>Role</label>
            <div className="radio-group">
              <label className="radio-option">
                <input
                  type="radio"
                  name="role"
                  value="user"
                  checked={formData.role === 'user'}
                  onChange={() => handleRoleChange('user')}
                  disabled={user?.username === 'admin'}
                />
                <div className={`radio-label user-role ${formData.role === 'user' ? 'active' : ''}`}>
                  <HiUserGroup className="radio-icon user-icon" />
                  <span>User</span>
                </div>
              </label>

              <label className="radio-option">
                <input
                  type="radio"
                  name="role"
                  value="admin"
                  checked={formData.role === 'admin'}
                  onChange={() => handleRoleChange('admin')}
                  disabled={user?.username === 'admin'}
                />
                <div className={`radio-label admin-role ${formData.role === 'admin' ? 'active' : ''}`}>
                  <HiShieldCheck className="radio-icon admin-icon" />
                  <span>Admin</span>
                </div>
              </label>
            </div>
          </div>

          <div className="form-group">
            <label>Status</label>
            <div className="radio-group">
              <label className="radio-option">
                <input
                  type="radio"
                  name="status"
                  value="approved"
                  checked={formData.status === 'approved'}
                  onChange={() => handleStatusChange('approved')}
                  disabled={user?.username === 'admin'}
                />
                <div className={`radio-label approved-status ${formData.status === 'approved' ? 'active' : ''}`}>
                  <HiCheck className="radio-icon approved-icon" />
                  <span>Approved</span>
                </div>
              </label>

              <label className="radio-option">
                <input
                  type="radio"
                  name="status"
                  value="pending"
                  checked={formData.status === 'pending'}
                  onChange={() => handleStatusChange('pending')}
                  disabled={user?.username === 'admin'}
                />
                <div className={`radio-label pending-status ${formData.status === 'pending' ? 'active' : ''}`}>
                  <HiExclamation className="radio-icon pending-icon" />
                  <span>Pending</span>
                </div>
              </label>

              <label className="radio-option">
                <input
                  type="radio"
                  name="status"
                  value="rejected"
                  checked={formData.status === 'rejected'}
                  onChange={() => handleStatusChange('rejected')}
                  disabled={user?.username === 'admin'}
                />
                <div className={`radio-label rejected-status ${formData.status === 'rejected' ? 'active' : ''}`}>
                  <HiX className="radio-icon rejected-icon" />
                  <span>Rejected</span>
                </div>
              </label>
            </div>
          </div>
        </div>
        <div className="modal-footer">
          <button
            className="modal-button cancel-button"
            onClick={onClose}
          >
            Cancel
          </button>
          <button
            className="modal-button confirm-button"
            onClick={handleSubmit}
            disabled={loading}
          >
            {loading ? (
              <>
                <Spinner size="sm" color="white" />
                <span>Updating...</span>
              </>
            ) : (
              <>
                <HiCheck />
                <span>Update User</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default EditUserModal;
