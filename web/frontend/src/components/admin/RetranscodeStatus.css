.retranscode-status-container {
  margin-bottom: 20px;
}

.retranscode-status-card {
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

.retranscode-status-header {
  background: #2a2a2a;
  border-bottom: 1px solid #333;
  padding: 16px 20px;
}

.retranscode-status-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
}

.status-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.status-icon.active {
  color: #2196f3;
  animation: pulse 2s infinite;
}

.status-icon.complete {
  color: #10b981;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.retranscode-status-body {
  padding: 20px;
}

/* Progress Section */
.progress-section {
  margin-bottom: 20px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.progress-percentage {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #333;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #2196f3, #1976d2);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-fill.with-errors {
  background: linear-gradient(90deg, #ff9800, #f57c00);
}

/* Status Details */
.status-details {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.detail-icon {
  width: 16px;
  height: 16px;
}

.detail-icon.success {
  color: #10b981;
}

.detail-icon.error {
  color: #ef4444;
}

/* Current Job */
.current-job {
  background: #2a2a2a;
  border: 1px solid #444;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 20px;
}

.current-job-label {
  font-size: 12px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 6px;
}

.current-job-filename {
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
  margin-bottom: 12px;
}

.current-job-progress {
  display: flex;
  align-items: center;
  gap: 12px;
}

.current-progress-bar {
  flex: 1;
  height: 6px;
  background-color: #444;
  border-radius: 3px;
  overflow: hidden;
}

.current-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #2196f3, #1976d2);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.current-progress-text {
  font-size: 12px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  min-width: 35px;
}

/* Activate Section */
.activate-section {
  border-top: 1px solid #333;
  padding-top: 20px;
  text-align: center;
}

.activate-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: #2196f3;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 200px;
  justify-content: center;
}

.activate-button:hover:not(:disabled) {
  background: #1976d2;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.activate-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.activate-description {
  margin: 12px 0 0 0;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 640px) {
  .retranscode-status-header,
  .retranscode-status-body {
    padding: 16px;
  }

  .status-details {
    flex-direction: column;
    gap: 12px;
  }

  .current-job-progress {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .current-progress-text {
    text-align: center;
    min-width: auto;
  }
}

.stabilized-indicator {
  margin-left: 8px;
  font-size: 0.8em;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.stabilized-indicator:hover {
  opacity: 1;
}
