import { useState } from 'react';
import { Spinner } from '@nextui-org/react';
import { toast } from 'react-toastify';
import { deleteUser } from '@/api/authApi';
import { User } from '@/types/auth';
import { HiX, HiTrash } from 'react-icons/hi';
import './CreateUserModal.css'; // Reuse the same styles

interface DeleteUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUserDeleted: () => void;
  user: User | null;
}

const DeleteUserModal = ({ isOpen, onClose, onUserDeleted, user }: DeleteUserModalProps) => {
  const [loading, setLoading] = useState(false);

  const handleDelete = async () => {
    if (!user) return;

    setLoading(true);
    try {
      await deleteUser(user.id);
      toast.success('User deleted successfully');
      onUserDeleted();
      onClose();
    } catch (error: any) {
      console.error('Failed to delete user:', error);

      if (error.response && error.response.data && error.response.data.error) {
        toast.error(error.response.data.error);
      } else {
        toast.error('Failed to delete user. Please try again later.');
      }
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen || !user) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h2>Delete User</h2>
          <button className="close-button" onClick={onClose}>
            <HiX />
          </button>
        </div>
        <div className="modal-body">
          <div className="delete-confirmation">
            <p>Are you sure you want to delete the user <strong>{user.username}</strong>?</p>
            <p className="text-red-500">This action cannot be undone.</p>
          </div>
        </div>
        <div className="modal-footer">
          <button
            className="modal-button cancel-button"
            onClick={onClose}
          >
            Cancel
          </button>
          <button
            className="modal-button delete-button"
            onClick={handleDelete}
            disabled={loading}
          >
            {loading ? (
              <>
                <Spinner size="sm" color="white" />
                <span>Deleting...</span>
              </>
            ) : (
              <>
                <HiTrash />
                <span>Delete User</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default DeleteUserModal;
