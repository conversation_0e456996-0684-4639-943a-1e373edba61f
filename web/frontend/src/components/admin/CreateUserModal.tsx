import { useState } from 'react';
import { Spinner } from '@nextui-org/react';
import { toast } from 'react-toastify';
import { createUser } from '@/api/authApi';
import { UserInput } from '@/types/auth';
import { Hi<PERSON>, <PERSON><PERSON><PERSON>ck, HiShieldCheck, HiUserGroup, HiExclamation } from 'react-icons/hi';
import './CreateUserModal.css';

// Email validation function
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

interface CreateUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUserCreated: () => void;
}

const CreateUserModal = ({ isOpen, onClose, onUserCreated }: CreateUserModalProps) => {
  const [formData, setFormData] = useState<UserInput>({
    username: '',
    email: '',
    password: '',
    role: 'user',
    status: 'pending'
  });
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleRoleChange = (value: string) => {
    setFormData(prev => ({ ...prev, role: value }));
  };

  const handleStatusChange = (value: string) => {
    setFormData(prev => ({ ...prev, status: value }));
  };

  const handleSubmit = async () => {
    if (!formData.username || !formData.email || !formData.password || !confirmPassword) {
      toast.error('Please fill in all required fields');
      return;
    }

    if (!isValidEmail(formData.email)) {
      toast.error('Please enter a valid email address');
      return;
    }

    if (formData.password !== confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    if (formData.password.length < 6) {
      toast.error('Password must be at least 6 characters long');
      return;
    }

    setLoading(true);
    try {
      await createUser(formData);
      toast.success('User created successfully');
      onUserCreated();
      onClose();
      // Reset form
      setFormData({
        username: '',
        email: '',
        password: '',
        role: 'user',
        status: 'pending'
      });
      setConfirmPassword('');
    } catch (error: any) {
      console.error('Failed to create user:', error);

      // Match the register page error handling
      if (error.response && error.response.data && error.response.data.error) {
        toast.error(error.response.data.error);
      } else {
        toast.error('Failed to create user. Please try again later.');
      }
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h2>Create New User</h2>
          <button className="close-button" onClick={onClose}>
            <HiX />
          </button>
        </div>
        <div className="modal-body">
          <div className="form-group">
            <label>Username</label>
            <input
              type="text"
              className="modal-input"
              name="username"
              value={formData.username}
              onChange={handleChange}
              placeholder="Enter username"
              required
            />
          </div>

          <div className="form-group">
            <label>Email</label>
            <input
              type="email"
              className="modal-input"
              name="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="Enter email"
              required
            />
          </div>

          <div className="form-group">
            <label>Password</label>
            <input
              type="password"
              className="modal-input"
              name="password"
              value={formData.password}
              onChange={handleChange}
              placeholder="Enter password"
              required
            />
          </div>

          <div className="form-group">
            <label>Confirm Password</label>
            <input
              type="password"
              className="modal-input"
              name="confirmPassword"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              placeholder="Confirm password"
              required
            />
          </div>

          <div className="form-group">
            <label>Role</label>
            <div className="radio-group">
              <label className="radio-option">
                <input
                  type="radio"
                  name="role"
                  value="user"
                  checked={formData.role === 'user'}
                  onChange={() => handleRoleChange('user')}
                />
                <div className={`radio-label user-role ${formData.role === 'user' ? 'active' : ''}`}>
                  <HiUserGroup className="radio-icon user-icon" />
                  <span>User</span>
                </div>
              </label>

              <label className="radio-option">
                <input
                  type="radio"
                  name="role"
                  value="admin"
                  checked={formData.role === 'admin'}
                  onChange={() => handleRoleChange('admin')}
                />
                <div className={`radio-label admin-role ${formData.role === 'admin' ? 'active' : ''}`}>
                  <HiShieldCheck className="radio-icon admin-icon" />
                  <span>Admin</span>
                </div>
              </label>
            </div>
          </div>

          <div className="form-group">
            <label>Status</label>
            <div className="radio-group">
              <label className="radio-option">
                <input
                  type="radio"
                  name="status"
                  value="approved"
                  checked={formData.status === 'approved'}
                  onChange={() => handleStatusChange('approved')}
                />
                <div className={`radio-label approved-status ${formData.status === 'approved' ? 'active' : ''}`}>
                  <HiCheck className="radio-icon approved-icon" />
                  <span>Approved</span>
                </div>
              </label>

              <label className="radio-option">
                <input
                  type="radio"
                  name="status"
                  value="pending"
                  checked={formData.status === 'pending'}
                  onChange={() => handleStatusChange('pending')}
                />
                <div className={`radio-label pending-status ${formData.status === 'pending' ? 'active' : ''}`}>
                  <HiExclamation className="radio-icon pending-icon" />
                  <span>Pending</span>
                </div>
              </label>
            </div>
          </div>
        </div>
        <div className="modal-footer">
          <button
            className="modal-button cancel-button"
            onClick={onClose}
          >
            Cancel
          </button>
          <button
            className="modal-button confirm-button"
            onClick={handleSubmit}
            disabled={loading}
          >
            {loading ? (
              <>
                <Spinner size="sm" color="white" />
                <span>Creating...</span>
              </>
            ) : (
              <>
                <HiCheck />
                <span>Create User</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CreateUserModal;
