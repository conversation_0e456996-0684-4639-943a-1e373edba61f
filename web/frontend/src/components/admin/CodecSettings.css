/* Codec Settings specific styles */
.admin-card-header {
  padding: 0.75rem 1rem !important;
}

.admin-card-header .admin-card-title {
  font-size: 1rem !important;
}

.admin-card-body {
  padding: 0.75rem !important;
}

.codec-settings-section {
  margin-bottom: 0.75rem !important;
  background-color: #1a1a1a !important;
  border-radius: 8px !important;
  overflow: hidden !important;
  border: 1px solid #333 !important;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 0;
  color: white;
  background-color: #2a2a2a;
  padding: 0.75rem 1rem !important;
  border-bottom: 1px solid #333;
}

.section-title svg {
  color: #2196f3;
}

.codec-settings-content {
  padding: 0.75rem !important;
}

.form-group.compact {
  margin-bottom: 0.5rem !important;
}

.form-group.compact label {
  display: block;
  font-size: 0.8rem;
  margin-bottom: 0.25rem !important;
  color: rgba(255, 255, 255, 0.7);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Flex layout for two fields in one row */
.flex.gap-2 {
  display: flex;
  gap: 0.75rem !important;
}

.flex-1 {
  flex: 1;
}

.form-select.compact,
.form-input.compact {
  width: 100%;
  padding: 0.35rem 0.5rem;
  font-size: 0.85rem;
  background-color: #1e1e1e;
  color: white;
  border: 1px solid #333;
  border-radius: 0.25rem;
  transition: all 0.3s ease;
  height: 2rem !important;
}

.form-select.compact:focus,
.form-input.compact:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 1px rgba(33, 150, 243, 0.5);
}

.form-input.compact[type="number"] {
  -moz-appearance: textfield;
}

.form-input.compact[type="number"]::-webkit-inner-spin-button,
.form-input.compact[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Invalid input styling */
.form-input.compact.invalid,
.form-select.compact.invalid {
  border-color: rgba(244, 67, 54, 0.7);
  background-color: rgba(244, 67, 54, 0.05);
}

.form-input.compact.invalid:focus,
.form-select.compact.invalid:focus {
  border-color: rgba(244, 67, 54, 0.9);
  box-shadow: 0 0 0 1px rgba(244, 67, 54, 0.5);
}

.seting-buttons {
  justify-items: end;
}

.save-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.35rem 0.75rem;
  background-color: #2a2a2a;
  color: white;
  border: 1px solid #3a3a3a;
  border-radius: 4px;
  font-weight: 500;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s;
}

.save-button:hover {
  background-color: #3a3a3a;
  border-color: #2196f3;
}

.save-button:disabled {
  background-color: #1a1a1a;
  color: #6b6b6b;
  border-color: #333;
  cursor: not-allowed;
}

.save-button-primary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.35rem 0.75rem;
  background-color: #2196f3;
  color: white;
  border: 1px solid #2196f3;
  border-radius: 4px;
  font-weight: 500;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s;
  margin-right: 0.5rem;
}

.save-button-primary:hover {
  background-color: #1976d2;
  border-color: #1976d2;
}

.save-button-primary:disabled {
  background-color: #4b4b4b;
  color: #a0a0a0;
  border-color: #4b4b4b;
  cursor: not-allowed;
}

.save-button-primary.invalid-settings:disabled {
  background-color: rgba(244, 67, 54, 0.5);
  color: #ffffff;
  border-color: rgba(244, 67, 54, 0.7);
  cursor: not-allowed;
  position: relative;
  overflow: hidden;
}

.error-message {
  color: #ff4d4f;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.validation-error-summary {
  background-color: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.5);
  border-radius: 4px;
  padding: 0.75rem;
  margin-bottom: 1rem;
  color: #f44336;
}

.validation-error-summary p {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.validation-error-summary ul {
  margin: 0;
  padding-left: 1.5rem;
  font-size: 0.9rem;
}

.validation-error-summary li {
  margin-bottom: 0.25rem;
  color: #f44336;
}

/* Dual Audio specific styles */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  font-size: 0.85rem;
  color: white;
  margin-bottom: 0.25rem;
  padding: 0.5rem;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.checkbox-label:hover {
  background-color: rgba(255, 255, 255, 0.02);
}

.form-checkbox {
  position: relative;
  width: 3rem;
  height: 1.5rem;
  background-color: #2a2a2a;
  border: 2px solid #444;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  appearance: none;
  outline: none;
  flex-shrink: 0;
}

.form-checkbox:before {
  content: "";
  position: absolute;
  top: 0px;
  left: 0px;
  width: 1.25rem;
  height: 1.25rem;
  background-color: #666;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateX(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.form-checkbox:checked {
  background-color: #2196f3;
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

.form-checkbox:checked:before {
  background-color: white;
  transform: translateX(1.5rem);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.form-checkbox:hover {
  border-color: #2196f3;
}

.form-checkbox:hover:before {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.checkbox-text {
  font-weight: 500;
  flex: 1;
}

.field-description {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
  margin-left: 0.25rem;
  font-style: italic;
  white-space: nowrap;
  min-width: 0;
}

/* Optimize layout for one row */
.dual-audio-toggle-row {
  display: flex;
  align-items: center;
  gap: 1rem;
  background-color: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 0.75rem;
  margin-bottom: 0.75rem;
}

.dual-audio-toggle-row .checkbox-label {
  margin-bottom: 0;
  padding: 0;
  flex: 1;
  min-width: 0;
}

.dual-audio-status {
  background-color: rgba(33, 150, 243, 0.1);
  border: 1px solid rgba(33, 150, 243, 0.3);
  color: #64b5f6;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.dual-audio-status.active {
  background-color: rgba(76, 175, 80, 0.15);
  border-color: rgba(76, 175, 80, 0.4);
  color: #81c784;
}

.audio-track-section {
  background-color: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 0.75rem;
  margin-bottom: 0.75rem;
  position: relative;
  overflow: hidden;
}

.audio-track-section:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background: linear-gradient(to bottom, #2196f3, #1976d2);
  opacity: 0.8;
}

.audio-track-title {
  font-size: 0.85rem;
  font-weight: 600;
  color: #2196f3;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding-left: 0.5rem;
}

/* Adjust spacing for dual audio mode */
.audio-track-section .form-group.compact {
  margin-bottom: 0.5rem;
}

.audio-track-section:last-child {
  margin-bottom: 0;
}

/* Retranscoding Prompt Styles */
.retranscode-prompt-card {
  background: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  margin-bottom: 20px;
  overflow: hidden;
}

.retranscode-prompt-header {
  background: #2a2a2a;
  border-bottom: 1px solid #333;
  padding: 16px 20px;
}

.retranscode-prompt-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2196f3;
}

.prompt-icon {
  width: 20px;
  height: 20px;
  color: #2196f3;
}

.retranscode-prompt-body {
  padding: 20px;
}

.prompt-description {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 20px 0;
}

.prompt-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

.retranscode-start-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: #2196f3;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retranscode-start-button:hover {
  background: #1976d2;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.retranscode-skip-button {
  display: inline-flex;
  align-items: center;
  background: transparent;
  color: rgba(255, 255, 255, 0.6);
  border: 1px solid #444;
  border-radius: 6px;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retranscode-skip-button:hover {
  background: #2a2a2a;
  border-color: #666;
  color: rgba(255, 255, 255, 0.8);
}
