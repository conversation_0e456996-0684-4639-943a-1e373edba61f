/* Modal Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* Modal Content */
.modal-content {
  background-color: #121212;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  border: 1px solid #333;
}

/* Modal Header */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #333;
  background-color: #1a1a1a;
}

.modal-header h2 {
  margin: 0;
  color: #2196f3;
  font-size: 1.25rem;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  color: #888;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover {
  color: #fff;
}

/* Modal Body */
.modal-body {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #fff;
  font-weight: 500;
  font-size: 0.9rem;
  text-align: left;
}

.modal-input {
  width: 100%;
  padding: 0.75rem 1rem;
  background-color: #1a1a1a;
  border: 1px solid #333;
  border-radius: 4px;
  color: white;
  font-size: 0.9rem;
  transition: all 0.3s;
}

.modal-input:focus {
  border-color: #2196f3;
  outline: none;
  box-shadow: 0 0 0 2px rgba(138, 112, 214, 0.2);
}

.modal-input::placeholder {
  color: #555;
}

/* Radio Group */
.radio-group {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
  justify-content: center;
}

.radio-option {
  position: relative;
  cursor: pointer;
}

.radio-option input[type="radio"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.radio-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: #1a1a1a;
  border: 1px solid #333;
  border-radius: 4px;
  transition: all 0.3s;
  min-width: 100px;
  justify-content: center;
}

.radio-option input[type="radio"]:checked + .radio-label {
  border-color: #2196f3;
  background-color: rgba(138, 112, 214, 0.2);
}

.radio-option input[type="radio"]:checked + .radio-label.user-role {
  border-color: #2196f3;
  background-color: rgba(138, 112, 214, 0.2);
}

.radio-option input[type="radio"]:checked + .radio-label.admin-role {
  border-color: #2196f3;
  background-color: rgba(138, 112, 214, 0.2);
}

.radio-option input[type="radio"]:checked + .radio-label.approved-status {
  border-color: #4caf50;
  background-color: rgba(76, 175, 80, 0.2);
}

.radio-option input[type="radio"]:checked + .radio-label.pending-status {
  border-color: #2196f3;
  background-color: rgba(138, 112, 214, 0.2);
}

.radio-icon {
  font-size: 1.1rem;
}

.radio-icon.user-icon {
  color: #2196f3;
}

.radio-icon.admin-icon {
  color: #2196f3;
}

.radio-icon.approved-icon {
  color: #4caf50;
}

.radio-icon.pending-icon {
  color: #2196f3;
}

/* Modal Footer */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1rem 1.5rem;
  border-top: 1px solid #333;
  background-color: #1a1a1a;
}

.modal-button {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.cancel-button {
  background-color: #2a2a2a;
  color: #aaa;
  border: 1px solid #3a3a3a;
}

.cancel-button:hover {
  background-color: #3a3a3a;
  color: #fff;
}

.confirm-button {
  background-color: #1a1a1a;
  color: white;
  border: 1px solid #2196f3;
}

.confirm-button:hover {
  background-color: rgba(138, 112, 214, 0.2);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
  }

  .radio-group {
    flex-direction: column;
    gap: 0.5rem;
    align-items: center;
  }
}
