import { useState, useEffect } from "react";
import { Spin<PERSON> } from "@nextui-org/react";
import { toast } from "react-toastify";
import {
  getRetranscodeStatus,
  activateNewCodecSettings,
  RetranscodeStatus as RetranscodeStatusType,
} from "@/api/retranscodeApi";
import webSocketService from "@/api/websocket";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  HiExclamationTriangle,
  HiClock,
  HiPlay,
} from "react-icons/hi2";
import "./RetranscodeStatus.css";

interface RetranscodeStatusProps {
  isRetranscoding: boolean;
  onRetranscodeComplete?: () => void;
}

const RetranscodeStatus = ({
  isRetranscoding,
  onRetranscodeComplete,
}: RetranscodeStatusProps) => {
  const [status, setStatus] = useState<RetranscodeStatusType | null>(null);
  const [, setLoading] = useState(false);
  const [activating, setActivating] = useState(false);
  const [maxProcessedJobs, setMaxProcessedJobs] = useState(0);
  const [maxTotalJobs, setMaxTotalJobs] = useState(0);

  // Fetch retranscode status
  const fetchStatus = async () => {
    try {
      setLoading(true);
      const data = await getRetranscodeStatus();
      console.log("RetranscodeStatus: Fetched status:", data);
      setStatus(data);
    } catch (error) {
      console.error("Failed to fetch retranscode status:", error);
    } finally {
      setLoading(false);
    }
  };

  // Handle activation of new codec settings
  const handleActivateNewSettings = async () => {
    try {
      setActivating(true);
      await activateNewCodecSettings();
      toast.success("New codec settings activated successfully!");

      // Clear the local status to force a refresh
      setStatus(null);

      // Reset max processed jobs counter after a delay to ensure clean state
      setTimeout(() => {
        setMaxProcessedJobs(0);
        setMaxTotalJobs(0);
      }, 500);

      // Refresh status first
      await fetchStatus();

      // Call the completion callback
      if (onRetranscodeComplete) {
        onRetranscodeComplete();
      }
    } catch (error) {
      console.error("Failed to activate new codec settings:", error);
      toast.error("Failed to activate new codec settings");
    } finally {
      setActivating(false);
    }
  };

  // Load status on component mount and when retranscoding state changes
  useEffect(() => {
    fetchStatus(); // Always fetch status when component mounts or state changes
    // Reset max processed jobs when component mounts
    setMaxProcessedJobs(0);
    setMaxTotalJobs(0);
  }, []);

  // Also fetch status when isRetranscoding changes
  useEffect(() => {
    if (isRetranscoding) {
      fetchStatus();
    } else {
      // Only reset max processed jobs when retranscoding stops AND we have no active status
      setTimeout(() => {
        // Add a small delay to avoid race conditions with status updates
        if (!isRetranscoding) {
          setMaxProcessedJobs(0);
          setMaxTotalJobs(0);
        }
      }, 1000);
    }
  }, [isRetranscoding]);

  // Subscribe to WebSocket updates for real-time progress
  useEffect(() => {
    const unsubscribe = webSocketService.subscribeToRetranscodeUpdates(
      (update: RetranscodeStatusType) => {
        if (process.env.NODE_ENV === "development") {
          console.log("Received retranscode status update:", update);
        }
        setStatus(update);

        // If retranscoding just completed (was active, now inactive with jobs), notify parent
        if (
          !update.is_active &&
          update.total_jobs > 0 &&
          (update.completed_jobs > 0 || update.failed_jobs > 0) &&
          update.completed_jobs + update.failed_jobs >= update.total_jobs
        ) {
          console.log("Retranscoding completed, notifying parent");
          if (onRetranscodeComplete) {
            onRetranscodeComplete();
          }
        }
      }
    );

    // Set up periodic status fetching as a fallback
    const intervalId = setInterval(() => {
      if (isRetranscoding) {
        if (process.env.NODE_ENV === "development") {
          console.log("Periodic status fetch while retranscoding");
        }
        fetchStatus();
      }
    }, 5000); // Every 5 seconds

    return () => {
      unsubscribe();
      clearInterval(intervalId);
    };
  }, [onRetranscodeComplete, isRetranscoding]);

  // Update max processed jobs and total jobs if we have new highs or if retranscoding becomes active
  useEffect(() => {
    if (status) {
      const rawProcessedJobs =
        (status.completed_jobs || 0) + (status.failed_jobs || 0);
      const rawTotalJobs = status.total_jobs || 0;

      // Update max processed jobs
      if (
        rawProcessedJobs > maxProcessedJobs ||
        (status.is_active && rawTotalJobs > 0 && maxProcessedJobs === 0)
      ) {
        if (process.env.NODE_ENV === "development") {
          console.log(
            `Updating max processed jobs: ${maxProcessedJobs} -> ${rawProcessedJobs} (active: ${status.is_active})`
          );
        }
        setMaxProcessedJobs(rawProcessedJobs);
      }

      // Update max total jobs
      if (
        rawTotalJobs > maxTotalJobs ||
        (status.is_active && rawTotalJobs > 0 && maxTotalJobs === 0)
      ) {
        if (process.env.NODE_ENV === "development") {
          console.log(
            `Updating max total jobs: ${maxTotalJobs} -> ${rawTotalJobs} (active: ${status.is_active})`
          );
        }
        setMaxTotalJobs(rawTotalJobs);
      }
    }
  }, [status, maxProcessedJobs, maxTotalJobs]);

  // Show component if retranscoding is active OR if there are completed jobs waiting for activation
  const shouldShow =
    isRetranscoding ||
    (status &&
      !status.is_active &&
      status.total_jobs > 0 &&
      (status.completed_jobs > 0 || status.failed_jobs > 0));

  if (!shouldShow || !status) {
    return null;
  }

  // Safely handle potentially undefined/null values
  const safeCompletedJobs = status.completed_jobs || 0;
  const safeFailedJobs = status.failed_jobs || 0;
  const rawTotalJobs = status.total_jobs || 0;
  const rawProcessedJobs = safeCompletedJobs + safeFailedJobs;

  // Prevent processed jobs count from decreasing during active retranscoding
  const currentMaxProcessed = Math.max(maxProcessedJobs, rawProcessedJobs);
  const currentMaxTotal = Math.max(maxTotalJobs, rawTotalJobs);

  // Use stabilized counts if retranscoding is active OR if we have higher max counts
  // This ensures we never show lower numbers than what we've already shown
  const safeProcessedJobs =
    status.is_active || currentMaxProcessed > rawProcessedJobs
      ? currentMaxProcessed
      : rawProcessedJobs;

  const safeTotalJobs =
    status.is_active || currentMaxTotal > rawTotalJobs
      ? currentMaxTotal
      : rawTotalJobs;

  // Calculate progress based on stable processed count
  const stableOverallProgress =
    safeTotalJobs > 0
      ? Math.round((safeProcessedJobs * 100) / safeTotalJobs)
      : status.overall_progress || 0;

  // Debug logging (can be removed in production)
  const isUsingStabilizedCount =
    safeProcessedJobs > rawProcessedJobs || safeTotalJobs > rawTotalJobs;
  if (process.env.NODE_ENV === "development") {
    console.log(
      `Progress calculation: raw=${rawProcessedJobs}/${rawTotalJobs}, max=${maxProcessedJobs}/${maxTotalJobs}, displayed=${safeProcessedJobs}/${safeTotalJobs}${
        isUsingStabilizedCount ? " (stabilized)" : ""
      }, progress=${stableOverallProgress}%, active=${status.is_active}`
    );
  }

  const isComplete =
    !status.is_active &&
    safeTotalJobs > 0 &&
    safeProcessedJobs >= safeTotalJobs;
  const hasFailures = safeFailedJobs > 0;

  return (
    <div className="retranscode-status-container">
      <div className="retranscode-status-card">
        <div className="retranscode-status-header">
          <h3 className="retranscode-status-title">
            {status.is_active ? (
              <>
                <HiPlay className="status-icon active" />
                Retranscoding In Progress
                {isUsingStabilizedCount && (
                  <span
                    className="stabilized-indicator"
                    title="Using stabilized count to prevent backward progress"
                  >
                    📊
                  </span>
                )}
              </>
            ) : isComplete ? (
              <>
                <HiCheck className="status-icon complete" />
                Retranscoding Complete
              </>
            ) : (
              <>
                <HiClock className="status-icon" />
                Retranscoding Status
              </>
            )}
          </h3>
        </div>

        <div className="retranscode-status-body">
          {/* Progress Bar */}
          <div className="progress-section">
            <div className="progress-info">
              <span className="progress-text">
                {safeProcessedJobs} of {safeTotalJobs} files processed
              </span>
              <span className="progress-percentage">
                {stableOverallProgress}%
              </span>
            </div>
            <div className="progress-bar">
              <div
                className={`progress-fill ${hasFailures ? "with-errors" : ""}`}
                style={{ width: `${stableOverallProgress}%` }}
              />
            </div>
          </div>

          {/* Status Details */}
          <div className="status-details">
            <div className="status-item">
              <HiCheck className="detail-icon success" />
              <span>Completed: {safeCompletedJobs}</span>
            </div>
            {safeFailedJobs > 0 && (
              <div className="status-item">
                <HiExclamationTriangle className="detail-icon error" />
                <span>Failed: {safeFailedJobs}</span>
              </div>
            )}
          </div>

          {/* Current Job */}
          {status.current_job && status.is_active && (
            <div className="current-job">
              <div className="current-job-label">Currently processing:</div>
              <div className="current-job-filename">
                {status.current_job.filename ||
                  `File ID: ${status.current_job.file_id}`}
              </div>
              <div className="current-job-progress">
                <div className="current-progress-bar">
                  <div
                    className="current-progress-fill"
                    style={{ width: `${status.current_job.progress || 0}%` }}
                  />
                </div>
                <span className="current-progress-text">
                  {Math.round(status.current_job.progress || 0)}%
                </span>
              </div>
            </div>
          )}

          {/* Activate Button */}
          {isComplete && (
            <div className="activate-section">
              <button
                onClick={handleActivateNewSettings}
                disabled={activating}
                className="activate-button"
              >
                {activating ? (
                  <>
                    <Spinner size="sm" color="white" />
                    <span>Activating...</span>
                  </>
                ) : (
                  <>
                    <HiCheck />
                    <span>Activate New Codec Settings</span>
                  </>
                )}
              </button>
              <p className="activate-description">
                Click to replace the original files with the newly transcoded
                versions.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default RetranscodeStatus;
