import { useState, useEffect } from "react";
import { Spin<PERSON> } from "@nextui-org/react";
import { toast } from "react-toastify";
import { HiRefresh, HiGlobe, HiSave, HiOutlineClipboard, HiServer } from "react-icons/hi";
import "./NetworkSettings.css";
import {
  GeneralSettings as GeneralSettingInterface,
  GeneralSettingsInput,
} from "@/types/general.ts";
import { getGeneralSettings, updateGeneralSettings } from "@/api/adminApi.ts";

const GeneralSettings = () => {
  const [, setGeneralSettings] = useState<GeneralSettingInterface | null>(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [isValid, setIsValid] = useState(true);
  const [serverType, setServerType] = useState("primary");
  const [backupIP, setBackupIP] = useState("");
  const [sshUser, setSSHUser] = useState("");
  const [sshPassword, setSSHPassword] = useState("");
  const [primarySSHUser, setPrimarySSHUser] = useState("");
  const [primarySSHPassword, setPrimarySSHPassword] = useState("");
  const [formData, setFormData] = useState<GeneralSettingsInput>({
    transcoder_threads: 3,
  });

  const fetchGeneralSettings = async () => {
    setLoading(true);
    try {
      const data = await getGeneralSettings();
      setGeneralSettings(data);

      // Initialize form data for each interface
      const formDataFromServer = {
        transcoder_threads: data.transcoder_threads,
      };

      setFormData(formDataFromServer);

      // Fetch server type
      const response = await fetch("/api/server-type");
      const serverTypeData = await response.json();
      setServerType(serverTypeData.server_type);

      // Fetch backup IP
      const backupResponse = await fetch("/api/backup-ip");
      const backupData = await backupResponse.json();
      setBackupIP(backupData.backup_ip || "");

      // Fetch SSH configuration
      const sshResponse = await fetch("/api/ssh-config");
      const sshData = await sshResponse.json();
      setSSHUser(sshData.ssh_user || "");
      setSSHPassword(sshData.ssh_password || "");

      // Fetch primary SSH configuration
      const primarySSHResponse = await fetch("/api/primary-ssh-config");
      const primarySSHData = await primarySSHResponse.json();
      setPrimarySSHUser(primarySSHData.primary_ssh_user || "");
      setPrimarySSHPassword(primarySSHData.primary_ssh_password || "");
    } catch (error) {
      console.error("Failed to fetch settings:", error);
      toast.error("Failed to fetch settings");
    } finally {
      setLoading(false);
    }
  };

  const testBackupServerConnectivity = async (ip: string): Promise<{ success: boolean; message: string }> => {
    try {
      const response = await fetch("/api/test-backup-server", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ backup_ip: ip }),
      });

      if (!response.ok) {
        throw new Error("Failed to test backup server connectivity");
      }

      return await response.json();
    } catch (error) {
      return {
        success: false,
        message: `Connection test failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setSaving(true);

      // Validate settings before saving
      if (!validateSettings()) {
        setSaving(false);
        return;
      }

      // Validate SSH settings before saving (only show errors on submit)
      if (!validateSSHSettings()) {
        setSaving(false);
        return;
      }

      // Test backup server connectivity if backup IP is provided and server type is primary
      if (serverType === "primary" && backupIP.trim() !== "") {
        toast.info("Testing backup server connectivity...");

        const connectivityTest = await testBackupServerConnectivity(backupIP.trim());

        if (connectivityTest.success) {
          toast.success(`✅ ${connectivityTest.message}`);
        } else {
          toast.error(`❌ ${connectivityTest.message}`);
          setSaving(false);
          return;
        }
      }

      // Save general settings
      await updateGeneralSettings(formData);

      // Save server type
      const response = await fetch("/api/server-type", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ server_type: serverType }),
      });

      if (!response.ok) {
        throw new Error("Failed to save server type");
      }

      // Save backup IP if server type is primary
      if (serverType === "primary") {
        const backupResponse = await fetch("/api/backup-ip", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ backup_ip: backupIP }),
        });

        if (!backupResponse.ok) {
          throw new Error("Failed to save backup IP");
        }

        // Save SSH configuration if backup IP is provided
        if (backupIP.trim() !== "") {
          const sshResponse = await fetch("/api/ssh-config", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              ssh_user: sshUser.trim(),
              ssh_password: sshPassword.trim()
            }),
          });

          if (!sshResponse.ok) {
            throw new Error("Failed to save SSH configuration");
          }
        }

        // Save primary SSH configuration
        const primarySSHResponse = await fetch("/api/primary-ssh-config", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            primary_ssh_user: primarySSHUser.trim(),
            primary_ssh_password: primarySSHPassword.trim()
          }),
        });

        if (!primarySSHResponse.ok) {
          throw new Error("Failed to save primary SSH configuration");
        }
      }

      toast.success("Settings saved successfully");
      await fetchGeneralSettings(); // Refresh settings
    } catch (error) {
      console.error("Failed to save settings:", error);
      toast.error("Failed to save settings");
    } finally {
      setSaving(false);
    }
  };

  const validateSettings = (): boolean => {
    if (formData.transcoder_threads <= 0) {
      toast.error("Transcoder threads is less than 0");
      return false;
    }
    if (formData.transcoder_threads > 1000) {
      toast.error("Transcoder threads is greater than 1000");
      return false;
    }

    return true;
  };

  // Separate SSH validation function for submit-time validation
  const validateSSHSettings = (): boolean => {
    // Validate SSH configuration if server type is primary and backup IP is provided
    if (serverType === "primary" && backupIP.trim() !== "") {
      if (sshUser.trim() === "") {
        toast.error("SSH username is required when backup server IP is configured");
        return false;
      }
      if (sshPassword.trim() === "") {
        toast.error("SSH password is required when backup server IP is configured");
        return false;
      }
    }
    return true;
  };

  const isFieldInvalid = (field: string): boolean => {
    if (field === "transcoder_threads") {
      return (
        formData.transcoder_threads > 0 && formData.transcoder_threads <= 1000
      );
    }

    return false;
  };

  // Handle form input changes
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    if (name === "server_type") {
      setServerType(value);
    } else if (name === "backup_ip") {
      setBackupIP(value);
    } else if (name === "ssh_user") {
      setSSHUser(value);
    } else if (name === "ssh_password") {
      setSSHPassword(value);
    } else if (name === "primary_ssh_user") {
      setPrimarySSHUser(value);
    } else if (name === "primary_ssh_password") {
      setPrimarySSHPassword(value);
    } else {
      let updatedFormData = {
        ...formData,
        [name]: parseInt(value),
      };
      setFormData(updatedFormData);
    }
  };

  useEffect(() => {
    fetchGeneralSettings();
  }, []);

  useEffect(() => {
    const valid = validateSettings();
    setIsValid(valid);
  }, [formData]);

  return (
    <div>
      <div className="admin-card">
        <div className="admin-card-header">
          <h2 className="admin-card-title">
            <HiGlobe />
            Default General Settings
          </h2>
          <div className="flex items-center gap-3">
            <button
              className="refresh-button"
              onClick={fetchGeneralSettings}
              disabled={loading}
            >
              <HiRefresh />
            </button>
          </div>
        </div>
        <div className="admin-card-body">
          {loading ? (
            <div className="loading-container">
              <div className="loading-text">
                <Spinner size="sm" color="primary" />
                <span>Loading general settings...</span>
              </div>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-3">
              <div className="codec-settings-section">
                <h3 className="section-title">
                  <HiServer />
                  Server Type
                </h3>
                <div className="space-y-1">
                  <div className="flex gap-2">
                    <div className="codec-settings-content">
                      <div className="form-group compact flex-1">
                        <label htmlFor="server_type">Server Type</label>
                        <select
                          id="server_type"
                          name="server_type"
                          value={serverType}
                          onChange={handleChange}
                          className="form-input compact"
                        >
                          <option value="primary">Primary</option>
                          <option value="backup">Backup</option>
                        </select>
                      </div>
                    </div>
                  </div>
                  {serverType === "primary" && (
                    <>
                      <div className="flex gap-2">
                        <div className="codec-settings-content">
                          <div className="form-group compact flex-1">
                            <label htmlFor="backup_ip">Backup Server IP</label>
                            <input
                              type="text"
                              id="backup_ip"
                              name="backup_ip"
                              value={backupIP}
                              onChange={handleChange}
                              placeholder="Enter backup server IP address"
                              className="form-input compact"
                            />
                          </div>
                        </div>
                      </div>
                      {backupIP.trim() !== "" && (
                        <>
                          <div className="flex gap-2">
                            <div className="codec-settings-content">
                              <div className="form-group compact flex-1">
                                <label htmlFor="ssh_user">SSH Username</label>
                                <input
                                  type="text"
                                  id="ssh_user"
                                  name="ssh_user"
                                  value={sshUser}
                                  onChange={handleChange}
                                  placeholder="Enter SSH username for backup server"
                                  className="form-input compact"
                                  required
                                />
                              </div>
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <div className="codec-settings-content">
                              <div className="form-group compact flex-1">
                                <label htmlFor="ssh_password">SSH Password</label>
                                <input
                                  type="password"
                                  id="ssh_password"
                                  name="ssh_password"
                                  value={sshPassword}
                                  onChange={handleChange}
                                  placeholder="Enter SSH password for backup server"
                                  className="form-input compact"
                                  required
                                />
                              </div>
                            </div>
                          </div>
                        </>
                      )}
                      {/* Primary SSH Configuration Section */}
                      <div className="primary-ssh-section mt-4">
                        <div className="flex gap-2">
                          <div className="codec-settings-content">
                            <div className="form-group compact flex-1">
                              <label htmlFor="primary_ssh_user">
                                Current Primary Server SSH Username
                              </label>
                              <input
                                type="text"
                                id="primary_ssh_user"
                                name="primary_ssh_user"
                                value={primarySSHUser}
                                onChange={handleChange}
                                placeholder="Enter SSH username for this primary server"
                                className="form-input compact"
                              />
                              <small className="form-help">
                                💡 Required for recovery: Backup server uses these credentials to connect back to this primary server during failover recovery
                              </small>
                            </div>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <div className="codec-settings-content">
                            <div className="form-group compact flex-1">
                              <label htmlFor="primary_ssh_password">
                                Current Primary Server SSH Password
                              </label>
                              <input
                                type="password"
                                id="primary_ssh_password"
                                name="primary_ssh_password"
                                value={primarySSHPassword}
                                onChange={handleChange}
                                placeholder="Enter SSH password for this primary server"
                                className="form-input compact"
                              />
                              <small className="form-help">
                                🔄 During recovery: When primary comes back online, backup will sync latest data back using these credentials
                              </small>
                            </div>
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>

              <div className="codec-settings-section">
                <h3 className="section-title">
                  <HiOutlineClipboard />
                  Transcoder Settings
                </h3>
                <div className="space-y-1">
                  <div className="flex gap-2">
                    <div className="codec-settings-content">
                      <div className="form-group compact flex-1">
                        <label htmlFor="transcoder_threads">
                          Transcoder Threads
                        </label>
                        <input
                          type="number"
                          id="transcoder_threads"
                          name="transcoder_threads"
                          value={formData.transcoder_threads}
                          onChange={handleChange}
                          min="1"
                          max="1000"
                          title="Transcoder Threads"
                          className={`form-input compact ${
                            isFieldInvalid("max_vbitrate") ? "invalid" : ""
                          }`}
                        />
                      </div>
                      <div className="form-group compact flex-1">
                        {/* Empty space to maintain layout */}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {!isValid && (
                <div className="validation-error-summary">
                  <p>Please correct the following validation errors:</p>
                  <ul>
                    {formData.transcoder_threads <= 0 && (
                      <li>Transcoder threads is less than 0</li>
                    )}
                    {formData.transcoder_threads > 1000 && (
                      <li>Transcoder Threads is greater than 1000</li>
                    )}
                  </ul>
                </div>
              )}

              <div className="seting-buttons">
                <button
                  type="submit"
                  className={`save-button-primary ${
                    !isValid ? "invalid-settings" : ""
                  }`}
                  disabled={loading || saving || !isValid}
                  title={
                    !isValid
                      ? "Please correct validation errors before saving"
                      : "Save settings"
                  }
                >
                  {saving ? (
                    <>
                      <Spinner size="sm" color="white" />
                      <span>Saving...</span>
                    </>
                  ) : (
                    <>
                      <HiSave />
                      <span>Save Settings</span>
                    </>
                  )}
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default GeneralSettings;
