import { useState, useEffect } from "react";
import { Spin<PERSON> } from "@nextui-org/react";
import { toast } from "react-toastify";
import { getNetworkInterfaces, updateNetworkInterface } from "@/api/adminApi";
import { NetworkInterface, NetworkInterfaceInput } from "@/types/network";
import {
  HiRefresh,
  HiWifi,
  HiServer,
  HiGlobe,
  HiDesktopComputer,
  HiCheck,
  HiX,
  HiSave,
} from "react-icons/hi";
import "./NetworkSettings.css";

const NetworkSettings = () => {
  const [interfaces, setInterfaces] = useState<NetworkInterface[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState<Record<string, boolean>>({});
  const [formData, setFormData] = useState<
    Record<string, NetworkInterfaceInput>
  >({});
  const [errors, setErrors] = useState<Record<string, Record<string, string>>>(
    {}
  );
  const [hasChanges, setHasChanges] = useState<Record<string, boolean>>({});

  const fetchInterfaces = async () => {
    setLoading(true);
    try {
      const data = await getNetworkInterfaces();
      setInterfaces(data);

      // Initialize form data for each interface
      const initialFormData: Record<string, NetworkInterfaceInput> = {};
      const initialHasChanges: Record<string, boolean> = {};
      data.forEach((iface) => {
        initialFormData[iface.name] = {
          ip_address: iface.ip_address,
          netmask: iface.netmask,
          gateway: iface.gateway,
          dns_servers: iface.dns_servers,
        };
        initialHasChanges[iface.name] = false;
      });
      setFormData(initialFormData);
      setHasChanges(initialHasChanges);
      setErrors({});
    } catch (error) {
      console.error("Failed to fetch network interfaces:", error);
      toast.error("Failed to fetch network interfaces");
    } finally {
      setLoading(false);
    }
  };

  const validateIPAddress = (ip: string): boolean => {
    const ipRegex =
      /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return ipRegex.test(ip);
  };

  const validateNetmask = (netmask: string): boolean => {
    // Check if it's CIDR notation (1-32)
    const cidrRegex = /^([1-9]|[12][0-9]|3[0-2])$/;
    if (cidrRegex.test(netmask)) return true;

    // Check if it's dotted decimal notation
    const dotRegex =
      /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return dotRegex.test(netmask);
  };

  const validateDNSServers = (dns: string): boolean => {
    const servers = dns.split(",").map((s) => s.trim());
    return servers.every((server) => validateIPAddress(server));
  };

  const validateFormData = (
    data: NetworkInterfaceInput
  ): Record<string, string> => {
    const validationErrors: Record<string, string> = {};

    if (!data.ip_address) {
      validationErrors.ip_address = "IP address is required";
    } else if (!validateIPAddress(data.ip_address)) {
      validationErrors.ip_address = "Invalid IP address format";
    }

    if (!data.netmask) {
      validationErrors.netmask = "Netmask is required";
    } else if (!validateNetmask(data.netmask)) {
      validationErrors.netmask =
        "Invalid netmask format (use CIDR notation like 24 or dotted decimal)";
    }

    if (!data.gateway) {
      validationErrors.gateway = "Gateway is required";
    } else if (!validateIPAddress(data.gateway)) {
      validationErrors.gateway = "Invalid gateway IP address format";
    }

    if (!data.dns_servers) {
      validationErrors.dns_servers = "DNS servers are required";
    } else if (!validateDNSServers(data.dns_servers)) {
      validationErrors.dns_servers =
        "Invalid DNS server format (use comma-separated IP addresses)";
    }

    return validationErrors;
  };

  const handleInputChange = (
    interfaceName: string,
    field: keyof NetworkInterfaceInput,
    value: string
  ) => {
    const newFormData = {
      ...formData,
      [interfaceName]: {
        ...formData[interfaceName],
        [field]: value,
      },
    };
    setFormData(newFormData);

    // Check if there are changes compared to original data
    const originalInterface = interfaces.find(
      (iface) => iface.name === interfaceName
    );
    if (originalInterface) {
      const hasChangesForInterface =
        newFormData[interfaceName].ip_address !==
          originalInterface.ip_address ||
        newFormData[interfaceName].netmask !== originalInterface.netmask ||
        newFormData[interfaceName].gateway !== originalInterface.gateway ||
        newFormData[interfaceName].dns_servers !==
          originalInterface.dns_servers;

      setHasChanges((prev) => ({
        ...prev,
        [interfaceName]: hasChangesForInterface,
      }));
    }

    // Validate the specific field
    const newErrors = { ...errors };
    if (!newErrors[interfaceName]) newErrors[interfaceName] = {};

    const fieldErrors = validateFormData(newFormData[interfaceName]);
    if (fieldErrors[field]) {
      newErrors[interfaceName][field] = fieldErrors[field];
    } else {
      delete newErrors[interfaceName][field];
      if (Object.keys(newErrors[interfaceName]).length === 0) {
        delete newErrors[interfaceName];
      }
    }
    setErrors(newErrors);
  };

  const handleSaveInterface = async (interfaceName: string) => {
    const data = formData[interfaceName];
    if (!data) return;

    // Validate all fields
    const validationErrors = validateFormData(data);
    if (Object.keys(validationErrors).length > 0) {
      setErrors((prev) => ({ ...prev, [interfaceName]: validationErrors }));
      toast.error("Please fix validation errors before saving");
      return;
    }

    setSaving((prev) => ({ ...prev, [interfaceName]: true }));
    try {
      await updateNetworkInterface(interfaceName, data);
      toast.success(`Network interface ${interfaceName} updated successfully`);

      // Update the interfaces list and reset change tracking
      await fetchInterfaces();
    } catch (error: any) {
      console.error("Failed to update network interface:", error);
      toast.error(
        error?.response?.data?.details || "Failed to update network interface"
      );
    } finally {
      setSaving((prev) => ({ ...prev, [interfaceName]: false }));
    }
  };

  useEffect(() => {
    fetchInterfaces();
  }, []);

  return (
    <div>
      <div className="admin-card">
        <div className="admin-card-header">
          <h2 className="admin-card-title">
            <HiGlobe />
            Network Configuration
          </h2>
          <div className="flex items-center gap-3">
            <button
              className="refresh-button"
              onClick={fetchInterfaces}
              disabled={loading}
            >
              <HiRefresh />
            </button>
          </div>
        </div>
        <div className="admin-card-body">
          {/* Notice banner for disabled network configuration */}
          {loading ? (
            <div className="loading-container">
              <div className="loading-text">
                <Spinner size="sm" color="primary" />
                <span>Loading network interfaces...</span>
              </div>
            </div>
          ) : interfaces.length === 0 ? (
            <div className="empty-state">
              <div className="empty-state-icon">
                <HiDesktopComputer />
              </div>
              <p className="empty-state-text">
                No network interfaces found on this system.
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {interfaces.map((iface) => (
                <div key={iface.name} className="network-card">
                  <div className="network-card-header">
                    <div>
                      <h3 className="network-card-title">
                        {iface.is_wireless ? <HiWifi /> : <HiServer />}
                        {iface.name}
                      </h3>
                      <p className="network-card-subtitle">
                        MAC: {iface.mac_address}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      {iface.is_wireless && (
                        <div className="status-chip admin">
                          <HiWifi />
                          Wireless
                        </div>
                      )}
                      <div
                        className={`status-chip ${
                          iface.is_active ? "approved" : "rejected"
                        }`}
                      >
                        {iface.is_active ? (
                          <>
                            <HiCheck />
                            Active
                          </>
                        ) : (
                          <>
                            <HiX />
                            Inactive
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="network-card-body">
                    <div className="network-form-grid">
                      <div className="form-group">
                        <label className="form-label">IP Address</label>
                        <input
                          type="text"
                          className={`form-input compact-input ${
                            errors[iface.name]?.ip_address
                              ? "border-red-500"
                              : ""
                          }`}
                          value={formData[iface.name]?.ip_address || ""}
                          onChange={(e) =>
                            handleInputChange(
                              iface.name,
                              "ip_address",
                              e.target.value
                            )
                          }
                          placeholder="e.g., *************"
                        />
                        {errors[iface.name]?.ip_address && (
                          <span className="text-red-500 text-xs mt-1">
                            {errors[iface.name].ip_address}
                          </span>
                        )}
                      </div>
                      <div className="form-group">
                        <label className="form-label">Netmask</label>
                        <input
                          type="text"
                          className={`form-input compact-input ${
                            errors[iface.name]?.netmask ? "border-red-500" : ""
                          }`}
                          value={formData[iface.name]?.netmask || ""}
                          onChange={(e) =>
                            handleInputChange(
                              iface.name,
                              "netmask",
                              e.target.value
                            )
                          }
                          placeholder="e.g., 24 or *************"
                        />
                        {errors[iface.name]?.netmask && (
                          <span className="text-red-500 text-xs mt-1">
                            {errors[iface.name].netmask}
                          </span>
                        )}
                      </div>
                      <div className="form-group">
                        <label className="form-label">Gateway</label>
                        <input
                          type="text"
                          className={`form-input compact-input ${
                            errors[iface.name]?.gateway ? "border-red-500" : ""
                          }`}
                          value={formData[iface.name]?.gateway || ""}
                          onChange={(e) =>
                            handleInputChange(
                              iface.name,
                              "gateway",
                              e.target.value
                            )
                          }
                          placeholder="e.g., 192.168.1.1"
                        />
                        {errors[iface.name]?.gateway && (
                          <span className="text-red-500 text-xs mt-1">
                            {errors[iface.name].gateway}
                          </span>
                        )}
                      </div>
                      <div className="form-group">
                        <label className="form-label">DNS Servers</label>
                        <input
                          type="text"
                          className={`form-input compact-input ${
                            errors[iface.name]?.dns_servers
                              ? "border-red-500"
                              : ""
                          }`}
                          value={formData[iface.name]?.dns_servers || ""}
                          onChange={(e) =>
                            handleInputChange(
                              iface.name,
                              "dns_servers",
                              e.target.value
                            )
                          }
                          placeholder="e.g., 8.8.8.8,8.8.4.4"
                        />
                        {errors[iface.name]?.dns_servers && (
                          <span className="text-red-500 text-xs mt-1">
                            {errors[iface.name].dns_servers}
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="mt-4 flex justify-end">
                      <button
                        className={`update-button ${
                          !hasChanges[iface.name] ||
                          Object.keys(errors[iface.name] || {}).length > 0
                            ? "opacity-50 cursor-not-allowed"
                            : ""
                        }`}
                        onClick={() => handleSaveInterface(iface.name)}
                        disabled={
                          !hasChanges[iface.name] ||
                          Object.keys(errors[iface.name] || {}).length > 0 ||
                          saving[iface.name]
                        }
                        title={
                          !hasChanges[iface.name]
                            ? "No changes to save"
                            : Object.keys(errors[iface.name] || {}).length > 0
                            ? "Fix validation errors first"
                            : "Save network configuration"
                        }
                      >
                        {saving[iface.name] ? (
                          <>
                            <Spinner size="sm" color="current" />
                            Saving...
                          </>
                        ) : (
                          <>
                            <HiSave className="mr-1" />
                            Save Changes
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default NetworkSettings;
