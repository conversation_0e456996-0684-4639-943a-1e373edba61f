import { Navigate } from 'react-router-dom';
import { ReactNode, useEffect, useState } from 'react';

interface AdminRouteProps {
  children: ReactNode;
}

const AdminRoute = ({ children }: AdminRouteProps) => {
  const [isAdmin, setIsAdmin] = useState<boolean | null>(null);

  useEffect(() => {
    // Check if user is logged in and is an admin
    const userJson = localStorage.getItem('user');
    if (!userJson) {
      setIsAdmin(false);
      return;
    }

    try {
      const user = JSON.parse(userJson);
      setIsAdmin(user.role === 'admin');
    } catch (error) {
      console.error('Failed to parse user data:', error);
      setIsAdmin(false);
    }
  }, []);

  // Show loading state while checking
  if (isAdmin === null) {
    return <div>Loading...</div>;
  }

  // Redirect to home if not admin
  if (!isAdmin) {
    return <Navigate to="/" replace />;
  }

  // Render children if admin
  return <>{children}</>;
};

export default AdminRoute;
