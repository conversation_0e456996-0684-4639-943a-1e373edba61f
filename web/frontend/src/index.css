:root {
  /* Font settings */
  font-family: 'Roboto', system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Color scheme */
  color-scheme: dark;

  /* Color variables */
  --color-primary: #2196f3;
  --color-primary-light: #64b5f6;
  --color-primary-dark: #1976d2;
  --color-secondary: #6c5ce7;
  --color-secondary-light: #a29bfe;
  --color-success: #00b894;
  --color-success-light: #00d1a0;
  --color-warning: #fdcb6e;
  --color-warning-light: #ffeaa7;
  --color-danger: #ff4757;
  --color-danger-light: #ff6b81;

  /* Background colors */
  --color-bg-primary: #121212;
  --color-bg-secondary: #1e1e1e;
  --color-bg-tertiary: #2a2a2a;
  --color-bg-elevated: #303030;

  /* Text colors */
  --color-text-primary: rgba(255, 255, 255, 0.92);
  --color-text-secondary: rgba(255, 255, 255, 0.7);
  --color-text-tertiary: rgba(255, 255, 255, 0.5);

  /* Border colors */
  --color-border-primary: #404040;
  --color-border-secondary: rgba(255, 255, 255, 0.15);

  /* Transition speeds */
  --transition-fast: 0.2s;
  --transition-medium: 0.3s;
  --transition-slow: 0.5s;

  /* Shadow values */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.2);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.4);
  --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.5);

  /* Apply base colors */
  color: var(--color-text-primary);
  background-color: var(--color-bg-secondary);
}

/* Add smooth scrolling to the entire document */
html {
  scroll-behavior: smooth;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
  background-color: var(--color-bg-secondary);
}

#root {
  width: 100%;
  height: 100vh;
}

a {
  font-weight: 500;
  color: var(--color-primary);
  text-decoration: inherit;
  transition: color var(--transition-fast), transform var(--transition-fast);
  position: relative;
}

a:hover {
  color: var(--color-primary-light);
  transform: translateY(-1px);
}

a:after {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  bottom: -2px;
  left: 0;
  background-color: var(--color-primary-light);
  transform: scaleX(0);
  transform-origin: bottom right;
  transition: transform var(--transition-medium);
}

a:hover:after {
  transform: scaleX(1);
  transform-origin: bottom left;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
  letter-spacing: -0.5px;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: 'Roboto', system-ui, sans-serif;
  background-color: var(--color-bg-secondary);
  color: var(--color-text-primary);
  cursor: pointer;
  transition: all var(--transition-medium);
  position: relative;
  overflow: hidden;
}

button:hover {
  border-color: var(--color-primary);
  background-color: var(--color-bg-tertiary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

button:after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: var(--transition-medium);
}

button:hover:after {
  left: 100%;
  transition: 0.5s;
}

button:focus,
button:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}
