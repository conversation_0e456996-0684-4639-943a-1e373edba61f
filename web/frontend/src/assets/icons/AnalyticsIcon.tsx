interface IconProps {
  className?: string;
}

const AnalyticsIcon = ({ className }: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    className={className}
    width="50"
    height="64"
    viewBox="0 0 512 512"
  >
    <g>
      <path
        fill="#fff"
        d="M128 256c0 17.7-14.3 32-32 32s-32-14.3-32-32s14.3-32 32-32s32 14.3 32 32zm128-32c-17.7 0-32 14.3-32 32s14.3 32 32 32s32-14.3 32-32s-14.3-32-32-32zm128-32c-17.7 0-32 14.3-32 32v128c0 17.7 14.3 32 32 32s32-14.3 32-32V224c0-17.7-14.3-32-32-32zM512 80c0-44.2-35.8-80-80-80H80C35.8 0 0 35.8 0 80v352c0 44.2 35.8 80 80 80h352c44.2 0 80-35.8 80-80V80zM256 480H80c-26.5 0-48-21.5-48-48V80c0-26.5 21.5-48 48-48h352c26.5 0 48 21.5 48 48v352c0 26.5-21.5 48-48 48H256z"
      />
      <path
        fill="#fff"
        d="M64 224c-17.7 0-32 14.3-32 32s14.3 32 32 32c17.7 0 32-14.3 32-32s-14.3-32-32-32zm96 32c0-17.7 14.3-32 32-32s32 14.3 32 32-14.3 32-32 32-32-14.3-32-32zm160 0c0 17.7-14.3 32-32 32s-32-14.3-32-32V192c0-17.7 14.3-32 32-32s32 14.3 32 32v64z"
      />
    </g>
  </svg>
);

export default AnalyticsIcon;
