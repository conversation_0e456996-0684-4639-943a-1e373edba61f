interface IconProps {
    className?: string;
}

const FileManagerIcon = ({ className }: IconProps) => (
    <svg className={className} width="64" height="64" viewBox="0 0 104 94" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
        <g id="3.Multimedia" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd" strokeLinecap="round" strokeLinejoin="round">
            <g id="Multimedia-(Stroke)" transform="translate(-1898.000000, -305.000000)" stroke="#fff" strokeWidth="3.5">
                <g id="20-multimeda-video-collection" transform="translate(1900.000000, 307.000000)">
                    <polygon id="Layer-1" points="80 20 4.26325641e-14 20 4.26325641e-14 90 80 90"></polygon>
                    <path d="M30,40.8258214 C30,39.1727116 31.1516665,38.5236013 32.5684311,39.37366 L55.2630336,52.9904215 C56.6815389,53.8415247 56.6797982,55.2224801 55.2630336,56.0725388 L32.5684311,69.6893004 C31.1499258,70.5404035 30,69.8797629 30,68.237139 L30,40.8258214 Z" id="Layer-2"></path>
                    <polygon id="Layer-3" points="90 15 90 10 10 10 10 20 80 20 80 80 90 80"></polygon>
                    <polygon id="Layer-4" points="100 5 100 0 20 0 20 10 90 10 90 70 100 70"></polygon>
                </g>
            </g>
        </g>
    </svg>
)

export default FileManagerIcon;
