import { useEffect } from "react";
import { useDispatch } from "react-redux";
import { uploadStorage } from "../utils/uploadStorage";
import {
  restoreUploadState,
  markInterruptedUploadsAsFailed,
} from "../redux/uploadSlice";
import { toast } from "react-toastify";

export const useUploadRestoration = () => {
  const dispatch = useDispatch();

  useEffect(() => {
    // Only run on initial app load
    const restoreUploadStateFromStorage = () => {
      try {
        const persistedState = uploadStorage.loadUploadState();

        if (persistedState) {
          console.log(
            "Restoring upload state from localStorage:",
            persistedState
          );

          // Restore the state to Redux
          dispatch(restoreUploadState(persistedState));

          // If uploads were in progress, they were likely interrupted
          if (persistedState.isUploading) {
            console.log(
              "Previous upload session was interrupted, showing restored state"
            );

            // Show a notification to the user
            toast.warning(
              "Previous upload session was interrupted. Files that were uploading will be marked as failed.",
              {
                position: "top-right",
                autoClose: 5000,
                hideProgressBar: false,
                closeOnClick: true,
                pauseOnHover: true,
                draggable: true,
              }
            );

            // Mark interrupted uploads as failed after a brief delay
            // This gives the user time to see the restored state
            setTimeout(() => {
              dispatch(markInterruptedUploadsAsFailed());
            }, 2000);
          } else {
            // If uploads were completed, show a success message
            const completedFiles = persistedState.files.filter(
              (f) => f.status === "completed"
            ).length;
            const failedFiles = persistedState.files.filter(
              (f) => f.status === "failed"
            ).length;

            if (completedFiles > 0) {
              toast.success(
                `Previous upload session: ${completedFiles} file(s) completed${
                  failedFiles > 0 ? `, ${failedFiles} failed` : ""
                }`,
                {
                  position: "top-right",
                  autoClose: 3000,
                }
              );
            }
          }
        }
      } catch (error) {
        console.error("Error restoring upload state:", error);
        uploadStorage.clearUploadState();
      }
    };

    restoreUploadStateFromStorage();
  }, [dispatch]);
};
