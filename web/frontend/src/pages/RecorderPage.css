.recorder-page {
  max-width: 1280px;
  margin: 0 auto;
  padding: 1rem;
}

/* Fade-in animation for the page */
.recorder-page-fade-in {
  animation: fadeIn 0.7s ease-in-out forwards;
  opacity: 0;
  box-shadow: 0 0 0 rgba(33, 150, 243, 0);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
    box-shadow: 0 0 20px rgba(33, 150, 243, 0.3);
  }
  50% {
    box-shadow: 0 0 15px rgba(33, 150, 243, 0.2);
  }
  to {
    opacity: 1;
    transform: translateY(0);
    box-shadow: 0 0 0 rgba(33, 150, 243, 0);
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.header h1 {
  margin: 0;
  color: #2196f3;
}

.header-buttons {
  display: flex;
  gap: 1rem;
}

.create-button,
.import-button {
  background-color: #2a2a2a;
  color: white;
  border: 1px solid #3a3a3a;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.create-button:hover,
.import-button:hover {
  background-color: #3a3a3a;
  border-color: #2196f3;
}

.import-button {
  background-color: #1a1a1a;
  border-color: #2196f3;
}

.import-button:hover {
  background-color: rgba(33, 150, 243, 0.2);
  border-color: #2196f3;
}

.button-icon {
  font-size: 1rem;
}

.action-button .button-icon {
  font-size: 0.85rem;
}

.empty-state {
  text-align: center;
  padding: 3rem;
  background-color: #1a1a1a;
  border-radius: 8px;
  margin-top: 2rem;
}

.empty-state h2 {
  margin-bottom: 1rem;
  color: #2196f3;
}

.empty-state p {
  margin-bottom: 2rem;
  color: #888;
}

.empty-state-button {
  background-color: #2a2a2a;
  color: white;
  border: 1px solid #3a3a3a;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 auto;
}

.empty-state-button:hover {
  background-color: #3a3a3a;
  border-color: #2196f3;
}

.recorder-table-container {
  border-radius: 12px;
  overflow: hidden;
  background-color: #1a1a1a;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  margin-top: 1rem;
  border: 1px solid #333;
  transition: all 0.3s ease;
  position: relative;
  overflow-x: auto;
  overflow-y: hidden;
}

/* Custom scrollbar styling */
.recorder-table-container::-webkit-scrollbar {
  height: 8px;
}

.recorder-table-container::-webkit-scrollbar-track {
  background: #2a2a2a;
  border-radius: 4px;
}

.recorder-table-container::-webkit-scrollbar-thumb {
  background: linear-gradient(90deg, #2196f3, #64b5f6);
  border-radius: 4px;
  border: 1px solid #1a1a1a;
  transition: all 0.3s ease;
}

.recorder-table-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(90deg, #1976d2, #2196f3);
  box-shadow: 0 0 8px rgba(33, 150, 243, 0.5);
}

.recorder-table-container::-webkit-scrollbar-thumb:active {
  background: linear-gradient(90deg, #1565c0, #1976d2);
}

/* Firefox scrollbar styling */
.recorder-table-container {
  scrollbar-width: thin;
  scrollbar-color: #2196f3 #2a2a2a;
}

/* Scroll shadows for better UX */
.recorder-table-container::before,
.recorder-table-container::after {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  width: 20px;
  pointer-events: none;
  z-index: 5;
  transition: opacity 0.3s ease;
}

.recorder-table-container::before {
  left: 0;
  background: linear-gradient(to right, rgba(26, 26, 26, 0.8), transparent);
  opacity: 0;
}

.recorder-table-container::after {
  right: 0;
  background: linear-gradient(to left, rgba(26, 26, 26, 0.8), transparent);
  opacity: 0;
}

/* Show shadows when content is scrollable */
.recorder-table-container[data-scroll="left"]::before,
.recorder-table-container[data-scroll="both"]::before {
  opacity: 1;
}

.recorder-table-container[data-scroll="right"]::after,
.recorder-table-container[data-scroll="both"]::after {
  opacity: 1;
}

.recorder-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  min-width: 800px; /* Minimum width to ensure proper layout */
}

.recorder-table thead {
  background-color: #1e1e1e;
  position: sticky;
  top: 0;
  z-index: 10;
}

.recorder-table th {
  color: #fff;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.85rem;
  letter-spacing: 0.5px;
  padding: 0.75rem 1.25rem;
  text-align: left;
  border-bottom: 2px solid #333;
  position: relative;
  height: 48px;
}

.recorder-table th::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(
    90deg,
    rgba(100, 108, 255, 0) 0%,
    rgba(100, 108, 255, 0.3) 50%,
    rgba(100, 108, 255, 0) 100%
  );
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.recorder-table th:hover::after {
  transform: scaleX(1);
}

.recorder-table td {
  padding: 0.5rem 1.25rem;
  text-align: left;
  border-bottom: 1px solid #333;
  vertical-align: middle;
  color: #e0e0e0;
  font-size: 0.95rem;
  height: 52px;
}

.recorder-table tr {
  transition: all 0.2s ease;
}

.recorder-table tr:hover {
  background-color: rgba(100, 108, 255, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.recorder-table tr:last-child td {
  border-bottom: none;
}

.text-muted {
  color: #888;
}

.text-small {
  font-size: 0.7rem;
  color: #666;
  line-height: 1.2;
}

.codec-info {
  display: flex;
  flex-direction: column;
  gap: 0.15rem;
  font-size: 0.85rem;
}

.scheduled-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.scheduled-status {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  display: inline-block;
  max-width: fit-content;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.scheduled-status.scheduled {
  background-color: rgba(33, 150, 243, 0.2);
  color: #2196f3;
  border: 1px solid rgba(33, 150, 243, 0.3);
}

.scheduled-status.immediate {
  background-color: rgba(76, 175, 80, 0.2);
  color: #4caf50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.scheduled-time {
  font-size: 0.75rem;
  color: #888;
  font-family: "Courier New", monospace;
  margin-top: 0.1rem;
}

.status-container {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-dot.running {
  background-color: #4caf50;
  box-shadow: 0 0 5px #4caf50;
  animation: pulse 1.5s infinite;
}

.status-dot.stopped {
  background-color: #f44336;
}

.status-dot.completed {
  background-color: #2196f3;
}

.status-dot.failed {
  background-color: #ff9800;
}

.status-dot.queue {
  background-color: #9c27b0;
  box-shadow: 0 0 5px #9c27b0;
}

.status-dot.transcoding {
  background-color: #ff9800;
  box-shadow: 0 0 5px #ff9800;
  animation: pulse 1.5s infinite;
}

.status-text {
  font-weight: 600;
  font-size: 0.7rem;
}

.status-text.running {
  color: #4caf50;
}

.status-text.stopped {
  color: #f44336;
}

.status-text.completed {
  color: #2196f3;
}

.status-text.failed {
  color: #ff9800;
}

.status-text.queue {
  color: #9c27b0;
}

.status-text.transcoding {
  color: #ff9800;
}

.progress-container {
  margin-top: 0.3rem;
  display: flex;
  flex-direction: column;
  gap: 0.15rem;
}

.progress-bar-container {
  width: 100%;
  height: 4px;
  background-color: #333;
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: #4caf50;
  transition: width 0.3s ease;
}

.progress-times {
  display: flex;
  justify-content: space-between;
  font-size: 0.65rem;
  color: #888;
}

.sync-status {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  margin-top: 0.2rem;
}

.sync-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
}

.sync-dot.synced {
  background-color: #4caf50;
}

.sync-dot.unsynced {
  background-color: #f44336;
}

.sync-text {
  font-size: 0.65rem;
  font-weight: 600;
}

.sync-text.synced {
  color: #4caf50;
}

.sync-text.unsynced {
  color: #f44336;
}

.sync-text.connecting-text {
  color: #ff9800;
}

.sync-dot.connecting-dot {
  background-color: #ff9800;
  animation: pulse-animation 1.5s infinite;
}

.joined-indicator {
  margin-top: 0.3rem;
  margin-bottom: 0.3rem;
}

.stream-info {
  margin-top: 0.2rem;
  font-size: 0.7rem;
  color: #888;
  display: flex;
  flex-direction: column;
  gap: 0.1rem;
}

.info-label {
  font-weight: 600;
  color: #aaa;
}

.action-buttons {
  display: flex;
  gap: 0.3rem;
  flex-wrap: nowrap;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.4rem;
  background-color: transparent;
  border: 1px solid #444;
  border-radius: 6px;
  padding: 0.4rem 0.6rem;
  color: #fff;
  font-size: 0.85rem;
  font-weight: 500;
  transition: all 0.2s ease;
  min-width: 80px;
  height: 32px;
  white-space: nowrap;
}

.action-button:hover:not(:disabled):not(.disabled) {
  background-color: rgba(33, 150, 243, 0.1);
  border-color: #2196f3;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.action-button.start {
  border-color: #4caf50;
  background-color: rgba(76, 175, 80, 0.1);
}

.action-button.start:hover:not(:disabled):not(.disabled) {
  background-color: rgba(76, 175, 80, 0.2);
  border-color: #4caf50;
  color: #4caf50;
}

.action-button.stop {
  border-color: #f44336;
  background-color: rgba(244, 67, 54, 0.1);
}

.action-button.stop:hover:not(:disabled):not(.disabled) {
  background-color: rgba(244, 67, 54, 0.2);
  border-color: #f44336;
  color: #f44336;
}

.action-button.edit {
  border-color: #2196f3;
  background-color: rgba(33, 150, 243, 0.1);
}

.action-button.edit:hover:not(:disabled):not(.disabled) {
  background-color: rgba(33, 150, 243, 0.2);
  border-color: #2196f3;
  color: #2196f3;
}

.action-button.delete {
  border-color: #ff9800;
  background-color: rgba(255, 152, 0, 0.1);
}

.action-button.delete:hover:not(:disabled):not(.disabled) {
  background-color: rgba(255, 152, 0, 0.2);
  border-color: #ff9800;
  color: #ff9800;
}

.action-button.join {
  border-color: #9c27b0;
  background-color: rgba(156, 39, 176, 0.1);
}

.action-button.join:hover:not(:disabled):not(.disabled) {
  background-color: rgba(156, 39, 176, 0.2);
  border-color: #9c27b0;
  color: #9c27b0;
}

.action-button.joined {
  border-color: #673ab7;
  background-color: rgba(103, 58, 183, 0.2);
  color: #ffffff;
}

.action-button.joined:hover:not(:disabled):not(.disabled) {
  background-color: rgba(103, 58, 183, 0.3);
  border-color: #673ab7;
  color: #673ab7;
}

.action-button.disabled,
.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
  pointer-events: auto !important;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 2rem;
  gap: 1rem;
}

.pagination-button {
  padding: 0.5rem 1rem;
  background-color: #2a2a2a;
  color: white;
  border: 1px solid #3a3a3a;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.pagination-button:hover:not(:disabled) {
  background-color: #3a3a3a;
  border-color: #2196f3;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  color: #888;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 1.5rem;
  z-index: 1000;
}

/* Skeleton loading styles */
.stream-info-skeleton {
  margin-top: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
  font-size: 0.7rem;
  color: #888;
}

.skeleton-item {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.skeleton-line {
  height: 8px;
  background: linear-gradient(90deg, #333 25%, #444 50%, #333 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
  flex-grow: 1;
}

.skeleton-item:nth-child(1) .skeleton-line {
  width: 60%;
}

.skeleton-item:nth-child(2) .skeleton-line {
  width: 50%;
}

.skeleton-item:nth-child(3) .skeleton-line {
  width: 40%;
}

@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.pulse {
  animation: pulse-animation 1.5s infinite;
}

@keyframes pulse-animation {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 152, 0, 0.7);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(255, 152, 0, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 152, 0, 0);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(76, 175, 80, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
  }
}

/* Simple loading animation for join/unjoin button */
.loading-text {
  animation: pulse-opacity 1.2s ease-in-out infinite;
}

@keyframes pulse-opacity {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.action-button.loading {
  background-color: #333;
  cursor: wait;
  border-color: #555;
}

/* Responsive design for smaller screens */
@media (max-width: 1024px) {
  .recorder-table {
    min-width: 900px; /* Slightly wider on tablets */
  }

  .recorder-table th,
  .recorder-table td {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }

  .action-buttons {
    gap: 0.2rem;
  }

  .action-button {
    min-width: 70px;
    padding: 0.3rem 0.5rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 768px) {
  .recorder-page {
    padding: 0.5rem;
  }

  .header {
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .recorder-table {
    min-width: 1000px; /* Force horizontal scroll on mobile */
  }

  .recorder-table th,
  .recorder-table td {
    padding: 0.3rem 0.6rem;
    font-size: 0.85rem;
  }

  .action-buttons {
    flex-direction: column;
    gap: 0.2rem;
  }

  .action-button {
    min-width: 60px;
    padding: 0.25rem 0.4rem;
    font-size: 0.75rem;
    height: 28px;
  }

  /* Enhance scrollbar on mobile */
  .recorder-table-container::-webkit-scrollbar {
    height: 12px;
  }

  .recorder-table-container::-webkit-scrollbar-thumb {
    background: linear-gradient(90deg, #2196f3, #64b5f6);
    border-radius: 6px;
    border: 2px solid #1a1a1a;
  }

  /* Add scroll hint on mobile */
  .recorder-table-container::after {
    width: 30px;
  }
}

@media (max-width: 480px) {
  .header h1 {
    font-size: 1.5rem;
  }

  .create-button {
    width: 100%;
    justify-content: center;
  }

  .recorder-table {
    min-width: 1100px; /* Even wider to ensure all content is accessible */
  }

  .scheduled-status {
    font-size: 0.7rem;
    padding: 0.15rem 0.4rem;
  }

  .scheduled-time {
    font-size: 0.7rem;
  }
}
