import { useDispatch, useSelector } from "react-redux";
import { useEffect, useState } from "react";
import { getSchedules } from "@/api/schedulerApi.ts";
import { selectSchedulers, setSchedulers } from "@/redux/schedulerSlice.ts";
import Loading from "@/components/common/Loading.tsx";
import EmptySchedulers from "@/components/scheduler/EmptySchedulers.tsx";
import SchedulersTable from "@/components/scheduler/SchedulersTable.tsx";
import CreateScheduleModal from "@/components/scheduler/modal/CreateScheduleModal.tsx";
import DeleteScheduleModal from "@/components/scheduler/modal/DeleteScheduleModal.tsx";
import { HiPlus } from "react-icons/hi";
import './SchedulersPage.css';

const SchedulersPage = () => {
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(false);

  const schedulers = useSelector(selectSchedulers);

  const [createModalOpen, setCreateModalOpen] = useState<boolean>(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState<boolean>(true);
  const [scheduleId, setScheduleId] = useState<number|null>(null);
  const [reload, setReload] = useState<boolean>(false);

  const [pagination, setPagination] = useState({ page: 1, limit: 10, totalPages: 1 });

  useEffect(() => {
    const fetchSchedulers = async () => {
      setIsLoading(true);

      try {
        const response = await getSchedules(pagination.page, pagination.limit);
        dispatch(setSchedulers({ schedulers: response.items }));

        setPagination({
          ...pagination,
          totalPages: response.total_pages,
        })
      } catch (error) {
        console.error("Failed load schedules", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSchedulers();
  }, [pagination.page, pagination.limit, reload]);

  if (isLoading) return <Loading />;

  return (
    <div className="schedulers-page">
      <div className="header">
        <h1>Schedules</h1>
        <button className="create-button" onClick={() => setCreateModalOpen(true)}>
          <HiPlus className="button-icon" />
          <span>Create New Schedule</span>
        </button>
      </div>
      <div className="schedulers-content">
        {(!schedulers || schedulers.length == 0) && (
          <EmptySchedulers handleCreate={() => setCreateModalOpen(true)} />
        )}
        {createModalOpen && (
          <CreateScheduleModal
            setIsLoading={setIsLoading}
            close={() => setCreateModalOpen(false)}
            reloadPage={() => setReload(!reload)}
          />
        )}
        {deleteModalOpen && scheduleId && (
          <DeleteScheduleModal
            id={scheduleId}
            setIsLoading={setIsLoading}
            reloadPage={() => setReload(!reload)}
            close={() => {
              setDeleteModalOpen(false);
              setScheduleId(null);
            }}
          />
        )}
        {schedulers && schedulers.length > 0 && (
          <SchedulersTable
            handleDelete={(id: number|null) => {
              setScheduleId(id);
              setDeleteModalOpen(true);
            }}
            pagination={pagination}
            setPagination={setPagination}
          />
        )}
      </div>
    </div>
  );
}

export default SchedulersPage;
