.file-manager-page {
  max-width: 1600px;
  margin: 0 auto;
  margin-bottom: 100px;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  background-color: #121212;
  color: #ffffff;
  height: 100%;
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #333;
  position: relative;
  gap: 2rem;
}

.header::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 10%;
  width: 80%;
  height: 1px;
  background: linear-gradient(
    90deg,
    rgba(33, 150, 243, 0) 0%,
    rgba(30, 139, 228, 0.3) 50%,
    rgba(33, 150, 243, 0) 100%
  );
}

.header h1 {
  margin: 0;
  font-size: 2rem;
  font-weight: 600;
  color: #fff;
  position: relative;
  display: inline-block;
  flex-shrink: 0;
}

.header h1::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, #2196f3, #ffa500);
  border-radius: 3px;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.upload-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  border-radius: 10px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 14px rgba(59, 130, 246, 0.3);
  min-height: 44px;
  white-space: nowrap;
}

.upload-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.upload-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.button-icon {
  width: 18px;
  height: 18px;
}

/* Upload Detail Button */
.upload-detail-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  border-radius: 10px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 14px rgba(16, 185, 129, 0.3);
  min-height: 44px;
  white-space: nowrap;
}

.upload-detail-button:hover {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.content-area {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  flex: 1;
}

.location-menu-container {
  width: 100%;
}

.upload-container {
  width: 100%;
}

/* Action bar styles */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(
    90deg,
    rgba(33, 150, 243, 0.15),
    rgba(33, 150, 243, 0.05)
  );
  border: 1px solid rgba(33, 150, 243, 0.3);
  border-radius: 12px;
  padding: 1rem 1.5rem;
  margin-bottom: 1.25rem;
  animation: fadeInDown 0.3s ease-out;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.selected-count {
  font-weight: 600;
  color: #fff;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.05rem;
}

.action-bar-buttons {
  display: flex;
  gap: 0.75rem;
}

.action-bar-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.6rem 1.2rem;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  background-color: rgba(0, 0, 0, 0.2);
  cursor: pointer;
}

.action-bar-button.cancel-button {
  color: #aaaaaa;
  border-color: rgba(170, 170, 170, 0.3);
  background-color: rgba(170, 170, 170, 0.1);
}

.action-bar-button.cancel-button:hover {
  background-color: rgba(170, 170, 170, 0.2);
  border-color: rgba(170, 170, 170, 0.5);
}

.action-bar-button.move-button {
  color: #2196f3;
  border-color: rgba(33, 150, 243, 0.3);
  background-color: rgba(33, 150, 243, 0.1);
}

.action-bar-button.move-button:hover {
  background-color: rgba(33, 150, 243, 0.2);
  border-color: rgba(33, 150, 243, 0.5);
}

.action-bar-button.delete-button {
  color: #f44336;
  border-color: rgba(244, 67, 54, 0.3);
  background-color: rgba(244, 67, 54, 0.1);
}

.action-bar-button.delete-button:hover {
  background-color: rgba(244, 67, 54, 0.2);
  border-color: rgba(244, 67, 54, 0.5);
}

/* Basic upload area styles */
.upload-area {
  border: 2px dashed #0c3a83;
  border-radius: 12px;
  padding: 2rem 1rem;
  text-align: center;
  background: linear-gradient(
    135deg,
    rgba(5, 46, 112, 0.05) 0%,
    rgba(25, 92, 235, 0.05) 100%
  );
  transition: all 0.3s ease;
  cursor: pointer;
}

.upload-area:hover {
  border-color: #3b82f6;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.05) 0%,
    rgba(37, 99, 235, 0.05) 100%
  );
}

.upload-icon {
  font-size: 3rem;
  color: #6b7280;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.upload-area:hover .upload-icon {
  color: #3b82f6;
}

.upload-text {
  font-size: 1.125rem;
  margin-bottom: 0.5rem;
  color: #374151;
}

.upload-highlight {
  font-weight: 600;
  color: #1f2937;
}

.upload-subtext {
  color: #6b7280;
  font-size: 0.875rem;
  margin: 0;
}

.file-table-container {
  flex: 1;
}

.file-table {
  width: 100%;
  border-collapse: collapse;
}

.file-table th,
.file-table td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid #333;
}

.file-table th {
  background-color: #2a2a2a;
  color: #2196f3;
  font-weight: 600;
  font-size: 0.9rem;
}

.file-table tr:hover {
  background-color: #1e1e1e;
}

.file-table tr:last-child td {
  border-bottom: none;
}

.folder-row {
  cursor: pointer;
}

.folder-cell {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #2196f3;
  font-weight: 500;
}

.folder-icon {
  color: #2196f3;
}

.file-cell {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.file-icon {
  color: #888;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.action-button {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.8rem;
  background-color: #2a2a2a;
  color: white;
  border: 1px solid #333;
}

.action-button:hover {
  background-color: #3a3a3a;
  border-color: #2196f3;
}

.action-button.preview:hover {
  color: #2196f3;
}

.action-button.delete:hover {
  color: #f44336;
}

.empty-state {
  text-align: center;
  padding: 3rem;
  background-color: #1a1a1a;
  border-radius: 8px;
  margin: 2rem 1.5rem;
}

.empty-state h2 {
  margin-bottom: 1rem;
  color: #2196f3;
}

.empty-state p {
  margin-bottom: 2rem;
  color: #888;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 1.5rem;
  z-index: 1000;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.bucket-selector {
  padding: 0.5rem;
  font-size: 1rem;

}

.file-list {
  width: 100%;
  overflow-x: auto;
}

.files-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
}

.files-table th,
.files-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.files-table th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.files-table tr:hover {
  background-color: #f9f9f9;
}

.action-button {
  padding: 0.5rem;
  border: none;
  background: none;
  cursor: pointer;
  color: #666;
  transition: color 0.2s;
}

.action-button:hover {
  color: #000;
}

.action-button.delete:hover {
  color: #dc3545;
}

.no-bucket-selected {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  color: #666;
}

.bucket-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  color: #999;
}

/* Responsive design */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }



  .upload-button,
  .upload-detail-button {
    font-size: 13px;
    padding: 10px 16px;
  }
}





