import { useState } from 'react';
import UserManagement from '@/components/admin/UserManagement';
import NetworkSettings from '@/components/admin/NetworkSettings';
import CodecSettings from '@/components/admin/CodecSettings';
import './AdminPage.css';
import { HiUserGroup, HiServer, HiVideoCamera } from 'react-icons/hi';
import {HiCog8Tooth} from "react-icons/hi2";
import GeneralSettings from "@/components/admin/GeneralSettings.tsx";

const AdminPage = () => {
  const [activeSection, setActiveSection] = useState('users');

  const renderContent = () => {
    switch (activeSection) {
      case 'users':
        return <UserManagement />;
      case 'network':
        return <NetworkSettings />;
      case 'codec':
        return <CodecSettings />;
      case 'general':
        return <GeneralSettings />;
      default:
        return <UserManagement />;
    }
  };

  return (
    <div className="admin-page">
      <div className="header">
        <h1>Administrator Dashboard</h1>
      </div>

      <div className="admin-content">
        <div className="admin-tabs">
          <button
            className={`admin-tab ${activeSection === 'users' ? 'active' : ''}`}
            onClick={() => setActiveSection('users')}
          >
            <HiUserGroup />
            <span>User Management</span>
          </button>
          <button
            className={`admin-tab ${activeSection === 'network' ? 'active' : ''}`}
            onClick={() => setActiveSection('network')}
          >
            <HiServer />
            <span>Network Settings</span>
          </button>
          <button
            className={`admin-tab ${activeSection === 'codec' ? 'active' : ''}`}
            onClick={() => setActiveSection('codec')}
          >
            <HiVideoCamera />
            <span>Codec Settings</span>
          </button>
          <button
            className={`admin-tab ${activeSection === 'general' ? 'active' : ''}`}
            onClick={() => setActiveSection('general')}
          >
            <HiCog8Tooth />
            <span>General Settings</span>
          </button>
        </div>

        <div className="admin-main">
          {renderContent()}
        </div>
      </div>
    </div>
  );
};

export default AdminPage;
