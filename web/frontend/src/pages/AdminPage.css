/* Admin Page Layout */
.admin-page {
  max-width: 1280px;
  margin: 0 auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  background-color: #121212;
  color: #ffffff;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.header h1 {
  margin: 0;
  color: #2196f3;
}

.admin-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.admin-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  background-color: #1a1a1a;
  border-radius: 8px;
  padding: 0.5rem;
  border: 1px solid #333;
}

.admin-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.2s;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
}

.admin-tab:hover {
  color: white;
  background-color: #2a2a2a;
}

.admin-tab.active {
  color: #2196f3;
  background-color: #2a2a2a;
}

.admin-tab svg {
  font-size: 1.2rem;
}

.admin-main {
  flex: 1;
  background-color: #121212;
}

.admin-card {
  background-color: #1a1a1a;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 2rem;
  border: 1px solid #333;
}

.admin-card-header {
  background-color: #2a2a2a;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #333;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.admin-card-title {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.admin-card-icon {
  color: #2196f3;
  font-size: 1.2rem;
}

.admin-card-body {
  padding: 1.5rem;
}

/* Table Styles */
.admin-table-container {
  overflow-x: auto;
  border-radius: 8px;
  background-color: #1a1a1a;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #333;
}

.admin-table {
  width: 100%;
  border-collapse: collapse;
}

.admin-table th {
  background-color: #2a2a2a;
  color: #2196f3;
  font-weight: 600;
  font-size: 0.9rem;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #333;
  text-align: left;
  height: 40px;
}

.admin-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #333;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

.admin-table tr:last-child td {
  border-bottom: none;
}

.admin-table tr:hover {
  background-color: #1e1e1e;
}

/* Status and Role Chips */
.status-chip {
  display: inline-flex;
  align-items: center;
  gap: 0.3rem;
  padding: 0.35rem 0.75rem;
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-chip.approved {
  background-color: rgba(76, 175, 80, 0.15);
  color: #4caf50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.status-chip.pending {
  background-color: rgba(255, 152, 0, 0.15);
  color: #ff9800;
  border: 1px solid rgba(255, 152, 0, 0.3);
}

.status-chip.rejected {
  background-color: rgba(244, 67, 54, 0.15);
  color: #f44336;
  border: 1px solid rgba(244, 67, 54, 0.3);
}

.role-chip {
  display: inline-flex;
  align-items: center;
  gap: 0.3rem;
  padding: 0.35rem 0.75rem;
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.role-chip.admin {
  background-color: rgba(33, 150, 243, 0.15);
  color: #2196f3;
  border: 1px solid rgba(33, 150, 243, 0.3);
}

.role-chip.user {
  background-color: rgba(158, 158, 158, 0.15);
  color: #9e9e9e;
  border: 1px solid rgba(158, 158, 158, 0.3);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.action-button {
  padding: 0.2rem 0.4rem;
  border: 1px solid #3a3a3a;
  border-radius: 3px;
  font-size: 0.65rem;
  cursor: pointer;
  transition: all 0.2s;
  background-color: #2a2a2a;
  color: white;
  display: flex;
  align-items: center;
  gap: 0.15rem;
  min-width: 50px;
  justify-content: center;
  height: 24px;
  white-space: nowrap;
}

.action-button.approve {
  background-color: rgba(76, 175, 80, 0.15);
  color: #4caf50;
}

.action-button:hover {
  background-color: #3a3a3a;
  border-color: #2196f3;
  transform: translateY(-1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.action-button.approve {
  border-color: #4caf50;
  background-color: rgba(76, 175, 80, 0.1);
}

.action-button.approve:hover {
  background-color: rgba(76, 175, 80, 0.2);
  border-color: #4caf50;
  color: #4caf50;
}

.action-button.reject {
  border-color: #f44336;
  background-color: rgba(244, 67, 54, 0.1);
}

.action-button.reject:hover {
  background-color: rgba(244, 67, 54, 0.2);
  border-color: #f44336;
  color: #f44336;
}

.action-button.primary {
  border-color: #2196f3;
  background-color: rgba(33, 150, 243, 0.1);
}

.action-button.primary:hover {
  background-color: rgba(33, 150, 243, 0.2);
  border-color: #2196f3;
  color: #2196f3;
}

.action-button.edit {
  border-color: #3498db;
  background-color: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.action-button.edit:hover {
  background-color: rgba(52, 152, 219, 0.2);
  border-color: #3498db;
  color: #3498db;
}

.action-button.delete {
  border-color: #e74c3c;
  background-color: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.action-button.delete:hover {
  background-color: rgba(231, 76, 60, 0.2);
  border-color: #e74c3c;
  color: #e74c3c;
}

.delete-confirmation {
  text-align: center;
  padding: 1rem;
}

.delete-button {
  background-color: #1a1a1a;
  color: #e74c3c;
  border: 1px solid #e74c3c;
}

.delete-button:hover {
  background-color: rgba(231, 76, 60, 0.2);
}

/* Network Card Styles */
.network-card {
  background-color: #1a1a1a;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #333;
}

.network-card-header {
  background-color: #2a2a2a;
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #333;
}

.network-card-title {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.network-card-title svg {
  color: #2196f3;
}

.network-card-subtitle {
  margin: 0.25rem 0 0;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.6);
}

.network-card-body {
  padding: 1.5rem;
}

.network-form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

@media (max-width: 768px) {
  .network-form-grid {
    grid-template-columns: 1fr;
  }
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  background-color: #1e1e1e;
  border: 1px solid #333;
  border-radius: 4px;
  color: white;
  font-size: 0.9rem;
  transition: all 0.3s;
}

.form-input:focus {
  border-color: #2196f3;
  outline: none;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

.update-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: #2a2a2a;
  color: white;
  border: 1px solid #3a3a3a;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  margin-left: auto;
}

.update-button:hover {
  background-color: #3a3a3a;
  border-color: #2196f3;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.create-button {
  background-color: #2a2a2a;
  color: white;
  border: 1px solid #3a3a3a;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.create-button:hover {
  background-color: #3a3a3a;
  border-color: #2196f3;
}

.create-user-button {
  background-color: #1a1a1a;
  color: white;
  border: 1px solid #2196f3;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.create-user-button:hover {
  background-color: rgba(138, 112, 214, 0.2);
}

.refresh-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  /* width: 32px; */
  /* height: 32px; */
  border-radius: 4px;
  background-color: #1a1a1a;
  color: #2196f3;
  border: 1px solid #333;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s;
}

.refresh-button:hover {
  background-color: rgba(138, 112, 214, 0.1);
  border-color: #2196f3;
}

.refresh-button svg {
  font-size: 1.1rem;
}

/* Loading States */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  width: 100%;
}

.loading-text {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.95rem;
}

.empty-state {
  text-align: center;
  padding: 3rem;
  background-color: #1a1a1a;
  border-radius: 8px;
  margin: 2rem 0;
}

.empty-state-icon {
  font-size: 3rem;
  color: #333;
  margin-bottom: 1rem;
}

.empty-state-text {
  color: #888;
  font-size: 1rem;
  max-width: 300px;
  margin: 0 auto;
}

/* Modal Styles */
.admin-modal {
  background-color: #1a1a1a;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  border: 1px solid #333;
  max-width: 600px;
  margin: 0 auto;
}

.admin-modal-header {
  background-color: #2a2a2a;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #333;
}

.admin-modal-title {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.admin-modal-title svg {
  color: #2196f3;
}

.admin-modal-body {
  padding: 1.5rem;
}

.admin-modal-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #333;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.admin-modal-cancel {
  padding: 0.5rem 1rem;
  background-color: #2a2a2a;
  color: white;
  border: 1px solid #3a3a3a;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.admin-modal-cancel:hover {
  background-color: #3a3a3a;
  border-color: #2196f3;
}

.admin-modal-submit {
  padding: 0.5rem 1rem;
  background-color: #2a2a2a;
  color: white;
  border: 1px solid #3a3a3a;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.admin-modal-submit:hover {
  background-color: #3a3a3a;
  border-color: #2196f3;
}

/* Responsive Adjustments */
@media (max-width: 1024px) {
  .admin-page {
    padding: 0.75rem;
  }

  .admin-tabs {
    flex-wrap: wrap;
  }

  .admin-tab {
    flex: 1;
    min-width: 120px;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .header h1 {
    font-size: 1.3rem;
  }

  .admin-card-header {
    padding: 0.75rem 1rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .admin-card-header > div {
    width: 100%;
    display: flex;
    justify-content: flex-end;
  }

  .admin-card-body {
    padding: 1rem;
  }

  .admin-table th,
  .admin-table td {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }

  .action-buttons {
    flex-direction: column;
    gap: 0.25rem;
  }

  .action-button {
    width: 100%;
  }
}
