import React, { useState, useEffect, useCallback, useRef } from "react";
import { toast } from "react-toastify";
import {
  HiRefresh,
  HiFilter,
  HiX,
  HiDownload,
  HiChevronLeft,
  HiChevronRight,
  HiClock,
  HiEye,
  HiDatabase,
  HiInformationCircle,
  HiSearch,
} from "react-icons/hi";
import { logsApi } from "../api/logsApi";
import {
  LogEntry,
  LogFilter,
  LogsResponse,
  DEFAULT_LOG_FILTER,
  LOG_LEVELS,
  LogLevel,
  LogExportRequest,
} from "../types/logs";
import "./LogsPage.css";

const LogsPage: React.FC = () => {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [loading, setLoading] = useState(false);
  const [filter, setFilter] = useState<LogFilter>(DEFAULT_LOG_FILTER || { limit: 50, offset: 0 });
  const [totalCount, setTotalCount] = useState(0);
  const [isInitialRender, setIsInitialRender] = useState(true);
  const tableContainerRef = useRef<HTMLDivElement>(null);
  const tableWrapperRef = useRef<HTMLDivElement>(null);

  // Auto-refresh functionality
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState(false);
  const [autoRefreshInterval, setAutoRefreshInterval] = useState(30); // seconds
  const autoRefreshRef = useRef<NodeJS.Timeout | null>(null);

  // Filtering
  const [filtersVisible, setFiltersVisible] = useState(false);
  const [tempFilter, setTempFilter] = useState<LogFilter>({
    ...(DEFAULT_LOG_FILTER || { limit: 50, offset: 0 }),
  });

  // Search
  const [searchValue, setSearchValue] = useState("");

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(50);
  const [totalPages, setTotalPages] = useState(1);

  // Export
  const [exporting, setExporting] = useState(false);

  // Load logs with pagination
  const loadLogs = useCallback(
    async (
      newFilter?: LogFilter,
      page?: number,
      size?: number,
      replaceFilter = false
    ) => {
      setLoading(true);
      try {
        const targetPage = page || currentPage;
        const targetSize = size || pageSize;

        let filterToUse: LogFilter;
        if (replaceFilter && newFilter) {
          // Replace the entire filter (for clearing)
          filterToUse = {
            ...newFilter,
            limit: targetSize,
            offset: (targetPage - 1) * targetSize,
          };
        } else {
          // Merge with existing filter (for normal filtering)
          filterToUse = {
            ...filter,
            ...newFilter,
            limit: targetSize,
            offset: (targetPage - 1) * targetSize,
          };
        }

        const response: LogsResponse = await logsApi.getLogs(filterToUse);

        // Add null checks for API response
        if (!response) {
          throw new Error("Invalid response from logs API");
        }

        const logs = Array.isArray(response.logs) ? response.logs : [];
        const totalCount = typeof response.total_count === 'number' ? response.total_count : 0;

        setLogs(logs);
        setTotalCount(totalCount);
        setTotalPages(Math.ceil(totalCount / targetSize));

        if (newFilter) {
          if (replaceFilter) {
            setFilter(newFilter);
          } else {
            setFilter({ ...filter, ...newFilter });
          }
        }
      } catch (error) {
        console.error("Failed to load logs:", error);
        toast.error("Failed to load logs");
        // Set safe defaults on error
        setLogs([]);
        setTotalCount(0);
        setTotalPages(1);
      } finally {
        setLoading(false);
      }
    },
    [filter, currentPage, pageSize]
  );

  // Handle refresh
  const handleRefresh = useCallback(() => {
    loadLogs(undefined, currentPage, pageSize, false);
  }, [loadLogs, currentPage, pageSize]);

  // Handle auto-refresh toggle
  const handleAutoRefreshToggle = useCallback(() => {
    if (autoRefreshEnabled) {
      if (autoRefreshRef.current) {
        clearInterval(autoRefreshRef.current);
        autoRefreshRef.current = null;
      }
      setAutoRefreshEnabled(false);
    } else {
      const intervalId = setInterval(() => {
        loadLogs(undefined, currentPage, pageSize, false);
      }, autoRefreshInterval * 1000);
      autoRefreshRef.current = intervalId;
      setAutoRefreshEnabled(true);
    }
  }, [
    autoRefreshEnabled,
    autoRefreshInterval,
    loadLogs,
    currentPage,
    pageSize,
  ]);

  // Handle search
  const handleSearch = useCallback(
    (searchTerm: string) => {
      setSearchValue(searchTerm);
      setCurrentPage(1);
      const searchFilter = searchTerm
        ? { search: searchTerm }
        : { search: undefined };
      loadLogs(searchFilter, 1, pageSize, false);
    },
    [loadLogs, pageSize]
  );

  // Validate date range
  const isDateRangeValid = useCallback(
    (startDate?: string, endDate?: string) => {
      if (!startDate || !endDate) return true; // Allow partial ranges
      return new Date(startDate) <= new Date(endDate);
    },
    []
  );

  // Handle filter changes
  const handleFilterChange = useCallback(
    (newFilter: Partial<LogFilter>) => {
      try {
        const safeCurrentFilter = tempFilter || { limit: 50, offset: 0 };
        const safeNewFilter = newFilter || {};
        const updatedFilter = { ...safeCurrentFilter, ...safeNewFilter };
        setTempFilter(updatedFilter);
      } catch (error) {
        console.error("Error handling filter change:", error);
      }
    },
    [tempFilter]
  );

  // Apply filters
  const applyFilters = useCallback(() => {
    setCurrentPage(1);
    setFilter(tempFilter);
    loadLogs(tempFilter, 1, pageSize, false);
    // Don't hide filters - keep them visible for further adjustments
  }, [tempFilter, pageSize, loadLogs]);

  // Clear filters
  const clearFilters = useCallback(() => {
    const clearedFilter = {
      limit: pageSize,
      offset: 0,
    };
    setTempFilter(clearedFilter);
    setFilter(clearedFilter);
    setCurrentPage(1);
    setSearchValue(""); // Clear search as well
    loadLogs(clearedFilter, 1, pageSize, true); // Use replaceFilter = true
  }, [pageSize, loadLogs]);

  // Handle pagination
  const handlePageChange = useCallback(
    (page: number) => {
      setCurrentPage(page);
      loadLogs(undefined, page, pageSize, false);
    },
    [loadLogs, pageSize]
  );

  const handlePageSizeChange = useCallback(
    (size: number) => {
      setPageSize(size);
      setCurrentPage(1);
      loadLogs(undefined, 1, size, false);
    },
    [loadLogs]
  );

  // Handle export
  const handleExport = useCallback(
    async (format: "csv" | "json") => {
      setExporting(true);
      try {
        const exportRequest: LogExportRequest = {
          format,
          filter: filter || { limit: 50, offset: 0 },
        };
        const response = await logsApi.exportLogs(exportRequest);

        if (!response || !response.download_url) {
          throw new Error("Invalid export response");
        }

        // Create download link
        const link = document.createElement("a");
        link.href = response.download_url;
        link.download = `logs.${format}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        toast.success(`Logs exported as ${format.toUpperCase()}`);
      } catch (error) {
        console.error("Failed to export logs:", error);
        toast.error("Failed to export logs");
      } finally {
        setExporting(false);
      }
    },
    [filter]
  );

  // Handle scroll shadows for better UX
  const handleTableScroll = useCallback(() => {
    const wrapper = tableWrapperRef.current;
    if (!wrapper) return;

    try {
      const { scrollLeft, scrollWidth, clientWidth } = wrapper;
      const isScrolledLeft = scrollLeft > 0;
      const isScrolledRight = scrollLeft < scrollWidth - clientWidth - 1;

      if (isScrolledLeft && isScrolledRight) {
        wrapper.setAttribute("data-scroll", "both");
      } else if (isScrolledLeft) {
        wrapper.setAttribute("data-scroll", "left");
      } else if (isScrolledRight) {
        wrapper.setAttribute("data-scroll", "right");
      } else {
        wrapper.removeAttribute("data-scroll");
      }
    } catch (error) {
      console.error("Error handling table scroll:", error);
    }
  }, []);

  // Format timestamp to MM/DD/YYYY, HH:MM:SS
  const formatTimestamp = (timestamp: string): string => {
    try {
      if (!timestamp) {
        return "Invalid Date";
      }

      const date = new Date(timestamp);

      // Check if date is valid
      if (isNaN(date.getTime())) {
        return "Invalid Date";
      }

      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const year = date.getFullYear();
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");

      return `${month}/${day}/${year}, ${hours}:${minutes}:${seconds}`;
    } catch (error) {
      console.error("Error formatting timestamp:", error);
      return "Invalid Date";
    }
  };

  // Initial load
  useEffect(() => {
    try {
      loadLogs(undefined, 1, pageSize || 50, false);
      // Trigger fade-in animation
      const timer = setTimeout(() => setIsInitialRender(false), 100);
      return () => clearTimeout(timer);
    } catch (error) {
      console.error("Error during initial load:", error);
      setLoading(false);
    }
  }, []);

  // Handle scroll shadows on mount and resize
  useEffect(() => {
    const wrapper = tableWrapperRef.current;
    if (!wrapper) return;

    const handleResize = () => handleTableScroll();
    handleTableScroll(); // Initial check

    try {
      wrapper.addEventListener("scroll", handleTableScroll);
      window.addEventListener("resize", handleResize);

      return () => {
        if (wrapper) {
          wrapper.removeEventListener("scroll", handleTableScroll);
        }
        window.removeEventListener("resize", handleResize);
      };
    } catch (error) {
      console.error("Error setting up scroll listeners:", error);
    }
  }, [handleTableScroll]);

  // Cleanup auto-refresh on unmount
  useEffect(() => {
    return () => {
      if (autoRefreshRef.current) {
        clearInterval(autoRefreshRef.current);
      }
    };
  }, []);

  // Update auto-refresh interval when it changes
  useEffect(() => {
    if (autoRefreshEnabled && autoRefreshRef.current) {
      clearInterval(autoRefreshRef.current);
      const intervalId = setInterval(() => {
        loadLogs(undefined, currentPage, pageSize, false);
      }, autoRefreshInterval * 1000);
      autoRefreshRef.current = intervalId;
    }
  }, [
    autoRefreshInterval,
    autoRefreshEnabled,
    loadLogs,
    currentPage,
    pageSize,
  ]);

  return (
    <div className={`logs-page ${isInitialRender ? "logs-page-fade-in" : ""}`}>
      {/* Consistent Header Design */}
      <div className="header">
        <h1>System Logs</h1>
        <div className="header-controls-horizontal">
          <div className="auto-refresh-horizontal-group">
            <button
              className={`header-control-btn auto-refresh ${
                autoRefreshEnabled ? "active" : ""
              }`}
              onClick={handleAutoRefreshToggle}
              title={
                autoRefreshEnabled ? "Stop Auto-refresh" : "Start Auto-refresh"
              }
            >
              <HiClock />
              <span>Auto-refreshing</span>
            </button>

            <select
              className="header-interval-select"
              value={autoRefreshInterval}
              onChange={(e) => setAutoRefreshInterval(Number(e.target.value))}
              disabled={!autoRefreshEnabled}
            >
              <option value={10}>10s</option>
              <option value={30}>30s</option>
              <option value={60}>1m</option>
              <option value={300}>5m</option>
            </select>
          </div>

          <button
            className="header-control-btn refresh"
            onClick={handleRefresh}
            disabled={loading}
            title="Refresh Logs"
          >
            <HiRefresh className={loading ? "spinning" : ""} />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Enhanced Content Area */}
      <div className="content-area">
        {/* Enhanced Action Bar */}
        <div className="action-bar">
          <div className="action-group search">
            <div className="action-search-wrapper">
              <HiSearch className="search-icon" />
              <input
                type="text"
                placeholder="Search logs..."
                value={searchValue}
                onChange={(e) => handleSearch(e.target.value)}
                className="action-search-input"
              />
              {searchValue && (
                <button
                  className="clear-search-action-btn"
                  onClick={() => handleSearch("")}
                  title="Clear search"
                >
                  <HiX />
                </button>
              )}
            </div>

            <button
              className={`action-btn filter ${filtersVisible ? "active" : ""}`}
              onClick={() => {
                if (!filtersVisible) {
                  // Sync tempFilter with current filter when opening
                  setTempFilter({ ...filter });
                }
                setFiltersVisible(!filtersVisible);
              }}
              title={filtersVisible ? "Hide Filters" : "Show Filters"}
            >
              <HiFilter />
              <span>{filtersVisible ? "Hide Filters" : "Show Filters"}</span>
              {(() => {
                // Count active filters: level, start_date, end_date, search
                try {
                  const activeFiltersCount = [
                    filter?.level,
                    filter?.start_date,
                    filter?.end_date,
                    filter?.search || searchValue,
                  ].filter(
                    (v) => v !== undefined && v !== "" && v !== null
                  ).length;

                  return (
                    activeFiltersCount > 0 && (
                      <span className="filters-count-badge">
                        {activeFiltersCount}
                      </span>
                    )
                  );
                } catch (error) {
                  console.error("Error counting active filters:", error);
                  return null;
                }
              })()}
            </button>

            {filtersVisible && (
              <button
                className={`action-btn apply-filters ${
                  !isDateRangeValid(tempFilter.start_date, tempFilter.end_date)
                    ? "disabled"
                    : ""
                }`}
                onClick={applyFilters}
                disabled={
                  !isDateRangeValid(tempFilter.start_date, tempFilter.end_date)
                }
                title={
                  !isDateRangeValid(tempFilter.start_date, tempFilter.end_date)
                    ? "Invalid date range: End date must be after start date"
                    : "Apply Filters"
                }
              >
                <HiFilter />
                <span>Apply Filters</span>
              </button>
            )}

            {(filter?.level ||
              filter?.start_date ||
              filter?.end_date ||
              filter?.search ||
              searchValue) && (
              <button
                className="action-btn clear-filters"
                onClick={clearFilters}
                title="Clear Filters"
              >
                <HiX />
                <span>Clear Filters</span>
              </button>
            )}
          </div>

          <div className="action-group secondary"></div>

          <div className="action-group export">
            <button
              className="action-btn export"
              onClick={() => handleExport("csv")}
              disabled={exporting}
              title="Export as CSV"
            >
              <HiDownload />
              <span>CSV</span>
            </button>

            <button
              className="action-btn export"
              onClick={() => handleExport("json")}
              disabled={exporting}
              title="Export as JSON"
            >
              <HiDownload />
              <span>JSON</span>
            </button>
          </div>
        </div>
        {/* Enhanced Filter Section */}
        {filtersVisible && (
          <div className="enhanced-filters-section">
            <div className="filters-content">
              <div className="filter-row">
                <div className="filter-group-inline">
                  <label className="filter-label">
                    <HiEye className="label-icon" />
                    Log Level
                  </label>
                  <div className="level-filter-buttons">
                    {Object.entries(LOG_LEVELS || {}).map(([level, config]) => {
                      if (!config) return null;

                      return (
                        <button
                          key={level}
                          className={`level-btn ${
                            tempFilter?.level === level ? "active" : ""
                          }`}
                          onClick={() =>
                            handleFilterChange({
                              level:
                                tempFilter?.level === level
                                  ? undefined
                                  : (level as LogLevel),
                            })
                          }
                          style={
                            {
                              "--level-color": config?.color || "#ffffff",
                              "--level-bg": config?.bgColor || "#000000",
                            } as React.CSSProperties
                          }
                        >
                          <span className="level-indicator"></span>
                          {config?.label || level}
                        </button>
                      );
                    })}
                  </div>
                </div>

                <div className="filter-group-inline">
                  <label className="filter-label">
                    <HiClock className="label-icon" />
                    Time Range
                  </label>
                  <div className="date-range-inputs-inline">
                    <input
                      type="datetime-local"
                      value={tempFilter?.start_date || ""}
                      onChange={(e) =>
                        handleFilterChange({
                          start_date: e.target.value || undefined,
                        })
                      }
                      className={`enhanced-filter-input date-input-inline ${
                        !isDateRangeValid(
                          tempFilter?.start_date,
                          tempFilter?.end_date
                        )
                          ? "invalid"
                          : ""
                      }`}
                      placeholder="Start date"
                    />
                    <span className="date-separator">to</span>
                    <input
                      type="datetime-local"
                      value={tempFilter?.end_date || ""}
                      onChange={(e) =>
                        handleFilterChange({
                          end_date: e.target.value || undefined,
                        })
                      }
                      className={`enhanced-filter-input date-input-inline ${
                        !isDateRangeValid(
                          tempFilter?.start_date,
                          tempFilter?.end_date
                        )
                          ? "invalid"
                          : ""
                      }`}
                      placeholder="End date"
                    />
                  </div>
                </div>
              </div>
              {!isDateRangeValid(
                tempFilter?.start_date,
                tempFilter?.end_date
              ) && (
                <div className="date-range-error">
                  <span className="error-text">
                    End date must be after start date
                  </span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Enhanced Table Section */}
        {(!logs || logs.length === 0) && !loading ? (
          <div className="enhanced-empty-state">
            <div className="empty-state-content">
              <div className="empty-state-icon">
                <HiDatabase />
              </div>
              <h2>No Logs Found</h2>
              <p>No log entries match your current filters or time range.</p>
              <div className="empty-state-actions">
                <button
                  className="empty-action-btn primary"
                  onClick={handleRefresh}
                >
                  <HiRefresh />
                  <span>Refresh Logs</span>
                </button>
                <button
                  className="empty-action-btn secondary"
                  onClick={clearFilters}
                >
                  <HiX />
                  <span>Clear Filters</span>
                </button>
              </div>
            </div>
          </div>
        ) : (
          <div className="enhanced-table-container" ref={tableContainerRef}>
            <div className="enhanced-table-wrapper" ref={tableWrapperRef}>
              <table className="enhanced-logs-table">
                <thead className="table-header">
                  <tr>
                    <th className="timestamp-header">
                      <div className="header-content">
                        <HiClock className="header-icon" />
                        <span>Timestamp</span>
                      </div>
                    </th>
                    <th className="level-header">
                      <div className="header-content">
                        <HiFilter className="header-icon" />
                        <span>Level</span>
                      </div>
                    </th>
                    <th className="message-header">
                      <div className="header-content">
                        <HiInformationCircle className="header-icon" />
                        <span>Message</span>
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody className="table-body">
                  {loading && (!logs || logs.length === 0) ? (
                    <tr className="loading-row">
                      <td colSpan={3} className="loading-cell">
                        <div className="loading-content">
                          <div className="loading-spinner"></div>
                          <span>Loading logs...</span>
                        </div>
                      </td>
                    </tr>
                  ) : (
                    (logs || []).map((log, index) => {
                      if (!log) return null;

                      const logLevel = log.level || 'info';
                      const levelConfig = LOG_LEVELS[logLevel] || LOG_LEVELS.info;

                      return (
                        <tr
                          key={log.id || index}
                          className={`log-row ${logLevel}`}
                          style={{ "--row-index": index } as React.CSSProperties}
                        >
                          <td className="timestamp-cell">
                            <div className="timestamp-content">
                              <span className="timestamp-text">
                                {formatTimestamp(log.timestamp)}
                              </span>
                            </div>
                          </td>
                          <td className="level-cell">
                            <div className="level-badge-wrapper">
                              <span
                                className={`enhanced-level-badge ${logLevel}`}
                                style={
                                  {
                                    "--level-color": levelConfig?.color || "#ffffff",
                                    "--level-bg": levelConfig?.bgColor || "#000000",
                                  } as React.CSSProperties
                                }
                              >
                                <span className="level-indicator"></span>
                                {levelConfig?.label || logLevel}
                              </span>
                            </div>
                          </td>
                          <td className="message-cell">
                            <div className="message-content">
                              <span className="message-text">{log.message || 'No message'}</span>
                            </div>
                          </td>
                        </tr>
                      );
                    })
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Compact Pagination */}
        {totalCount > 0 && (
          <div className="compact-pagination-section">
            <div className="pagination-row">
              {/* Left: Stats and Page Size */}
              <div className="pagination-left">
                <span className="stats-text">
                  Showing <strong>{logs?.length || 0}</strong> of{" "}
                  <strong>{(totalCount || 0).toLocaleString()}</strong>
                </span>
                <div className="page-size-controls">
                  <label className="page-size-label">Rows:</label>
                  <select
                    value={pageSize}
                    onChange={(e) =>
                      handlePageSizeChange(Number(e.target.value))
                    }
                    className="compact-page-size-select"
                  >
                    <option value={25}>25</option>
                    <option value={50}>50</option>
                    <option value={100}>100</option>
                    <option value={200}>200</option>
                  </select>
                </div>
              </div>

              {/* Center: Page Navigation */}
              {totalPages > 1 && (
                <div className="pagination-center">
                  <button
                    className="pagination-btn nav-btn"
                    disabled={(currentPage || 1) === 1}
                    onClick={() => handlePageChange(1)}
                    title="First Page"
                  >
                    <HiChevronLeft />
                    <HiChevronLeft />
                  </button>

                  <button
                    className="pagination-btn nav-btn"
                    disabled={(currentPage || 1) === 1}
                    onClick={() => handlePageChange((currentPage || 1) - 1)}
                    title="Previous Page"
                  >
                    <HiChevronLeft />
                  </button>

                  <div className="page-numbers">
                    {Array.from({ length: Math.min(totalPages || 1, 5) }, (_, i) => {
                      let pageNum;
                      const safeTotalPages = totalPages || 1;
                      const safeCurrentPage = currentPage || 1;

                      if (safeTotalPages <= 5) {
                        pageNum = i + 1;
                      } else if (safeCurrentPage <= 3) {
                        pageNum = i + 1;
                      } else if (safeCurrentPage >= safeTotalPages - 2) {
                        pageNum = safeTotalPages - 4 + i;
                      } else {
                        pageNum = safeCurrentPage - 2 + i;
                      }

                      return (
                        <button
                          key={pageNum}
                          className={`pagination-btn page-btn ${
                            safeCurrentPage === pageNum ? "active" : ""
                          }`}
                          onClick={() => handlePageChange(pageNum)}
                        >
                          {pageNum}
                        </button>
                      );
                    })}
                  </div>

                  <button
                    className="pagination-btn nav-btn"
                    disabled={(currentPage || 1) === (totalPages || 1)}
                    onClick={() => handlePageChange((currentPage || 1) + 1)}
                    title="Next Page"
                  >
                    <HiChevronRight />
                  </button>

                  <button
                    className="pagination-btn nav-btn"
                    disabled={(currentPage || 1) === (totalPages || 1)}
                    onClick={() => handlePageChange(totalPages || 1)}
                    title="Last Page"
                  >
                    <HiChevronRight />
                    <HiChevronRight />
                  </button>
                </div>
              )}

              {/* Right: Page Info */}
              <div className="pagination-right">
                {(totalPages || 1) > 1 && (
                  <span className="page-info">
                    Page <strong>{currentPage || 1}</strong> of{" "}
                    <strong>{totalPages || 1}</strong>
                  </span>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LogsPage;
