.system-page {
  padding: 24px;
  max-width: 1280px;
  margin: 0 auto;
  background-color: var(--color-bg-primary);
  font-family: 'Roboto', system-ui, sans-serif;
}

.system-page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.system-page-title {
  display: flex;
  align-items: center;
}

.system-page-icon {
  font-size: 32px;
  margin-right: 16px;
  color: #2196f3;
  background-color: rgba(33, 150, 243, 0.1);
  padding: 6px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.system-page-title h1 {
  margin: 0;
  font-size: 32px;
  font-weight: 700;
  color: var(--color-text-primary);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.system-page-actions {
  display: flex;
  align-items: center;
}

.system-page-last-updated {
  display: flex;
  align-items: center;
  margin-right: 16px;
  color: var(--color-text-secondary);
  font-size: 14px;
  background-color: rgba(255, 255, 255, 0.05);
  padding: 8px 12px;
  border-radius: 6px;
}

.system-page-last-updated svg {
  margin-right: 8px;
  color: var(--color-primary);
}

.system-page-refresh {
  display: flex;
  align-items: center;
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.system-page-refresh:hover {
  background-color: var(--color-primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

.system-page-refresh:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.system-page-refresh.refreshing {
  background-color: var(--color-primary-light);
  cursor: not-allowed;
  transform: none;
}

.system-page-refresh svg {
  margin-right: 10px;
  font-size: 18px;
}

.system-page-refresh.refreshing svg {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.system-page-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
  animation: fadeIn 0.5s ease-out;
}

/* Special layout when GPU is available */
.system-page-summary.with-gpu {
  grid-template-columns: repeat(5, 1fr);
}

.system-summary-card {
  background-color: var(--color-bg-secondary);
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  height: 140px; /* Fixed height for consistent cards */
}

.system-summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-primary-light));
  opacity: 0.7;
}

.system-summary-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15);
}

.system-summary-card.alarm {
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.system-summary-card.alarm::before {
  background: linear-gradient(90deg, var(--color-danger), var(--color-danger-light));
  animation: pulse 2s infinite;
}

.summary-icon {
  font-size: 28px;
  color: #2196f3;
  margin-right: 14px;
  background-color: rgba(33, 150, 243, 0.1);
  padding: 6px;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.system-summary-card.alarm .summary-icon {
  color: var(--color-danger);
  background-color: rgba(239, 68, 68, 0.1);
}

.summary-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.summary-content h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-secondary);
  font-family: 'Roboto', system-ui, sans-serif;
}

.summary-value {
  font-size: 28px;
  font-weight: 700;
  color: var(--color-text-primary);
  font-family: 'Roboto', system-ui, sans-serif;
  line-height: 1.2;
}

.summary-subtitle {
  font-size: 12px;
  font-family: 'Roboto Mono', monospace;
  color: #2196f3;
  margin-top: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.system-summary-card.alarm .summary-value {
  color: var(--color-danger);
}

.summary-status {
  font-size: 20px;
  font-weight: 700;
  padding: 4px 12px;
  border-radius: 20px;
  display: inline-block;
}

.summary-status.healthy {
  color: #10b981;
  background-color: rgba(16, 185, 129, 0.1);
}

.summary-status.warning {
  color: var(--color-danger);
  background-color: rgba(239, 68, 68, 0.1);
}

.system-page-content {
  margin-bottom: 30px;
  display: grid;
  grid-gap: 20px;
}

@media (min-width: 1201px) and (max-width: 1400px) {
  .system-page-summary.with-gpu {
    grid-template-columns: repeat(5, 1fr);
  }

  .summary-icon {
    font-size: 26px;
    margin-right: 12px;
  }

  .summary-value {
    font-size: 26px;
  }
}

@media (max-width: 1280px) {
  .system-page {
    padding: 20px;
  }

  .system-page-summary.with-gpu {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 992px) {
  .system-page-summary.with-gpu {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .system-page {
    padding: 16px;
  }

  .system-page-header {
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 24px;
    padding-bottom: 12px;
  }

  .system-page-title h1 {
    font-size: 24px;
  }

  .system-page-icon {
    font-size: 24px;
    padding: 8px;
  }

  .system-page-actions {
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
  }

  .system-page-last-updated {
    margin-right: 0;
    margin-bottom: 12px;
    width: 100%;
  }

  .system-page-refresh {
    width: 100%;
    justify-content: center;
  }

  .system-page-summary {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .system-summary-card {
    padding: 16px;
  }

  .summary-icon {
    font-size: 24px;
    padding: 8px;
  }

  .summary-value {
    font-size: 24px;
  }

  .summary-status {
    font-size: 18px;
  }
}

/* Add some custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.system-page {
  animation: fadeIn 0.5s ease-out;
}

/* Add a subtle pulse animation for the system icon */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.system-page-icon {
  animation: pulse 3s infinite ease-in-out;
}
