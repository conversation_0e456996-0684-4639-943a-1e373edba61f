import { Link } from "react-router-dom";
import { useEffect, useState } from "react";
import "./HomePage.css";
import logoImage from "../assets/images/logo.png";
import RecorderIcon from "../assets/icons/RecorderIcon";
import SchedulerIcon from "../assets/icons/SchedulerIcon";
import FileManagerIcon from "../assets/icons/FileManagerIcon";
import AnalyticsIcon from "../assets/icons/AnalyticsIcon";
import GuideViewTimeline from "../components/guide/GuideViewTimeline";
import PreviewSchedulerModal from "../components/scheduler/modal/PreviewSchedulerModal";

const HomePage = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [previewShortId, setPreviewShortId] = useState<string | null>(null);

  useEffect(() => {
    // Trigger animations after component mounts
    setIsLoaded(true);
  }, []);

  return (
    <div className="home-page">
      <header className="home-header">
        <div className={`logo-container ${isLoaded ? "logo-loaded" : ""}`}>
          <img
            src={logoImage}
            alt="TraffIQ Media Logo"
            className="logo-image"
          />
        </div>

        <nav className={`nav-container ${isLoaded ? "nav-loaded" : ""}`}>
          <Link
            to="/scheduler"
            className="nav-item"
            data-tooltip="Schedule your content"
          >
            <SchedulerIcon className="nav-icon" />
            <span className="nav-text">Scheduler</span>
          </Link>

          <Link
            to="/file_manager"
            className="nav-item"
            data-tooltip="Manage your media files"
          >
            <FileManagerIcon className="nav-icon" />
            <span className="nav-text">File Manager</span>
          </Link>

          <Link
            to="/recorder"
            className="nav-item"
            data-tooltip="Configure input feeds"
          >
            <RecorderIcon className="nav-icon" />
            <span className="nav-text">Input Feeds</span>
          </Link>

          <Link
            to="/analytics"
            className="nav-item"
            data-tooltip="View analytics data"
          >
            <AnalyticsIcon className="nav-icon" />
            <span className="nav-text">Analytics</span>
          </Link>
        </nav>
      </header>

      <main className="home-main">
        <div
          className={`home-guide-container ${
            isLoaded ? "home-guide-loaded" : ""
          }`}
        >
          <div className="home-guide-header">
            <h2 className="home-guide-title">Program Guide</h2>
            <p className="home-guide-description">
              View your scheduled content in a timeline format. Scroll
              horizontally to navigate through the programming schedule.
            </p>
          </div>
          <GuideViewTimeline onPreview={setPreviewShortId} />
        </div>
      </main>

      {previewShortId && (
        <PreviewSchedulerModal
          onClose={() => setPreviewShortId(null)}
          shortId={previewShortId}
        />
      )}
    </div>
  );
};

export default HomePage;
