import React, { useEffect, useRef, useState } from "react";
import FullCalendar from "@fullcalendar/react";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";
import {
  DateSelectArg,
  EventChangeArg,
  EventClickArg,
  EventInput,
} from "@fullcalendar/core";
import Loading from "@/components/common/Loading";
import { useDispatch, useSelector } from "react-redux";
import moment from "moment";
import { useNavigate, useParams } from "react-router-dom";
import { toast } from "react-toastify";
import {
  changeSchedulerDetail,
  editItemTime,
  openScheduler,
  REGULAR,
  selectActiveItem,
  selectItem,
  selectRegularDays,
  selectSchedule,
} from "@/redux/schedulerSlice.ts";
import {
  generateGuideByScheduleId,
  getGuideByScheduleId,
  getSchedule,
  updateSchedule,
} from "@/api/schedulerApi.ts";
import SideBar from "@/components/scheduler/sidebar/SideBar.tsx";
import FolderItem from "@/components/scheduler/FolderItem.tsx";
import GuideSection from "@/components/scheduler/sidebar/GuideSection.tsx";
import CalendarSection from "@/components/scheduler/sidebar/CalendarSection.tsx";
import PreviewModal from "@/components/scheduler/modal/PreviewModal.tsx";
import FillerSettingModal from "@/components/scheduler/modal/FillerSettingModal/FillerSettingModal.tsx";
import OutputSettingModal from "@/components/scheduler/modal/OutputSettingModal.tsx";
import ContentSettingModal from "@/components/scheduler/modal/ContentSettingModal/ContentSettingModal.tsx";
import AddItemModal from "@/components/scheduler/modal/AddItemModal/AddItemModal.tsx";
import EditItemModal from "@/components/scheduler/modal/EditItemModal/EditItemModal.tsx";
import { AiFillSave, AiOutlineWarning } from "react-icons/ai";

import "./SchedulerPage.css";

const SchedulerPage = () => {
  let { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  if (!id) {
    toast.error("Schedule not found");
    navigate("/scheduler");
    return <></>;
  }

  const schedule = useSelector(selectSchedule);
  const timerDebounceRef = useRef<NodeJS.Timeout | null>(null);
  const dirty = React.useRef(false);

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isChangeAction, setIsChangeAction] = useState<boolean>(false);
  const [isSaveAction, setIsSaveAction] = useState<boolean>(false);
  const [hideLeftControls, setHideLeftControls] = useState<boolean>(false);
  const [hideCalendarSection, setHideCalendarSection] = useState<boolean>(true);
  const [hideGuideSection, setHideGuideSection] = useState<boolean>(true);
  const [previewURL, setPreviewURL] = useState<string | null>(null);
  const [isAddItemModalOpen, setIsAddItemModalOpen] = useState<boolean>(false);
  const [isContentSettingModalOpen, setIsContentSettingModalOpen] =
    useState<boolean>(false);
  const [isEditItemModalOpen, setIsEditItemModalOpen] =
    useState<boolean>(false);
  const [isFillerSettingModalOpen, setIsFillerSettingModalOpen] =
    useState<boolean>(false);
  const [isRtpOutputSettingModalOpen, setIsRtpOutputSettingModalOpen] =
    useState<boolean>(false);

  const [type, setType] = useState<string>(REGULAR);
  const [day, setDay] = useState<string>("");
  const [start, setStart] = useState<string>("");
  const [end, setEnd] = useState<string>("");

  const regularDays = useSelector(selectRegularDays);
  const activeItem = useSelector(selectActiveItem);

  useEffect(() => {
    const fetchSchedule = async () => {
      setIsLoading(true);
      try {
        if (id) {
          const schedule = await getSchedule(Number.parseInt(id));
          const guide = await getGuideByScheduleId(Number.parseInt(id));

          dispatch(
            openScheduler({
              schedule: schedule,
              guide: guide,
            })
          );
        }
      } catch (err) {
        console.error("Failed to load schedule: ", err);
        toast.error("Failed to load schedule");
        navigate("/scheduler");
      } finally {
        setIsLoading(false);
      }
    };

    fetchSchedule();
  }, [id]);

  useEffect(() => {
    dirty.current = isChangeAction;
    if (isChangeAction && schedule.autosave) {
      saveDebounce();
    }
  }, [schedule]);

  useEffect(() => {
    const onBeforeUnload = (ev: { returnValue: boolean }) => {
      if (dirty.current) {
        ev.returnValue = true;
        return true;
      }
    };
    window.addEventListener("beforeunload", onBeforeUnload);
    return () => {
      window.removeEventListener("beforeunload", onBeforeUnload);
    };
  }, []);

  const saveDebounce = () => {
    if (timerDebounceRef.current) {
      clearTimeout(timerDebounceRef.current);
    }
    timerDebounceRef.current = setTimeout(() => {
      setIsSaveAction(true);

      // Check if filler settings are empty when autosaving
      if (checkFillerSettings()) {
        // Show enhanced toast warning for autosave
        toast.warning(
          <div className="enhanced-toast-container">
            <div className="enhanced-toast-content">
              <AiOutlineWarning size={24} color="#2196f3" style={{ marginRight: '10px', display: 'inline-block', verticalAlign: 'middle' }} />
              Filler settings are empty. Consider setting filler settings in the sidebar.
            </div>
          </div>,
          {
            autoClose: 5000,
            position: "bottom-center",
            className: "enhanced-toast-wrapper",
            progressClassName: "enhanced-toast-progress",
            toastId: "filler-settings-autosave-warning",
            icon: false, // Disable default icon as we're using our own
          }
        );
      }

      updateSchedule(Number.parseInt(id), schedule)
        .then(() => {
          setIsChangeAction(false);
          dispatch(
            changeSchedulerDetail({
              field: "updated_at",
              value: moment().format(),
            })
          );
          toast.success("Schedule autosaved");
        })
        .finally(() => {
          setIsSaveAction(false);
        });
    }, 3000);
  };

  const checkFillerSettings = () => {
    // Check if filler settings are empty
    return (
      schedule.fillers.folders.length === 0 && schedule.fillers.files.length === 0
    );
  };

  const handleSave = async () => {
    // Check if filler settings are empty
    if (checkFillerSettings()) {
      // Show enhanced warning toast with action buttons
      toast.warning(
        <div className="enhanced-toast-container">
          <div className="enhanced-toast-content">
            <AiOutlineWarning size={24} color="rgba(255, 165, 0, 0.9)" style={{ marginRight: '10px', display: 'inline-block', verticalAlign: 'middle' }} />
            Filler settings are empty. It's recommended to set filler settings.
          </div>
          <div className="toast-actions">
            <button
              onClick={() => {
                toast.dismiss();
                setIsFillerSettingModalOpen(true);
              }}
              className="toast-action-button primary"
            >
              Set Fillers
            </button>
            <button
              onClick={() => {
                toast.dismiss();
                saveSchedule();
              }}
              className="toast-action-button secondary"
            >
              Save Anyway
            </button>
          </div>
        </div>,
        {
          autoClose: false,
          closeOnClick: false,
          draggable: false,
          closeButton: true,
          position: "bottom-center",
          className: "enhanced-toast-wrapper",
          progressClassName: "enhanced-toast-progress",
          toastId: "filler-settings-warning",
          icon: false,
        }
      );
      return;
    }

    // If filler settings are not empty, proceed with saving
    saveSchedule();
  };

  const saveSchedule = async () => {
    setIsLoading(true);
    try {
      const updatedSchedule = await updateSchedule(
        Number.parseInt(id),
        schedule
      );
      let guide = await getGuideByScheduleId(Number.parseInt(id));

      if (!guide.id) {
        guide = await generateGuideByScheduleId(Number.parseInt(id));
      }

      dispatch(
        openScheduler({
          schedule: updatedSchedule,
          guide: guide,
        })
      );

      toast.success("Schedule successfully saved");
    } catch (err) {
      console.error("Failed to save schedule: ", err);
      toast.error("Failed to save schedule");
    } finally {
      setIsLoading(false);
      setIsChangeAction(false);
    }
  };

  const events: EventInput[] = [];
  regularDays.forEach((day) => {
    day.items.forEach((item) => {
      events.push(item);
    });
  });

  const shortDayName = (date: Date) =>
    date.toLocaleDateString("en", { weekday: "short" });

  const editItem = (arg: EventChangeArg, type: string) => {
    setIsChangeAction(true);
    dispatch(
      editItemTime({
        type: type,
        oldStart: arg.oldEvent.startStr,
        item: {
          start: arg.event.startStr,
          end: arg.event.endStr,
        },
      })
    );
  };

  const selectSection = (day: string, info: DateSelectArg, type: string) => {
    setDay(day);
    setStart(info.startStr);
    setEnd(info.endStr);
    setIsAddItemModalOpen(true);
    setType(type);
  };

  const editEvent = (arg: EventClickArg, type: string) => {
    setType(type);
    const event = arg.event;
    const props = event.extendedProps;
    setIsEditItemModalOpen(true);

    dispatch(
      selectItem({
        start: event.startStr,
        end: event.endStr,
        type: props.type,
        connection: props.connection,
        link: props.link,
        folders: props.folders,
        files: props.files,
        fillers: props.fillers,
        name: props.name,
        description: props.description,
        port: props.port,
        mode: props.mode,
        expire_date: props.expireDate,
        expire_time: props.expireTime,
      })
    );
  };

  return (
    <>
      <div className="scheduler-page">
        {previewURL && (
          <PreviewModal
            close={() => setPreviewURL(null)}
            previewURL={previewURL}
          />
        )}
        {isFillerSettingModalOpen && (
          <FillerSettingModal
            close={() => setIsFillerSettingModalOpen(false)}
            setChangeAction={setIsChangeAction}
            setIsLoading={setIsLoading}
          />
        )}
        {isRtpOutputSettingModalOpen && (
          <OutputSettingModal
            close={() => setIsRtpOutputSettingModalOpen(false)}
            setChangeAction={setIsChangeAction}
          />
        )}
        <SideBar
          hidden={hideLeftControls}
          setHidden={setHideLeftControls}
          setChangeAction={setIsChangeAction}
          hideCalendarSection={hideCalendarSection}
          hideGuideSection={hideGuideSection}
          setHideCalendarSection={setHideCalendarSection}
          setHideGuideSection={setHideGuideSection}
          handleSave={handleSave}
          openFillerModal={() => setIsFillerSettingModalOpen(true)}
          openContentSettingModal={() => setIsContentSettingModalOpen(true)}
          openRtpOutputSettingModal={() => setIsRtpOutputSettingModalOpen(true)}
          isSaveAction={isSaveAction}
          isChangeAction={isChangeAction}
        />
        <div
          style={{
            width: `calc(100vw - ${hideLeftControls ? 60 : 290}px)`,
            position: "relative",
          }}
        >
          {!hideCalendarSection && (
            <CalendarSection
              changeView={() => setHideCalendarSection(true)}
              selectSection={selectSection}
              editItem={editItem}
              editEvent={editEvent}
            />
          )}
          {!hideGuideSection && (
            <GuideSection
              setPreviewURL={setPreviewURL}
              setIsLoading={setIsLoading}
              hide={() => setHideGuideSection(true)}
            />
          )}
          <div className="scheduler-content">
            {isContentSettingModalOpen && (
              <ContentSettingModal
                close={() => setIsContentSettingModalOpen(false)}
                setIsLoading={setIsLoading}
              />
            )}
            {isAddItemModalOpen && (
              <AddItemModal
                type={type}
                day={day}
                start={start}
                end={end}
                setChangeAction={setIsChangeAction}
                close={() => setIsAddItemModalOpen(false)}
              />
            )}
            {isEditItemModalOpen && activeItem && (
              <EditItemModal
                type={type}
                setChangeAction={setIsChangeAction}
                close={() => setIsEditItemModalOpen(false)}
              />
            )}
            <FullCalendar
              timeZone={"UTC"}
              editable={true}
              eventOverlap={false}
              selectOverlap={false}
              dayHeaderFormat={{ weekday: "short" }}
              eventAllow={(dropInfo) => {
                const start = moment(dropInfo.start.toUTCString())
                  .utc()
                  .format("YYYY-MM-DD");
                const end = moment(dropInfo.end.toUTCString())
                  .utc()
                  .add(-1, "minute")
                  .format("YYYY-MM-DD");

                return start === end;
              }}
              headerToolbar={{
                left: "",
                center: "",
                right: "",
              }}
              slotLabelFormat={{
                hour: "2-digit",
                minute: "2-digit",
              }}
              selectable={true}
              initialView={"timeGridWeek"}
              allDaySlot={false}
              scrollTime={"00:00:00"}
              plugins={[interactionPlugin, timeGridPlugin]}
              select={(info) =>
                selectSection(shortDayName(info.start), info, REGULAR)
              }
              eventClick={(arg) => editEvent(arg, REGULAR)}
              eventDrop={(arg) => editItem(arg, REGULAR)}
              eventResize={(arg) => editItem(arg, REGULAR)}
              height={"100%"}
              locale={"UTC"}
              events={events}
              eventColor={"#1E1E1E"}
              eventClassNames="calendar-event"
              slotLaneClassNames="calendar-time-slot"
              dayCellClassNames="calendar-day-cell"
              eventResizableFromStart={true}
              eventContent={(arg) => {
                const props = arg.event.extendedProps;

                return FolderItem ? (
                  <FolderItem
                    folders={props.folders || []}
                    files={props.files || []}
                  />
                ) : null;
              }}
            />
          </div>
        </div>
        {isLoading && <Loading />}
        {isChangeAction && !schedule.autosave && !isSaveAction && (
          <div className="save-notification">
            <AiOutlineWarning className="save-notification-icon" />
            <span className="save-notification-text">
              You have unsaved changes!
            </span>
            <button className="save-notification-button" onClick={handleSave}>
              <AiFillSave /> Save Now
            </button>
          </div>
        )}
      </div>
    </>
  );
};

export default SchedulerPage;
