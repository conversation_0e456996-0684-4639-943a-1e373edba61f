import { useState, useEffect, useRef } from "react";
import {
  getRecorders,
  getRecorderStatus,
  stopRecorder,
  failRecorder,
  deleteRecorder,
  joinRecorder,
  unjoinRecorder,
} from "../api/recorderApi";
import { getFilesByRecorderId } from "../api/filesApi";
import webSocketService from "../api/websocket";
import {
  Recorder,
  RecorderStatus,
  RecorderStatusUpdate,
} from "../types/recorder";
import { ConvertItem, FileStatus } from "../types/files";
import { calculateProgress, formatSeconds } from "../utils/formatters";
import CreateRecorderModal from "../components/recorder/CreateRecorderModal";
import SelectServiceModal from "../components/recorder/SelectServiceModal";
import Loading from "../components/common/Loading";
import { HiPlus, HiChevronLeft, HiChevronRight } from "react-icons/hi";
import {
  Hi<PERSON><PERSON>,
  HiS<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>Link,
  HiXCircle,
} from "react-icons/hi";
import "./RecorderPage.css";

const RecorderPage = () => {
  const [recorders, setRecorders] = useState<Recorder[]>([]);
  const [recorderStatuses, setRecorderStatuses] = useState<RecorderStatus[]>(
    []
  );
  const [isInitialLoading, setIsInitialLoading] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showServiceModal, setShowServiceModal] = useState(false);
  const [selectedRecorder, setSelectedRecorder] = useState<Recorder | null>(
    null
  );
  const [selectedRecorderId, setSelectedRecorderId] = useState<number | null>(
    null
  );
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    totalPages: 1,
  });
  // We'll use intervalRef instead of this state
  // const [statusInterval, setStatusInterval] = useState<number | null>(null);
  const [joinedRecorders, setJoinedRecorders] = useState<number[]>([]);
  // Track recorders that are currently being joined (for loading state)
  const [joiningRecorders, setJoiningRecorders] = useState<number[]>([]);
  // Track TS sync loss time for each recorder
  const [tsSyncLossTime, setTsSyncLossTime] = useState<Record<number, number>>(
    {}
  );
  // Track processing files for each recorder
  const [processingFiles, setProcessingFiles] = useState<
    Record<number, ConvertItem[]>
  >({});
  // State to track if this is the initial render for animation
  const [isInitialRender, setIsInitialRender] = useState(true);
  // State to track if the component is active/visible
  const [isPageActive, setIsPageActive] = useState(true);
  // Reference to store the interval ID
  const intervalRef = useRef<number | null>(null);
  // Reference for the table container to handle scroll shadows
  const tableContainerRef = useRef<HTMLDivElement>(null);

  // Effect for fade-in animation - only runs once on initial mount
  useEffect(() => {
    // Set a timeout to reset the initial render state after animation completes
    const animationTimeout = setTimeout(() => {
      setIsInitialRender(false);
    }, 700); // Match this with the animation duration in CSS

    // Clean up timeout on unmount
    return () => clearTimeout(animationTimeout);
  }, []);

  // Load recorders on mount and when pagination changes
  useEffect(() => {
    console.log("Loading recorders...");
    loadRecorders();
  }, [pagination.page, pagination.limit]);

  // Effect to handle document visibility changes
  useEffect(() => {
    // Function to handle visibility change
    const handleVisibilityChange = () => {
      const isVisible = document.visibilityState === "visible";
      console.log(
        `Document visibility changed: ${isVisible ? "visible" : "hidden"}`
      );
      setIsPageActive(isVisible);

      // If page becomes hidden, stop polling
      if (!isVisible && intervalRef.current) {
        console.log("Page hidden, stopping status polling");
        window.clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      // If page becomes visible again and we have joined recorders, restart polling
      else if (
        isVisible &&
        joinedRecorders.length > 0 &&
        !intervalRef.current
      ) {
        console.log(
          "Page visible again with joined recorders, restarting polling"
        );
        startStatusPolling();
      }
    };

    // Add event listener for visibility change
    document.addEventListener("visibilitychange", handleVisibilityChange);

    // Clean up
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [joinedRecorders]);

  // Function to start status polling
  const startStatusPolling = () => {
    // Clear any existing interval
    if (intervalRef.current) {
      window.clearInterval(intervalRef.current);
    }

    // Start a new interval
    console.log("Starting status polling");
    const interval = window.setInterval(fetchRecorderStatus, 1000);
    intervalRef.current = interval;
  };

  // Function to stop status polling
  const stopStatusPolling = () => {
    if (intervalRef.current) {
      console.log("Stopping status polling");
      window.clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  // Initialize joined recorders state when the page loads
  // This effect will only run when the component is mounted (when on the recorder page)
  useEffect(() => {
    console.log("RecorderPage mounted - initializing state");

    const initializeJoinedRecorders = async () => {
      try {
        console.log("Initializing joined recorders state...");
        const statuses = await getRecorderStatus();

        // Find all recorders that are joined according to the backend
        const joinedRecorderIds = statuses
          .filter((status) => status.is_joined)
          .map((status) => status.id);

        if (joinedRecorderIds.length > 0) {
          console.log("Found joined recorders:", joinedRecorderIds);
          setJoinedRecorders(joinedRecorderIds);

          // Also update the recorder statuses
          setRecorderStatuses(statuses);

          // Start polling if page is active
          if (document.visibilityState === "visible") {
            startStatusPolling();
          }
        }
      } catch (error) {
        console.error("Failed to initialize joined recorders:", error);
      }
    };

    // Call the initialization function
    initializeJoinedRecorders();

    // Clean up function that will run when the component is unmounted
    return () => {
      console.log("RecorderPage unmounted - cleaning up");
      // Stop polling
      stopStatusPolling();

      // Clear statuses to prevent memory leaks
      setRecorderStatuses([]);
    };
  }, []);

  // Set up WebSocket listener for recorder status updates
  useEffect(() => {
    console.log("Setting up WebSocket listener for recorder status updates");

    const unsubscribe = webSocketService.subscribeToRecorderUpdates(
      (update: RecorderStatusUpdate) => {
        console.log("Received WebSocket update:", update);

        // For regular status updates, update that specific recorder's status
        setRecorders((prev) =>
          prev.map((recorder) =>
            recorder.id === update.id
              ? { ...recorder, status: update.status }
              : recorder
          )
        );

        // If a recorder has completed or stopped, check if it's automatically joined
        if (
          update.status === "stopped" ||
          update.status === "completed" ||
          update.status === "failed"
        ) {
          // Add to joined recorders if not already there
          setJoinedRecorders((prev) => {
            if (!prev.includes(update.id)) {
              console.log(
                `Recorder ${update.id} automatically joined after recording completed`
              );
              return [...prev, update.id];
            }
            return prev;
          });

          // Fetch recorder status to check if it's joined
          fetchRecorderStatus();

          // If a recording completed, reload the recorder data to get updated scheduling fields
          if (update.status === "completed") {
            console.log(
              "Recording completed, reloading recorder data to refresh scheduling fields"
            );
            loadRecorders();
          }
        }

        // Show a notification with appropriate message based on status
        let title = "Input Feed Status Changed";
        let description = `Input Feed #${update.id} is now ${update.status}.`;

        if (update.status === "completed") {
          title = "Input Feed Completed";
          description = `Input Feed #${update.id} has completed successfully.`;
        } else if (update.status === "failed") {
          title = "Input Feed Failed";
          description = `Input Feed #${update.id} has failed.`;
        } else if (
          update.status === "running" ||
          update.status === "recording"
        ) {
          title = "Input Feed Started";
          description = `Input Feed #${update.id} is now recording.`;
        } else if (update.status === "transcoding") {
          title = "Input Feed Transcoding";
          description = `Input Feed #${update.id} is now transcoding.`;
        } else if (update.status === "stopped") {
          title = "Input Feed Stopped";
          description = `Input Feed #${update.id} has been stopped.`;
        }

        console.log(`${title}: ${description}`);
      }
    );

    // Clean up subscription on unmount
    return () => {
      console.log("Cleaning up WebSocket subscription");
      unsubscribe();
    };
  }, []);

  // Set up polling for recorder status (for both active and joined recorders)
  useEffect(() => {
    // Only poll if the page is active
    if (!isPageActive) {
      console.log("Page is not active, not starting polling");
      return;
    }

    // Check if any recorders are running, recording, transcoding or joined
    const hasRunningRecorders = recorders.some(
      (r) =>
        r.status === "running" ||
        r.status === "recording" ||
        r.status === "transcoding"
    );
    const hasJoinedRecorders = joinedRecorders.length > 0;

    // If we have running or joined recorders, start polling for status
    if (hasRunningRecorders || hasJoinedRecorders) {
      startStatusPolling();
      console.log(
        `Status polling started: ${
          hasRunningRecorders ? "running recorders" : ""
        } ${hasJoinedRecorders ? "joined recorders" : ""}`
      );
    } else {
      // If no recorders are running or joined, stop polling
      stopStatusPolling();
      console.log("Status polling stopped: no running or joined recorders");

      // Clear all statuses if there are no joined recorders
      if (!hasJoinedRecorders) {
        setRecorderStatuses([]);
      }
    }

    // Cleanup on unmount is handled in the component unmount effect
  }, [recorders, joinedRecorders, isPageActive]);

  const fetchRecorderStatus = async () => {
    try {
      // Fetch and update the status for UI display (progress bars, TS sync status, etc.)
      const statuses = await getRecorderStatus();

      // Debug log to see what statuses are being returned
      console.log("Recorder statuses:", statuses);

      // Additional debug for audio tracks specifically
      statuses.forEach((status, index) => {
        console.log(`Status ${index} (ID: ${status.id}):`, {
          hasServices: status.services && status.services.length > 0,
          serviceCount: status.services?.length || 0,
          firstService: status.services?.[0] || "none",
          audioTracks: status.services?.[0]?.audio_tracks || "none",
          audioTracksLength: status.services?.[0]?.audio_tracks?.length || 0,
          fallbackAudio: status.audio_info || "none",
        });
      });

      // Create a copy of the current TS sync loss time state
      const updatedTsSyncLossTime = { ...tsSyncLossTime };
      const currentTime = Date.now();
      const TS_SYNC_LOSS_THRESHOLD = 5000; // 5 seconds in milliseconds

      // Check for running recorders that have lost TS sync
      for (const status of statuses) {
        const recorder = recorders.find((r) => r.id === status.id);

        // Only process running recorders for TS sync loss
        if (recorder && recorder.status === "running") {
          if (!status.ts_sync) {
            // If TS sync is lost, record the time it was first lost
            if (!updatedTsSyncLossTime[status.id]) {
              console.log(
                `Recorder ${status.id} has lost TS sync, starting timer`
              );
              updatedTsSyncLossTime[status.id] = currentTime;
            } else {
              // Check if TS sync has been lost for more than 5 seconds
              const tsSyncLostDuration =
                currentTime - updatedTsSyncLossTime[status.id];
              console.log(
                `Recorder ${status.id} has lost TS sync for ${tsSyncLostDuration}ms`
              );

              if (tsSyncLostDuration >= TS_SYNC_LOSS_THRESHOLD) {
                console.log(
                  `Recorder ${status.id} has lost TS sync for over 5 seconds, marking as failed`
                );

                // Update the UI immediately to show failed status
                setRecorders((prev) =>
                  prev.map((r) =>
                    r.id === status.id ? { ...r, status: "failed" } : r
                  )
                );

                // Mark the recorder as failed in the backend
                try {
                  await failRecorder(status.id);
                  console.log(
                    `Marked recorder ${status.id} as failed due to TS sync loss over 5 seconds`
                  );

                  // Clear the TS sync loss time for this recorder
                  delete updatedTsSyncLossTime[status.id];
                } catch (error) {
                  console.error(
                    `Failed to mark recorder ${status.id} as failed after TS sync loss:`,
                    error
                  );
                }
              }
            }
          } else {
            // If TS sync is restored, clear the loss time
            if (updatedTsSyncLossTime[status.id]) {
              console.log(
                `Recorder ${status.id} has regained TS sync, clearing timer`
              );
              delete updatedTsSyncLossTime[status.id];
            }
          }
        } else {
          // If recorder is not running, clear any existing TS sync loss time
          if (updatedTsSyncLossTime[status.id]) {
            delete updatedTsSyncLossTime[status.id];
          }
        }
      }

      // Update the TS sync loss time state
      setTsSyncLossTime(updatedTsSyncLossTime);

      // Update the recorder statuses
      setRecorderStatuses(statuses);
    } catch (error) {
      console.error("Failed to fetch recorder status:", error);
    }
  };

  const loadRecorders = async () => {
    try {
      console.log("Setting loading state...");
      setIsInitialLoading(true);

      console.log("Calling getRecorders API...");
      const response = await getRecorders(pagination.page, pagination.limit);

      // Handle null items
      const items = response.items || [];
      setRecorders(items);

      console.log("Setting pagination:", {
        ...pagination,
        totalPages: response.totalPages,
      });
      setPagination({
        ...pagination,
        totalPages: response.totalPages,
      });

      // Fetch processing files for each recorder
      const filesMap: Record<number, ConvertItem[]> = {};
      for (const recorder of items) {
        try {
          const files = await getFilesByRecorderId(recorder.id);
          if (files.length > 0) {
            filesMap[recorder.id] = files;
          }
        } catch (error) {
          console.error(
            `Error fetching files for recorder ${recorder.id}:`,
            error
          );
        }
      }
      setProcessingFiles(filesMap);

      // Fetch recorder statuses to update joined status
      // This is now handled in the mount effect to avoid unnecessary API calls
    } catch (error) {
      console.error("Failed to load recorders:", error);
    } finally {
      console.log("Setting loading state to false");
      setIsInitialLoading(false);
    }
  };

  const handleCreateRecorder = () => {
    setSelectedRecorder(null);
    setShowCreateModal(true);
  };

  const handleEditRecorder = (recorder: Recorder) => {
    setSelectedRecorder(recorder);
    setShowCreateModal(true);
  };

  const handleToggleJoinStatus = async (id: number) => {
    try {
      // Check if the recorder is already joined
      const isJoined = joinedRecorders.includes(id);

      if (isJoined) {
        // Unjoin the recorder
        try {
          // Set loading state for unjoining
          setJoiningRecorders((prev) => [...prev, id]);

          await unjoinRecorder(id);
          setJoinedRecorders((prev) =>
            prev.filter((recorderId) => recorderId !== id)
          );
          console.log(`Unjoined recorder ${id}`);

          // Remove this recorder from the statuses
          setRecorderStatuses((prev) =>
            prev.filter((status) => status.id !== id)
          );
        } catch (error) {
          console.error("Failed to unjoin recorder:", error);
          // Even if the API call fails, update the UI state to reflect the user's intent
          setJoinedRecorders((prev) =>
            prev.filter((recorderId) => recorderId !== id)
          );
        } finally {
          // Clear loading state
          setJoiningRecorders((prev) =>
            prev.filter((recorderId) => recorderId !== id)
          );
        }
      } else {
        // Join the recorder
        try {
          // Set loading state for joining
          setJoiningRecorders((prev) => [...prev, id]);

          console.log(`Joining recorder ${id}...`);
          await joinRecorder(id);

          // Update the UI state to reflect the joined status
          setJoinedRecorders((prev) => [...prev, id]);
          console.log(`Joined recorder ${id}`);

          // Immediately fetch status once to show TS sync status
          try {
            console.log(`Fetching initial status for joined recorder ${id}...`);
            await fetchRecorderStatus();
          } catch (error) {
            console.error("Failed to fetch status after joining:", error);
          }
        } catch (error) {
          console.error("Failed to join recorder:", error);
          // Don't update the UI state if the API call fails
        } finally {
          // Clear loading state
          setJoiningRecorders((prev) =>
            prev.filter((recorderId) => recorderId !== id)
          );
        }
      }
    } catch (error) {
      console.error("Failed to toggle join status:", error);
      // Make sure loading state is cleared in case of unexpected errors
      setJoiningRecorders((prev) =>
        prev.filter((recorderId) => recorderId !== id)
      );
    }
  };

  const handleToggleRecorderStatus = async (id: number) => {
    try {
      // Don't set loading state for toggle operations
      const recorder = recorders.find((r) => r.id === id);
      if (!recorder) return;

      // If we're stopping a recording, proceed without additional checks
      if (
        recorder.status === "running" ||
        recorder.status === "recording" ||
        recorder.status === "transcoding"
      ) {
        // Update the UI immediately for better responsiveness
        setRecorders((prev) =>
          prev.map((r) => (r.id === id ? { ...r, status: "stopped" } : r))
        );

        try {
          await stopRecorder(id);
        } catch (error) {
          console.error("Failed to stop recorder:", error);

          // If there was an error, revert the UI change
          setRecorders((prev) =>
            prev.map((r) =>
              r.id === id ? { ...r, status: recorder.status } : r
            )
          );

          console.error(`Failed to stop Input Feed: ${error}`);
        }
        return;
      }

      // For starting a recording, perform additional checks

      // Check if the recorder is joined
      const isJoined = joinedRecorders.includes(id);
      if (!isJoined) {
        console.error("Cannot start recording: Recorder is not joined");
        return;
      }

      // Check if the recorder has TS sync
      const recorderStatus = recorderStatuses.find(
        (status) => status.id === id
      );
      if (recorderStatus && !recorderStatus.ts_sync) {
        console.error("Cannot start recording: No TS sync detected");
        return;
      }

      // Show the service selection modal instead of starting the recording directly
      setSelectedRecorderId(id);
      setShowServiceModal(true);
    } catch (error) {
      console.error("Unexpected error:", error);
    }
  };

  const handleDeleteRecorder = async (id: number) => {
    try {
      // Use isInitialLoading for delete operation
      setIsInitialLoading(true);
      await deleteRecorder(id);

      // After successful deletion, reload the list
      loadRecorders();

      console.log("Recorder deleted successfully");
    } catch (error) {
      console.error("Failed to delete recorder:", error);
      console.error(`Failed to delete recorder: ${error}`);
    } finally {
      setIsInitialLoading(false);
    }
  };

  const handlePageChange = (page: number) => {
    setPagination({
      ...pagination,
      page,
    });
  };

  // Function to handle scroll and update shadows
  const handleTableScroll = () => {
    const container = tableContainerRef.current;
    if (!container) return;

    const { scrollLeft, scrollWidth, clientWidth } = container;
    const isScrolledLeft = scrollLeft > 0;
    const isScrolledRight = scrollLeft < scrollWidth - clientWidth - 1;

    // Determine scroll state
    let scrollState = "";
    if (isScrolledLeft && isScrolledRight) {
      scrollState = "both";
    } else if (isScrolledLeft) {
      scrollState = "left";
    } else if (isScrolledRight) {
      scrollState = "right";
    }

    // Update data attribute
    if (scrollState) {
      container.setAttribute("data-scroll", scrollState);
    } else {
      container.removeAttribute("data-scroll");
    }
  };

  // Check scroll state on content changes
  useEffect(() => {
    const timer = setTimeout(() => {
      handleTableScroll();
    }, 100);
    return () => clearTimeout(timer);
  }, [recorders]);

  // Add scroll event listener
  useEffect(() => {
    const container = tableContainerRef.current;
    if (container) {
      container.addEventListener("scroll", handleTableScroll);
      // Initial check
      handleTableScroll();

      return () => {
        container.removeEventListener("scroll", handleTableScroll);
      };
    }
  }, []);

  return (
    <div
      className={`recorder-page ${
        isInitialRender ? "recorder-page-fade-in" : ""
      }`}
    >
      <div className="header">
        <h1>Input Feeds</h1>
        <button className="create-button" onClick={handleCreateRecorder}>
          <HiPlus className="button-icon" />
          <span>Create New Input Feed</span>
        </button>
      </div>

      {isInitialLoading && <Loading />}

      {showCreateModal && (
        <CreateRecorderModal
          recorder={selectedRecorder}
          onClose={() => {
            setShowCreateModal(false);
            setSelectedRecorder(null);
          }}
          onSuccess={() => {
            setShowCreateModal(false);
            setSelectedRecorder(null);
            loadRecorders();
          }}
        />
      )}

      {showServiceModal &&
        selectedRecorderId !== null &&
        (() => {
          const selectedRecorder = recorders.find(
            (r) => r.id === selectedRecorderId
          );
          if (!selectedRecorder) {
            console.error("Selected recorder not found:", selectedRecorderId);
            return null;
          }

          return (
            <SelectServiceModal
              recorder={selectedRecorder}
              onClose={() => {
                setShowServiceModal(false);
                setSelectedRecorderId(null);
              }}
              onSuccess={() => {
                // Close the modal
                setShowServiceModal(false);

                // Get the selected recorder
                const recorder = recorders.find(
                  (r) => r.id === selectedRecorderId
                );

                // Update the recorder status to 'running' in the UI
                if (recorder) {
                  setRecorders((prev) =>
                    prev.map((r) =>
                      r.id === selectedRecorderId
                        ? { ...r, status: "running" }
                        : r
                    )
                  );

                  // Make sure this recorder is in the joined recorders list
                  if (!joinedRecorders.includes(selectedRecorderId)) {
                    setJoinedRecorders((prev) => [...prev, selectedRecorderId]);
                  }
                }

                // Clear the selected recorder ID
                setSelectedRecorderId(null);

                // Immediately fetch recorder status to update UI with progress information
                fetchRecorderStatus();

                // Set up a more aggressive polling interval initially to ensure UI updates quickly
                let pollCount = 0;
                const maxPolls = 10;
                const statusInterval = setInterval(() => {
                  fetchRecorderStatus();
                  pollCount++;

                  // After maxPolls, clear the interval
                  if (pollCount >= maxPolls) {
                    clearInterval(statusInterval);
                  }
                }, 500); // Poll every 500ms for quick updates

                // Make sure to clean up the interval if component unmounts
                return () => {
                  if (statusInterval) {
                    clearInterval(statusInterval);
                  }
                };
              }}
            />
          );
        })()}

      {recorders.length === 0 ? (
        <div className="empty-state">
          <h2>No Input Feeds Found</h2>
          <p>Create your first input feed to get started.</p>
          <button className="empty-state-button" onClick={handleCreateRecorder}>
            <HiPlus className="button-icon" />
            <span>Create Input Feed</span>
          </button>
        </div>
      ) : (
        <>
          <div
            className="recorder-table-container"
            ref={tableContainerRef}
            onScroll={handleTableScroll}
          >
            <table className="recorder-table">
              <thead>
                <tr>
                  <th style={{ width: "12%" }}>Name</th>
                  <th style={{ width: "18%" }}>Input</th>
                  <th style={{ width: "10%" }}>Duration</th>
                  <th style={{ width: "18%" }}>Scheduled</th>
                  <th style={{ width: "20%" }}>Status</th>
                  <th style={{ width: "22%" }}>Actions</th>
                </tr>
              </thead>
              <tbody>
                {recorders.map((recorder) => (
                  <tr key={recorder.id}>
                    <td>{recorder.name}</td>
                    <td className="text-muted">{recorder.input}</td>
                    <td className="text-muted">{recorder.duration}</td>
                    <td className="text-muted">
                      {recorder.is_scheduled &&
                      recorder.scheduled_start_time ? (
                        <div className="scheduled-info">
                          <div className="scheduled-status scheduled">
                            Scheduled
                          </div>
                          <div className="scheduled-time">
                            {new Date(
                              recorder.scheduled_start_time
                            ).toLocaleString(undefined, {
                              month: "short",
                              day: "numeric",
                              hour: "2-digit",
                              minute: "2-digit",
                              hour12: false,
                            })}
                          </div>
                        </div>
                      ) : (
                        <div className="scheduled-info">
                          <div className="scheduled-status immediate">
                            Immediate
                          </div>
                        </div>
                      )}
                    </td>
                    <td>
                      <div className="status-container">
                        <div className="status-indicator">
                          <div
                            className={`status-dot ${
                              processingFiles[recorder.id] &&
                              processingFiles[recorder.id].length > 0
                                ? processingFiles[recorder.id][0].status ===
                                  FileStatus.Queue
                                  ? "queue"
                                  : "transcoding"
                                : recorder.status === "stopped" &&
                                  recorderStatuses.some(
                                    (s) =>
                                      s.id === recorder.id &&
                                      s.elapsed_seconds > 0 &&
                                      s.duration_seconds > 0 &&
                                      s.elapsed_seconds >= s.duration_seconds
                                  )
                                ? "completed"
                                : recorder.status === "failed"
                                ? "failed"
                                : recorder.status === "running"
                                ? "recording"
                                : recorder.status === "transcoding"
                                ? "transcoding"
                                : recorder.status
                            }`}
                          ></div>
                          <span
                            className={`status-text ${
                              processingFiles[recorder.id] &&
                              processingFiles[recorder.id].length > 0
                                ? processingFiles[recorder.id][0].status ===
                                  FileStatus.Queue
                                  ? "queue"
                                  : "transcoding"
                                : recorder.status === "stopped" &&
                                  recorderStatuses.some(
                                    (s) =>
                                      s.id === recorder.id &&
                                      s.elapsed_seconds > 0 &&
                                      s.duration_seconds > 0 &&
                                      s.elapsed_seconds >= s.duration_seconds
                                  )
                                ? "completed"
                                : recorder.status === "failed"
                                ? "failed"
                                : recorder.status === "running"
                                ? "recording"
                                : recorder.status === "transcoding"
                                ? "transcoding"
                                : recorder.status
                            }`}
                          >
                            {/* Show processing status if there are files being processed */}
                            {processingFiles[recorder.id] &&
                            processingFiles[recorder.id].length > 0
                              ? processingFiles[recorder.id][0].status ===
                                FileStatus.Queue
                                ? "QUEUE"
                                : "TRANSCODING"
                              : recorder.status === "stopped" &&
                                recorderStatuses.some(
                                  (s) =>
                                    s.id === recorder.id &&
                                    s.elapsed_seconds > 0 &&
                                    s.duration_seconds > 0 &&
                                    s.elapsed_seconds >= s.duration_seconds
                                )
                              ? "COMPLETED"
                              : recorder.status === "failed"
                              ? "FAILED"
                              : recorder.status === "running"
                              ? "RECORDING"
                              : recorder.status === "transcoding"
                              ? "TRANSCODING"
                              : recorder.status.toUpperCase()}
                          </span>
                        </div>

                        {/* Show connecting status with skeleton loading while waiting for data */}
                        {joinedRecorders.includes(recorder.id) &&
                          !recorderStatuses.some(
                            (status) => status.id === recorder.id
                          ) && (
                            <div className="joined-indicator">
                              <div className="sync-status">
                                <div className="sync-dot connecting-dot pulse"></div>
                                <span className="sync-text connecting-text">
                                  TS CONNECTING...
                                </span>
                              </div>
                              {/* Skeleton loading for stream info with labels */}
                              <div className="stream-info-skeleton">
                                <div className="skeleton-item">
                                  <span className="info-label">Services: </span>
                                  <div className="skeleton-line"></div>
                                </div>
                                <div className="skeleton-item">
                                  <span className="info-label">Video: </span>
                                  <div className="skeleton-line"></div>
                                </div>
                                <div className="skeleton-item">
                                  <span className="info-label">Audio: </span>
                                  <div className="skeleton-line"></div>
                                </div>
                              </div>
                            </div>
                          )}

                        {/* Show stream info if recorder is joined or running and we have status data */}
                        {(joinedRecorders.includes(recorder.id) ||
                          recorder.status === "running" ||
                          recorder.status === "recording" ||
                          recorder.status === "transcoding") &&
                          recorderStatuses.some(
                            (status) => status.id === recorder.id
                          ) && (
                            <div className="progress-container">
                              {(() => {
                                const status = recorderStatuses.find(
                                  (s) => s.id === recorder.id
                                );
                                if (!status) return null;

                                // Only show progress bar if recording is running/recording but not transcoding
                                const showProgressBar =
                                  recorder.status === "running" ||
                                  recorder.status === "recording";

                                // Stop calculating if elapsed time reaches or exceeds duration
                                const hasReachedDuration =
                                  status.elapsed_seconds >=
                                  status.duration_seconds;

                                const progress = hasReachedDuration
                                  ? 100
                                  : calculateProgress(
                                      status.elapsed_seconds,
                                      status.duration_seconds
                                    );

                                const timeRemaining = hasReachedDuration
                                  ? 0
                                  : status.duration_seconds -
                                    status.elapsed_seconds;

                                return (
                                  <>
                                    {showProgressBar && (
                                      <>
                                        <div className="progress-bar-container">
                                          <div
                                            className="progress-bar"
                                            style={{ width: `${progress}%` }}
                                          ></div>
                                        </div>
                                        <div className="progress-times">
                                          <span>
                                            {formatSeconds(
                                              status.elapsed_seconds
                                            )}
                                          </span>
                                          <span>
                                            {timeRemaining > 0
                                              ? formatSeconds(timeRemaining)
                                              : "00:00:00"}
                                          </span>
                                          <span>
                                            {formatSeconds(
                                              status.duration_seconds
                                            )}
                                          </span>
                                        </div>
                                      </>
                                    )}

                                    {/* TS Sync Status - Always show when joined */}
                                    <div className="sync-status">
                                      <div
                                        className={`sync-dot ${
                                          status.ts_sync ? "synced" : "unsynced"
                                        }`}
                                      ></div>
                                      <span
                                        className={`sync-text ${
                                          status.ts_sync ? "synced" : "unsynced"
                                        }`}
                                      >
                                        TS {status.ts_sync ? "SYNC" : "NO SYNC"}
                                      </span>
                                    </div>

                                    {/* Stream Information - Always show when joined and synced */}
                                    {status.ts_sync && (
                                      <>
                                        {/* Show skeleton loading if no stream data is available */}
                                        {(!status.services ||
                                          status.services.length === 0) &&
                                        (!status.video_info ||
                                          status.video_info.codec ===
                                            "Unknown") &&
                                        (!status.audio_info ||
                                          status.audio_info.codec ===
                                            "Unknown") ? (
                                          <div className="stream-info-skeleton">
                                            <div className="skeleton-item">
                                              <span className="info-label">
                                                Services:{" "}
                                              </span>
                                              <div className="skeleton-line"></div>
                                            </div>
                                            <div className="skeleton-item">
                                              <span className="info-label">
                                                Video:{" "}
                                              </span>
                                              <div className="skeleton-line"></div>
                                            </div>
                                            <div className="skeleton-item">
                                              <span className="info-label">
                                                Audio:{" "}
                                              </span>
                                              <div className="skeleton-line"></div>
                                            </div>
                                          </div>
                                        ) : (
                                          <div className="stream-info">
                                            {/* Services */}
                                            {status.services.length > 0 && (
                                              <div>
                                                <span className="info-label">
                                                  Services:{" "}
                                                </span>
                                                {status.services.map(
                                                  (service, idx) => (
                                                    <span
                                                      key={service.service_id}
                                                    >
                                                      {service.service_id}
                                                      {idx <
                                                      status.services.length - 1
                                                        ? ", "
                                                        : ""}
                                                    </span>
                                                  )
                                                )}
                                              </div>
                                            )}

                                            {/* Video Info */}
                                            {status.video_info &&
                                              status.video_info.codec !==
                                                "Unknown" && (
                                                <div>
                                                  <span className="info-label">
                                                    Video:{" "}
                                                  </span>
                                                  {status.video_info.codec} (
                                                  {status.video_info.resolution}
                                                  )
                                                </div>
                                              )}

                                            {/* Audio Tracks - Show AUDIO1 and AUDIO2 if available in services */}
                                            {(() => {
                                              // Check if we have services with audio tracks
                                              const hasServices =
                                                status.services &&
                                                status.services.length > 0;
                                              const hasAudioTracks =
                                                hasServices &&
                                                status.services[0]
                                                  .audio_tracks &&
                                                Array.isArray(
                                                  status.services[0]
                                                    .audio_tracks
                                                ) &&
                                                status.services[0].audio_tracks
                                                  .length > 0;

                                              console.log("Audio Debug:", {
                                                hasServices,
                                                hasAudioTracks,
                                                serviceCount:
                                                  status.services?.length || 0,
                                                audioTracksCount:
                                                  status.services?.[0]
                                                    ?.audio_tracks?.length || 0,
                                                audioTracks:
                                                  status.services?.[0]
                                                    ?.audio_tracks || "none",
                                                fallbackAudio:
                                                  status.audio_info,
                                              });

                                              if (hasAudioTracks) {
                                                const audioTracks =
                                                  status.services[0]
                                                    .audio_tracks;

                                                if (
                                                  audioTracks &&
                                                  Array.isArray(audioTracks)
                                                ) {
                                                  return (
                                                    <>
                                                      {/* AUDIO1 - Always show first track if available */}
                                                      {audioTracks[0] && (
                                                        <div>
                                                          <span className="info-label">
                                                            AUDIO1:{" "}
                                                          </span>
                                                          {audioTracks[0]
                                                            .codec ||
                                                            "Unknown"}{" "}
                                                          (
                                                          {audioTracks[0]
                                                            .channels === 6
                                                            ? "5.1, "
                                                            : audioTracks[0]
                                                                .channels === 2
                                                            ? ""
                                                            : audioTracks[0]
                                                                .channels + " "}
                                                          {
                                                            audioTracks[0]
                                                              .channels
                                                          }{" "}
                                                          ch)
                                                        </div>
                                                      )}

                                                      {/* AUDIO2 - Show second track if available */}
                                                      {audioTracks.length > 1 &&
                                                        audioTracks[1] && (
                                                          <div>
                                                            <span className="info-label">
                                                              AUDIO2:{" "}
                                                            </span>
                                                            {audioTracks[1]
                                                              .codec ||
                                                              "Unknown"}{" "}
                                                            (
                                                            {audioTracks[1]
                                                              .channels === 6
                                                              ? "5.1, "
                                                              : audioTracks[1]
                                                                  .channels ===
                                                                2
                                                              ? ""
                                                              : audioTracks[1]
                                                                  .channels +
                                                                " "}
                                                            {
                                                              audioTracks[1]
                                                                .channels
                                                            }{" "}
                                                            ch)
                                                          </div>
                                                        )}

                                                      {/* Debug info - show how many audio tracks we have */}
                                                      <div
                                                        style={{
                                                          fontSize: "0.8em",
                                                          color: "#666",
                                                          marginTop: "4px",
                                                        }}
                                                      >
                                                        Audio tracks:{" "}
                                                        {audioTracks.length}
                                                      </div>
                                                    </>
                                                  );
                                                }
                                              } else {
                                                // Fallback to single audio_info if no audio_tracks available
                                                if (
                                                  status.audio_info &&
                                                  status.audio_info.codec &&
                                                  status.audio_info.codec !==
                                                    "Unknown"
                                                ) {
                                                  return (
                                                    <div>
                                                      <span className="info-label">
                                                        AUDIO1:{" "}
                                                      </span>
                                                      {status.audio_info.codec}{" "}
                                                      (
                                                      {status.audio_info
                                                        .channels === 6
                                                        ? "5.1, "
                                                        : status.audio_info
                                                            .channels === 2
                                                        ? ""
                                                        : status.audio_info
                                                            .channels + " "}
                                                      {
                                                        status.audio_info
                                                          .channels
                                                      }{" "}
                                                      ch)
                                                      <div
                                                        style={{
                                                          fontSize: "0.8em",
                                                          color: "#666",
                                                          marginTop: "4px",
                                                        }}
                                                      >
                                                        (Fallback mode - no
                                                        audio_tracks)
                                                      </div>
                                                    </div>
                                                  );
                                                } else {
                                                  return (
                                                    <div>
                                                      <div
                                                        style={{
                                                          fontSize: "0.8em",
                                                          color: "#999",
                                                        }}
                                                      >
                                                        No audio information
                                                        available
                                                      </div>
                                                      <div
                                                        style={{
                                                          fontSize: "0.7em",
                                                          color: "#ccc",
                                                          marginTop: "2px",
                                                        }}
                                                      >
                                                        Debug: Services=
                                                        {status.services
                                                          ?.length || 0}
                                                        , AudioInfo=
                                                        {status.audio_info
                                                          ? "present"
                                                          : "missing"}
                                                      </div>
                                                    </div>
                                                  );
                                                }
                                              }
                                            })()}
                                          </div>
                                        )}
                                      </>
                                    )}
                                  </>
                                );
                              })()}
                            </div>
                          )}
                      </div>
                    </td>
                    <td>
                      <div className="action-buttons">
                        {/* Join/Unjoin Button */}
                        <button
                          className={`action-button icon-button ${
                            joinedRecorders.includes(recorder.id)
                              ? "joined"
                              : "join"
                          } ${
                            joiningRecorders.includes(recorder.id)
                              ? "loading"
                              : ""
                          }`}
                          onClick={() => handleToggleJoinStatus(recorder.id)}
                          disabled={
                            recorder.status === "running" ||
                            recorder.status === "recording" ||
                            recorder.status === "transcoding" ||
                            joiningRecorders.includes(recorder.id)
                          }
                          title={
                            joiningRecorders.includes(recorder.id)
                              ? joinedRecorders.includes(recorder.id)
                                ? "Unjoining stream..."
                                : "Joining stream..."
                              : joinedRecorders.includes(recorder.id)
                              ? "Unjoin this stream"
                              : "Join this stream to view TS sync status"
                          }
                        >
                          {joiningRecorders.includes(recorder.id) ? (
                            <span className="loading-text">
                              {joinedRecorders.includes(recorder.id)
                                ? "Unjoining..."
                                : "Joining..."}
                            </span>
                          ) : joinedRecorders.includes(recorder.id) ? (
                            <HiXCircle className="button-icon" />
                          ) : (
                            <HiLink className="button-icon" />
                          )}
                        </button>

                        {/* Record/Stop Button */}
                        <button
                          className={`action-button icon-button ${
                            recorder.status === "running" ||
                            recorder.status === "recording" ||
                            recorder.status === "transcoding"
                              ? "stop"
                              : "start"
                          }`}
                          onClick={() =>
                            handleToggleRecorderStatus(recorder.id)
                          }
                          disabled={
                            recorder.status === "running" ||
                            recorder.status === "recording" ||
                            recorder.status === "transcoding"
                              ? false
                              : !joinedRecorders.includes(recorder.id) ||
                                // Disable the Record button if TS sync is false
                                recorderStatuses.some(
                                  (status) =>
                                    status.id === recorder.id && !status.ts_sync
                                )
                          }
                          title={
                            recorder.status === "running" ||
                            recorder.status === "recording"
                              ? "Stop this recording"
                              : recorder.status === "transcoding"
                              ? "Stop transcoding"
                              : !joinedRecorders.includes(recorder.id)
                              ? "Join the stream first to start recording"
                              : recorderStatuses.some(
                                  (status) =>
                                    status.id === recorder.id && !status.ts_sync
                                )
                              ? "Cannot start recording: No TS sync detected"
                              : "Start this recording"
                          }
                        >
                          {recorder.status === "running" ||
                          recorder.status === "recording" ||
                          recorder.status === "transcoding" ? (
                            <HiStop className="button-icon" />
                          ) : (
                            <HiPlay className="button-icon" />
                          )}
                        </button>

                        {/* Edit Button */}
                        <button
                          className={`action-button icon-button edit ${
                            recorder.status === "running" ||
                            recorder.status === "recording" ||
                            recorder.status === "transcoding"
                              ? "disabled"
                              : ""
                          }`}
                          onClick={() => handleEditRecorder(recorder)}
                          disabled={
                            recorder.status === "running" ||
                            recorder.status === "recording" ||
                            recorder.status === "transcoding"
                          }
                          title={
                            recorder.status === "running" ||
                            recorder.status === "recording"
                              ? "Cannot edit while recording is in progress"
                              : recorder.status === "transcoding"
                              ? "Cannot edit while transcoding is in progress"
                              : "Edit this input feed"
                          }
                        >
                          <HiPencil className="button-icon" />
                        </button>

                        {/* Delete Button */}
                        <button
                          className={`action-button icon-button delete ${
                            recorder.status === "running" ||
                            recorder.status === "recording" ||
                            recorder.status === "transcoding"
                              ? "disabled"
                              : ""
                          }`}
                          onClick={() => handleDeleteRecorder(recorder.id)}
                          disabled={
                            recorder.status === "running" ||
                            recorder.status === "recording" ||
                            recorder.status === "transcoding"
                          }
                          title={
                            recorder.status === "running" ||
                            recorder.status === "recording"
                              ? "Cannot delete while recording is in progress"
                              : recorder.status === "transcoding"
                              ? "Cannot delete while transcoding is in progress"
                              : "Delete this input feed"
                          }
                        >
                          <HiTrash className="button-icon" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {pagination.totalPages > 1 && (
            <div className="pagination">
              <button
                className="pagination-button"
                disabled={pagination.page === 1}
                onClick={() => handlePageChange(pagination.page - 1)}
              >
                <HiChevronLeft className="button-icon" />
                <span>Previous</span>
              </button>
              <span className="pagination-info">
                Page {pagination.page} of {pagination.totalPages}
              </span>
              <button
                className="pagination-button"
                disabled={pagination.page === pagination.totalPages}
                onClick={() => handlePageChange(pagination.page + 1)}
              >
                <span>Next</span>
                <HiChevronRight className="button-icon" />
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default RecorderPage;
