/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 165, 0, 0.4);
  }
  70% {
    transform: scale(1.02);
    box-shadow: 0 0 0 10px rgba(255, 165, 0, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 165, 0, 0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes gradientBG {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Background elements */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #121212;
  position: relative;
  overflow: hidden;
}

.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 50%, rgba(40, 40, 40, 0.3) 0%, rgba(20, 20, 20, 0.5) 100%);
  z-index: 0;
}

/* Animated background circles */
.bg-circles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.bg-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(255, 165, 0, 0.05), rgba(255, 69, 0, 0.05));
  filter: blur(20px);
}

.bg-circle-1 {
  top: -100px;
  left: -100px;
  width: 500px;
  height: 500px;
  animation: float-circle 25s infinite alternate ease-in-out;
}

.bg-circle-2 {
  bottom: -150px;
  right: -150px;
  width: 600px;
  height: 600px;
  animation: float-circle 20s infinite alternate-reverse ease-in-out;
}

.bg-circle-3 {
  top: 50%;
  left: 40%;
  transform: translate(-50%, -50%);
  width: 400px;
  height: 400px;
  animation: pulse-circle 15s infinite alternate ease-in-out;
}

@keyframes float-circle {
  0% {
    transform: translate(0, 0) scale(1);
  }
  50% {
    transform: translate(50px, 30px) scale(1.1);
  }
  100% {
    transform: translate(-30px, 50px) scale(0.9);
  }
}

@keyframes pulse-circle {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.3;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.5;
  }
  100% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.3;
  }
}

.login-card {
  position: relative;
  background-color: rgba(30, 30, 30, 0.9);
  border-radius: 12px;
  padding: 2.5rem;
  width: 100%;
  max-width: 420px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3),
              0 0 30px rgba(255, 165, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 1;
  animation: fadeIn 0.8s ease-out forwards;
  transform: translateY(0);
}

.login-card::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  /* background: linear-gradient(45deg, #ffa500, #ff4500, #ffa500);
  background-size: 200% 200%; */
  animation: gradientBG 15s ease infinite;
  border-radius: 14px;
  z-index: -1;
  opacity: 0.3;
}

.login-logo {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
  animation: float 6s ease-in-out infinite;
}

.login-logo img {
  height: 90px;
  object-fit: contain;
  transition: all 0.3s ease;
}

.login-logo img:hover {
  transform: scale(1.05);
  filter: drop-shadow(0 0 12px rgba(255, 165, 0, 0.5));
}

.login-card h2 {
  color: #ffffff;
  text-align: center;
  margin-bottom: 2rem;
  font-size: 1.8rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  animation: slideInFromRight 0.6s ease-out forwards;
  animation-delay: 0.2s;
  opacity: 0;
  transform: translateX(30px);
}

.form-group {
  margin-bottom: 1.8rem;
  position: relative;
  animation: slideInFromLeft 0.6s ease-out forwards;
  opacity: 0;
}

.form-group:nth-child(1) {
  animation-delay: 0.3s;
}

.form-group:nth-child(2) {
  animation-delay: 0.4s;
}

.form-group:nth-child(3) {
  animation-delay: 0.5s;
}

.form-group:nth-child(4) {
  animation-delay: 0.6s;
}

.form-group label {
  display: block;
  margin-bottom: 0.6rem;
  color: #e0e0e0;
  font-size: 0.95rem;
  font-weight: 500;
  text-align: left;
  transition: all 0.3s ease;
}

.form-group input {
  width: 100%;
  padding: 0.9rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  background-color: rgba(40, 40, 40, 0.8);
  color: #ffffff;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.form-group input:focus {
  outline: none;
  border-color: #ffa500;
  box-shadow: 0 0 0 3px rgba(255, 165, 0, 0.2),
              inset 0 1px 3px rgba(0, 0, 0, 0.2);
  background-color: rgba(50, 50, 50, 0.8);
}

.form-group input::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

.login-button {
  width: 100%;
  padding: 0.9rem;
  margin-top: 30px;
  background: linear-gradient(45deg, #ffa500, #ff8c00);
  color: #000000;
  border: none;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(255, 165, 0, 0.3);
  animation: slideInFromRight 0.6s ease-out forwards;
  animation-delay: 0.7s;
  opacity: 0;
  transform: translateX(30px);
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: all 0.6s ease;
}

.login-button:hover {
  background: linear-gradient(45deg, #ff8c00, #ffa500);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 165, 0, 0.4);
}

.login-button:hover::before {
  left: 100%;
}

.login-button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 10px rgba(255, 165, 0, 0.3);
}

.login-button:disabled {
  background: linear-gradient(45deg, #666666, #555555);
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

.login-button.loading {
  position: relative;
  color: transparent;
}

.login-button.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid rgba(0, 0, 0, 0.3);
  border-top-color: #000;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.form-footer {
  margin-top: 2rem;
  text-align: center;
  color: #cccccc;
  font-size: 0.95rem;
  animation: fadeIn 0.6s ease-out forwards;
  animation-delay: 0.8s;
  opacity: 0;
}

.form-footer a {
  color: #ffa500;
  text-decoration: none;
  font-weight: 600;
  margin-left: 0.5rem;
  transition: all 0.3s ease;
  position: relative;
}

.form-footer a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -2px;
  left: 0;
  background-color: #ffa500;
  transition: width 0.3s ease;
}

.form-footer a:hover {
  color: #ffb733;
  text-decoration: none;
}

.form-footer a:hover::after {
  width: 100%;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .login-card {
    padding: 2rem 1.5rem;
    max-width: 90%;
  }

  .login-logo img {
    height: 70px;
  }

  .login-card h2 {
    font-size: 1.5rem;
  }
}
