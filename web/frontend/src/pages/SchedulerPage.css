/* Toast action buttons styles */
.toast-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 12px;
}

/* Enhanced toast container styles */
.enhanced-toast-container {
  background-color: rgba(25, 25, 25, 0.95) !important;
  border: 2px solid #2196f3 !important;
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3) !important;
  padding: 16px !important;
  border-radius: 8px !important;
  width: 100% !important;
  max-width: 450px !important;
  animation: toastFadeIn 0.5s ease forwards, toastGlow 2s infinite alternate !important;
}

.enhanced-toast-content {
  font-size: 1.05rem;
  line-height: 1.5;
  margin-bottom: 4px;
  color: rgba(255, 255, 255, 0.95);
}

.toast-action-button {
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-size: 0.95rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.toast-action-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: all 0.6s ease;
}

.toast-action-button:hover::before {
  left: 100%;
}

.toast-action-button.primary {
  background-color: #2196f3;
  color: white;
}

.toast-action-button.primary:hover {
  background-color: #1976d2;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(33, 150, 243, 0.4);
}

.toast-action-button.secondary {
  background-color: #333;
  color: white;
}

.toast-action-button.secondary:hover {
  background-color: #444;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Toast animations */
@keyframes toastFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes toastGlow {
  from {
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.2);
  }
  to {
    box-shadow: 0 4px 20px rgba(33, 150, 243, 0.5);
  }
}

/* Enhanced toast progress bar */
.enhanced-toast-progress {
  background: linear-gradient(to right, #2196f3, #64b5f6) !important;
  height: 4px !important;
  opacity: 0.8 !important;
}

/* Enhanced toast wrapper */
.enhanced-toast-wrapper {
  padding: 0 !important;
  min-height: 80px !important;
  border-radius: 8px !important;
}

/* Style the close button */
.Toastify__close-button {
  color: rgba(255, 255, 255, 0.7) !important;
  opacity: 0.7 !important;
  transition: all 0.3s ease !important;
}

.Toastify__close-button:hover {
  color: #2196f3 !important;
  opacity: 1 !important;
  transform: scale(1.1) !important;
}

.scheduler-page {
  margin: 0 auto;
  display: flex;
  width: 100%;
  height: 100%;
}

.scheduler-content {
  width: 100%;
  height: 100%;
}

.short-sidebar {
  width: 60px;
  min-width: 60px;
}

.full-sidebar {
  width: 290px;
  min-width: 290px;
}

.files-modal {
  width: 800px;
  max-width: 800px;
  min-height: 750px;
  max-height: 750px;
}

.files-modal .modal-body {
  height: 619px;
  max-height: 619px;
  overflow-y: auto;
  padding: 10px;
}

.files-modal .file-body {
  height: 70%;
  max-height: 70%;
  overflow-y: scroll;
  overflow-x: hidden;
}

.files-modal .filler-body {
  height: 70%;
  max-height: 70%;
  overflow-y: scroll;
  overflow-x: hidden;
}

.file-body .location-menu {
  margin-bottom: 20px;
}

.select-folder {
  display: flex;
  width: 100%;
  padding: 5px;
  justify-content: space-between;
}

.select-folder:hover {
  background-color: #222;
}

.filler-body .select-folder-name,
.file-body .select-folder-name {
  display: flex;
  gap: 10px;
  align-items: center;
  cursor: pointer;
}

.filler-body .select-folder-name span,
.file-body .select-folder-name span {
  font-weight: 500;
  font-size: 0.95rem;
  display: flex;
  gap: 10px;
}

.filler-body .select-folder-name:hover span,
.file-body .select-folder-name:hover span {
  color: #2196f3;
}

.select-folder-button {
  width: 50px;
}

.select-folder-button button:focus,
.select-folder-button button {
  padding: 0 20px;
  transition: none;
  background: none;
  outline: none;
}

.select-folder-button button:hover {
  border: none;
  background: none;
  color: #2196f3;
}

.remove-button svg {
  width: 18px;
  height: 18px;
}

.remove-button button:hover {
  color: #f44336;
}

.file-duration {
  color: #2196f3;
  font-size: 0.85rem;
}

.files-modal .added-files {
  height: 100%;
  max-height: 100%;
  overflow-y: scroll;
  overflow-x: hidden;
  border-top: 1px solid #333;
}

.sidebar {
  display: flex;
  flex-direction: column;
  background: #1a1a1a;
  overflow: auto;
  gap: 1rem;
  border-radius: 0 10px 10px 0;
  padding: 4px;
  z-index: 20;
  justify-content: space-between;
  height: 100%;
}

.sidebar-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.sidebar-footer {
  justify-items: flex-end;
}

.sidebar-row {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  justify-content: center;
  align-items: center;
  padding-inline: 8px;
}

.sidebar-row-multiple {
  flex-direction: row;
}

.sidebar-row button {
  width: 100%;
  height: 37px;
  padding: 0.5rem;
  border: 1px solid #333;
  border-radius: 4px;
  background-color: #2a2a2a;
  color: #fff;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.text-info {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 0.8rem;
  color: #2196f3;
}

.confirm-button:disabled {
  cursor: default;
  color: gray;
  border: 1px solid #3a3a3a;
  background-color: #2a2a2a;
}

.text-center {
  text-align: center;
}

.text-justify {
  text-align: justify;
}

.sidebar-updated {
  font-size: 0.8rem;
  color: #a8a8a8;
  line-height: 1.2;
}

.react-select-container .react-select__control {
  width: 100%;
  border: 1px solid #333;
  border-radius: 4px;
  background-color: #2a2a2a;
  color: #fff;
  font-size: 1rem;
}

.react-select-container .react-select__control:hover {
  border: 1px solid #333;
}

.react-select-container .react-select__menu {
  background: #151515;
  border: 1px solid #4d4d4d;
}

.react-select-container .react-select__option {
  color: #fff;
  background: #151515;
  text-wrap: nowrap;
  cursor: pointer;
}

.react-select-container .react-select__option:hover {
  background: #1e1e1e;
}

.react-select-container .react-select__indicator {
  color: #fff !important;
}
.react-select-container .react-select__indicator-separator {
  background: #fff;
}

.react-select-container .react-select__input-container,
.react-select-container .react-select__single-value {
  color: #fff;
}

.fc-timegrid-slot {
  height: 4em !important; /* 1.5em by default */
}

.fc .fc-toolbar.fc-header-toolbar {
  margin-bottom: 0 !important;
}

/* Base styles for all calendar elements */
.fc {
  --fc-event-bg-color: #0f0f0fe5;
  --fc-highlight-color: rgba(33, 150, 243, 0.1);
  --fc-theme-color: #2196f3;
}

/* Apply a consistent default cursor to the entire calendar container */
.fc-view-harness,
.fc-scroller,
.fc-scroller-liquid-absolute,
.fc-timegrid,
.fc-timegrid-slots,
.fc-timegrid-cols,
.fc-timegrid-body,
.fc-timegrid-col,
.fc-timegrid-col-bg,
.fc-timegrid-col-frame,
.fc-timegrid-now-indicator-container,
.fc-scrollgrid,
.fc-scrollgrid-section,
.fc-scrollgrid-section-body,
.fc-scrollgrid-section-header,
.fc-scrollgrid-sync-table,
.fc-cell-shaded,
.fc-timegrid-axis,
.fc-timegrid-axis-frame {
  cursor: default !important;
}

/* Event styling and interactions */
.fc-event {
  background: #0f0f0fe5 !important;
  border: 1px solid #4d4d4d !important;
  border-radius: 5px !important;
}

/* Cursor for cell selection */
.fc-timegrid-col-events {
  cursor: cell !important;
}

/* Grid styling */
.fc-theme-standard .fc-scrollgrid {
  border: 3px solid #0f0f0fe5 !important;
}

.fc-theme-standard th {
  background: #0f0f0fe5 !important;
}

.fc-theme-standard td,
.fc-theme-standard th {
  border: 3px solid #0f0f0fe5 !important;
}

.fc-theme-standard td .fc-timegrid-now-indicator-container {
  background: transparent !important;
  border-radius: 5px !important;
}

/* Highlight styling */
.fc-theme-standard td .fc-highlight {
  background: rgba(15, 15, 15, 0.9) !important;
  border-radius: 5px !important;
}

.fc-theme-standard th .fc-scrollgrid-sync-inner {
  background: #202020 !important;
  border-radius: 10px !important;
  padding: 5px !important;
}

.fc-timegrid-event-harness-inset .fc-timegrid-event {
  box-shadow: none !important;
}

.fc-scrollgrid-shrink,
.fc-timegrid-slot-label {
  background: #0f0f0fe5 !important;
}

/* Resize handle styling */
.fc-event-resizer-start {
  cursor: w-resize !important;
  left: 0 !important;
}

.fc-event-resizer-end {
  cursor: e-resize !important;
  right: 0 !important;
}

/* Prevent cursor flickering during interactions */
.fc-event-selected,
.fc-event-selected:hover {
  z-index: 10 !important;
}

/* Improve selection experience */
.fc-timegrid-col-bg .fc-highlight {
  transition: background-color 0.2s ease, border-color 0.2s ease,
    box-shadow 0.2s ease !important;
}

/* Ensure consistent cursor behavior */
.fc-timegrid-col,
.fc-timegrid-body,
.fc-timegrid-slots,
.fc-timegrid-cols,
.fc-timegrid {
  cursor: default !important;
}

/* Event dragging style */
.fc-event.fc-event-dragging {
  opacity: 0.8 !important;
}

/* Custom class styles for calendar elements */
.calendar-event {
  cursor: pointer !important;
}

.calendar-time-slot {
  cursor: pointer !important;
}

/* Improve interaction feedback */
.fc-highlight {
  background-color: rgba(33, 150, 243, 0.1) !important;
  border-radius: 4px !important;
}

/* Fix for cursor flickering */
.fc-timegrid-event-harness {
  z-index: 5 !important;
}

.fc-timegrid-event-harness:hover {
  z-index: 9 !important;
}

/* Specific cursor overrides for interactions */
.fc-event-dragging {
  cursor: move !important;
}

.fc-event-resizing {
  cursor: col-resize !important;
}

/* Force cursor for time slots */
.fc-timegrid-slot {
  cursor: pointer !important;
}

/* Force cursor for events */
.fc-timegrid-event {
  cursor: pointer !important;
}

/* Day header styling */
.fc-col-header-cell {
  cursor: default !important;
}

.fc-col-header-cell-cushion {
  color: #2196f3 !important;
  font-weight: 600 !important;
  font-family: "Roboto", monospace !important;
  letter-spacing: 0.5px !important;
}

/* Improve interaction with time labels */
.fc-timegrid-axis-cushion {
  cursor: default !important;
  color: rgba(255, 255, 255, 0.7) !important;
  font-family: "Roboto", monospace !important;
  font-weight: 500 !important;
}

.fc-media-screen .fc-timegrid-cols {
  z-index: 1000 !important;
}

.rmdp-day {
  color: #ffffff !important;
}

.rmdp-header-values {
  color: #ffffff !important;
}

.rmdp-week-day {
  color: #2196f3 !important;
}

.rmdp-arrow {
  border: solid #2196f3 !important;
  border-width: 0 2px 2px 0 !important;
  margin: 0 !important;
}

.rmdp-arrow-container {
  display: flex;
  align-items: center;
}

.rmdp-arrow-container:hover {
  background: #2196f3 !important;
}

.rmdp-arrow-container:hover .rmdp-arrow {
  border: solid #ffffff !important;
  border-width: 0 2px 2px 0 !important;
}

.rmdp-day.rmdp-today span {
  background: rgba(33, 150, 243, 0.4) !important;
}

.rmdp-day.rmdp-selected span:not(.highlight) {
  background: #2196f3 !important;
}

.rmdp-day:not(.rmdp-disabled, .rmdp-day-hidden) span:hover {
  background: #ffffff !important;
  color: #0f0f0fe5 !important;
}

.rmdp-shadow {
  box-shadow: none !important;
}

#player-component .btn {
  width: auto !important;
  height: 44px;
}

#player-component .btn svg {
  padding: 0 !important;
}

/* Save notification banner */
.save-notification {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: rgba(255, 165, 0, 0.9);
  color: #000;
  padding: 12px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  gap: 12px;
  z-index: 1000;
  animation: pulse 2s infinite;
  transition: all 0.3s ease;
}

.save-notification-icon {
  font-size: 20px;
}

.save-notification-text {
  font-weight: 600;
}

.save-notification-button {
  background-color: #222;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.2s;
}

.save-notification-button:hover {
  background-color: #000;
}

.save-button-highlight {
  background-color: #ffa500 !important;
  color: #000 !important;
  border-color: #ffb733 !important;
  font-weight: bold;
  animation: pulse 2s infinite;
}

.save-button-highlight:hover {
  background-color: #ffb733 !important;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}
