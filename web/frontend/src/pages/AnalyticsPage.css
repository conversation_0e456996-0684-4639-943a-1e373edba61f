.analytics-page {
  padding: 20px;
  max-width: 100%;
  overflow-x: hidden;
  color: rgba(255, 255, 255, 0.87);
  background-color: #1a1a1a;
  animation: fadeIn 0.6s ease-out forwards;
  font-family: "Roboto", system-ui, sans-serif;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.analytics-page h1 {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  color: rgba(255, 255, 255, 0.87);
  font-size: 24px;
  font-family: "Roboto", system-ui, sans-serif;
  font-weight: 500;
  letter-spacing: 0.5px;
  position: relative;
  animation: slideDown 0.7s ease-out forwards;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-15px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.analytics-page h1::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: #2196f3;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.analytics-page h1:hover::after {
  width: 60px;
}

.page-icon {
  margin-right: 10px;
  color: #2196f3;
  font-size: 28px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    filter: drop-shadow(0 0 0 rgba(33, 150, 243, 0));
  }
  50% {
    transform: scale(1.05);
    filter: drop-shadow(0 0 3px rgba(33, 150, 243, 0.5));
  }
  100% {
    transform: scale(1);
    filter: drop-shadow(0 0 0 rgba(33, 150, 243, 0));
  }
}

.analytics-controls {
  margin-bottom: 30px;
  background-color: #2a2a2a;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  border: 1px solid #333;
  animation: fadeIn 0.8s ease-out forwards;
  animation-delay: 0.2s;
  opacity: 0;
  position: relative;
  overflow: hidden;
}

.analytics-controls::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #2196f3, #21f3e7, #2196f3);
  background-size: 200% 100%;
  animation: gradientMove 3s linear infinite;
}

@keyframes gradientMove {
  0% {
    background-position: 0% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.analytics-tabs {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid #333;
  position: relative;
  font-family: "Roboto", system-ui, sans-serif;
}

.analytics-tab {
  padding: 12px 20px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  border-bottom: 2px solid transparent;
  position: relative;
  overflow: hidden;
  font-family: "Roboto", system-ui, sans-serif;
  font-weight: 500;
}

.analytics-tab::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: #2196f3;
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.analytics-tab:hover {
  color: rgba(255, 255, 255, 0.95);
  background-color: rgba(33, 150, 243, 0.05);
}

.analytics-tab:hover::before {
  width: 80%;
}

.analytics-tab.active {
  color: #2196f3;
  border-bottom: 2px solid #2196f3;
  font-weight: 500;
  background-color: rgba(33, 150, 243, 0.1);
}

.analytics-tab.active::before {
  width: 100%;
}

.analytics-tab svg {
  transition: transform 0.3s ease;
}

.analytics-tab:hover svg {
  transform: scale(1.2);
}

.analytics-tab.active svg {
  filter: drop-shadow(0 0 3px rgba(33, 150, 243, 0.5));
}

.filter-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
  animation: fadeIn 0.8s ease-out forwards;
  animation-delay: 0.4s;
  opacity: 0;
}

.date-range-selector,
.schedule-selector {
  display: flex;
  align-items: center;
  gap: 12px;
}

.date-range-selector span,
.schedule-selector span {
  font-weight: 500;
  color: rgba(255, 255, 255, 0.87);
  font-size: 14px;
  font-family: "Roboto", system-ui, sans-serif;
}

.date-range-buttons {
  display: flex;
  gap: 8px;
}

.date-range-buttons button {
  padding: 8px 14px;
  background-color: #1a1a1a;
  border: 1px solid #333;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.87);
  font-family: "Roboto", system-ui, sans-serif;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.date-range-buttons button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.7s ease;
}

.date-range-buttons button:hover::before {
  left: 100%;
}

.date-range-buttons button:hover {
  background-color: rgba(33, 150, 243, 0.1);
  border-color: #2196f3;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(33, 150, 243, 0.3);
}

.date-range-buttons button.active {
  background-color: #2196f3;
  color: white;
  border-color: #2196f3;
  box-shadow: 0 0 10px rgba(33, 150, 243, 0.5);
  transform: translateY(-1px);
}

.date-range-buttons button.active::before {
  display: none;
}

.select-wrapper {
  position: relative;
  display: inline-block;
}

.schedule-selector select {
  padding: 8px 12px;
  border: 1px solid #333;
  border-radius: 6px;
  font-size: 14px;
  min-width: 200px;
  background-color: #1a1a1a;
  color: rgba(255, 255, 255, 0.87);
  font-family: "Roboto", system-ui, sans-serif;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  background-image: linear-gradient(45deg, transparent 50%, #2196f3 50%),
    linear-gradient(135deg, #2196f3 50%, transparent 50%);
  background-position: calc(100% - 20px) calc(1em + 2px),
    calc(100% - 15px) calc(1em + 2px);
  background-size: 5px 5px, 5px 5px;
  background-repeat: no-repeat;
  appearance: none;
}

.schedule-selector select:hover:not(:disabled) {
  border-color: #2196f3;
  box-shadow: 0 4px 8px rgba(33, 150, 243, 0.3);
}

.schedule-selector select:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.3);
}

.schedule-selector select:disabled {
  opacity: 0.7;
  cursor: wait;
  background-image: none;
}

.schedule-selector select.loading {
  padding-right: 40px; /* Make room for the loading indicator */
}

.select-loading-indicator {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  border: 2px solid rgba(33, 150, 243, 0.3);
  border-top: 2px solid #2196f3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: translateY(-50%) rotate(0deg);
  }
  100% {
    transform: translateY(-50%) rotate(360deg);
  }
}

.analytics-content {
  padding: 10px 0;
  animation: fadeIn 0.8s ease-out forwards;
  animation-delay: 0.5s;
  opacity: 0;
}

.analytics-overview h2,
.content-analytics h2,
.schedule-analytics h2 {
  margin: 30px 0 15px;
  font-size: 18px;
  color: rgba(255, 255, 255, 0.87);
  border-bottom: 1px solid #333;
  padding-bottom: 10px;
  font-family: "Roboto", system-ui, sans-serif;
  font-weight: 500;
  position: relative;
  animation: fadeInUp 0.8s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.analytics-overview h2::after,
.content-analytics h2::after,
.schedule-analytics h2::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 60px;
  height: 3px;
  background-color: #2196f3;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.analytics-overview h2:hover::after,
.content-analytics h2:hover::after,
.schedule-analytics h2:hover::after {
  width: 100px;
}

.analytics-stats {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.analytics-card {
  background-color: #2a2a2a;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  border: 1px solid #333;
  position: relative;
  overflow: hidden;
  animation: fadeInCard 0.8s ease-out forwards;
  animation-delay: calc(0.1s * var(--card-index, 0));
  opacity: 0;
  transform: translateY(20px);
}

@keyframes fadeInCard {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.analytics-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle at top right,
    rgba(33, 150, 243, 0.1),
    transparent 70%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.analytics-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.3);
  border-color: #2196f3;
}

.analytics-card:hover::before {
  opacity: 1;
}

.analytics-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 12px;
}

.analytics-card-header h3 {
  margin: 0;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.87);
  font-weight: 500;
  font-family: "Roboto", system-ui, sans-serif;
}

.card-icon {
  color: #2196f3;
  font-size: 24px;
  filter: drop-shadow(0 0 3px rgba(33, 150, 243, 0.3));
  transition: all 0.3s ease;
}

.analytics-card:hover .card-icon {
  transform: scale(1.2);
  filter: drop-shadow(0 0 5px rgba(33, 150, 243, 0.5));
}

.analytics-card-value {
  font-size: 32px;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.95);
  margin-bottom: 8px;
  font-family: "Roboto", system-ui, sans-serif;
  transition: all 0.3s ease;
}

.analytics-card:hover .analytics-card-value {
  color: #2196f3;
  text-shadow: 0 0 10px rgba(33, 150, 243, 0.3);
}

.analytics-card-subtitle {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.6);
  font-family: "Roboto", system-ui, sans-serif;
}

.recent-plays-table,
.content-groups,
.search-results {
  margin-top: 15px;
  background-color: #1a1a1a;
  border-radius: 12px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  border: 1px solid #333;
  transition: all 0.3s ease;
}

table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

th,
td {
  padding: 0.75rem 1.25rem;
  text-align: left;
  border-bottom: 1px solid #333;
}

th {
  background-color: #1e1e1e;
  font-weight: 600;
  color: #fff;
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  height: 48px;
}

th::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(
    90deg,
    rgba(100, 108, 255, 0) 0%,
    rgba(100, 108, 255, 0.3) 50%,
    rgba(100, 108, 255, 0) 100%
  );
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

th:hover::after {
  transform: scaleX(1);
}

td {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.87);
}

.content-name-cell {
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
}

.play-type {
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 12px;
  text-transform: capitalize;
  display: inline-block;
}

.play-type-scheduled {
  background-color: #2196f3;
  color: white;
}

.play-type-filler {
  background-color: #ff9800;
  color: black;
}

.play-type-pre-filler {
  background-color: #8c00ff;
  color: white;
}

.play-type-ad {
  background-color: #f44336;
  color: white;
}

tr {
  transition: all 0.2s ease;
}

tr:hover {
  background-color: rgba(100, 108, 255, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.content-group-row:hover {
  background-color: rgba(100, 108, 255, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.schedule-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.schedule-summary-card {
  background-color: #2a2a2a;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  transition: transform 0.2s;
  border: 1px solid #333;
}

.schedule-summary-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  border-color: #2196f3;
}

.schedule-summary-card h3 {
  margin-top: 0;
  color: rgba(255, 255, 255, 0.87);
  font-size: 18px;
  margin-bottom: 15px;
  border-bottom: 1px solid #333;
  padding-bottom: 10px;
}

.schedule-stats {
  margin-bottom: 15px;
}

.schedule-stat {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
  color: rgba(255, 255, 255, 0.87);
  font-size: 14px;
}

.stat-icon {
  color: #2196f3;
}

.top-content-preview {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #333;
}

.top-content-preview h4 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 15px;
  color: rgba(255, 255, 255, 0.87);
}

.top-content-preview ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.top-content-preview li {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #333;
  font-size: 13px;
}

.top-content-preview li:last-child {
  border-bottom: none;
}

.top-content-preview .content-name {
  max-width: 70%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.top-content-preview .content-plays {
  color: #2196f3;
  font-weight: 500;
}

.view-schedule-btn {
  background-color: #2196f3;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 15px;
  transition: background-color 0.2s;
  width: 100%;
}

.view-schedule-btn:hover {
  background-color: #1976d2;
}

.search-controls {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.search-controls input {
  flex: 1;
  padding: 10px 15px;
  border: 1px solid #333;
  border-radius: 4px;
  font-size: 14px;
  background-color: #1a1a1a;
  color: rgba(255, 255, 255, 0.87);
}

.search-button {
  background-color: #2196f3;
  color: white;
  border: none;
  padding: 0 20px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-button:hover {
  background-color: #1976d2;
}

/* Pagination styles */
.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 2rem;
  padding: 1rem;
  animation: fadeIn 0.5s ease-out;
}

.pagination-container .pagination-button {
  background-color: var(--color-bg-secondary, #2a2a2a);
  border: none;
  border-radius: 8px;
  padding: 0.5rem;
  color: var(--color-text-primary, #e0e0e0);
  font-size: 0.9rem;
  transition: all var(--transition-fast, 0.3s ease);
  min-width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: "Roboto", system-ui, sans-serif;
  cursor: pointer;
  margin: 0 0.25rem;
}

.pagination-container .pagination-button:hover:not(.pagination-button-active) {
  background-color: var(--color-bg-tertiary, #3a3a3a);
  box-shadow: 0 0 5px rgba(33, 150, 243, 0.5);
  transform: translateY(-2px);
}

.pagination-container .pagination-button-active {
  background-color: var(--color-primary, #2196f3);
  color: white;
  box-shadow: 0 0 8px rgba(33, 150, 243, 0.7);
}

.pagination-container .pagination-info {
  color: var(--color-text-secondary, #b0b0b0);
  font-size: 0.9rem;
  margin: 0 1rem;
  font-family: "Roboto", system-ui, sans-serif;
}

.pagination-container .pagination-ellipsis {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #aaa;
  font-size: 1.2rem;
  padding: 0 0.5rem;
}

.no-results,
.no-data-message {
  padding: 30px;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  font-size: 16px;
  background-color: #2a2a2a;
  border-radius: 8px;
  margin-top: 20px;
  border: 1px solid #333;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(33, 150, 243, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(33, 150, 243, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(33, 150, 243, 0);
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes glow {
  0% {
    filter: drop-shadow(0 0 2px rgba(33, 150, 243, 0.3));
  }
  50% {
    filter: drop-shadow(0 0 8px rgba(33, 150, 243, 0.6));
  }
  100% {
    filter: drop-shadow(0 0 2px rgba(33, 150, 243, 0.3));
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.no-schedules-message {
  text-align: center;
  margin: 50px auto;
  padding: 40px;
  max-width: 600px;
  background: linear-gradient(145deg, #222, #2a2a2a);
  background-image: linear-gradient(145deg, #222, #2a2a2a),
    radial-gradient(
      circle at 20% 20%,
      rgba(33, 150, 243, 0.05) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 80%,
      rgba(33, 150, 243, 0.05) 0%,
      transparent 50%
    );
  background-size: 100% 100%, 50% 50%, 50% 50%;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3),
    0 0 30px rgba(33, 150, 243, 0.1) inset;
  border: 1px solid #333;
  color: rgba(255, 255, 255, 0.87);
  animation: fadeIn 0.8s ease-out forwards;
  position: relative;
  overflow: hidden;
}

.no-schedules-message::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #2196f3, #21f3e7, #2196f3);
  background-size: 200% 100%;
  animation: gradientMove 3s linear infinite;
}

@keyframes gradientMove {
  0% {
    background-position: 0% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.no-schedules-message h2 {
  color: #2196f3;
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 24px;
  position: relative;
  display: inline-block;
}

.no-schedules-message h2::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background-color: #2196f3;
}

.no-schedules-message p {
  font-size: 16px;
  margin-bottom: 20px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

@keyframes shine {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.no-schedules-message a {
  display: inline-block;
  padding: 12px 28px;
  background: linear-gradient(45deg, #2196f3, #21cbf3, #2196f3);
  background-size: 200% 200%;
  color: white;
  border-radius: 30px;
  text-decoration: none;
  font-weight: bold;
  transition: all 0.3s ease;
  position: relative;
  margin-top: 15px;
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.4);
  overflow: hidden;
  z-index: 1;
  border: 2px solid transparent;
  letter-spacing: 0.5px;
}

.no-schedules-message a::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #2196f3, #21cbf3, #2196f3);
  background-size: 400% 400%;
  z-index: -1;
  animation: shine 3s ease infinite;
  border-radius: 30px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.no-schedules-message a::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.no-schedules-message a:hover {
  transform: translateY(-3px) scale(1.03);
  box-shadow: 0 7px 20px rgba(33, 150, 243, 0.6);
  color: white;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
  border-color: rgba(255, 255, 255, 0.3);
  background-position: right center;
}

.no-schedules-message a:hover::before {
  opacity: 1;
}

.no-schedules-message a:hover::after {
  transform: translateX(100%);
}

.no-schedules-icon {
  font-size: 60px;
  color: #2196f3;
  display: inline-block;
  animation: float 3s ease-in-out infinite, glow 2s ease-in-out infinite;
  background: rgba(33, 150, 243, 0.1);
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 25px;
  box-shadow: 0 0 20px rgba(33, 150, 243, 0.2);
  border: 2px solid rgba(33, 150, 243, 0.3);
  position: relative;
  z-index: 1;
}

.no-schedules-icon::before {
  content: "";
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 50%;
  border: 2px solid transparent;
  border-top-color: #2196f3;
  border-bottom-color: #2196f3;
  animation: rotate 3s linear infinite;
  z-index: -1;
}

.no-schedules-icon::after {
  content: "";
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border-radius: 50%;
  border: 2px solid transparent;
  border-left-color: #2196f3;
  border-right-color: #2196f3;
  animation: rotate 3s linear infinite reverse;
  z-index: -1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .no-schedules-message {
    margin: 30px 15px;
    padding: 30px 20px;
  }

  .no-schedules-message h2 {
    font-size: 20px;
  }

  .no-schedules-message p {
    font-size: 14px;
  }

  .no-schedules-message a {
    padding: 12px 24px;
    font-size: 15px;
    width: auto;
    min-width: 200px;
  }

  .no-schedules-icon {
    font-size: 40px;
    width: 80px;
    height: 80px;
  }

  .no-schedules-icon::before,
  .no-schedules-icon::after {
    animation-duration: 4s;
  }
}

@media (max-width: 480px) {
  .no-schedules-message {
    padding: 25px 15px;
    margin: 20px 10px;
  }

  .no-schedules-message a {
    padding: 12px 20px;
    font-size: 14px;
    width: 80%;
    max-width: 250px;
    box-sizing: border-box;
    border-radius: 25px;
  }

  .no-schedules-icon {
    font-size: 32px;
    width: 70px;
    height: 70px;
  }

  .no-schedules-message h2 {
    font-size: 18px;
  }

  .no-schedules-message h2::after {
    width: 40px;
  }
}
