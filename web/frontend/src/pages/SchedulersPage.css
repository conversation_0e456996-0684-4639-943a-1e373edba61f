.schedulers-page {
    max-width: 1600px;
    margin: 0 auto;
    padding: 1.5rem;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #333;
    position: relative;
}

.header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 10%;
    width: 80%;
    height: 1px;
    background: linear-gradient(90deg,
        rgba(33, 150, 243, 0) 0%,
        rgba(33, 150, 243, 0.3) 50%,
        rgba(33, 150, 243, 0) 100%
    );
}

.header h1 {
    font-size: 2rem;
    font-weight: 600;
    color: #fff;
    margin: 0;
    position: relative;
    display: inline-block;
}

.header h1::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(90deg, #2196f3, #ffa500);
    border-radius: 3px;
}

.create-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #2196f3, #1976d2);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 0.75rem 1.25rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.create-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.2);
    background: linear-gradient(135deg, #1976d2, #2196f3);
}

.button-icon {
    font-size: 1.2rem;
}

.schedulers-content {
    min-height: calc(100vh - 250px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    overflow-x: auto;
    background-color: transparent;
    border-radius: 12px;
    padding-bottom: 2rem;
}

.pagination-ellipsis {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #aaa;
    font-size: 1.2rem;
    padding: 0 0.5rem;
}