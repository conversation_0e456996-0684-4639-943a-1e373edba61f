/* HomePage.css - Redesigned for better Program Guide focus */

.home-page {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #121212;
  padding: 0;
  overflow-x: hidden;
}

/* Header Section */
.home-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: linear-gradient(to right, rgba(18, 18, 18, 0.95), rgba(30, 30, 30, 0.95));
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  position: sticky;
  top: 0;
  z-index: 100;
  border-bottom: 1px solid rgba(33, 150, 243, 0.2);
}

/* Logo */
.logo-container {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

.logo-loaded {
  opacity: 1;
  transform: translateY(0);
}

.logo-image {
  max-width: 180px;
  height: auto;
  filter: drop-shadow(0 2px 8px rgba(33, 150, 243, 0.3));
  transition: transform 0.3s ease;
}

.logo-image:hover {
  transform: scale(1.05);
}

/* Navigation */
.nav-container {
  display: flex;
  gap: 1.5rem;
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 0.8s ease-out, transform 0.8s ease-out;
  transition-delay: 0.2s;
}

.nav-loaded {
  opacity: 1;
  transform: translateY(0);
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  color: #ffffff;
  padding: 0.6rem 1rem;
  border-radius: 30px;
  background: rgba(33, 150, 243, 0.1);
  border: 1px solid rgba(33, 150, 243, 0.2);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
  overflow: hidden;
}

.nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.nav-item:hover {
  background: rgba(33, 150, 243, 0.2);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2), 0 0 5px rgba(33, 150, 243, 0.3);
}

.nav-item:hover::before {
  transform: translateX(100%);
}

.nav-icon {
  width: 20px;
  height: 20px;
  color: #2196f3;
}

.nav-text {
  font-size: 0.9rem;
  font-weight: 500;
}

/* Tooltip for nav items */
.nav-item[data-tooltip]::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: -40px;
  left: 50%;
  transform: translateX(-50%) scale(0.8);
  background: rgba(33, 150, 243, 0.9);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 0.8rem;
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s ease;
  white-space: nowrap;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  z-index: 10;
}

.nav-item[data-tooltip]:hover::after {
  opacity: 1;
  transform: translateX(-50%) scale(1);
  bottom: -35px;
}

/* Main Content */
.home-main {
  flex: 1;
  padding: 1rem 2rem 3rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Guide Container */
.home-guide-container {
  width: 100%;
  max-width: 1600px; /* Wider container for the guide */
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 1s ease-out, transform 1s ease-out;
  transition-delay: 0.5s;
}

.home-guide-loaded {
  opacity: 1;
  transform: translateY(0);
}

.home-guide-header {
  text-align: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  animation: fadeIn 1s ease-out forwards;
}

.home-guide-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  background: linear-gradient(90deg, #2196f3, #6c5ce7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
  position: relative;
}

.home-guide-title::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 3px;
  background: linear-gradient(90deg, #2196f3, #6c5ce7);
  border-radius: 3px;
}

.home-guide-description {
  color: var(--color-text-secondary);
  font-size: 1.1rem;
  max-width: 800px;
  margin: 1rem auto 0;
  line-height: 1.6;
}

/* Media Queries */
@media (max-width: 900px) {
  .home-header {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .nav-container {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
  }

  .home-main {
    padding: 1rem;
  }

  .home-guide-title {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .logo-image {
    max-width: 150px;
  }

  .nav-item {
    padding: 0.5rem 0.8rem;
  }

  .nav-text {
    font-size: 0.8rem;
  }

  .home-guide-container {
    margin-top: 1rem;
  }

  .home-guide-title {
    font-size: 1.8rem;
  }

  .home-guide-description {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .home-header {
    padding: 0.8rem;
  }

  .logo-image {
    max-width: 120px;
  }

  .nav-container {
    gap: 0.5rem;
  }

  .nav-item {
    padding: 0.4rem 0.6rem;
  }

  .nav-icon {
    width: 16px;
    height: 16px;
  }

  .nav-text {
    font-size: 0.7rem;
  }

  .home-guide-title {
    font-size: 1.5rem;
  }

  .home-guide-description {
    font-size: 0.9rem;
  }
}
