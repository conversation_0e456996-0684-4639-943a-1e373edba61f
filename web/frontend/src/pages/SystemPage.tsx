import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>r, HiRefresh, HiChip, HiDatabase, HiStatusOnline, HiClock, HiCube } from 'react-icons/hi';
import SystemMonitor from '../components/system/SystemMonitor';
import { getSystemStats } from '../api/systemApi';
import { SystemStats } from '../types/system';
import webSocketService from '../api/websocket';
import './SystemPage.css';

const SystemPage: React.FC = () => {
  const [refreshing, setRefreshing] = useState(false);
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Fetch initial stats
  useEffect(() => {
    fetchStats();

    // Subscribe to WebSocket updates
    const unsubscribe = webSocketService.subscribeToSystemStats((newStats) => {
      setStats(newStats);
      setLastUpdated(new Date());
    });

    return () => {
      unsubscribe();
    };
  }, []);

  const fetchStats = async () => {
    setRefreshing(true);
    try {
      // Manually fetch new system stats
      const newStats = await getSystemStats();
      setStats(newStats);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to refresh system stats:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    fetchStats();
  };

  // Format the last updated time
  const formatLastUpdated = () => {
    if (!lastUpdated) return 'Never';

    return lastUpdated.toLocaleTimeString();
  };

  // Check if any alarms are active
  const hasAlarms = stats?.disk_alarm || stats?.cpu_alarm || stats?.memory_alarm || stats?.gpu_alarm;

  return (
    <div className="system-page">
      <div className="system-page-header">
        <div className="system-page-title">
          <HiServer className="system-page-icon" />
          <h1>System Monitoring</h1>
        </div>
        <div className="system-page-actions">
          <div className="system-page-last-updated">
            <HiClock />
            <span>Last updated: {formatLastUpdated()}</span>
          </div>
          <button
            className={`system-page-refresh ${refreshing ? 'refreshing' : ''}`}
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <HiRefresh />
            <span>{refreshing ? 'Refreshing...' : 'Refresh'}</span>
          </button>
        </div>
      </div>

      {stats && (
        <div className={`system-page-summary ${stats.gpu_available ? 'with-gpu' : ''}`}>
          {/* GPU card - only displayed when GPU is available */}
          {stats.gpu_available && (
            <div className={`system-summary-card ${stats.gpu_alarm ? 'alarm' : ''}`}>
              <HiCube className="summary-icon" />
              <div className="summary-content">
                <h3>GPU Usage</h3>
                <div className="summary-value">{stats.gpu_usage.toFixed(1)}%</div>
                <div className="summary-subtitle">{stats.gpu_model}</div>
              </div>
            </div>
          )}

          <div className={`system-summary-card ${stats.cpu_alarm ? 'alarm' : ''}`}>
            <HiChip className="summary-icon" />
            <div className="summary-content">
              <h3>CPU Usage</h3>
              <div className="summary-value">{stats.cpu_usage.toFixed(1)}%</div>
            </div>
          </div>

          <div className={`system-summary-card ${stats.memory_alarm ? 'alarm' : ''}`}>
            <HiServer className="summary-icon" />
            <div className="summary-content">
              <h3>Memory Usage</h3>
              <div className="summary-value">{stats.memory_usage.toFixed(1)}%</div>
            </div>
          </div>

          <div className={`system-summary-card ${stats.disk_alarm ? 'alarm' : ''}`}>
            <HiDatabase className="summary-icon" />
            <div className="summary-content">
              <h3>Disk Usage</h3>
              <div className="summary-value">{stats.disk_usage.toFixed(1)}%</div>
            </div>
          </div>

          <div className="system-summary-card status">
            <HiStatusOnline className="summary-icon" />
            <div className="summary-content">
              <h3>System Status</h3>
              <div className={`summary-status ${hasAlarms ? 'warning' : 'healthy'}`}>
                {hasAlarms ? 'Warning' : 'Healthy'}
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="system-page-content">
        <SystemMonitor />
      </div>
    </div>
  );
};

export default SystemPage;
