import { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  HiPlay,
  HiCalendar,
  <PERSON>Search,
} from "react-icons/hi";
import { HiSignal } from "react-icons/hi2";
import moment from "moment";
import {
  getScheduleAnalytics,
  getAnalyticsForDateRange,
  getAnalyticsForDateRangePaginated,
  getAnalyticsStatsForDateRange,
  getScheduleSummary,
} from "@/api/analyticsApi";
import { getSchedules } from "@/api/schedulerApi";
import {
  ContentAnalytics,
  ScheduleAnalyticsSummary,
  ContentAnalyticsListResult,
} from "@/types/analytics";
import { Schedule } from "@/types/schedule";
import Loading from "@/components/common/Loading";
import Pagination from "@/components/scheduler/Pagination";
import "./AnalyticsPage.css";

const AnalyticsPage = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<
    "overview" | "content" | "schedules" | "search"
  >("overview");
  const [recentPlays, setRecentPlays] = useState<ContentAnalytics[]>([]);
  const [totalAnalyticsStats, setTotalAnalyticsStats] = useState<{
    totalPlays: number;
    totalDuration: number;
    totalOutputs: number;
  }>({
    totalPlays: 0,
    totalDuration: 0,
    totalOutputs: 0,
  });
  const [scheduleSummaries, setScheduleSummaries] = useState<
    ScheduleAnalyticsSummary[]
  >([]);
  const [scheduleList, setScheduleList] = useState<Schedule[]>([]);
  const [selectedScheduleId, setSelectedScheduleId] = useState<number | null>(
    null
  );
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [searchResults, setSearchResults] = useState<ContentAnalytics[]>([]);
  const [dateRange, setDateRange] = useState<{ start: string; end: string }>({
    start: moment().subtract(7, "days").startOf("day").format(),
    end: moment().endOf("day").format(),
  });

  // Pagination state
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    totalPages: 1,
  });

  const [searchPagination, setSearchPagination] = useState({
    page: 1,
    limit: 20,
    totalPages: 1,
  });

  // Fetch all schedules for reference
  useEffect(() => {
    const fetchAllSchedules = async () => {
      try {
        const response = await getSchedules();
        setScheduleList(response.items);
      } catch (error) {
        console.error("Failed to fetch schedules:", error);
      }
    };

    fetchAllSchedules();
  }, []);

  // Get schedule name by ID
  const getScheduleName = (scheduleId: number) => {
    if (!scheduleList || scheduleList.length === 0) {
      return `Schedule ${scheduleId}`;
    }
    const schedule = scheduleList.find((s) => s.id === scheduleId);
    return schedule ? schedule.name : `Schedule ${scheduleId}`;
  };

  // Fetch recent content plays across all schedules with pagination
  useEffect(() => {
    const fetchRecentPlays = async () => {
      // If we already know there are no schedules, don't make unnecessary API calls
      if (!scheduleList || scheduleList.length === 0) {
        setRecentPlays([]);
        setTotalAnalyticsStats({
          totalPlays: 0,
          totalDuration: 0,
          totalOutputs: 0,
        });
        return;
      }

      setIsLoading(true);
      try {
        // If a schedule is selected, get analytics for that schedule with pagination
        if (selectedScheduleId) {
          const data: ContentAnalyticsListResult = await getScheduleAnalytics(
            selectedScheduleId,
            pagination.page,
            pagination.limit
          );
          setRecentPlays(data.items || []);
          setPagination({
            page: data.page,
            limit: data.limit,
            totalPages: data.total_pages,
          });

          // Calculate total stats from all the data (not just current page)
          setTotalAnalyticsStats({
            totalPlays: data.total_items,
            totalDuration:
              data.items?.reduce((sum, play) => sum + play.duration, 0) || 0,
            totalOutputs: [
              ...new Set(data.items?.map((play) => play.rtp_output) || []),
            ].length,
          });
        } else {
          // For date range analytics, use the new paginated API for items
          const [pageData, statsData] = await Promise.all([
            getAnalyticsForDateRangePaginated(
              dateRange.start,
              dateRange.end,
              pagination.page,
              pagination.limit
            ),
            getAnalyticsStatsForDateRange(dateRange.start, dateRange.end),
          ]);

          setRecentPlays(pageData.items || []);
          setPagination({
            page: pageData.page,
            limit: pageData.limit,
            totalPages: pageData.total_pages,
          });

          // Use the aggregate stats for accurate totals
          setTotalAnalyticsStats({
            totalPlays: statsData.total_plays,
            totalDuration: statsData.total_duration,
            totalOutputs: statsData.unique_outputs,
          });
        }
      } catch (error) {
        console.error("Failed to fetch recent plays:", error);
        setRecentPlays([]);
        setTotalAnalyticsStats({
          totalPlays: 0,
          totalDuration: 0,
          totalOutputs: 0,
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchRecentPlays();
  }, [
    selectedScheduleId,
    dateRange,
    scheduleList,
    pagination.page,
    pagination.limit,
  ]);

  // Fetch schedule summaries
  useEffect(() => {
    const fetchScheduleSummaries = async () => {
      // If we already know there are no schedules, don't make unnecessary API calls
      if (!scheduleList || scheduleList.length === 0) {
        setScheduleSummaries([]);
        return;
      }

      setIsLoading(true);
      try {
        // First fetch all available schedules
        const schedulesResponse = await getSchedules();

        if (!schedulesResponse.items || schedulesResponse.items.length === 0) {
          setScheduleSummaries([]);
          setIsLoading(false);
          return;
        }

        const scheduleIds = schedulesResponse.items.map(
          (schedule) => schedule.id
        );
        const summaries: ScheduleAnalyticsSummary[] = [];

        // Fetch summaries in batches to reduce database load
        const batchSize = 3;
        for (let i = 0; i < scheduleIds.length; i += batchSize) {
          const batchIds = scheduleIds.slice(i, i + batchSize);
          const batchPromises = batchIds.map((id) =>
            getScheduleSummary(id).catch((error) => {
              console.error(
                `Failed to fetch summary for schedule ${id}:`,
                error
              );
              return null; // Return null for failed requests
            })
          );

          // Wait for all promises in the batch to resolve
          const batchResults = await Promise.all(batchPromises);

          // Add successful results to summaries
          batchResults.forEach((summary) => {
            if (summary) {
              summaries.push(summary);
            }
          });

          // Small delay between batches to reduce database contention
          if (i + batchSize < scheduleIds.length) {
            await new Promise((resolve) => setTimeout(resolve, 300));
          }
        }

        setScheduleSummaries(summaries);
      } catch (error) {
        console.error("Failed to fetch schedule summaries:", error);
        setScheduleSummaries([]);
      } finally {
        setIsLoading(false);
      }
    };

    // Always fetch schedule summaries when the component mounts or scheduleList changes
    // This ensures the dropdown is populated regardless of which tab is active
    fetchScheduleSummaries();
  }, [scheduleList]);

  // Search content analytics with pagination
  const handleSearch = async (page: number = 1) => {
    if (!searchTerm.trim()) return;

    setIsLoading(true);
    try {
      // In a real implementation, you would have a dedicated search endpoint
      // For now, we'll use the date range endpoint and filter client-side
      const data = await getAnalyticsForDateRange(
        moment().subtract(30, "days").format(),
        moment().format()
      );

      const allResults = data.items.filter(
        (item) =>
          item.content_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.content_path.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.rtp_output.toLowerCase().includes(searchTerm.toLowerCase())
      );

      // Implement pagination for search results
      const startIndex = (page - 1) * searchPagination.limit;
      const endIndex = startIndex + searchPagination.limit;
      const pageResults = allResults.slice(startIndex, endIndex);

      setSearchResults(pageResults);
      setSearchPagination({
        page: page,
        limit: searchPagination.limit,
        totalPages: Math.ceil(allResults.length / searchPagination.limit),
      });
    } catch (error) {
      console.error("Failed to search analytics:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDateRangeChange = (
    range: "today" | "week" | "month" | "year"
  ) => {
    let start, end;

    switch (range) {
      case "today":
        start = moment().startOf("day").format();
        end = moment().endOf("day").format();
        break;
      case "week":
        start = moment().subtract(7, "days").startOf("day").format();
        end = moment().endOf("day").format();
        break;
      case "month":
        start = moment().subtract(30, "days").startOf("day").format();
        end = moment().endOf("day").format();
        break;
      case "year":
        start = moment().subtract(365, "days").startOf("day").format();
        end = moment().endOf("day").format();
        break;
    }

    setDateRange({ start, end });
    // Reset pagination when date range changes
    setPagination({
      page: 1,
      limit: pagination.limit,
      totalPages: 1,
    });
  };

  const formatDuration = (seconds: number) => {
    const duration = moment.duration(seconds, "seconds");
    const hours = Math.floor(duration.asHours());
    const minutes = duration.minutes();

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m ${duration.seconds()}s`;
  };

  const formatDate = (dateString: string) => {
    return moment(dateString).format("MMM D, YYYY h:mm A");
  };

  // Group content plays by item for content-centric view
  const getContentGroups = () => {
    if (!recentPlays || recentPlays.length === 0) {
      return [];
    }

    const groups: { [key: string]: ContentAnalytics[] } = {};

    recentPlays.forEach((play) => {
      if (!groups[play.content_path]) {
        groups[play.content_path] = [];
      }
      groups[play.content_path].push(play);
    });

    // If there are no groups, return empty array
    if (Object.keys(groups).length === 0) {
      return [];
    }

    return Object.entries(groups).map(([path, plays]) => {
      // Ensure plays array is not empty
      if (plays.length === 0) {
        return {
          path,
          name: "Unknown",
          plays: [],
          count: 0,
          totalDuration: 0,
          lastPlayed: "",
          outputs: [],
          schedulerId: {},
        };
      }

      return {
        path,
        name: plays[0].content_name,
        plays,
        count: plays.length,
        totalDuration: plays.reduce((sum, play) => sum + play.duration, 0),
        lastPlayed: plays.reduce((latest, play) => {
          return moment(play.played_at).isAfter(moment(latest))
            ? play.played_at
            : latest;
        }, plays[0].played_at),
        outputs: [...new Set(plays.map((play) => play.rtp_output))],
        // Get the most common scheduler for this content
        schedulerId:
          plays.length > 0
            ? plays.reduce((acc, curr) => {
                acc[curr.schedule_id] = (acc[curr.schedule_id] || 0) + 1;
                return acc;
              }, {} as Record<number, number>)
            : {},
      };
    });
  };

  // Check if there are any schedules in the system
  const hasSchedules = scheduleList && scheduleList.length > 0;

  // If there are no schedules, show a message instead of the analytics page
  if (!hasSchedules) {
    return (
      <div className="analytics-page">
        <h1>
          <HiChartBar className="page-icon" /> Content Analytics
        </h1>
        <div className="no-schedules-message">
          <div className="no-schedules-icon">
            <HiCalendar />
          </div>
          <h2>No Schedules Available</h2>
          <p>
            There are currently no schedules configured in the system. Analytics
            data is based on schedule playback.
          </p>
          <p>
            Please create at least one schedule in the Scheduler page to start
            collecting analytics data.
          </p>
          <a href="/scheduler">
            Go to Scheduler Page{" "}
            <HiCalendar
              style={{ marginLeft: "8px", verticalAlign: "middle" }}
            />
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="analytics-page">
      <h1>
        <HiChartBar className="page-icon" /> Content Analytics
      </h1>

      <div className="analytics-controls">
        <div className="analytics-tabs">
          <button
            className={`analytics-tab ${
              activeTab === "overview" ? "active" : ""
            }`}
            onClick={() => setActiveTab("overview")}
          >
            <HiChartBar /> Overview
          </button>
          <button
            className={`analytics-tab ${
              activeTab === "content" ? "active" : ""
            }`}
            onClick={() => setActiveTab("content")}
          >
            <HiPlay /> Content
          </button>
          <button
            className={`analytics-tab ${
              activeTab === "schedules" ? "active" : ""
            }`}
            onClick={() => setActiveTab("schedules")}
          >
            <HiCalendar /> Schedules
          </button>
          <button
            className={`analytics-tab ${
              activeTab === "search" ? "active" : ""
            }`}
            onClick={() => setActiveTab("search")}
          >
            <HiSearch /> Search
          </button>
        </div>

        <div className="filter-controls">
          <div className="date-range-selector">
            <span>Time period:</span>
            <div className="date-range-buttons">
              <button
                className={
                  dateRange.start === moment().startOf("day").format()
                    ? "active"
                    : ""
                }
                onClick={() => handleDateRangeChange("today")}
              >
                Today
              </button>
              <button
                className={
                  dateRange.start ===
                  moment().subtract(7, "days").startOf("day").format()
                    ? "active"
                    : ""
                }
                onClick={() => handleDateRangeChange("week")}
              >
                Last 7 days
              </button>
              <button
                className={
                  dateRange.start ===
                  moment().subtract(30, "days").startOf("day").format()
                    ? "active"
                    : ""
                }
                onClick={() => handleDateRangeChange("month")}
              >
                Last 30 days
              </button>
              <button
                className={
                  dateRange.start ===
                  moment().subtract(365, "days").startOf("day").format()
                    ? "active"
                    : ""
                }
                onClick={() => handleDateRangeChange("year")}
              >
                Last year
              </button>
            </div>
          </div>

          <div className="schedule-selector">
            <span>Schedule:</span>
            <div className="select-wrapper">
              <select
                value={selectedScheduleId || ""}
                onChange={(e) => {
                  setSelectedScheduleId(
                    e.target.value ? Number(e.target.value) : null
                  );
                  // Reset pagination when schedule changes
                  setPagination({
                    page: 1,
                    limit: pagination.limit,
                    totalPages: 1,
                  });
                }}
                disabled={isLoading && scheduleSummaries.length === 0}
                className={
                  isLoading && scheduleSummaries.length === 0 ? "loading" : ""
                }
              >
                <option value="">All schedules</option>
                {scheduleSummaries.map((summary) => (
                  <option key={summary.schedule_id} value={summary.schedule_id}>
                    {summary.schedule_name}
                  </option>
                ))}
              </select>
              {isLoading && scheduleSummaries.length === 0 && (
                <div className="select-loading-indicator"></div>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="analytics-content">
        {isLoading ? (
          <Loading />
        ) : (
          <>
            {activeTab === "overview" && (
              <div className="analytics-overview">
                <div className="analytics-stats">
                  <div
                    className="analytics-card"
                    style={{ "--card-index": 0 } as React.CSSProperties}
                  >
                    <div className="analytics-card-header">
                      <HiPlay className="card-icon" />
                      <h3>Content Played</h3>
                    </div>
                    <div className="analytics-card-value">
                      {totalAnalyticsStats.totalPlays.toLocaleString()}
                    </div>
                    <div className="analytics-card-subtitle">
                      Total items played in selected period
                    </div>
                  </div>

                  <div
                    className="analytics-card"
                    style={{ "--card-index": 1 } as React.CSSProperties}
                  >
                    <div className="analytics-card-header">
                      <HiClock className="card-icon" />
                      <h3>Total Playback Time</h3>
                    </div>
                    <div className="analytics-card-value">
                      {formatDuration(totalAnalyticsStats.totalDuration)}
                    </div>
                    <div className="analytics-card-subtitle">
                      Cumulative playback time
                    </div>
                  </div>

                  <div
                    className="analytics-card"
                    style={{ "--card-index": 2 } as React.CSSProperties}
                  >
                    <div className="analytics-card-header">
                      <HiSignal className="card-icon" />
                      <h3>Active RTP Outputs</h3>
                    </div>
                    <div className="analytics-card-value">
                      {totalAnalyticsStats.totalOutputs}
                    </div>
                    <div className="analytics-card-subtitle">
                      Distinct output channels
                    </div>
                  </div>
                </div>

                <h2>Recent Content Plays</h2>
                <div className="recent-plays-table">
                  <table>
                    <thead>
                      <tr>
                        <th>Content</th>
                        <th>Type</th>
                        <th>RTP Output</th>
                        <th>Scheduler</th>
                        <th>Duration</th>
                        <th>Played At</th>
                      </tr>
                    </thead>
                    <tbody>
                      {recentPlays && recentPlays.length > 0 ? (
                        recentPlays.map(
                          (play: ContentAnalytics, index: number) => (
                            <tr key={index}>
                              <td className="content-name-cell">
                                {play.content_name}
                              </td>
                              <td>
                                <span
                                  className={`play-type play-type-${play.play_type}`}
                                >
                                  {play.play_type}
                                </span>
                              </td>
                              <td>{play.rtp_output}</td>
                              <td>{getScheduleName(play.schedule_id)}</td>
                              <td>{formatDuration(play.duration)}</td>
                              <td>{formatDate(play.played_at)}</td>
                            </tr>
                          )
                        )
                      ) : (
                        <tr>
                          <td colSpan={6} className="no-data-message">
                            No content plays recorded in selected period
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>

                {/* Pagination for overview table */}
                {pagination.totalPages > 1 && (
                  <div className="pagination-container">
                    <Pagination
                      pagination={pagination}
                      setPagination={setPagination}
                    />
                  </div>
                )}
              </div>
            )}

            {activeTab === "content" && (
              <div className="content-analytics">
                <h2>Content Performance</h2>
                <div className="content-groups">
                  {getContentGroups().length > 0 ? (
                    <table className="content-table">
                      <thead>
                        <tr>
                          <th>Content</th>
                          <th>Play Count</th>
                          <th>Total Duration</th>
                          <th>Scheduler</th>
                          <th>Outputs</th>
                          <th>Last Played</th>
                        </tr>
                      </thead>
                      <tbody>
                        {getContentGroups()
                          .sort((a, b) => b.count - a.count)
                          .map((group, index) => {
                            // Find most frequent scheduler
                            const scheduleEntries = Object.entries(
                              group.schedulerId
                            );
                            const mostUsedSchedulerId =
                              scheduleEntries.length > 0
                                ? Number(
                                    scheduleEntries.sort(
                                      (a, b) => b[1] - a[1]
                                    )[0][0]
                                  )
                                : 0;

                            return (
                              <tr key={index} className="content-group-row">
                                <td className="content-name-cell">
                                  {group.name}
                                </td>
                                <td>{group.count}</td>
                                <td>{formatDuration(group.totalDuration)}</td>
                                <td>{getScheduleName(mostUsedSchedulerId)}</td>
                                <td>{group.outputs.length} outputs</td>
                                <td>{formatDate(group.lastPlayed)}</td>
                              </tr>
                            );
                          })}
                      </tbody>
                    </table>
                  ) : (
                    <div className="no-data-message">
                      No content plays recorded in selected period
                    </div>
                  )}
                </div>

                {/* Note: Content tab shows grouped data, pagination might not be as relevant here */}
                {/* but we could add it if needed in the future */}
              </div>
            )}

            {activeTab === "schedules" && (
              <div className="schedule-analytics">
                <h2>Schedule Performance</h2>
                <div className="schedule-list">
                  {scheduleSummaries.length > 0 ? (
                    scheduleSummaries.map((summary, index) => (
                      <div key={index} className="schedule-summary-card">
                        <h3>{summary.schedule_name}</h3>
                        <div className="schedule-stats">
                          <div className="schedule-stat">
                            <HiPlay className="stat-icon" />
                            <span>
                              {summary.total_content_played.toLocaleString()}{" "}
                              content items played
                            </span>
                          </div>
                          <div className="schedule-stat">
                            <HiClock className="stat-icon" />
                            <span>
                              {formatDuration(summary.total_play_time)} total
                              duration
                            </span>
                          </div>
                          <div className="schedule-stat">
                            <HiSignal className="stat-icon" />
                            <span>
                              {summary.rtp_outputs.length} active outputs
                            </span>
                          </div>
                        </div>
                        {summary.top_content.length > 0 && (
                          <div className="top-content-preview">
                            <h4>Top Content</h4>
                            <ul>
                              {summary.top_content
                                .slice(0, 3)
                                .map((content, idx) => (
                                  <li key={idx}>
                                    <span className="content-name">
                                      {content.content_name}
                                    </span>
                                    <span className="content-plays">
                                      {content.total_plays.toLocaleString()}{" "}
                                      plays
                                    </span>
                                  </li>
                                ))}
                            </ul>
                          </div>
                        )}
                        <button
                          className="view-schedule-btn"
                          onClick={() => {
                            setSelectedScheduleId(summary.schedule_id);
                            // Reset pagination when switching to a specific schedule
                            setPagination({
                              page: 1,
                              limit: pagination.limit,
                              totalPages: 1,
                            });
                            setActiveTab("overview");
                          }}
                        >
                          View Details
                        </button>
                      </div>
                    ))
                  ) : (
                    <div className="no-data-message">
                      No schedule data available
                    </div>
                  )}
                </div>
              </div>
            )}

            {activeTab === "search" && (
              <div className="search-analytics">
                <div className="search-controls">
                  <input
                    type="text"
                    placeholder="Search content by name, path or output..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        setSearchPagination({
                          page: 1,
                          limit: searchPagination.limit,
                          totalPages: 1,
                        });
                        handleSearch(1);
                      }
                    }}
                  />
                  <button
                    className="search-button"
                    onClick={() => {
                      setSearchPagination({
                        page: 1,
                        limit: searchPagination.limit,
                        totalPages: 1,
                      });
                      handleSearch(1);
                    }}
                  >
                    <HiSearch /> Search
                  </button>
                </div>

                {searchResults.length > 0 ? (
                  <div className="search-results">
                    <h3>Search Results</h3>
                    <table>
                      <thead>
                        <tr>
                          <th>Content</th>
                          <th>Type</th>
                          <th>RTP Output</th>
                          <th>Scheduler</th>
                          <th>Duration</th>
                          <th>Played At</th>
                        </tr>
                      </thead>
                      <tbody>
                        {searchResults.map((play, index) => (
                          <tr key={index}>
                            <td className="content-name-cell">
                              {play.content_name}
                            </td>
                            <td>
                              <span
                                className={`play-type play-type-${play.play_type}`}
                              >
                                {play.play_type}
                              </span>
                            </td>
                            <td>{play.rtp_output}</td>
                            <td>{getScheduleName(play.schedule_id)}</td>
                            <td>{formatDuration(play.duration)}</td>
                            <td>{formatDate(play.played_at)}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>

                    {/* Pagination for search results */}
                    {searchPagination.totalPages > 1 && (
                      <div className="pagination-container">
                        <Pagination
                          pagination={searchPagination}
                          setPagination={(newPagination) => {
                            setSearchPagination(newPagination);
                            handleSearch(newPagination.page);
                          }}
                        />
                      </div>
                    )}
                  </div>
                ) : searchTerm && !isLoading ? (
                  <div className="no-results">
                    No results found for "{searchTerm}"
                  </div>
                ) : null}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default AnalyticsPage;
