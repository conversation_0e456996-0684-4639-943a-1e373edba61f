/* ===== ENHANCED LOGS PAGE - MODERN UI/UX DESIGN ===== */

.logs-page {
  max-width: 1280px;
  margin: 0 auto;
  /* padding: 1.5rem; */
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  background-color: #121212;
  color: #ffffff;
  overflow: hidden;
  box-sizing: border-box;
  font-family: 'Inter', 'Roboto', system-ui, sans-serif;
  position: relative;
}

/* Enhanced fade-in animation */
.logs-page-fade-in {
  animation: enhancedFadeIn 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
}

@keyframes enhancedFadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.98);
    filter: blur(4px);
  }
  50% {
    opacity: 0.7;
    transform: translateY(10px) scale(0.99);
    filter: blur(2px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

/* ===== CONSISTENT HEADER DESIGN ===== */

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.25rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #333;
  position: relative;
}

.header::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 10%;
  width: 80%;
  height: 1px;
  background: linear-gradient(
    90deg,
    rgba(33, 150, 243, 0) 0%,
    rgba(33, 150, 243, 0.3) 50%,
    rgba(33, 150, 243, 0) 100%
  );
}

.header h1 {
  margin: 0;
  font-size: 2rem;
  font-weight: 600;
  color: #fff;
  position: relative;
  display: inline-block;
}

.header h1::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, #2196f3, #ffa500);
  border-radius: 3px;
}

.header-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.15) 0%, rgba(33, 150, 243, 0.05) 100%);
  border: 1px solid rgba(33, 150, 243, 0.2);
  border-radius: 20px;
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
  font-weight: 500;
  color: #64b5f6;
}

.badge-icon {
  font-size: 1rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

/* Quick Stats */
.quick-stats {
  display: flex;
  gap: 1.5rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  min-width: 80px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: rgba(255, 255, 255, 0.06);
  border-color: rgba(33, 150, 243, 0.3);
  transform: translateY(-2px);
}

.stat-icon {
  font-size: 1.25rem;
  margin-bottom: 0.25rem;
}

.stat-icon.info { color: #64b5f6; }
.stat-icon.warning { color: #ffb74d; }
.stat-icon.success { color: #81c784; }

.stat-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #ffffff;
}

.stat-label {
  font-size: 0.75rem;
  color: #b0b0b0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* ===== ENHANCED ACTION BAR ===== */

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
}

.action-group {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.action-group.primary {
  flex-grow: 1;
  flex-shrink: 1;
  flex-basis: 0%;
}

.action-group.secondary {
  flex-grow: 0;
  flex-shrink: 0;
  flex-basis: auto;
  justify-content: center;
  min-height: 48px;
  align-items: center;
}

.action-group.export {
  flex-grow: 2;
  flex-shrink: 1;
  flex-basis: 0%;
  justify-content: flex-end;
}

.action-group.search {
  flex-grow: 3;
  flex-shrink: 1;
  flex-basis: 0%;
  justify-content: flex-start;
  gap: 0.75rem;
}

/* Action Search Input */
.action-search-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  max-width: 300px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 0.5rem 0.75rem;
  transition: all 0.3s ease;
}

.action-search-wrapper:focus-within {
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
  background: rgba(255, 255, 255, 0.08);
}

.search-icon {
  color: #888;
  font-size: 1rem;
  margin-right: 0.5rem;
  flex-shrink: 0;
}

.action-search-input {
  background: transparent;
  border: none;
  outline: none;
  color: #e0e0e0;
  font-size: 0.875rem;
  width: 100%;
  padding: 0;
}

.action-search-input::placeholder {
  color: #888;
}

.clear-search-action-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  width: 32px;
  height: 32px;
  min-width: 32px;
  min-height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #b0b0b0;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-left: 0.75rem;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
  font-size: 1rem;
  padding: 0;
}

.clear-search-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(244, 67, 54, 0.2) 0%, rgba(244, 67, 54, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
  border-radius: 8px;
}

.clear-search-action-btn svg {
  position: relative;
  z-index: 2;
  width: 16px;
  height: 16px;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.clear-search-action-btn:hover {
  background: rgba(244, 67, 54, 0.15);
  border-color: rgba(244, 67, 54, 0.3);
  color: #f44336;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(244, 67, 54, 0.2);
}

.clear-search-action-btn:hover::before {
  opacity: 1;
}

.clear-search-action-btn:hover svg {
  transform: rotate(90deg);
}

.clear-search-action-btn:active {
  transform: scale(0.95);
}

/* Enhanced Action Buttons */
.action-btn {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.5rem 0.75rem;
  border: none;
  border-radius: 8px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  text-decoration: none;
  background: rgba(255, 255, 255, 0.05);
  color: #e0e0e0;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.action-btn:hover::before {
  left: 100%;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  border-color: rgba(33, 150, 243, 0.5);
}

.action-btn:active {
  transform: translateY(0);
}

.action-btn.primary {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  color: white;
  border-color: #2196f3;
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.action-btn.primary:hover {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  box-shadow: 0 8px 25px rgba(33, 150, 243, 0.4);
}

.action-btn.primary.active {
  background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
  border-color: #4caf50;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.action-btn.filter.active {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
  border-color: #ff9800;
  color: white;
}

.action-btn.auto-refresh.active {
  background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
  border-color: #9c27b0;
  color: white;
}

.action-btn.export {
  background: rgba(96, 125, 139, 0.1);
  border-color: rgba(96, 125, 139, 0.3);
  color: #90a4ae;
}

.action-btn.export:hover {
  background: rgba(96, 125, 139, 0.2);
  border-color: #607d8b;
  color: #b0bec5;
}

.action-btn.hide-filters {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  color: #b0b0b0;
}

.action-btn.hide-filters:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

.action-btn.apply-filters {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  color: white;
  border-color: #2196f3;
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.action-btn.apply-filters:hover {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  box-shadow: 0 8px 25px rgba(33, 150, 243, 0.4);
}

.action-btn.clear-filters {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  color: #b0b0b0;
}

.action-btn.clear-filters:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.action-btn:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Active indicator for filter button */
.active-indicator {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background: #4caf50;
  border-radius: 50%;
  border: 2px solid #1a1a1a;
  animation: pulse 2s infinite;
}

/* Filters count badge */
.filters-count-badge {
  background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%);
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 600;
  min-width: 18px;
  text-align: center;
  line-height: 1;
  box-shadow: 0 2px 4px rgba(233, 30, 99, 0.3);
  border: 1px solid rgba(233, 30, 99, 0.4);
}

/* Auto-refresh group */
.auto-refresh-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.interval-select {
  padding: 0.5rem 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
  color: #e0e0e0;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-grow: 1;
  flex-shrink: 1;
  flex-basis: 0%;
}

.interval-select:hover {
  border-color: rgba(33, 150, 243, 0.5);
  background: rgba(255, 255, 255, 0.08);
}

.interval-select:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

/* Header Controls Horizontal Layout */
.header-controls-horizontal {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.03);
  padding: 0.375rem 0.5rem;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.08);
}

.auto-refresh-horizontal-group {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  background: rgba(33, 150, 243, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  border: 1px solid rgba(33, 150, 243, 0.2);
}

.auto-refresh-horizontal-group .header-control-btn.auto-refresh.active {
  background: rgba(33, 150, 243, 0.2);
  color: #64b5f6;
  border-color: rgba(33, 150, 243, 0.3);
}

.header-control-btn {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  color: #e0e0e0;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.header-control-btn:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(33, 150, 243, 0.5);
  transform: translateY(-1px);
}

.header-control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.header-control-btn.refresh {
  background: rgba(76, 175, 80, 0.1);
  border-color: rgba(76, 175, 80, 0.2);
  color: #81c784;
}

.header-control-btn.refresh:hover:not(:disabled) {
  background: rgba(76, 175, 80, 0.2);
  border-color: rgba(76, 175, 80, 0.4);
}

.header-interval-select {
  padding: 0.25rem 0.375rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.05);
  color: #e0e0e0;
  font-size: 0.7rem;
  min-width: 45px;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1 1 0;
}

.header-interval-select:hover:not(:disabled) {
  border-color: rgba(33, 150, 243, 0.5);
  background: rgba(255, 255, 255, 0.08);
}

.header-interval-select:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

.header-interval-select:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Spinning animation for refresh button */
.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Content Area */
.content-area {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  flex-grow: 1;
  flex-shrink: 1;
  flex-basis: 0%;
  overflow: visible;
  min-height: 0; /* Important for flex shrinking */
}

/* ===== ENHANCED FILTER SECTION ===== */

.enhanced-filters-section {
  background: linear-gradient(135deg, #1a1a1a 0%, #252525 100%);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 16px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
  flex-shrink: 0;
  overflow: hidden;
  position: relative;
}

.enhanced-filters-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #ff9800, transparent);
  opacity: 0.6;
}



.filters-content {
  padding: 0.75rem;
}

.filter-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.filter-row {
  display: flex;
  gap: 2rem;
  align-items: flex-end;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.filter-group-inline {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
  min-width: 200px;
}

.filter-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #e0e0e0;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.label-icon {
  font-size: 1rem;
  color: #64b5f6;
}

/* Level Filter Buttons */
.level-filter-buttons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  align-items: center;
  min-height: 40px;
}

.level-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.03);
  color: #e0e0e0;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  font-weight: 500;
}

.level-btn:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.level-btn.active {
  background: var(--level-bg);
  border-color: var(--level-color);
  color: white;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.level-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--level-color);
}

/* Enhanced Search Input */
.search-input-wrapper {
  position: relative;
}

.enhanced-filter-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
  color: #ffffff;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.enhanced-filter-input:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
  background: rgba(255, 255, 255, 0.08);
}

.enhanced-filter-input::placeholder {
  color: #888;
}

.enhanced-filter-select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
  color: #ffffff;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  cursor: pointer;
}

.enhanced-filter-select:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
  background: rgba(255, 255, 255, 0.08);
}

.enhanced-filter-select option {
  background: #1a1a1a;
  color: #ffffff;
  padding: 0.5rem;
}

.date-range-inputs-inline {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.date-input-inline {
  flex: 1;
  min-width: 150px;
}

.date-input-inline.invalid {
  border-color: #f44336 !important;
  background: rgba(244, 67, 54, 0.05) !important;
  box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.2) !important;
}

.date-input-inline.invalid:focus {
  border-color: #f44336 !important;
  box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.3) !important;
}

.date-range-error {
  margin-top: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.3);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.error-text {
  color: #f44336;
  font-size: 0.8rem;
  font-weight: 500;
  text-align: center;
}

.action-btn.apply-filters.disabled {
  background: rgba(255, 255, 255, 0.05) !important;
  color: #666 !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
  cursor: not-allowed !important;
  box-shadow: none !important;
}

.action-btn.apply-filters.disabled:hover {
  background: rgba(255, 255, 255, 0.05) !important;
  transform: none !important;
  box-shadow: none !important;
}

.clear-search-btn {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #b0b0b0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clear-search-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

/* Date Range Inputs */
.date-range-inputs {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.date-separator {
  color: #888;
  font-size: 0.85rem;
  font-weight: 500;
}

.date-input {
  flex-grow: 1;
  flex-shrink: 1;
  flex-basis: 0%;
}

/* Filter Actions */
.filter-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.08);
}

.filter-action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-grow: 1;
  flex-shrink: 1;
  flex-basis: 0%;
  justify-content: center;
}

.filter-action-btn.hide {
  background: rgba(255, 255, 255, 0.05);
  color: #b0b0b0;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.filter-action-btn.hide:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.filter-action-btn.apply {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.filter-action-btn.apply:hover {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  box-shadow: 0 8px 25px rgba(33, 150, 243, 0.4);
  transform: translateY(-2px);
}

.filter-action-btn.clear {
  background: rgba(255, 255, 255, 0.05);
  color: #b0b0b0;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.filter-action-btn.clear:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* ===== ENHANCED EMPTY STATE ===== */

.enhanced-empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-grow: 1;
  flex-shrink: 1;
  flex-basis: 0%;
  min-height: 400px;
}

.empty-state-content {
  text-align: center;
  padding: 3rem 2rem;
  background: linear-gradient(135deg, #1a1a1a 0%, #252525 100%);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  max-width: 500px;
  width: 100%;
}

.empty-state-icon {
  font-size: 4rem;
  color: #64b5f6;
  margin-bottom: 1.5rem;
  opacity: 0.7;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.empty-state-content h2 {
  margin-bottom: 1rem;
  color: #ffffff;
  font-size: 1.5rem;
  font-weight: 600;
}

.empty-state-content p {
  margin-bottom: 2rem;
  color: #b0b0b0;
  font-size: 1rem;
  line-height: 1.5;
}

.empty-state-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.empty-action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.empty-action-btn.primary {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.empty-action-btn.primary:hover {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  box-shadow: 0 8px 25px rgba(33, 150, 243, 0.4);
  transform: translateY(-2px);
}

.empty-action-btn.secondary {
  background: rgba(255, 255, 255, 0.05);
  color: #e0e0e0;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.empty-action-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* ===== ENHANCED TABLE CONTAINER ===== */

.enhanced-table-container {
  background: linear-gradient(135deg, #1a1a1a 0%, #252525 100%);
  border-radius: 16px;
  overflow: hidden;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.08);
  transition: all 0.3s ease;
  position: relative;
  flex-grow: 1;
  flex-shrink: 1;
  flex-basis: 0%;
  min-height: 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 220px);
}

.table-header-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.02);
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.table-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.table-icon {
  font-size: 1.25rem;
  color: #64b5f6;
}

.table-title span {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffffff;
}

.live-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.live-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #666;
  transition: all 0.3s ease;
}

.live-dot.active {
  background: #4caf50;
  box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
  animation: pulse 2s infinite;
}

.live-indicator span {
  font-size: 0.75rem;
  color: #b0b0b0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.showing-count {
  font-size: 0.85rem;
  color: #b0b0b0;
}



/* ===== ENHANCED TABLE WRAPPER ===== */

.enhanced-table-wrapper {
  flex-grow: 1;
  flex-shrink: 1;
  flex-basis: 0%;
  overflow: auto;
  width: 100%;
  position: relative;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* Enhanced scroll shadows */
.enhanced-table-wrapper::before,
.enhanced-table-wrapper::after {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  width: 30px;
  pointer-events: none;
  z-index: 10;
  transition: opacity 0.3s ease;
}

.enhanced-table-wrapper::before {
  left: 0;
  background: linear-gradient(to right, rgba(26, 26, 26, 0.9), transparent);
  opacity: 0;
}

.enhanced-table-wrapper::after {
  right: 0;
  background: linear-gradient(to left, rgba(26, 26, 26, 0.9), transparent);
  opacity: 0;
}

.enhanced-table-wrapper[data-scroll="left"]::before,
.enhanced-table-wrapper[data-scroll="both"]::before {
  opacity: 1;
}

.enhanced-table-wrapper[data-scroll="right"]::after,
.enhanced-table-wrapper[data-scroll="both"]::after {
  opacity: 1;
}

/* ===== ENHANCED LOGS TABLE ===== */

.enhanced-logs-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  min-width: 900px;
  background: transparent;
}

.table-header {
  background: linear-gradient(135deg, #1e1e1e 0%, #2a2a2a 100%);
  position: sticky;
  top: 0;
  z-index: 15;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.table-header tr {
  border-bottom: 2px solid rgba(33, 150, 243, 0.3);
}

.timestamp-header,
.level-header,
.message-header {
  padding: 1rem 1.25rem;
  text-align: left;
  position: relative;
  transition: all 0.3s ease;
}

.timestamp-header {
  width: 18%;
  min-width: 180px;
}

.level-header {
  width: 12%;
  min-width: 120px;
}

.message-header {
  width: 70%;
  min-width: 300px;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #ffffff;
  font-weight: 600;
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.header-icon {
  font-size: 1rem;
  color: #64b5f6;
}

.timestamp-header:hover,
.level-header:hover,
.message-header:hover {
  background: rgba(33, 150, 243, 0.1);
}

.timestamp-header::after,
.level-header::after,
.message-header::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #2196f3, transparent);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.timestamp-header:hover::after,
.level-header:hover::after,
.message-header:hover::after {
  transform: scaleX(1);
}

/* ===== ENHANCED TABLE BODY ===== */

.table-body {
  background: transparent;
}

.log-row {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  position: relative;
  animation: slideIn 0.3s ease-out;
  animation-delay: calc(var(--row-index) * 0.05s);
  animation-fill-mode: both;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.log-row:last-child {
  border-bottom: none;
}

.log-row:hover {
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.08) 0%, rgba(33, 150, 243, 0.03) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.log-row.error:hover {
  background: linear-gradient(135deg, rgba(244, 67, 54, 0.08) 0%, rgba(244, 67, 54, 0.03) 100%);
}

.log-row.warn:hover {
  background: linear-gradient(135deg, rgba(255, 152, 0, 0.08) 0%, rgba(255, 152, 0, 0.03) 100%);
}

.log-row.fatal:hover {
  background: linear-gradient(135deg, rgba(139, 69, 19, 0.08) 0%, rgba(139, 69, 19, 0.03) 100%);
}

.loading-row {
  height: 80px;
}

.loading-cell {
  text-align: center;
  padding: 2rem;
  color: #888;
}

.loading-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  font-size: 0.9rem;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(33, 150, 243, 0.3);
  border-radius: 50%;
  border-top-color: #2196f3;
  animation: spin 1s linear infinite;
}

/* ===== TABLE CELL STYLES ===== */

.timestamp-cell,
.level-cell,
.message-cell {
  padding: 0.75rem 1.25rem;
  vertical-align: middle;
  position: relative;
}

.timestamp-content {
  display: flex;
  align-items: center;
}

.timestamp-text {
  font-family: 'JetBrains Mono', 'Courier New', monospace;
  font-size: 0.85rem;
  color: #94a3b8;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.level-badge-wrapper {
  display: flex;
  align-items: center;
}

.enhanced-level-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.4rem 0.8rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: var(--level-bg);
  color: var(--level-color);
  border: 1px solid var(--level-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.enhanced-level-badge:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.enhanced-level-badge .level-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--level-color);
  animation: pulse 2s infinite;
}

.message-content {
  display: flex;
  align-items: center;
}

.message-text {
  color: #e0e0e0;
  font-size: 0.9rem;
  line-height: 1.4;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* Enhanced scrollbar for table wrapper */
.enhanced-table-wrapper::-webkit-scrollbar {
  height: 10px;
  width: 10px;
}

.enhanced-table-wrapper::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 5px;
}

.enhanced-table-wrapper::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #2196f3 0%, #64b5f6 100%);
  border-radius: 5px;
  border: 2px solid rgba(26, 26, 26, 0.8);
  transition: all 0.3s ease;
}

.enhanced-table-wrapper::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #1976d2 0%, #2196f3 100%);
  box-shadow: 0 0 10px rgba(33, 150, 243, 0.5);
}

.enhanced-table-wrapper::-webkit-scrollbar-corner {
  background: rgba(255, 255, 255, 0.05);
}

/* Firefox scrollbar */
.enhanced-table-wrapper {
  scrollbar-width: thin;
  scrollbar-color: #2196f3 rgba(255, 255, 255, 0.05);
}

/* ===== ENHANCED PAGINATION ===== */

.compact-pagination-section {
  background: linear-gradient(135deg, #1a1a1a 0%, #252525 100%);
  border-radius: 8px;
  padding: 0.5rem 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  flex-shrink: 0;
}

.pagination-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.75rem;
  min-height: 32px;
}

.pagination-left {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-grow: 1;
  flex-shrink: 1;
  flex-basis: 0%;
}

.pagination-center {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-grow: 0;
  flex-shrink: 0;
  flex-basis: auto;
}

.pagination-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-grow: 1;
  flex-shrink: 1;
  flex-basis: 0%;
}

.stats-text {
  font-size: 0.85rem;
  color: #e0e0e0;
  white-space: nowrap;
}

.stats-text strong {
  color: #2196f3;
  font-weight: 600;
}

.page-info {
  font-size: 0.85rem;
  color: #b0b0b0;
  white-space: nowrap;
}

.page-info strong {
  color: #2196f3;
  font-weight: 600;
}

.page-size-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.page-size-label {
  font-size: 0.8rem;
  color: #b0b0b0;
  font-weight: 500;
  white-space: nowrap;
  margin-bottom: 0rem !important;
}

.compact-page-size-select {
  padding: 0.4rem 0.6rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.05);
  color: #e0e0e0;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 60px;
}

.compact-page-size-select:hover {
  border-color: rgba(33, 150, 243, 0.5);
  background: rgba(255, 255, 255, 0.08);
}

.compact-page-size-select:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

.pagination-btn {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.3rem 0.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.05);
  color: #e0e0e0;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.75rem;
  font-weight: 500;
  min-height: 28px;
}

.pagination-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(33, 150, 243, 0.5);
  transform: translateY(-1px);
}

.pagination-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none;
}

.pagination-btn.nav-btn {
  padding: 0.4rem 0.8rem;
}

.pagination-btn.page-btn {
  min-width: 32px;
  justify-content: center;
  padding: 0.4rem 0.6rem;
}

.pagination-btn.page-btn.active {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  border-color: #2196f3;
  color: white;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
}

.pagination-btn.page-btn.active:hover {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4);
}

.page-numbers {
  display: flex;
  gap: 0.25rem;
  align-items: center;
}

.level-cell {
  text-align: center;
  width: 10%;
  min-width: 80px;
}

.level-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 60px;
  text-align: center;
}

/* Log level badge colors */
.level-badge.debug {
  background-color: #374151;
  color: #9ca3af;
  border: 1px solid #4b5563;
}

.level-badge.info {
  background-color: #1e3a8a;
  color: #60a5fa;
  border: 1px solid #2563eb;
}

.level-badge.warn {
  background-color: #92400e;
  color: #fbbf24;
  border: 1px solid #d97706;
}

.level-badge.error {
  background-color: #991b1b;
  color: #f87171;
  border: 1px solid #dc2626;
}

.level-badge.fatal {
  background-color: #7f1d1d;
  color: #ef4444;
  border: 1px solid #b91c1c;
}

.message-cell {
  width: 72%;
  min-width: 300px;
}

.message-content {
  word-wrap: break-word;
  white-space: pre-wrap;
  line-height: 1.5;
  max-width: 100%;
  overflow-wrap: break-word;
}

.loading-cell {
  text-align: center;
  padding: 2rem;
  color: #888;
}

.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #333;
  border-radius: 50%;
  border-top-color: #2196f3;
  animation: spin 1s ease-in-out infinite;
  margin-right: 0.5rem;
}

/* Pagination Section */
.pagination-section {
  padding: 1rem 1.5rem;
  background-color: #1a1a1a;
  border: 1px solid #333;
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  flex-shrink: 0;
}

.pagination-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.total-count {
  font-weight: 600;
  color: #e2e8f0;
  font-size: 0.875rem;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #888;
  font-size: 0.875rem;
}

.page-size-select {
  background-color: #2a2a2a;
  color: white;
  border: 1px solid #3a3a3a;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
}

.page-size-select:hover {
  border-color: #2196f3;
}

.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.pagination-button {
  background-color: #2a2a2a;
  color: white;
  border: 1px solid #3a3a3a;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
}

.pagination-button:hover:not(:disabled) {
  background-color: #3a3a3a;
  border-color: #2196f3;
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-numbers {
  display: flex;
  gap: 0.25rem;
}

.page-number {
  background-color: #2a2a2a;
  color: white;
  border: 1px solid #3a3a3a;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 0.875rem;
  min-width: 40px;
  text-align: center;
}

.page-number:hover {
  background-color: #3a3a3a;
  border-color: #2196f3;
}

.page-number.active {
  background-color: #2196f3;
  border-color: #2196f3;
  color: white;
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 1024px) {
  .logs-page {
    padding: 1rem;
  }

  .logs-header {
    padding: 1.25rem;
  }

  .header-main {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .quick-stats {
    gap: 1rem;
  }

  .action-bar {
    flex-direction: column;
    gap: 1rem;
  }

  .action-group {
    width: 100%;
    justify-content: center;
  }

  .enhanced-table-container {
    height: calc(100vh - 280px);
  }

  .enhanced-logs-table {
    min-width: 900px;
  }

  .timestamp-header,
  .level-header,
  .message-header {
    padding: 0.75rem 1rem;
  }

  .timestamp-cell,
  .level-cell,
  .message-cell {
    padding: 0.5rem 1rem;
  }

  .filter-grid {
    gap: 1rem;
  }

  .level-filter-buttons {
    gap: 0.25rem;
  }

  .level-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 768px) {
  .logs-page {
    padding: 0.75rem;
    gap: 1rem;
  }

  .logs-header {
    padding: 1rem;
  }

  .header-main {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 1rem;
  }

  .header-title {
    flex-direction: column;
    gap: 0.75rem;
  }

  .header-title h1 {
    font-size: 1.75rem;
  }

  .quick-stats {
    flex-direction: column;
    gap: 0.75rem;
    width: 100%;
  }

  .stat-item {
    flex-direction: row;
    justify-content: space-between;
    padding: 0.75rem 1rem;
  }

  .action-bar {
    flex-direction: column;
    gap: 1rem;
  }

  .action-group {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
  }

  .action-btn {
    flex-grow: 1;
    flex-shrink: 1;
    flex-basis: 0%;
    min-width: 120px;
    justify-content: center;
  }

  .enhanced-filters-section {
    margin: 0 -0.25rem;
  }

  .filter-grid {
    gap: 1rem;
  }

  .level-filter-buttons {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .level-btn {
    flex-grow: 1;
    flex-shrink: 1;
    flex-basis: 0%;
    min-width: 80px;
    justify-content: center;
  }

  .date-range-inputs {
    flex-direction: column;
    gap: 0.75rem;
  }

  .filter-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .enhanced-table-container {
    height: calc(100vh - 320px);
    margin: 0 -0.25rem;
  }

  .table-header-info {
    flex-direction: column;
    gap: 0.75rem;
    text-align: center;
  }

  .enhanced-logs-table {
    min-width: 1000px;
  }

  .timestamp-header,
  .level-header,
  .message-header {
    padding: 0.5rem 0.75rem;
  }

  .header-content {
    font-size: 0.8rem;
  }

  .timestamp-cell,
  .level-cell,
  .message-cell {
    padding: 0.5rem 0.75rem;
  }

  .timestamp-text {
    font-size: 0.8rem;
  }

  .enhanced-level-badge {
    padding: 0.3rem 0.6rem;
    font-size: 0.7rem;
  }

  .message-text {
    font-size: 0.85rem;
  }

  .compact-pagination-section {
    padding: 0.75rem 1rem;
    margin: 0 -0.25rem;
  }

  .pagination-row {
    flex-direction: column;
    gap: 0.75rem;
    text-align: center;
  }

  .pagination-left,
  .pagination-center,
  .pagination-right {
    flex: none;
    width: 100%;
    justify-content: center;
  }

  .pagination-center {
    order: 1;
  }

  .pagination-left {
    order: 2;
  }

  .pagination-right {
    order: 3;
  }

  .pagination-btn.nav-btn {
    flex-grow: 1;
    flex-shrink: 1;
    flex-basis: 0%;
    min-width: 80px;
  }

  /* Enhanced scrollbar on mobile */
  .enhanced-table-wrapper::-webkit-scrollbar {
    height: 12px;
    width: 12px;
  }

  .enhanced-table-wrapper::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #2196f3, #64b5f6);
    border-radius: 6px;
    border: 2px solid rgba(26, 26, 26, 0.8);
  }
}

@media (max-width: 480px) {
  .logs-page {
    padding: 0.5rem;
    gap: 0.75rem;
  }

  .logs-header {
    padding: 0.75rem;
  }

  .header-title h1 {
    font-size: 1.5rem;
  }

  .title-icon {
    font-size: 1.5rem;
  }

  .header-badge {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }

  .quick-stats {
    gap: 0.5rem;
  }

  .stat-item {
    padding: 0.5rem 0.75rem;
    min-width: auto;
  }

  .stat-value {
    font-size: 1rem;
  }

  .stat-label {
    font-size: 0.7rem;
  }

  .action-btn {
    padding: 0.6rem 1rem;
    font-size: 0.85rem;
  }

  .enhanced-filters-section {
    margin: 0 -0.25rem;
  }



  .filters-content {
    padding: 1rem;
  }

  .level-btn {
    padding: 0.4rem 0.6rem;
    font-size: 0.75rem;
  }

  .enhanced-filter-input {
    padding: 0.6rem 0.8rem;
    font-size: 0.85rem;
  }

  .enhanced-table-container {
    height: calc(100vh - 360px);
    margin: 0 -0.25rem;
  }

  .table-header-info {
    padding: 0.75rem 1rem;
  }

  .enhanced-logs-table {
    min-width: 1100px;
  }

  .timestamp-header,
  .level-header,
  .message-header {
    padding: 0.4rem 0.6rem;
  }

  .header-content {
    font-size: 0.75rem;
    gap: 0.25rem;
  }

  .header-icon {
    font-size: 0.85rem;
  }

  .timestamp-cell,
  .level-cell,
  .message-cell {
    padding: 0.4rem 0.6rem;
  }

  .timestamp-text {
    font-size: 0.75rem;
  }

  .enhanced-level-badge {
    padding: 0.25rem 0.5rem;
    font-size: 0.65rem;
    gap: 0.25rem;
  }

  .level-indicator {
    width: 5px;
    height: 5px;
  }

  .message-text {
    font-size: 0.8rem;
  }

  .compact-pagination-section {
    padding: 0.5rem 0.75rem;
    margin: 0 -0.25rem;
  }

  .pagination-btn {
    padding: 0.4rem 0.6rem;
    font-size: 0.8rem;
  }

  .pagination-btn.nav-btn {
    padding: 0.4rem 0.8rem;
  }

  .empty-state-content {
    padding: 2rem 1rem;
  }

  .empty-state-icon {
    font-size: 3rem;
  }

  .empty-state-content h2 {
    font-size: 1.25rem;
  }

  .empty-action-btn {
    padding: 0.6rem 1rem;
    font-size: 0.85rem;
  }
}