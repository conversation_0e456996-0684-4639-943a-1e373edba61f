import momentTimezone from 'moment-timezone';

// Format seconds to HH:MM:SS
export const formatSeconds = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  return [hours, minutes, secs]
    .map(val => val.toString().padStart(2, '0'))
    .join(':');
};

// Convert seconds to HH:MM:SS format
export const secondsToHHMMSS = (seconds: string): string => {
  const secs = parseInt(seconds);
  if (isNaN(secs)) return seconds; // If not a valid number, return as is

  const hours = Math.floor(secs / 3600);
  const minutes = Math.floor((secs % 3600) / 60);
  const remainingSeconds = secs % 60;

  return [hours, minutes, remainingSeconds]
    .map(val => val.toString().padStart(2, '0'))
    .join(':');
};

// Calculate progress percentage
export const calculateProgress = (elapsed: number, total: number): number => {
  if (total <= 0) return 0;
  const progress = (elapsed / total) * 100;
  return Math.min(Math.max(progress, 0), 100); // Clamp between 0 and 100
};

export const generateShortId = (length: number): string => {
  let result = '';
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const charactersLength = characters.length;
  let counter = 0;
  while (counter < length) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
    counter += 1;
  }
  return result;
}

export const getTimezones = (): { value: string, label: string }[] => {
  let offsetTmz = [];
  let timeZones = momentTimezone.tz.names();

  timeZones = timeZones.sort((a, b) => {
    let first  = 0;
    let second = 0;

    switch (true) {
      case -1 !== a.search(/US\//):
        first = 3;
        break;
      case -1 !== a.search(/America\//):
        first = 1;
        break;
      case a === a.toUpperCase() && a.length === 3:
      case a === a.toUpperCase() && a.length === 7 && /^\d+$/.test(a[3]):
        first = 2;
        break;
    }

    switch (true) {
      case -1 !== b.search(/US\//):
        second = 3;
        break;
      case -1 !== b.search(/America\//):
        second = 1;
        break;
      case b === b.toUpperCase() && b.length === 3:
      case b === b.toUpperCase() && b.length === 7 && /^\d+$/.test(b[3]):
        second = 2;
        break;
    }

    return second - first;
  })

  for (let i in timeZones) {
    offsetTmz.push({
      label: " (GMT"+momentTimezone.tz(timeZones[i]).format('Z')+") " + timeZones[i],
      value: timeZones[i]
    })
  }

  return offsetTmz;
}