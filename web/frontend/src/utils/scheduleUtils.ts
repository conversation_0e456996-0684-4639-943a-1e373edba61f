import { Schedule } from "@/types/schedule";
import axios from "axios";

/**
 * Information about the currently playing item and next item in a schedule
 */
export interface SchedulePlayingInfo {
  currentItem: Item | null;
  nextItem: Item | null;
  currentItemDuration: string;
  nextItemDuration: string;
  currentItemRemainingTime: string;
  currentItemEndTime: Date | null;
}

interface Item {
  name: string;
  start: string;
  end: string;
  fileName?: string;
  description?: string;
}

/**
 * Get information about the currently playing item and next item in a schedule by parsing the EPG XML
 * @param schedule The schedule to get playing info for
 * @returns Promise containing current and next items with their durations
 */
export const getSchedulePlayingInfo = async (
  schedule: Schedule
): Promise<SchedulePlayingInfo> => {
  try {
    // Get the EPG XML URL
    const epgUrl = `${window.location.origin}/data/epg/${schedule.short_id}.xml`;

    // Fetch the EPG XML
    const response = await axios.get(epgUrl);
    const xmlData = response.data;

    // Parse the XML using browser's built-in DOMParser
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(xmlData, "text/xml");

    // Get all programme elements
    const programmeElements = xmlDoc.getElementsByTagName("programme");

    if (!programmeElements || programmeElements.length === 0) {
      console.warn("No programme elements found in the EPG XML");
      return {
        currentItem: null,
        nextItem: null,
        currentItemDuration: "",
        nextItemDuration: "",
        currentItemRemainingTime: "",
        currentItemEndTime: null,
      };
    }

    // Get current time
    const now = new Date();
    const currentTime = now.getTime();

    let currentProgram: Element | null = null;
    let nextProgram: Element | null = null;

    // Find current and next program
    for (let i = 0; i < programmeElements.length; i++) {
      const program = programmeElements[i];

      // Parse start and stop times
      const startAttr = program.getAttribute("start");
      const stopAttr = program.getAttribute("stop");

      if (!startAttr || !stopAttr) {
        console.warn("Program missing start or stop attribute:", program);
        continue;
      }

      try {
        const startTime = parseEPGTime(startAttr);
        const stopTime = parseEPGTime(stopAttr);

        if (
          currentTime >= startTime.getTime() &&
          currentTime < stopTime.getTime()
        ) {
          // This is the current program
          currentProgram = program;

          // Next program is the one after this
          if (i < programmeElements.length - 1) {
            nextProgram = programmeElements[i + 1];
          }

          break;
        } else if (currentTime < startTime.getTime() && !nextProgram) {
          // This is the next program (if we haven't found the current one)
          nextProgram = program;
        }
      } catch (err) {
        console.error("Error parsing program times:", err, program);
        continue;
      }
    }

    // Extract file information from the program elements
    const extractItemInfo = (program: Element | null): Item | null => {
      if (!program) return null;

      const title = getElementTextContent(program, "title");
      const desc = getElementTextContent(program, "desc");

      // Get filename from sub-title element
      let fileName = getElementTextContent(program, "sub-title");

      // If no sub-title or it's empty, use title as fallback
      if (!fileName) {
        fileName = title;
      }

      return {
        name: title,
        fileName: fileName,
        description: desc,
        start: formatEPGTimeToISO(program.getAttribute("start") || ""),
        end: formatEPGTimeToISO(program.getAttribute("stop") || ""),
      };
    };

    // Get end time for current program
    const currentEndTime = currentProgram
      ? parseEPGTime(currentProgram.getAttribute("stop") || "")
      : null;

    // Format the response
    return {
      currentItem: extractItemInfo(currentProgram),
      nextItem: extractItemInfo(nextProgram),
      currentItemDuration: currentProgram
        ? formatDuration(
            parseEPGTime(currentProgram.getAttribute("start") || ""),
            parseEPGTime(currentProgram.getAttribute("stop") || "")
          )
        : "",
      nextItemDuration: nextProgram
        ? formatDuration(
            parseEPGTime(nextProgram.getAttribute("start") || ""),
            parseEPGTime(nextProgram.getAttribute("stop") || "")
          )
        : "",
      currentItemRemainingTime: currentEndTime
        ? calculateRemainingTime(currentEndTime)
        : "",
      currentItemEndTime: currentEndTime,
    };
  } catch (error) {
    console.error("Error parsing EPG XML:", error);
    return {
      currentItem: null,
      nextItem: null,
      currentItemDuration: "",
      nextItemDuration: "",
      currentItemRemainingTime: "",
      currentItemEndTime: null,
    };
  }
};

/**
 * Get text content of the first matching child element
 */
function getElementTextContent(element: Element, tagName: string): string {
  const childElement = element.getElementsByTagName(tagName)[0];
  return childElement ? childElement.textContent || "" : "";
}

/**
 * Parse EPG time format (YYYYMMDDHHMMSS +0000) to Date object
 */
function parseEPGTime(timeString: string): Date {
  if (!timeString || timeString.length < 14) {
    return new Date(); // Return current time as fallback
  }

  // EPG format: "20230405123000 +0000"
  const dateStr = timeString.substring(0, 14);
  const year = parseInt(dateStr.substring(0, 4));
  const month = parseInt(dateStr.substring(4, 6)) - 1; // JS months are 0-based
  const day = parseInt(dateStr.substring(6, 8));
  const hour = parseInt(dateStr.substring(8, 10));
  const minute = parseInt(dateStr.substring(10, 12));
  const second = parseInt(dateStr.substring(12, 14));

  // Parse timezone offset
  const tzOffset = timeString.substring(15);

  // Create date in UTC
  const date = new Date(Date.UTC(year, month, day, hour, minute, second));

  // Adjust for timezone if needed
  if (tzOffset && tzOffset !== "+0000") {
    const tzHours = parseInt(tzOffset.substring(1, 3));
    const tzMinutes = parseInt(tzOffset.substring(3, 5));
    const tzMilliseconds = (tzHours * 60 + tzMinutes) * 60 * 1000;

    if (tzOffset.startsWith("+")) {
      date.setTime(date.getTime() - tzMilliseconds);
    } else {
      date.setTime(date.getTime() + tzMilliseconds);
    }
  }

  return date;
}

/**
 * Format EPG time to ISO string
 */
function formatEPGTimeToISO(timeString: string): string {
  if (!timeString) return "";
  return parseEPGTime(timeString).toISOString();
}

/**
 * Format duration between two dates
 */
function formatDuration(start: Date, end: Date): string {
  if (!start || !end) return "";

  // Calculate duration in seconds
  const durationSeconds = Math.floor((end.getTime() - start.getTime()) / 1000);

  // Convert to hours, minutes, seconds
  const hours = Math.floor(durationSeconds / 3600);
  const minutes = Math.floor((durationSeconds % 3600) / 60);
  const seconds = durationSeconds % 60;

  // Format with leading zeros
  return `${hours.toString().padStart(2, "0")}:${minutes
    .toString()
    .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
}

/**
 * Calculate and format remaining time between now and end time
 */
function calculateRemainingTime(end: Date): string {
  if (!end) return "";

  const now = new Date();

  // If end time is in the past, return "00:00:00"
  if (end.getTime() <= now.getTime()) {
    return "00:00:00";
  }

  // Calculate remaining time in seconds
  const remainingSeconds = Math.floor((end.getTime() - now.getTime()) / 1000);

  // Convert to hours, minutes, seconds
  const hours = Math.floor(remainingSeconds / 3600);
  const minutes = Math.floor((remainingSeconds % 3600) / 60);
  const seconds = remainingSeconds % 60;

  // Format with leading zeros
  return `${hours.toString().padStart(2, "0")}:${minutes
    .toString()
    .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
}
