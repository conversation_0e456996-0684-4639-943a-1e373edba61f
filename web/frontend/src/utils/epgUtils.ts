import { Element, Guide } from "@/types/schedule";

export interface EPGChannel {
  id: string;
  displayName: string;
  icon?: string;
}

export interface EPGProgram {
  channelId: string;
  start: string;
  stop: string;
  title: string;
  description?: string;
  category?: string;
  episodeNum?: string;
  length?: number;
}

export interface EPGData {
  channels: EPGChannel[];
  programs: EPGProgram[];
}

/**
 * Parses an XML string containing EPG data
 * @param xmlString The XML string to parse
 * @returns Parsed EPG data
 */
export const parseEPGXml = (xmlString: string): EPGData => {
  const parser = new DOMParser();
  const xmlDoc = parser.parseFromString(xmlString, "text/xml");

  // Check for parsing errors
  const parserError = xmlDoc.querySelector("parsererror");
  if (parserError) {
    console.error("XML parsing error:", parserError.textContent);
    throw new Error("Failed to parse XML: Invalid XML format");
  }

  // Parse channels
  const channelElements = xmlDoc.getElementsByTagName("channel");
  const channels: EPGChannel[] = [];

  for (let i = 0; i < channelElements.length; i++) {
    const channelElement = channelElements[i];
    const id = channelElement.getAttribute("id") || "";
    const displayNameElement =
      channelElement.getElementsByTagName("display-name")[0];
    const iconElement = channelElement.getElementsByTagName("icon")[0];

    channels.push({
      id,
      displayName: displayNameElement
        ? displayNameElement.textContent || ""
        : "",
      icon: iconElement
        ? iconElement.getAttribute("src") || undefined
        : undefined,
    });
  }

  // Parse programs
  const programElements = xmlDoc.getElementsByTagName("programme");
  const programs: EPGProgram[] = [];

  for (let i = 0; i < programElements.length; i++) {
    const programElement = programElements[i];
    const channelId = programElement.getAttribute("channel") || "";
    const start = programElement.getAttribute("start") || "";
    const stop = programElement.getAttribute("stop") || "";

    if (!start || !stop) {
      console.warn("Program missing start or stop time:", programElement);
      continue; // Skip programs with missing time data
    }

    const titleElement = programElement.getElementsByTagName("title")[0];
    const descElement = programElement.getElementsByTagName("desc")[0];
    const categoryElement = programElement.getElementsByTagName("category")[0];
    const episodeNumElement =
      programElement.getElementsByTagName("episode-num")[0];
    const lengthElement = programElement.getElementsByTagName("length")[0];

    programs.push({
      channelId,
      start,
      stop,
      title: titleElement ? titleElement.textContent || "" : "",
      description: descElement
        ? descElement.textContent || undefined
        : undefined,
      category: categoryElement
        ? categoryElement.textContent || undefined
        : undefined,
      episodeNum: episodeNumElement
        ? episodeNumElement.textContent || undefined
        : undefined,
      length: lengthElement
        ? parseInt(lengthElement.textContent || "0")
        : undefined,
    });
  }

  if (channels.length === 0) {
    console.warn("No channels found in the XML data");
  }

  if (programs.length === 0) {
    console.warn("No programs found in the XML data");
  } else {
    console.log(
      `Parsed ${programs.length} programs for ${channels.length} channels`
    );
  }

  return { channels, programs };
};

/**
 * Converts EPG date format (YYYYMMDDHHMMSS +0000) to ISO string
 * @param epgDate EPG date string
 * @returns ISO date string
 */
export const convertEPGDateToISO = (epgDate: string): string => {
  try {
    // Check if we have a valid string
    if (!epgDate || typeof epgDate !== "string") {
      console.warn("Invalid EPG date format", epgDate);
      return new Date().toISOString(); // Return current time as fallback
    }

    // Handle different formats
    if (epgDate.includes("T") && epgDate.includes("Z")) {
      // Already in ISO format
      return epgDate;
    }

    if (epgDate.includes("-") && epgDate.includes(":")) {
      // Likely already a standard date format
      return new Date(epgDate).toISOString();
    }

    // EPG format: "20230405123000 +0000"
    // Split by space to handle timezone offset if present
    const dateParts = epgDate.split(" ");
    const dateString = dateParts[0];
    const tzOffset = dateParts.length > 1 ? dateParts[1] : "+0000";

    if (dateString.length < 14) {
      console.warn("EPG date string too short", epgDate);
      return new Date().toISOString(); // Return current time as fallback
    }

    // Extract date parts from the EPG date format (YYYYMMDDHHMMSS)
    const year = parseInt(dateString.substring(0, 4));
    const month = parseInt(dateString.substring(4, 6)) - 1; // JS months are 0-based
    const day = parseInt(dateString.substring(6, 8));
    const hour = parseInt(dateString.substring(8, 10));
    const minute = parseInt(dateString.substring(10, 12));
    const second = parseInt(dateString.substring(12, 14));

    // Create date in UTC
    const date = new Date(Date.UTC(year, month, day, hour, minute, second));

    // Adjust for timezone if needed
    if (tzOffset && tzOffset !== "+0000") {
      const tzHours = parseInt(tzOffset.substring(1, 3));
      const tzMinutes = parseInt(tzOffset.substring(3, 5));
      const tzMilliseconds = (tzHours * 60 + tzMinutes) * 60 * 1000;

      if (tzOffset.startsWith("+")) {
        date.setTime(date.getTime() - tzMilliseconds);
      } else {
        date.setTime(date.getTime() + tzMilliseconds);
      }
    }

    return date.toISOString();
  } catch (err) {
    console.error("Error converting EPG date to ISO", epgDate, err);
    return new Date().toISOString(); // Return current time as fallback
  }
};

/**
 * Converts EPG programs to Element format used by the GuideViewTimeline
 * @param programs EPG programs
 * @returns Elements in the format expected by GuideViewTimeline
 */
export const convertEPGProgramsToElements = (
  programs: EPGProgram[]
): Element[] => {
  return programs.map((program) => {
    // Convert EPG date format to ISO string
    const start = convertEPGDateToISO(program.start);
    const end = convertEPGDateToISO(program.stop);

    return {
      start,
      end,
      title: program.title,
      description: program.description || "",
      type: program.category?.toLowerCase() || "file",
      file: {
        file_id: 0,
        folder: "/",
        filename: `${program.title}.mp4`,
        episode: program.episodeNum || "",
      },
      connection: {
        type: "",
        link: "",
        port: "",
        mode: "",
        expire_date: "",
        expire_time: "",
      },
    };
  });
};

/**
 * Scans the EPG directory for available XML files
 * @returns Promise resolving to an array of EPG file names
 */
export const scanEPGDirectory = async (): Promise<string[]> => {
  try {
    // Use the Go backend API endpoint to get schedules with short_ids
    const response = await fetch("/api/v1/schedule/epg", {
      method: "GET",
      headers: {
        Accept: "application/json",
      },
      // Add timeout to prevent hanging requests
      signal: AbortSignal.timeout(10000), // 10 second timeout
    });

    console.log("scanEPGDirectory Response:", response);
    // Check if the response is ok (status in the range 200-299)
    if (!response.ok) {
      throw new Error(
        `Failed to fetch schedules: ${response.status} ${response.statusText}`
      );
    }

    // Parse the response as JSON
    const data = await response.json();

    // Check if the data has the expected structure
    if (!data || !Array.isArray(data.schedules)) {
      console.warn(
        "Unexpected response format from /api/v1/schedule/epg:",
        data
      );
      return [];
    }

    // Extract short_ids and append .xml to form filenames
    const shortIds = data.schedules
      .filter(
        (schedule: any) =>
          schedule &&
          typeof schedule.short_id === "string" &&
          schedule.short_id.trim() !== ""
      )
      .map((schedule: { short_id: string }) => `${schedule.short_id}.xml`);

    console.log(
      `Found ${shortIds.length} schedule short_ids for EPG files:`,
      shortIds
    );
    return shortIds;
  } catch (error) {
    // Handle network errors and other exceptions
    if (error instanceof TypeError && error.message.includes("fetch")) {
      console.error("Network error while fetching schedules:", error);
      // Return empty array but log a more specific error
      return [];
    } else if (error instanceof DOMException && error.name === "AbortError") {
      console.error("Request to fetch schedules timed out");
      return [];
    } else {
      console.error("Error fetching schedules:", error);
      return [];
    }
  }
};

/**
 * Loads EPG data from XML files in the backend/data/epg directory
 * @param scheduleId Optional schedule ID to look for a matching XML file
 * @returns Promise resolving to EPG data
 */
export const loadEPGData = async (
  scheduleId?: string
): Promise<EPGData | null> => {
  try {
    // First try to fetch the schedule-specific EPG file if scheduleId is provided
    if (scheduleId) {
      try {
        console.log(`Attempting to load EPG file for schedule ${scheduleId}`);
        const response = await fetch(`/data/epg/${scheduleId}.xml`, {
          method: "GET",
          headers: {
            Accept: "text/xml",
          },
          signal: AbortSignal.timeout(10000), // 10 second timeout
        });

        if (response.ok) {
          console.log(
            `Successfully loaded EPG file for schedule ${scheduleId}`
          );
          const xmlString = await response.text();
          return parseEPGXml(xmlString);
        } else {
          console.warn(
            `EPG file for schedule ${scheduleId} not found or not accessible: ${response.status} ${response.statusText}`
          );
        }
      } catch (error) {
        console.error(
          `Error loading EPG file for schedule ${scheduleId}:`,
          error
        );
        // Continue to fallback options
      }
    }

    // Fall back to any available EPG file from schedules
    try {
      // Get a list of all schedules with short_ids
      console.log("Falling back to any available EPG file from schedules");
      const files = await scanEPGDirectory();

      if (files.length > 0) {
        // Try each file in the directory until one works
        for (const file of files) {
          try {
            console.log(`Attempting to load EPG file: ${file}`);
            const response = await fetch(`/data/epg/${file}`, {
              method: "GET",
              headers: {
                Accept: "text/xml",
              },
              signal: AbortSignal.timeout(10000), // 10 second timeout
            });

            if (response.ok) {
              console.log(`Successfully loaded EPG file: ${file}`);
              const xmlString = await response.text();
              return parseEPGXml(xmlString);
            } else {
              console.warn(
                `Failed to load EPG file ${file}: ${response.status} ${response.statusText}`
              );
            }
          } catch (fileError) {
            console.error(`Error loading EPG file ${file}:`, fileError);
            // Continue to the next file
          }
        }

        console.warn("None of the available EPG files could be loaded");
      } else {
        console.warn("No EPG files found from schedules");
      }
    } catch (fallbackError) {
      console.error(
        "Error during fallback EPG file loading process:",
        fallbackError
      );
    }

    console.error("Failed to load any EPG data");
    return null;
  } catch (error) {
    console.error("Unexpected error loading EPG data:", error);
    return null;
  }
};

/**
 * Converts EPG data to Guide format
 * @param epgData EPG data
 * @returns Guide object
 */
export const convertEPGDataToGuide = (
  epgData: EPGData,
  scheduleId: number = 1
): Guide => {
  const elements = epgData.programs.map((program) => {
    // Convert EPG date format to ISO string
    const start = convertEPGDateToISO(program.start);
    const end = convertEPGDateToISO(program.stop);

    return {
      start,
      end,
      title: program.title,
      description: program.description || "",
      type: program.category?.toLowerCase() || "file",
      file: {
        file_id: 0,
        folder: "/",
        filename: `${program.title}.mp4`,
        episode: program.episodeNum || "",
      },
      connection: {
        type: "",
        link: "",
        port: "",
        mode: "",
        expire_date: "",
        expire_time: "",
      },
    };
  });

  return {
    id: 0,
    schedule_id: scheduleId,
    elements,
    updated_at: new Date().toISOString(),
    scheduleName:
      epgData.channels.length > 0 ? epgData.channels[0].displayName : "Channel",
  };
};

/**
 * Groups EPG programs by channel
 * @param epgData EPG data
 * @returns Map of channel ID to programs
 */
export const groupProgramsByChannel = (
  epgData: EPGData
): Map<string, EPGProgram[]> => {
  const programsByChannel = new Map<string, EPGProgram[]>();

  epgData.programs.forEach((program) => {
    if (!programsByChannel.has(program.channelId)) {
      programsByChannel.set(program.channelId, []);
    }

    programsByChannel.get(program.channelId)?.push(program);
  });

  return programsByChannel;
};

/**
 * Converts EPG data to multiple Guide objects, one per channel
 * @param epgData EPG data
 * @returns Array of Guide objects
 */
export const convertEPGDataToGuides = (epgData: EPGData): Guide[] => {
  const programsByChannel = groupProgramsByChannel(epgData);
  const guides: Guide[] = [];

  epgData.channels.forEach((channel, index) => {
    const channelPrograms = programsByChannel.get(channel.id) || [];
    const elements = channelPrograms.map((program) => {
      // Convert EPG date format to ISO string
      const start = convertEPGDateToISO(program.start);
      const end = convertEPGDateToISO(program.stop);

      return {
        start,
        end,
        title: program.title,
        description: program.description || "",
        type: program.category?.toLowerCase() || "file",
        file: {
          file_id: 0,
          folder: "/",
          filename: `${program.title}.mp4`,
          episode: program.episodeNum || "",
        },
        connection: {
          type: "",
          link: "",
          port: "",
          mode: "",
          expire_date: "",
          expire_time: "",
        },
      };
    });

    guides.push({
      id: index + 1,
      schedule_id: index + 1,
      elements,
      updated_at: new Date().toISOString(),
      scheduleName: channel.displayName,
    });
  });

  return guides;
};
