import { UploadState } from "../redux/uploadSlice";

const UPLOAD_STORAGE_KEY = "traffiq_upload_state";

export interface PersistedUploadState {
  isUploading: boolean;
  files: UploadState["files"];
  currentFileIndex: number;
  totalProgress: number;
  uploadSpeed: string;
  showModal: boolean;
  showStatusNotification: boolean;
  persistModal: boolean;
  timestamp: number; // To check if state is stale
}

export const uploadStorage = {
  // Save upload state to localStorage
  saveUploadState: (state: UploadState): void => {
    try {
      const persistedState: PersistedUploadState = {
        isUploading: state.isUploading,
        files: state.files,
        currentFileIndex: state.currentFileIndex,
        totalProgress: state.totalProgress,
        uploadSpeed: state.uploadSpeed,
        showModal: state.showModal,
        showStatusNotification: state.showStatusNotification,
        persistModal: state.persistModal,
        timestamp: Date.now(),
      };
      localStorage.setItem(UPLOAD_STORAGE_KEY, JSON.stringify(persistedState));
    } catch (error) {
      console.warn("Failed to save upload state to localStorage:", error);
    }
  },

  // Load upload state from localStorage
  loadUploadState: (): PersistedUploadState | null => {
    try {
      const stored = localStorage.getItem(UPLOAD_STORAGE_KEY);
      if (!stored) return null;

      const state: PersistedUploadState = JSON.parse(stored);

      // Check if state is too old (more than 1 hour)
      const isStale = Date.now() - state.timestamp > 60 * 60 * 1000;
      if (isStale) {
        uploadStorage.clearUploadState();
        return null;
      }

      return state;
    } catch (error) {
      console.warn("Failed to load upload state from localStorage:", error);
      uploadStorage.clearUploadState();
      return null;
    }
  },

  // Clear upload state from localStorage
  clearUploadState: (): void => {
    try {
      localStorage.removeItem(UPLOAD_STORAGE_KEY);
    } catch (error) {
      console.warn("Failed to clear upload state from localStorage:", error);
    }
  },

  // Check if there's a persisted upload state
  hasPersistedState: (): boolean => {
    return uploadStorage.loadUploadState() !== null;
  },
};
