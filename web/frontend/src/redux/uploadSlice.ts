import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "./store";
import { uploadStorage, PersistedUploadState } from "../utils/uploadStorage";

export interface UploadFile {
  id: string;
  name: string;
  size: number;
  progress: number;
  status: "uploading" | "completed" | "failed" | "queued";
  location: string;
}

export interface UploadState {
  isUploading: boolean;
  files: UploadFile[];
  currentFileIndex: number;
  totalProgress: number;
  uploadSpeed: string;
  showModal: boolean;
  showStatusNotification: boolean;
  persistModal: boolean;
}

const initialState: UploadState = {
  isUploading: false,
  files: [],
  currentFileIndex: 0,
  totalProgress: 0,
  uploadSpeed: "0 KB/s",
  showModal: false,
  showStatusNotification: false,
  persistModal: false,
};

export const uploadSlice = createSlice({
  name: "upload",
  initialState,
  reducers: {
    restoreUploadState: (
      state,
      action: PayloadAction<PersistedUploadState>
    ) => {
      const persistedState = action.payload;
      state.isUploading = persistedState.isUploading;
      state.files = persistedState.files;
      state.currentFileIndex = persistedState.currentFileIndex;
      state.totalProgress = persistedState.totalProgress;
      state.uploadSpeed = persistedState.uploadSpeed;
      state.showModal = persistedState.showModal;
      state.showStatusNotification = persistedState.showStatusNotification;
      state.persistModal = persistedState.persistModal;
    },
    startUpload: (
      state,
      action: PayloadAction<{ files: UploadFile[]; showModal?: boolean }>
    ) => {
      state.isUploading = true;
      state.files = action.payload.files;
      state.currentFileIndex = 0;
      state.totalProgress = 0;
      state.uploadSpeed = "0 KB/s";
      state.showStatusNotification = true;

      // Always show modal when showModal is true, and persist it during navigation
      if (action.payload.showModal) {
        state.showModal = true;
        state.persistModal = true;
      }

      // Save to localStorage
      uploadStorage.saveUploadState(state);
    },
    updateFileProgress: (
      state,
      action: PayloadAction<{
        fileId: string;
        progress: number;
        speed?: string;
      }>
    ) => {
      const { fileId, progress, speed } = action.payload;
      const fileIndex = state.files.findIndex((file) => file.id === fileId);

      if (fileIndex !== -1) {
        state.files[fileIndex].progress = progress;

        // Update total progress
        const totalProgress = state.files.reduce(
          (acc, file) => acc + file.progress,
          0
        );
        state.totalProgress = totalProgress / state.files.length;

        if (speed) {
          state.uploadSpeed = speed;
        }

        // Save to localStorage
        uploadStorage.saveUploadState(state);
      }
    },
    updateFileStatus: (
      state,
      action: PayloadAction<{
        fileId: string;
        status: UploadFile["status"];
      }>
    ) => {
      const { fileId, status } = action.payload;
      const fileIndex = state.files.findIndex((file) => file.id === fileId);

      if (fileIndex !== -1) {
        state.files[fileIndex].status = status;

        // If file is completed, move to next file
        if (status === "completed" && fileIndex === state.currentFileIndex) {
          state.currentFileIndex = Math.min(
            state.currentFileIndex + 1,
            state.files.length - 1
          );
        }

        // Save to localStorage
        uploadStorage.saveUploadState(state);
      }
    },
    completeUpload: (state) => {
      state.isUploading = false;
      state.totalProgress = 100;
      // Keep modal open for a brief moment if it was persistent
      // The auto-hide logic in uploadService will handle closing it

      // Save to localStorage
      uploadStorage.saveUploadState(state);
    },
    failUpload: (
      state,
      action: PayloadAction<{ fileId?: string; error: string }>
    ) => {
      if (action.payload.fileId) {
        const fileIndex = state.files.findIndex(
          (file) => file.id === action.payload.fileId
        );
        if (fileIndex !== -1) {
          state.files[fileIndex].status = "failed";
        }
      }
      state.isUploading = false;

      // Save to localStorage
      uploadStorage.saveUploadState(state);
    },
    setShowModal: (state, action: PayloadAction<boolean>) => {
      state.showModal = action.payload;
      // If explicitly hiding modal, reset persist flag
      if (!action.payload) {
        state.persistModal = false;
      }

      // Save to localStorage
      uploadStorage.saveUploadState(state);
    },
    hideStatusNotification: (state) => {
      state.showStatusNotification = false;
      if (!state.isUploading) {
        state.files = [];
        state.persistModal = false;
        // Clear localStorage when hiding notification and not uploading
        uploadStorage.clearUploadState();
      } else {
        // Save to localStorage
        uploadStorage.saveUploadState(state);
      }
    },
    resetUpload: () => {
      // Clear localStorage when resetting
      uploadStorage.clearUploadState();
      return initialState;
    },
    markInterruptedUploadsAsFailed: (state) => {
      // Mark any uploading files as failed if they were interrupted
      state.files.forEach((file) => {
        if (file.status === "uploading" || file.status === "queued") {
          file.status = "failed";
        }
      });
      state.isUploading = false;

      // Save to localStorage
      uploadStorage.saveUploadState(state);
    },
  },
});

export const {
  restoreUploadState,
  startUpload,
  updateFileProgress,
  updateFileStatus,
  completeUpload,
  failUpload,
  setShowModal,
  hideStatusNotification,
  resetUpload,
  markInterruptedUploadsAsFailed,
} = uploadSlice.actions;

// Selectors
export const selectUploadState = (state: RootState) => state.upload;
export const selectIsUploading = (state: RootState) => state.upload.isUploading;
export const selectUploadFiles = (state: RootState) => state.upload.files;
export const selectCurrentFile = (state: RootState) => {
  const { files, currentFileIndex } = state.upload;
  return files[currentFileIndex] || null;
};
export const selectTotalProgress = (state: RootState) =>
  state.upload.totalProgress;
export const selectUploadSpeed = (state: RootState) => state.upload.uploadSpeed;
export const selectShowModal = (state: RootState) => state.upload.showModal;
export const selectShowStatusNotification = (state: RootState) =>
  state.upload.showStatusNotification;
export const selectPersistModal = (state: RootState) =>
  state.upload.persistModal;

export default uploadSlice.reducer;
