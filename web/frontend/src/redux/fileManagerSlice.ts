import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from './store';
import { ConvertItem } from '../types/files';

export interface FileManagerState {
  items: ConvertItem[];
  folders: string[];
  pagination: {
    page: number;
    limit: number;
    totalItems: number;
    totalPages: number;
  };
  isLoading: boolean;
  error: string | null;
  selectedBucket: string | null;
}

const initialState: FileManagerState = {
  items: [],
  folders: [],
  pagination: {
    page: 1,
    limit: 50,
    totalItems: 0,
    totalPages: 0,
  },
  isLoading: false,
  error: null,
  selectedBucket: null,
};

export const fileManagerSlice = createSlice({
  name: 'fileManager',
  initialState,
  reducers: {
    setPage: (state, action: PayloadAction<number>) => {
      state.pagination.page = action.payload;
    },
    setLimit: (state, action: PayloadAction<number>) => {
      state.pagination.page = 1;
      state.pagination.limit = action.payload;
    },
    setConvertItems: (state, action: PayloadAction<{
      items: ConvertItem[];
      folders: string[];
      totalItems?: number;
      totalPages?: number;
    }>) => {
      state.items = action.payload.items || [];
      state.folders = action.payload.folders || [];

      if (action.payload.totalItems !== undefined) {
        state.pagination.totalItems = action.payload.totalItems;
      }

      if (action.payload.totalPages !== undefined) {
        state.pagination.totalPages = action.payload.totalPages;
      }
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    updateFileStatus: (state, action: PayloadAction<{ id: number; status: number }>) => {
      const { id, status } = action.payload;
      const fileIndex = state.items.findIndex(item => item.id === id);

      if (fileIndex !== -1) {
        state.items[fileIndex].status = status;
      }
    },
    setSelectedBucket: (state, action: PayloadAction<string | null>) => {
      state.selectedBucket = action.payload;
    },
  },
});

export const {
  setPage,
  setLimit,
  setConvertItems,
  setLoading,
  setError,
  updateFileStatus,
  setSelectedBucket,
} = fileManagerSlice.actions;

// Selectors
export const selectConvertItems = (state: RootState) => state.fileManager.items;
export const selectFolders = (state: RootState) => state.fileManager.folders;
export const selectPagination = (state: RootState) => state.fileManager.pagination;
export const selectIsLoading = (state: RootState) => state.fileManager.isLoading;
export const selectError = (state: RootState) => state.fileManager.error;
export const selectSelectedBucket = (state: RootState) => state.fileManager.selectedBucket;

export default fileManagerSlice.reducer;
