import { configureStore } from "@reduxjs/toolkit";
import fileManagerReducer from "./fileManagerSlice";
import schedulerSlice from "@/redux/schedulerSlice.ts";
import uploadReducer from "./uploadSlice";

export const store = configureStore({
  reducer: {
    fileManager: fileManagerReducer,
    scheduler: schedulerSlice,
    upload: uploadReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
