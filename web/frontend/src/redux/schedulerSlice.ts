import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "./store";
import moment from "moment";
import { generateShortId } from "../utils/formatters.ts";
import { FileInfo, Fillers, Guide, Item, Schedule } from "@/types/schedule.ts";

export const REGULAR = "regular";
export const SPECIAL = "special";

interface ISchedulerState {
  schedulers: Schedule[];
  scheduler: Schedule;
  activeItem: Item | null;
  guide: Guide | null;
}

export const EmptyScheduler: Schedule = {
  id: 0,
  short_id: generateShortId(5),
  name: "",
  timezone: "America/Los_Angeles",
  icon: "",
  created_at: moment().format(),
  updated_at: moment().format(),
  output_url: "",
  network_interface: "",
  ads: {
    links: [],
    folders: [],
    files: [],
  },
  fillers: {
    folders: [],
    files: [],
    pre_filler: null,
  },
  regular_days: [
    {
      name: "Sun",
      date: "",
      items: [],
    },
    {
      name: "Mon",
      date: "",
      items: [],
    },
    {
      name: "Tue",
      date: "",
      items: [],
    },
    {
      name: "Wed",
      date: "",
      items: [],
    },
    {
      name: "Thu",
      date: "",
      items: [],
    },
    {
      name: "Fri",
      date: "",
      items: [],
    },
    {
      name: "Sat",
      date: "",
      items: [],
    },
  ],
  special_days: [],
  autosave: true,
  channels: [],
};

const initialState: ISchedulerState = {
  schedulers: [],
  scheduler: EmptyScheduler,
  activeItem: null,
  guide: null,
};

const normalizeDate = (date: string, isEnd: boolean) => {
  let newDate;
  let start = moment(date).utc();
  let dayNumber = start.day();
  if (dayNumber === 0 && start.format("HH:mm:ss") === "00:00:00" && isEnd) {
    newDate = moment()
      .add(1, "weeks")
      .utc()
      .startOf("week")
      .add(dayNumber, "days");
  } else {
    newDate = moment().utc().startOf("week").add(dayNumber, "days");
  }

  return newDate.format("YYYY-MM-DD") + "T" + start.format("HH:mm:ss") + "Z";
};

const sortItems = (scheduler: Schedule) => {
  const days: Item[][] = [];

  scheduler.regular_days.forEach((day) => {
    day.items.forEach((item) => {
      const dayNumber = moment(item.start).utc().day();
      if (!days.hasOwnProperty(dayNumber)) {
        days[dayNumber] = [];
      }
      days[dayNumber].push(item);
    });
  });

  scheduler.regular_days.map((day, index) => {
    if (days.hasOwnProperty(index)) {
      day.items = days[index];
    } else {
      day.items = [];
    }

    return day;
  });

  scheduler.regular_days.map((day) => {
    return {
      ...day,
      items: day.items.sort((a: Item, b: Item) => {
        return moment(a.start).diff(moment(b.start));
      }),
    };
  });

  scheduler.special_days.map((day) => {
    return {
      ...day,
      items: day.items.sort((a: Item, b: Item) => {
        return moment(a.start).diff(moment(b.start));
      }),
    };
  });

  return scheduler;
};

const schedulerSlice = createSlice({
  name: "scheduler",
  initialState,
  reducers: {
    setSchedulers(state, action: PayloadAction<{ schedulers: any[] }>) {
      state.schedulers = action.payload.schedulers;
    },
    openScheduler: (
      state,
      action: PayloadAction<{ schedule: Schedule; guide: Guide }>
    ) => {
      let schedule = action.payload.schedule;

      schedule.regular_days = schedule.regular_days.map((day) => {
        return {
          ...day,
          items: day.items.map((item) => {
            return {
              ...item,
              start: normalizeDate(item.start, false),
              end: normalizeDate(item.end, true),
            };
          }),
        };
      });

      schedule = sortItems(schedule);
      if (!schedule.ads) {
        schedule = {
          ...schedule,
          ads: {
            links: [],
            folders: [],
            files: [],
          },
        };
      }

      return {
        ...state,
        guide: action.payload.guide,
        scheduler: schedule,
      };
    },
    updateGuide(state, action: PayloadAction<Guide>) {
      state.guide = action.payload;
    },
    changeSchedulerDetail: (
      state,
      action: PayloadAction<{
        field: string;
        value: string | boolean | string[] | null;
      }>
    ) => {
      return {
        ...state,
        scheduler: {
          ...state.scheduler,
          [action.payload.field]: action.payload.value,
        },
      };
    },
    addNewItem(
      state,
      action: PayloadAction<{
        type: string;
        day: string;
        item: Item;
      }>
    ) {
      switch (action.payload.type) {
        case REGULAR:
          state.scheduler.regular_days.map((day) => {
            if (day.name === action.payload.day) {
              day.items.push(action.payload.item);
            }
          });
          break;
        case SPECIAL:
          state.scheduler.special_days = state.scheduler.special_days ?? [];
          const hasDay = state.scheduler.special_days.find(
            (day) => day.name === action.payload.day
          );
          if (!hasDay) {
            const newDay = {
              name: action.payload.day,
              date: action.payload.day,
              items: [],
            };
            state.scheduler.special_days.push(newDay);
          }

          state.scheduler.special_days.map((day) => {
            if (day.name === action.payload.day) {
              day.items.push(action.payload.item);
            }
          });
          break;
      }

      state.scheduler = sortItems(state.scheduler);
    },
    editItemTime(
      state,
      action: PayloadAction<{
        type: string;
        oldStart: string;
        item: {
          start: string;
          end: string;
        };
      }>
    ) {
      if (action.payload.type === SPECIAL) {
        state.scheduler.special_days = state.scheduler.special_days.map(
          (day) => {
            return {
              ...day,
              items: day.items.map((item: Item) => {
                if (item.start === action.payload.oldStart) {
                  return {
                    ...item,
                    start: action.payload.item.start,
                    end: action.payload.item.end,
                  };
                }

                return item;
              }),
            };
          }
        );
      } else {
        const itemsByDays: { items: Item[] }[] = [];
        for (let i = 0; i < 7; i++) {
          itemsByDays.push({ items: [] });
        }
        state.scheduler.regular_days.map((day) => {
          day.items.forEach((item: Item) => {
            if (item.start === action.payload.oldStart) {
              item = {
                ...item,
                start: action.payload.item.start,
                end: action.payload.item.end,
              };
            }
            const dayIndex = moment(item.start).utc().day();

            itemsByDays[dayIndex].items.push(item);
          });
        });

        state.scheduler.regular_days = state.scheduler.regular_days.map(
          (day, index) => {
            return {
              ...day,
              items: itemsByDays[index].items,
            };
          }
        );

        state.scheduler = sortItems(state.scheduler);
      }
    },
    editFolderItem(
      state,
      action: PayloadAction<{
        type: string;
        start: string;
        folders: string[];
        files: FileInfo[];
        fillers: Fillers;
      }>
    ) {
      const days =
        action.payload.type === REGULAR
          ? state.scheduler.regular_days
          : state.scheduler.special_days;

      const updatedDays = days.map((day) => {
        return {
          ...day,
          items: day.items.map((item: Item) => {
            if (item.start === action.payload.start) {
              return {
                ...item,
                folders: action.payload.folders,
                files: action.payload.files,
                fillers: action.payload.fillers,
              };
            }

            return item;
          }),
        };
      });

      switch (action.payload.type) {
        case REGULAR:
          state.scheduler.regular_days = updatedDays;
          break;
        default:
          state.scheduler.special_days = updatedDays;
      }
    },
    deleteItem(
      state,
      action: PayloadAction<{
        type: string;
        start: string;
      }>
    ) {
      const days =
        action.payload.type === REGULAR
          ? state.scheduler.regular_days
          : state.scheduler.special_days;

      const updatedDays = days.map((day) => {
        return {
          ...day,
          items: day.items.filter((item: Item) => {
            return item.start !== action.payload.start;
          }),
        };
      });
      state.activeItem = null;

      switch (action.payload.type) {
        case REGULAR:
          state.scheduler.regular_days = updatedDays;
          break;
        default:
          state.scheduler.special_days = updatedDays.filter(
            (day) => day.items.length > 0
          );
      }
    },
    selectItem(state, action: PayloadAction<Item | null>) {
      state.activeItem = action.payload;
    },
    deleteSpecialDay(state, action: PayloadAction<string>) {
      state.scheduler.special_days = state.scheduler.special_days.filter(
        (day) => day.name !== action.payload
      );
    },
    updateFillers(state, action: PayloadAction<Fillers>) {
      state.scheduler.fillers = action.payload;
    },
  },
});

export const selectSchedulers = (state: RootState) =>
  state.scheduler.schedulers;
export const selectSchedule = (state: RootState) => state.scheduler.scheduler;
export const selectGuide = (state: RootState) => state.scheduler.guide;
export const selectRegularDays = (state: RootState) =>
  state.scheduler.scheduler.regular_days;
export const selectSpecialDays = (state: RootState) =>
  state.scheduler.scheduler.special_days;
export const selectActiveItem = (state: RootState) =>
  state.scheduler.activeItem;
export const selectFillers = (state: RootState) =>
  state.scheduler.scheduler.fillers;

export const {
  setSchedulers,
  openScheduler,
  updateGuide,
  changeSchedulerDetail,
  addNewItem,
  editItemTime,
  editFolderItem,
  selectItem,
  deleteItem,
  deleteSpecialDay,
  updateFillers,
} = schedulerSlice.actions;

export default schedulerSlice.reducer;
