export interface ContentAnalytics {
  id: number;
  schedule_id: number;
  item_id?: number;
  content_name: string;
  content_path: string;
  rtp_output: string;
  played_at: string;
  duration: number;
  play_type: string;
}

export interface ContentAnalyticsListResult {
  items: ContentAnalytics[];
  total_items: number;
  total_pages: number;
  page: number;
  limit: number;
}

// For date range queries that now support pagination
export interface DateRangeAnalyticsResult {
  items: ContentAnalytics[];
  total_items: number;
  total_pages: number;
  page: number;
  limit: number;
}

export interface ContentAnalyticsSummary {
  content_name: string;
  content_path: string;
  total_plays: number;
  total_duration: number;
  last_played_at: string;
  output_channels: string[];
}

export interface ScheduleAnalyticsSummary {
  schedule_id: number;
  schedule_name: string;
  total_content_played: number;
  total_play_time: number;
  top_content: ContentAnalyticsSummary[];
  rtp_outputs: string[];
}
