// System statistics types
export interface SystemStats {
  timestamp: string;

  // Disk usage
  disk_total: number;
  disk_used: number;
  disk_free: number;
  disk_usage: number; // Percentage (0-100)

  // CPU usage
  cpu_usage: number; // Percentage (0-100)

  // Memory usage
  memory_total: number;
  memory_used: number;
  memory_free: number;
  memory_usage: number; // Percentage (0-100)

  // GPU information (only available if GPU is detected)
  gpu_available: boolean; // true if GPU is available
  gpu_model: string; // GPU model name
  gpu_mem_total: number; // Total GPU memory in bytes
  gpu_mem_used: number; // Used GPU memory in bytes
  gpu_mem_free: number; // Free GPU memory in bytes
  gpu_usage: number; // GPU utilization percentage (0-100)
  gpu_temp: number; // GPU temperature in Celsius

  // Alarm status
  disk_alarm: boolean;
  cpu_alarm: boolean;
  memory_alarm: boolean;
  gpu_alarm: boolean;
}

// Alarm thresholds
export interface AlarmThresholds {
  disk_threshold: number; // Percentage (0-100)
  cpu_threshold: number; // Percentage (0-100)
  memory_threshold: number; // Percentage (0-100)
  gpu_threshold: number; // Percentage (0-100)
}

// WebSocket message types
export interface WebSocketMessage<T> {
  type: string;
  payload: T;
}
