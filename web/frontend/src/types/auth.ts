// User represents a user in the system
export interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  status: string;
  created_at: string;
  updated_at: string;
}

// LoginInput represents the input for user login
export interface LoginInput {
  username: string;
  password: string;
}

// LoginResponse represents the response for a successful login
export interface LoginResponse {
  token: string;
  user: User;
}

// UserInput represents the input for creating or updating a user
export interface UserInput {
  username: string;
  email: string;
  password?: string;
  role: string;
  status?: string;
}
