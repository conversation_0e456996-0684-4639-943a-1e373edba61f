// Network interface represents a network interface configuration
export interface NetworkInterface {
  name: string;
  ip_address: string;
  netmask: string;
  gateway: string;
  dns_servers: string;
  is_active: boolean;
  is_wireless: boolean;
  mac_address: string;
}

// NetworkInterfaceInput represents the input for updating a network interface
export interface NetworkInterfaceInput {
  ip_address: string;
  netmask: string;
  gateway: string;
  dns_servers: string;
}
