export interface Recorder {
  id: number;
  name: string;
  input: string;
  rtp_url_id: number;
  duration: string;
  status: string;
  VCodec: string;
  ACodec: string;
  resolution: string;
  FPS: number;
  sampleRate: number;
  VBitrate: number;
  ABitrate: number;
  MaxVBitrate: number;
  network_interface?: string; // Optional network interface to use for receiving RTP stream
  source_ip?: string; // Optional source IP address for iptables filtering
  is_scheduled: boolean;
  scheduled_start_time?: string; // ISO 8601 datetime string
  created_at: string;
  updated_at: string;
}

export interface RecorderInput {
  name: string;
  input: string;
  duration: string;
  vcodec: string;
  acodec: string;
  resolution: string;
  fps: number;
  sample_rate: number;
  vbitrate: number;
  abitrate: number;
  max_vbitrate: number;
  network_interface?: string;
  source_ip?: string; // Optional source IP address for iptables filtering
  is_scheduled: boolean;
  scheduled_start_time?: string;
}

export interface RecorderListResult {
  items: Recorder[];
  totalItems: number;
  totalPages: number;
  page: number;
  limit: number;
}

export interface RecorderStatusUpdate {
  id: number;
  status:
    | "running"
    | "recording"
    | "transcoding"
    | "stopped"
    | "completed"
    | "failed"
    | "ts_sync_update";
}

export interface ServiceInfo {
  service_id: number;
  service_name: string;
  provider_name?: string;
  video_codec?: string;
  audio_codec?: string; // Legacy single audio codec (for backwards compatibility)
  video_resolution?: string;
  audio_channels?: number; // Legacy single audio channels (for backwards compatibility)
  audio_tracks?: AudioInfo[]; // Multiple audio tracks support
}

export interface VideoInfo {
  resolution: string;
  codec: string;
  bitrate?: string;
  framerate?: string;
}

export interface AudioInfo {
  channels: number;
  codec: string;
  sampleRate?: string;
  bitrate?: string;
}

export interface RecorderStatus {
  id: number;
  rtp_url: string;
  output_dir: string;
  elapsed_seconds: number;
  duration_seconds: number;
  is_active: boolean;
  is_joined: boolean; // Whether the recorder is joined (either active or in joined state)
  start_time: string;
  ts_sync: boolean;
  services: ServiceInfo[];
  video_info: VideoInfo;
  audio_info: AudioInfo;
}

export interface RtpUrl {
  id: number;
  url: string;
  recorder_id: number;
  created_at: string;
  updated_at: string;
}

export interface RtpSender {
  ip_address: string;
  last_seen: string;
  packet_count: number;
}
