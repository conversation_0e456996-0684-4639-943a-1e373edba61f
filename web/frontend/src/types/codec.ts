export interface CodecSettings {
  id: number;
  vcodec: string;
  acodec: string;
  resolution: string;
  fps: number;
  sample_rate: number;
  vbitrate: number;
  abitrate: number;
  max_vbitrate: number;
  // New dual audio fields
  dual_audio_mode: boolean;
  audio1_codec: string;
  audio1_bitrate: number;
  audio1_channels: number;
  audio2_codec: string;
  audio2_bitrate: number;
  audio2_channels: number;
}

export interface CodecSettingsInput {
  vcodec: string;
  acodec: string;
  resolution: string;
  fps: number;
  sample_rate: number;
  vbitrate: number;
  abitrate: number;
  max_vbitrate: number;
  // New dual audio fields
  dual_audio_mode: boolean;
  audio1_codec: string;
  audio1_bitrate: number;
  audio1_channels: number;
  audio2_codec: string;
  audio2_bitrate: number;
  audio2_channels: number;
}

// Video codec options
export const VIDEO_CODEC_OPTIONS = [
  // { value: 'h265', label: 'H.265 (HEVC)' },
  { value: "h264", label: "H.264 (AVC)" },
  // { value: 'mpeg2', label: 'MPEG-2' }
];

// Audio codec options (legacy single audio mode)
export const AUDIO_CODEC_OPTIONS = [
  { value: "ac3_downmix", label: "AC3 Downmix (High Quality)" },
  { value: "aac_downmix", label: "AAC Downmix (Web Compatible)" },
  { value: "mpeg1l2_downmix", label: "MPEG1L2 Downmix (DVB Compatible)" },
];

// Dual audio codec options for 5.1 surround track (Audio 1)
export const AUDIO1_CODEC_OPTIONS = [
  { value: "ac3_passthrough", label: "AC3 Passthrough (5.1 Surround)" },
  { value: "ac3_downmix", label: "AC3 Encoding (5.1 Surround)" },
  { value: "aac_downmix", label: "AAC Encoding (5.1 Surround)" },
];

// Dual audio codec options for stereo track (Audio 2)
export const AUDIO2_CODEC_OPTIONS = [
  { value: "ac3_passthrough", label: "AC3 Passthrough (Stereo)" },
  { value: "aac_downmix", label: "AAC Downmix (Stereo)" },
  { value: "mpeg1l2_downmix", label: "MPEG1L2 Downmix (Stereo)" },
  { value: "ac3_downmix", label: "AC3 Downmix (Stereo)" },
];

// Audio channel options
export const AUDIO_CHANNEL_OPTIONS = [
  { value: 2, label: "2 Channels (Stereo)" },
  { value: 6, label: "6 Channels (5.1 Surround)" },
];

// Resolution options
export const RESOLUTION_OPTIONS = [
  { value: "1920x1080p", label: "1920x1080p" },
  { value: "1920x1080i", label: "1920x1080i" },
  { value: "1280x720p", label: "1280x720p" },
  { value: "720x480p", label: "720x480p" },
  { value: "720x480i", label: "720x480i" },
];

// FPS options
export const FPS_OPTIONS = [
  { value: 60, label: "60" },
  { value: 59.94, label: "59.94" },
  { value: 30, label: "30" },
  { value: 29.97, label: "29.97" },
];

// Audio bitrate options
export const AUDIO_BITRATE_OPTIONS = [
  { value: 512, label: "512 kbps" },
  { value: 448, label: "448 kbps" },
  { value: 384, label: "384 kbps" },
  { value: 256, label: "256 kbps" },
  { value: 192, label: "192 kbps" },
  { value: 128, label: "128 kbps" },
];

// Sample rate options
export const SAMPLE_RATE_OPTIONS = [
  { value: 48000, label: "48000 Hz" },
  { value: 44100, label: "44100 Hz" },
  { value: 32000, label: "32000 Hz" },
];
