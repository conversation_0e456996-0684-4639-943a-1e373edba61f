export interface ScheduleListResult {
  items: Schedule[];
  total_items: number;
  total_pages: number;
  page: number;
  limit: number;
}

export interface Guide {
  id: number;
  schedule_id: number;
  elements: Element[];
  updated_at: string;
  scheduleName?: string;
}

export interface Element {
  start: string;
  end: string;
  title: string;
  description: string;
  type: string;
  connection: Connection;
  file: File;
}

export interface File {
  file_id: number;
  folder: string;
  filename: string;
  episode: string;
}

export interface Connection {
  type: string;
  link: string;
  port: string;
  mode: string;
  expire_date: string;
  expire_time: string;
}

export interface Schedule {
  id: number;
  name: string;
  icon: string;
  timezone: string;
  created_at: string;
  updated_at: string;
  short_id: string;
  autosave: boolean;
  output_url: string;
  network_interface: string;
  ads: Ads;
  channels: any[];
  regular_days: Day[];
  special_days: Day[];
  fillers: Fillers;
}

export interface Ads {
  links: AdLink[];
  folders: string[];
  files: FileInfo[];
}

export interface AdLink {
  url: string;
  channel: number;
}

export interface FileInfo {
  id: number | string;
  path: string;
}

export interface Day {
  name: string;
  date: string;
  items: Item[];
}

export interface Item {
  start: string;
  end: string;
  type: string;
  connection: string;
  link: string;
  name: string;
  description: string;
  port: string;
  mode: string;
  expire_date: string;
  expire_time: string;
  folders: string[];
  files: FileInfo[];
  fillers: Fillers;
}

export interface Fillers {
  folders: string[];
  files: FileInfo[];
  pre_filler: FileInfo|null|undefined;
}

export interface IFile {
  id: number;
  fileName: string;
  duration: number;
  episode: {
    String: string;
  };
  name: string;
  description: {
    String: string;
  };
}

export interface IFolder {
  folder: string;
  path: string;
  folders: IFolder[];
  files: IFile[];
}

export interface SchedulerStatus {
  id: number;
  short_id: string;
  is_active: boolean;
  rtp_synced: boolean;
}
