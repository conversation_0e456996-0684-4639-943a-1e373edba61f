export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'fatal';

export interface LogEntry {
  id: number;
  timestamp: string;
  level: LogLevel;
  message: string;
  source: string;
  user_id?: number;
  request_id?: string;
  metadata?: string;
  created_at: string;
}

export interface LogFilter {
  start_date?: string;
  end_date?: string;
  level?: LogLevel;
  search?: string;
  limit?: number;
  offset?: number;
}

export interface LogsResponse {
  logs: LogEntry[];
  total_count: number;
  limit: number;
  offset: number;
}

export interface LogExportRequest {
  format: 'csv' | 'json';
  filter: LogFilter;
}

export interface LogExportResponse {
  download_url: string;
  expires_at: string;
}

export interface LogWebSocketMessage {
  type: 'log_entry' | 'pong' | 'subscribed' | 'unsubscribed' | 'error';
  timestamp: number;
  log_entry?: LogEntry;
  message?: string;
  error?: string;
}

export const LOG_LEVELS: Record<LogLevel, { label: string; color: string; bgColor: string }> = {
  debug: { label: 'Debug', color: '#9ca3af', bgColor: '#374151' },
  info: { label: 'Info', color: '#60a5fa', bgColor: '#1e3a8a' },
  warn: { label: 'Warning', color: '#fbbf24', bgColor: '#92400e' },
  error: { label: 'Error', color: '#f87171', bgColor: '#991b1b' },
  fatal: { label: 'Fatal', color: '#ef4444', bgColor: '#7f1d1d' },
};

export const DEFAULT_LOG_FILTER: LogFilter = {
  limit: 50,
  offset: 0,
};
