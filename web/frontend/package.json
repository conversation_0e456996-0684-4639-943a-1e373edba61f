{"name": "frontend", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"@fullcalendar/interaction": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@heroui/react": "^2.7.8", "@nextui-org/react": "^2.6.11", "@reduxjs/toolkit": "^2.7.0", "axios": "^1.9.0", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "path-browserify": "^1.0.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-loading-indicators": "^1.0.1", "react-multi-date-picker": "^4.5.2", "react-player": "^2.16.0", "react-redux": "^9.2.0", "react-router-dom": "^7.5.3", "react-select": "^5.10.1", "react-toastify": "^11.0.5"}, "devDependencies": {"@types/node": "^22.15.3", "@types/path-browserify": "^1.0.3", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "typescript": "^5.8.3", "vite": "^6.3.3"}}