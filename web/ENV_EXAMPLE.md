# Environment Configuration for PostgreSQL

Create a `.env` file in the backend directory with the following variables:

```env
# PostgreSQL Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your_password_here
DB_NAME=showfer
DB_SSLMODE=disable
```

## Alternative Configuration Methods

### Option 1: Using Environment Variables

Set these variables in your shell before running the application:

```bash
export DB_HOST=localhost
export DB_PORT=5432
export DB_USER=postgres
export DB_PASSWORD=your_password_here
export DB_NAME=showfer
export DB_SSLMODE=disable
```

### Option 2: Using Docker Compose

If using Docker, set these in your `docker-compose.yml`:

```yaml
environment:
  - DB_HOST=postgres
  - DB_PORT=5432
  - DB_USER=postgres
  - DB_PASSWORD=your_password_here
  - DB_NAME=showfer
  - DB_SSLMODE=disable
```

## Database Setup

1. Install PostgreSQL on your system
2. Create a new database:
   ```sql
   CREATE DATABASE showfer;
   ```
3. Create a user (optional):
   ```sql
   CREATE USER showfer_user WITH PASSWORD 'your_password_here';
   GRANT ALL PRIVILEGES ON DATABASE showfer TO showfer_user;
   ```
