# sudo nano /etc/systemd/system/traffiq-backend.service

[Unit]
Description=Traffiq Backend Service
After=network.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/home/<USER>/traffiq/web/backend
ExecStart=/home/<USER>/traffiq/web/backend/backend
Restart=always
Environment=ENV_VAR_NAME=value
Environment=PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
Environment=LD_LIBRARY_PATH=/usr/local/lib:/usr/lib
Environment=HOME=/root
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target