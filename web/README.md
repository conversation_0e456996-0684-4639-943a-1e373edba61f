# Showfer Web

A web-based application for managing media content, particularly focused on capturing RTP streams and converting them to MP4 files.

## Features

- **Input Feeds**: Capture RTP streams and convert them to MP4 files
- **Real-time Status Updates**: Monitor recording progress with WebSocket updates
- **RTP URL Management**: Store and reuse RTP URLs
- **Responsive UI**: Modern web interface built with React

## Technology Stack

### Backend
- Go with Gin web framework
- SQLite database
- GStreamer for media processing
- WebSockets for real-time updates

### Frontend
- React with TypeScript
- Vite for development and building
- Axios for API requests
- WebSockets for real-time updates

## Prerequisites

- Go 1.16 or higher
- Node.js 14 or higher
- npm or yarn
- GStreamer and required plugins

### GStreamer Installation

For Ubuntu/Debian:
```bash
sudo apt-get update
sudo apt-get install -y gstreamer1.0-tools gstreamer1.0-plugins-base gstreamer1.0-plugins-good gstreamer1.0-plugins-bad gstreamer1.0-plugins-ugly
```

For hardware acceleration:
```bash
# For NVIDIA GPU support
sudo apt-get install -y gstreamer1.0-vaapi

# For Intel GPU support
sudo apt-get install -y gstreamer1.0-vaapi
```

## Project Structure

```
web/
├── backend/             # Go backend
│   ├── api/             # API handlers
│   ├── config/          # Configuration
│   ├── middleware/      # Middleware
│   ├── models/          # Data models
│   ├── repository/      # Database repositories
│   ├── service/         # Business logic
│   └── utils/           # Utility functions
├── frontend/            # React frontend
│   ├── public/          # Static assets
│   └── src/             # Source code
│       ├── api/         # API clients
│       ├── components/  # React components
│       ├── hooks/       # Custom hooks
│       ├── pages/       # Page components
│       ├── types/       # TypeScript types
│       └── utils/       # Utility functions
├── data/                # Application data
│   └── recordings/      # Recorded media files
└── logs/                # Application logs
```

## Getting Started

### Running the Application

1. Clone the repository
2. Run the application using the provided script:

```bash
cd web
./run.sh
```

This will start both the backend server and the frontend development server.

- Backend: http://localhost:8080
- Frontend: http://localhost:5173

### Building for Production

#### Backend

```bash
cd web/backend
go build -o showfer-web
```

#### Frontend

```bash
cd web/frontend
npm run build
```

The built frontend will be in the `web/frontend/dist` directory, which can be served by the backend or a web server like Nginx.

## API Endpoints

### Recorders

- `GET /api/v1/recorders` - Get all recorders with pagination
- `GET /api/v1/recorders/:id` - Get a recorder by ID
- `POST /api/v1/recorders` - Create a new recorder
- `PUT /api/v1/recorders/:id` - Update a recorder
- `DELETE /api/v1/recorders/:id` - Delete a recorder
- `POST /api/v1/recorders/:id/start` - Start a recorder
- `POST /api/v1/recorders/:id/stop` - Stop a recorder
- `GET /api/v1/recorders/status` - Get recorder status

### RTP URLs

- `GET /api/v1/rtp-urls` - Get all RTP URLs

### WebSocket

- `GET /ws` - WebSocket endpoint for real-time updates

## License

This project is licensed under the MIT License - see the LICENSE file for details.
