<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Traffiq API Documentation</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        line-height: 1.6;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f8f9fa;
      }
      .container {
        background: white;
        padding: 30px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      h1 {
        color: #2c3e50;
        border-bottom: 3px solid #3498db;
        padding-bottom: 10px;
      }
      .api-link {
        display: inline-block;
        margin: 10px 15px 10px 0;
        padding: 12px 24px;
        background-color: #3498db;
        color: white;
        text-decoration: none;
        border-radius: 5px;
        transition: background-color 0.3s;
      }
      .api-link:hover {
        background-color: #2980b9;
      }
      .spec-link {
        background-color: #27ae60;
      }
      .spec-link:hover {
        background-color: #229954;
      }
      .description {
        background-color: #ecf0f1;
        padding: 15px;
        border-radius: 5px;
        margin: 20px 0;
      }
      .endpoints {
        margin-top: 30px;
      }
      .endpoint {
        background-color: #f8f9fa;
        padding: 10px;
        margin: 5px 0;
        border-left: 4px solid #3498db;
      }
      code {
        background-color: #e8e8e8;
        padding: 2px 6px;
        border-radius: 3px;
        font-family: "Courier New", monospace;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🚀 Traffiq API Documentation</h1>

      <div class="description">
        <p>
          Welcome to the Traffiq Channel API documentation. This API provides
          comprehensive management capabilities for schedules, channels, and
          media content.
        </p>
      </div>

      <h2>📚 Documentation Access</h2>
      <div>
        <a href="/swagger/index.html" class="api-link">Swagger UI</a>
        <a href="/api-docs/index.html" class="api-link">Alternative API Docs</a>
        <a href="/schedule_api_doc.yaml" class="api-link spec-link"
          >OpenAPI Spec (YAML)</a
        >
      </div>

      <h2>🎯 API Base URL</h2>
      <div class="endpoint">
        <code>http://localhost:8080/api/v1</code>
      </div>

      <h2>🔗 Key Endpoints</h2>
      <div class="endpoints">
        <div class="endpoint">
          <strong>GET</strong> <code>/schedule</code> - Retrieve paginated list
          of schedules
        </div>
        <div class="endpoint">
          <strong>GET</strong> <code>/schedule/status</code> - Get current
          schedule statuses
        </div>
        <div class="endpoint">
          <strong>GET</strong> <code>/schedule/{id}</code> - Get specific
          channel by ID
        </div>
        <div class="endpoint">
          <strong>PUT</strong> <code>/schedule/{id}</code> - Update schedule by
          ID
        </div>
        <div class="endpoint">
          <strong>GET</strong> <code>/schedule/{id}/guide</code> - Get schedule
          guide
        </div>
        <div class="endpoint">
          <strong>POST</strong> <code>/schedule/{id}/guide</code> - Force
          regenerate guide
        </div>
        <div class="endpoint">
          <strong>GET</strong> <code>/schedule/files</code> - Get media files
          and folders
        </div>
      </div>

      <h2>🔐 Authentication</h2>
      <div class="description">
        <p>
          Most endpoints require Bearer token authentication. Include the token
          in the Authorization header:
        </p>
        <code>Authorization: Bearer &lt;your_token&gt;</code>
      </div>

      <h2>ℹ️ Additional Information</h2>
      <div class="description">
        <p>
          <strong>Version:</strong> 1.0.0<br />
          <strong>OpenAPI Version:</strong> 3.1.0<br />
          <strong>Server:</strong> Running on port 8080
        </p>
      </div>
    </div>
  </body>
</html>
