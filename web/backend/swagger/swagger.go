package swagger

import (
	"net/http"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// SetupSwaggerRoutes sets up Swagger UI routes
func SetupSwaggerRoutes(router *gin.Engine) {
	// Serve the API documentation landing page
	router.GET("/docs", func(c *gin.Context) {
		c.File("./api_docs.html")
	})
	
	router.GET("/api/docs", func(c *gin.Context) {
		c.File("./api_docs.html")
	})
	
	// Serve the OpenAPI spec file directly
	router.GET("/schedule_api_doc.yaml", func(c *gin.Context) {
		c.File("./schedule_api_doc.yaml")
	})
	
	// Serve OpenAPI spec at alternative paths
	router.GET("/api/docs/openapi.yaml", func(c *gin.Context) {
		c.File("./schedule_api_doc.yaml")
	})
	
	router.GET("/swagger.yaml", func(c *gin.Context) {
		c.File("./schedule_api_doc.yaml")
	})
	
	// Custom Swagger UI configuration - simplified to use only supported fields
	swaggerConfig := &ginSwagger.Config{
		URL:                      "/schedule_api_doc.yaml",
		DocExpansion:             "list",
		InstanceName:             "swagger",
		DeepLinking:              true,
		PersistAuthorization:     true,
		DefaultModelsExpandDepth: 1,
	}
	
	// Main Swagger UI endpoint
	router.GET("/swagger/*any", ginSwagger.CustomWrapHandler(swaggerConfig, swaggerFiles.Handler))
	
	// Alternative endpoint for API docs
	router.GET("/api-docs/*any", ginSwagger.CustomWrapHandler(swaggerConfig, swaggerFiles.Handler))
	
	// Redirect root swagger path to swagger UI
	router.GET("/swagger", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "/swagger/index.html")
	})
	
	router.GET("/api-docs", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "/api-docs/index.html")
	})
} 