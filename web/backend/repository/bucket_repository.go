package repository

import (
	"context"
	"fmt"
	"showfer-web/models"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// BucketRepository handles bucket-related database operations
type BucketRepository struct {
	collection *mongo.Collection
}

// NewBucketRepository creates a new BucketRepository
func NewBucketRepository(db *mongo.Database) *BucketRepository {
	return &BucketRepository{
		collection: db.Collection("buckets"),
	}
}

// GetAllBuckets retrieves all buckets
func (r *BucketRepository) GetAllBuckets(ctx context.Context) ([]models.Bucket, error) {
	cursor, err := r.collection.Find(ctx, bson.M{})
	if err != nil {
		return nil, fmt.Errorf("failed to find buckets: %w", err)
	}
	defer cursor.Close(ctx)

	var buckets []models.Bucket
	if err = cursor.All(ctx, &buckets); err != nil {
		return nil, fmt.Errorf("failed to decode buckets: %w", err)
	}

	return buckets, nil
}

// GetBucketByID retrieves a bucket by its ID
func (r *BucketRepository) GetBucketByID(ctx context.Context, id primitive.ObjectID) (*models.Bucket, error) {
	var bucket models.Bucket
	err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&bucket)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("bucket not found")
		}
		return nil, fmt.Errorf("failed to find bucket: %w", err)
	}

	return &bucket, nil
}

// GetBucketByName retrieves a bucket by its bucket name
func (r *BucketRepository) GetBucketByName(ctx context.Context, bucketName string) (*models.Bucket, error) {
	var bucket models.Bucket
	err := r.collection.FindOne(ctx, bson.M{"bucketName": bucketName}).Decode(&bucket)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("bucket not found")
		}
		return nil, fmt.Errorf("failed to find bucket: %w", err)
	}

	return &bucket, nil
}

// CreateBucket creates a new bucket
func (r *BucketRepository) CreateBucket(ctx context.Context, input models.BucketCreateInput) (*models.Bucket, error) {
	// Check if bucket name already exists
	existing, _ := r.GetBucketByName(ctx, input.BucketName)
	if existing != nil {
		return nil, fmt.Errorf("bucket with name '%s' already exists", input.BucketName)
	}

	// If this is set as default, unset other defaults
	if input.Default {
		_, err := r.collection.UpdateMany(ctx,
			bson.M{"default": true},
			bson.M{"$set": bson.M{"default": false, "updatedAt": time.Now()}})
		if err != nil {
			return nil, fmt.Errorf("failed to unset other default buckets: %w", err)
		}
	}

	bucket := models.Bucket{
		ID:         primitive.NewObjectID(),
		Title:      input.Title,
		BucketName: input.BucketName,
		IsShowAll:  input.IsShowAll,
		Default:    input.Default,
		User:       input.User,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
		Version:    0,
	}

	_, err := r.collection.InsertOne(ctx, bucket)
	if err != nil {
		return nil, fmt.Errorf("failed to create bucket: %w", err)
	}

	return &bucket, nil
}

// UpdateBucket updates an existing bucket
func (r *BucketRepository) UpdateBucket(ctx context.Context, id primitive.ObjectID, input models.BucketUpdateInput) (*models.Bucket, error) {
	updateDoc := bson.M{
		"updatedAt": time.Now(),
	}

	if input.Title != nil {
		updateDoc["title"] = *input.Title
	}
	if input.BucketName != nil {
		// Check if new bucket name already exists (excluding current bucket)
		existing, _ := r.GetBucketByName(ctx, *input.BucketName)
		if existing != nil && existing.ID != id {
			return nil, fmt.Errorf("bucket with name '%s' already exists", *input.BucketName)
		}
		updateDoc["bucketName"] = *input.BucketName
	}
	if input.IsShowAll != nil {
		updateDoc["isShowAll"] = *input.IsShowAll
	}
	if input.Default != nil {
		updateDoc["default"] = *input.Default

		// If setting as default, unset other defaults
		if *input.Default {
			_, err := r.collection.UpdateMany(ctx,
				bson.M{"_id": bson.M{"$ne": id}, "default": true},
				bson.M{"$set": bson.M{"default": false, "updatedAt": time.Now()}})
			if err != nil {
				return nil, fmt.Errorf("failed to unset other default buckets: %w", err)
			}
		}
	}
	if input.User != nil {
		updateDoc["user"] = input.User
	}

	_, err := r.collection.UpdateOne(ctx,
		bson.M{"_id": id},
		bson.M{"$set": updateDoc})
	if err != nil {
		return nil, fmt.Errorf("failed to update bucket: %w", err)
	}

	return r.GetBucketByID(ctx, id)
}

// DeleteBucket deletes a bucket by its ID
func (r *BucketRepository) DeleteBucket(ctx context.Context, id primitive.ObjectID) error {
	result, err := r.collection.DeleteOne(ctx, bson.M{"_id": id})
	if err != nil {
		return fmt.Errorf("failed to delete bucket: %w", err)
	}

	if result.DeletedCount == 0 {
		return fmt.Errorf("bucket not found")
	}

	return nil
}

// GetBucketsByUser retrieves buckets for a specific user
func (r *BucketRepository) GetBucketsByUser(ctx context.Context, userID primitive.ObjectID) ([]models.Bucket, error) {
	cursor, err := r.collection.Find(ctx, bson.M{"user": userID})
	if err != nil {
		return nil, fmt.Errorf("failed to find buckets for user: %w", err)
	}
	defer cursor.Close(ctx)

	var buckets []models.Bucket
	if err = cursor.All(ctx, &buckets); err != nil {
		return nil, fmt.Errorf("failed to decode buckets: %w", err)
	}

	return buckets, nil
}

// GetDefaultBucket retrieves the default bucket
func (r *BucketRepository) GetDefaultBucket(ctx context.Context) (*models.Bucket, error) {
	var bucket models.Bucket
	err := r.collection.FindOne(ctx, bson.M{"default": true}).Decode(&bucket)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("no default bucket found")
		}
		return nil, fmt.Errorf("failed to find default bucket: %w", err)
	}

	return &bucket, nil
}

// TestConnection tests the MongoDB connection and returns collection info
func (r *BucketRepository) TestConnection(ctx context.Context) (int64, error) {
	count, err := r.collection.CountDocuments(ctx, bson.M{})
	if err != nil {
		return 0, fmt.Errorf("failed to count documents: %w", err)
	}
	return count, nil
}
