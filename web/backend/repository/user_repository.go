package repository

import (
	"database/sql"
	"errors"
	"showfer-web/models"
	"showfer-web/service/logger"
	"time"

	"golang.org/x/crypto/bcrypt"
)

// UserRepository handles database operations for users
type UserRepository struct {
	db *sql.DB
}

// NewUserRepository creates a new UserRepository
func NewUserRepository(db *sql.DB) *UserRepository {
	return &UserRepository{
		db: db,
	}
}

// GetUserByID retrieves a user by ID
func (r *UserRepository) GetUserByID(id int) (*models.User, error) {
	row := r.db.QueryRow(`
		SELECT id, username, email, password, role, status, created_at, updated_at
		FROM users
		WHERE id = $1
	`, id)

	var user models.User
	var createdAt, updatedAt string

	err := row.Scan(
		&user.ID,
		&user.Username,
		&user.Email,
		&user.Password,
		&user.Role,
		&user.Status,
		&createdAt,
		&updatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.New("user not found")
		}
		logger.Error("Failed to get user by ID: %v", err)
		return nil, err
	}

	// Parse timestamps
	user.CreatedAt, _ = time.Parse(time.RFC3339, createdAt)
	user.UpdatedAt, _ = time.Parse(time.RFC3339, updatedAt)

	return &user, nil
}

// GetUserByUsername retrieves a user by username
func (r *UserRepository) GetUserByUsername(username string) (*models.User, error) {
	row := r.db.QueryRow(`
		SELECT id, username, email, password, role, status, created_at, updated_at
		FROM users
		WHERE username = $1
	`, username)

	var user models.User
	var createdAt, updatedAt string

	err := row.Scan(
		&user.ID,
		&user.Username,
		&user.Email,
		&user.Password,
		&user.Role,
		&user.Status,
		&createdAt,
		&updatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.New("user not found")
		}
		logger.Error("Failed to get user by username: %v", err)
		return nil, err
	}

	// Parse timestamps
	user.CreatedAt, _ = time.Parse(time.RFC3339, createdAt)
	user.UpdatedAt, _ = time.Parse(time.RFC3339, updatedAt)

	return &user, nil
}

// CreateUser creates a new user
func (r *UserRepository) CreateUser(input models.UserInput) (*models.User, error) {
	// Check if username already exists
	var count int
	err := r.db.QueryRow("SELECT COUNT(*) FROM users WHERE username = $1", input.Username).Scan(&count)
	if err != nil {
		logger.Error("Failed to check username uniqueness: %v", err)
		return nil, err
	}
	if count > 0 {
		return nil, errors.New("username already exists")
	}

	// Check if email already exists
	err = r.db.QueryRow("SELECT COUNT(*) FROM users WHERE email = $1", input.Email).Scan(&count)
	if err != nil {
		logger.Error("Failed to check email uniqueness: %v", err)
		return nil, err
	}
	if count > 0 {
		return nil, errors.New("email already exists")
	}

	// Hash the password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(input.Password), bcrypt.DefaultCost)
	if err != nil {
		logger.Error("Failed to hash password: %v", err)
		return nil, err
	}

	// Get current time
	now := time.Now().UTC()
	nowStr := now.Format(time.RFC3339)

	// Set default status if not provided
	if input.Status == "" {
		input.Status = "pending"
	}

	// Insert the user
	var id int64
	err = r.db.QueryRow(`
		INSERT INTO users (username, email, password, role, status, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7)
		RETURNING id
	`, input.Username, input.Email, string(hashedPassword), input.Role, input.Status, nowStr, nowStr).Scan(&id)

	if err != nil {
		logger.Error("Failed to create user: %v", err)
		return nil, err
	}

	// Return the created user
	user := &models.User{
		ID:        int(id),
		Username:  input.Username,
		Email:     input.Email,
		Role:      input.Role,
		Status:    input.Status,
		CreatedAt: now,
		UpdatedAt: now,
	}

	return user, nil
}

// UpdateUser updates an existing user
func (r *UserRepository) UpdateUser(id int, input models.UserUpdateInput) (*models.User, error) {
	// Check if user exists
	existingUser, err := r.GetUserByID(id)
	if err != nil {
		return nil, err
	}

	// Protect admin user from modifications
	if existingUser.Username == "admin" {
		// Check if trying to change role or status
		if input.Role != "admin" || input.Status != "approved" {
			return nil, errors.New("cannot modify admin user role or status")
		}

		// Admin user can only update their own email or password, not username
		if input.Username != "admin" {
			return nil, errors.New("cannot change admin username")
		}
	}

	// Check if username is being changed and if it's already taken
	if input.Username != existingUser.Username {
		var count int
		err := r.db.QueryRow("SELECT COUNT(*) FROM users WHERE username = $1 AND id != $2", input.Username, id).Scan(&count)
		if err != nil {
			logger.Error("Failed to check username uniqueness: %v", err)
			return nil, err
		}
		if count > 0 {
			return nil, errors.New("username already exists")
		}
	}

	// Check if email is being changed and if it's already taken
	if input.Email != existingUser.Email {
		var count int
		err := r.db.QueryRow("SELECT COUNT(*) FROM users WHERE email = $1 AND id != $2", input.Email, id).Scan(&count)
		if err != nil {
			logger.Error("Failed to check email uniqueness: %v", err)
			return nil, err
		}
		if count > 0 {
			return nil, errors.New("email already exists")
		}
	}

	// Get current time
	now := time.Now().UTC()
	nowStr := now.Format(time.RFC3339)

	// Update the user
	var result sql.Result
	if input.Password != "" {
		// Hash the new password
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(input.Password), bcrypt.DefaultCost)
		if err != nil {
			logger.Error("Failed to hash password: %v", err)
			return nil, err
		}

		// Update with new password
		result, err = r.db.Exec(`
			UPDATE users
			SET username = $1, email = $2, password = $3, role = $4, status = $5, updated_at = $6
			WHERE id = $7
		`, input.Username, input.Email, string(hashedPassword), input.Role, input.Status, nowStr, id)
	} else {
		// Update without changing password
		result, err = r.db.Exec(`
			UPDATE users
			SET username = $1, email = $2, role = $3, status = $4, updated_at = $5
			WHERE id = $6
		`, input.Username, input.Email, input.Role, input.Status, nowStr, id)
	}

	if err != nil {
		logger.Error("Failed to update user: %v", err)
		return nil, err
	}

	// Check if any rows were affected
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logger.Error("Failed to get rows affected: %v", err)
		return nil, err
	}
	if rowsAffected == 0 {
		return nil, errors.New("user not found")
	}

	// Return the updated user
	user := &models.User{
		ID:        id,
		Username:  input.Username,
		Email:     input.Email,
		Role:      input.Role,
		Status:    input.Status,
		CreatedAt: existingUser.CreatedAt,
		UpdatedAt: now,
	}

	return user, nil
}

// DeleteUser deletes a user
func (r *UserRepository) DeleteUser(id int) error {
	// Check if trying to delete admin user
	var username string
	err := r.db.QueryRow("SELECT username FROM users WHERE id = $1", id).Scan(&username)
	if err != nil {
		if err == sql.ErrNoRows {
			return errors.New("user not found")
		}
		logger.Error("Failed to get username for user ID %d: %v", id, err)
		return err
	}

	// Protect admin user from deletion
	if username == "admin" {
		return errors.New("cannot delete admin user")
	}

	result, err := r.db.Exec("DELETE FROM users WHERE id = $1", id)
	if err != nil {
		logger.Error("Failed to delete user: %v", err)
		return err
	}

	// Check if any rows were affected
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		logger.Error("Failed to get rows affected: %v", err)
		return err
	}
	if rowsAffected == 0 {
		return errors.New("user not found")
	}

	return nil
}

// VerifyPassword checks if the provided password matches the stored hash
func (r *UserRepository) VerifyPassword(username, password string) (*models.User, error) {
	// Get the user
	user, err := r.GetUserByUsername(username)
	if err != nil {
		return nil, err
	}

	// Check if user is approved
	if user.Status != "approved" {
		return nil, errors.New("user account is not approved")
	}

	// Compare the provided password with the stored hash
	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password))
	if err != nil {
		return nil, errors.New("invalid password")
	}

	return user, nil
}

// GetUsersByStatus retrieves users by status
func (r *UserRepository) GetUsersByStatus(status string) ([]models.User, error) {
	rows, err := r.db.Query(`
		SELECT id, username, email, password, role, status, created_at, updated_at
		FROM users
		WHERE status = $1
	`, status)
	if err != nil {
		logger.Error("Failed to get users by status: %v", err)
		return nil, err
	}
	defer rows.Close()

	users := []models.User{}
	for rows.Next() {
		var user models.User
		var createdAt, updatedAt string

		err := rows.Scan(
			&user.ID,
			&user.Username,
			&user.Email,
			&user.Password,
			&user.Role,
			&user.Status,
			&createdAt,
			&updatedAt,
		)
		if err != nil {
			logger.Error("Failed to scan user row: %v", err)
			return nil, err
		}

		// Parse timestamps
		user.CreatedAt, _ = time.Parse(time.RFC3339, createdAt)
		user.UpdatedAt, _ = time.Parse(time.RFC3339, updatedAt)

		users = append(users, user)
	}

	if err = rows.Err(); err != nil {
		logger.Error("Error iterating user rows: %v", err)
		return nil, err
	}

	return users, nil
}

// GetAllUsers retrieves all users
func (r *UserRepository) GetAllUsers() ([]models.User, error) {
	rows, err := r.db.Query(`
		SELECT id, username, email, password, role, status, created_at, updated_at
		FROM users
	`)
	if err != nil {
		logger.Error("Failed to get all users: %v", err)
		return nil, err
	}
	defer rows.Close()

	users := []models.User{}
	for rows.Next() {
		var user models.User
		var createdAt, updatedAt string

		err := rows.Scan(
			&user.ID,
			&user.Username,
			&user.Email,
			&user.Password,
			&user.Role,
			&user.Status,
			&createdAt,
			&updatedAt,
		)
		if err != nil {
			logger.Error("Failed to scan user row: %v", err)
			return nil, err
		}

		// Parse timestamps
		user.CreatedAt, _ = time.Parse(time.RFC3339, createdAt)
		user.UpdatedAt, _ = time.Parse(time.RFC3339, updatedAt)

		users = append(users, user)
	}

	if err = rows.Err(); err != nil {
		logger.Error("Error iterating user rows: %v", err)
		return nil, err
	}

	return users, nil
}
