package repository

import (
	"context"
	"fmt"
	"showfer-web/config"
	"showfer-web/models"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// MongoExampleRepository demonstrates how to use MongoDB alongside PostgreSQL
type MongoExampleRepository struct {
	collection *mongo.Collection
}

// NewMongoExampleRepository creates a new MongoDB example repository
func NewMongoExampleRepository() *MongoExampleRepository {
	db := config.GetMongoDatabase()
	return &MongoExampleRepository{
		collection: db.Collection("examples"),
	}
}

// Create inserts a new document into MongoDB
func (r *MongoExampleRepository) Create(ctx context.Context, example *models.MongoExample) error {
	example.ID = primitive.NewObjectID()
	example.CreatedAt = time.Now()
	example.UpdatedAt = time.Now()

	_, err := r.collection.InsertOne(ctx, example)
	if err != nil {
		return fmt.Errorf("failed to create example: %w", err)
	}

	return nil
}

// GetByID retrieves a document by its ID
func (r *MongoExampleRepository) GetByID(ctx context.Context, id string) (*models.MongoExample, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, fmt.Errorf("invalid object ID: %w", err)
	}

	var example models.MongoExample
	err = r.collection.FindOne(ctx, bson.M{"_id": objectID}).Decode(&example)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("example not found")
		}
		return nil, fmt.Errorf("failed to get example: %w", err)
	}

	return &example, nil
}

// Update updates an existing document
func (r *MongoExampleRepository) Update(ctx context.Context, id string, example *models.MongoExample) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid object ID: %w", err)
	}

	example.UpdatedAt = time.Now()
	update := bson.M{
		"$set": bson.M{
			"title":       example.Title,
			"description": example.Description,
			"tags":        example.Tags,
			"metadata":    example.Metadata,
			"is_active":   example.IsActive,
			"updated_at":  example.UpdatedAt,
		},
	}

	result, err := r.collection.UpdateOne(ctx, bson.M{"_id": objectID}, update)
	if err != nil {
		return fmt.Errorf("failed to update example: %w", err)
	}

	if result.MatchedCount == 0 {
		return fmt.Errorf("example not found")
	}

	return nil
}

// Delete removes a document by its ID
func (r *MongoExampleRepository) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return fmt.Errorf("invalid object ID: %w", err)
	}

	result, err := r.collection.DeleteOne(ctx, bson.M{"_id": objectID})
	if err != nil {
		return fmt.Errorf("failed to delete example: %w", err)
	}

	if result.DeletedCount == 0 {
		return fmt.Errorf("example not found")
	}

	return nil
}

// List retrieves multiple documents with pagination
func (r *MongoExampleRepository) List(ctx context.Context, limit, offset int64) ([]*models.MongoExample, error) {
	opts := options.Find().
		SetLimit(limit).
		SetSkip(offset).
		SetSort(bson.D{{Key: "created_at", Value: -1}})

	cursor, err := r.collection.Find(ctx, bson.M{}, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to find examples: %w", err)
	}
	defer cursor.Close(ctx)

	var examples []*models.MongoExample
	for cursor.Next(ctx) {
		var example models.MongoExample
		if err := cursor.Decode(&example); err != nil {
			return nil, fmt.Errorf("failed to decode example: %w", err)
		}
		examples = append(examples, &example)
	}

	if err := cursor.Err(); err != nil {
		return nil, fmt.Errorf("cursor error: %w", err)
	}

	return examples, nil
}

// FindByTags finds documents that contain any of the specified tags
func (r *MongoExampleRepository) FindByTags(ctx context.Context, tags []string) ([]*models.MongoExample, error) {
	filter := bson.M{
		"tags": bson.M{
			"$in": tags,
		},
	}

	cursor, err := r.collection.Find(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to find examples by tags: %w", err)
	}
	defer cursor.Close(ctx)

	var examples []*models.MongoExample
	for cursor.Next(ctx) {
		var example models.MongoExample
		if err := cursor.Decode(&example); err != nil {
			return nil, fmt.Errorf("failed to decode example: %w", err)
		}
		examples = append(examples, &example)
	}

	return examples, nil
}

// Count returns the total number of documents
func (r *MongoExampleRepository) Count(ctx context.Context) (int64, error) {
	count, err := r.collection.CountDocuments(ctx, bson.M{})
	if err != nil {
		return 0, fmt.Errorf("failed to count examples: %w", err)
	}
	return count, nil
}

// ContentMetadataRepository demonstrates storing rich metadata for PostgreSQL content items
type ContentMetadataRepository struct {
	collection *mongo.Collection
}

// NewContentMetadataRepository creates a new content metadata repository
func NewContentMetadataRepository() *ContentMetadataRepository {
	db := config.GetMongoDatabase()
	return &ContentMetadataRepository{
		collection: db.Collection("content_metadata"),
	}
}

// CreateMetadata creates metadata for a content item
func (r *ContentMetadataRepository) CreateMetadata(ctx context.Context, metadata *models.ContentMetadata) error {
	metadata.ID = primitive.NewObjectID()
	metadata.CreatedAt = time.Now()
	metadata.UpdatedAt = time.Now()

	_, err := r.collection.InsertOne(ctx, metadata)
	if err != nil {
		return fmt.Errorf("failed to create content metadata: %w", err)
	}

	return nil
}

// GetMetadataByConvertItemID retrieves metadata for a specific convert item
func (r *ContentMetadataRepository) GetMetadataByConvertItemID(ctx context.Context, convertItemID int64) (*models.ContentMetadata, error) {
	var metadata models.ContentMetadata
	err := r.collection.FindOne(ctx, bson.M{"convert_item_id": convertItemID}).Decode(&metadata)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("metadata not found for convert item %d", convertItemID)
		}
		return nil, fmt.Errorf("failed to get metadata: %w", err)
	}

	return &metadata, nil
}

// UpdateAnalytics updates the analytics data for content
func (r *ContentMetadataRepository) UpdateAnalytics(ctx context.Context, convertItemID int64, analytics models.MongoContentAnalytics) error {
	update := bson.M{
		"$set": bson.M{
			"analytics.view_count":       analytics.ViewCount,
			"analytics.last_viewed":      analytics.LastViewed,
			"analytics.total_duration":   analytics.TotalDuration,
			"analytics.popular_segments": analytics.PopularSegments,
			"analytics.user_ratings":     analytics.UserRatings,
			"updated_at":                 time.Now(),
		},
	}

	result, err := r.collection.UpdateOne(ctx, bson.M{"convert_item_id": convertItemID}, update)
	if err != nil {
		return fmt.Errorf("failed to update analytics: %w", err)
	}

	if result.MatchedCount == 0 {
		return fmt.Errorf("metadata not found for convert item %d", convertItemID)
	}

	return nil
}

// AddProcessingLog adds a processing log entry
func (r *ContentMetadataRepository) AddProcessingLog(ctx context.Context, convertItemID int64, log models.ProcessingLog) error {
	log.Timestamp = time.Now()

	update := bson.M{
		"$push": bson.M{
			"processing_logs": log,
		},
		"$set": bson.M{
			"updated_at": time.Now(),
		},
	}

	result, err := r.collection.UpdateOne(ctx, bson.M{"convert_item_id": convertItemID}, update)
	if err != nil {
		return fmt.Errorf("failed to add processing log: %w", err)
	}

	if result.MatchedCount == 0 {
		return fmt.Errorf("metadata not found for convert item %d", convertItemID)
	}

	return nil
}
