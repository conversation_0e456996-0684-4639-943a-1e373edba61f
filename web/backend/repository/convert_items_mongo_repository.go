package repository

import (
	"context"
	"fmt"
	"showfer-web/models"
	"strings"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// ConvertItemsMongoRepository handles convert items operations in MongoDB
type ConvertItemsMongoRepository struct {
	collection *mongo.Collection
}

// NewConvertItemsMongoRepository creates a new ConvertItemsMongoRepository
func NewConvertItemsMongoRepository(db *mongo.Database) *ConvertItemsMongoRepository {
	return &ConvertItemsMongoRepository{
		collection: db.Collection("convert_items"),
	}
}

// GetItemsByBucket retrieves convert items filtered by bucket ID
func (r *ConvertItemsMongoRepository) GetItemsByBucket(ctx context.Context, bucketID primitive.ObjectID) ([]models.ConvertItemMongo, error) {
	filter := bson.M{
		"bucket": bucketID,
	}

	cursor, err := r.collection.Find(ctx, filter, options.Find().SetSort(bson.M{"updatedAt": -1}))
	if err != nil {
		return nil, fmt.Errorf("failed to find convert items: %w", err)
	}
	defer cursor.Close(ctx)

	var items []models.ConvertItemMongo
	if err = cursor.All(ctx, &items); err != nil {
		return nil, fmt.Errorf("failed to decode convert items: %w", err)
	}

	return items, nil
}

// GetItemsByBucketAndLocation retrieves convert items filtered by bucket ID and location
func (r *ConvertItemsMongoRepository) GetItemsByBucketAndLocation(ctx context.Context, bucketID primitive.ObjectID, location string) ([]models.ConvertItemMongo, error) {
	filter := bson.M{
		"bucket":   bucketID,
		"location": location,
	}

	cursor, err := r.collection.Find(ctx, filter, options.Find().SetSort(bson.M{"updatedAt": -1}))
	if err != nil {
		return nil, fmt.Errorf("failed to find convert items: %w", err)
	}
	defer cursor.Close(ctx)

	var items []models.ConvertItemMongo
	if err = cursor.All(ctx, &items); err != nil {
		return nil, fmt.Errorf("failed to decode convert items: %w", err)
	}

	return items, nil
}

// GetFoldersByBucket retrieves unique folders (locations) for a specific bucket
func (r *ConvertItemsMongoRepository) GetFoldersByBucket(ctx context.Context, bucketID primitive.ObjectID) ([]string, error) {
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"bucket": bucketID,
			},
		},
		{
			"$group": bson.M{
				"_id": "$location",
			},
		},
		{
			"$sort": bson.M{
				"_id": 1,
			},
		},
	}

	cursor, err := r.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, fmt.Errorf("failed to aggregate folders: %w", err)
	}
	defer cursor.Close(ctx)

	var results []bson.M
	if err = cursor.All(ctx, &results); err != nil {
		return nil, fmt.Errorf("failed to decode folders: %w", err)
	}

	var folders []string
	for _, result := range results {
		if location, ok := result["_id"].(string); ok && location != "" {
			folders = append(folders, location)
		}
	}

	return folders, nil
}

// GetSubfoldersByBucket retrieves subfolders for a specific location within a bucket
func (r *ConvertItemsMongoRepository) GetSubfoldersByBucket(ctx context.Context, bucketID primitive.ObjectID, location string) ([]string, error) {
	// Ensure location ends with / for proper matching
	if location != "/" && !strings.HasSuffix(location, "/") {
		location += "/"
	}

	// Create regex pattern to match subfolders
	var filter bson.M
	if location == "/" {
		// For root, match any location that doesn't start with /
		filter = bson.M{
			"bucket": bucketID,
			"location": bson.M{
				"$regex": "^[^/]",
			},
		}
	} else {
		// For specific location, match items that start with the location
		filter = bson.M{
			"bucket": bucketID,
			"location": bson.M{
				"$regex": "^" + strings.ReplaceAll(location, "/", "\\/"),
			},
		}
	}

	pipeline := []bson.M{
		{"$match": filter},
		{
			"$group": bson.M{
				"_id": "$location",
			},
		},
		{
			"$sort": bson.M{
				"_id": 1,
			},
		},
	}

	cursor, err := r.collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, fmt.Errorf("failed to aggregate subfolders: %w", err)
	}
	defer cursor.Close(ctx)

	var results []bson.M
	if err = cursor.All(ctx, &results); err != nil {
		return nil, fmt.Errorf("failed to decode subfolders: %w", err)
	}

	var folders []string
	for _, result := range results {
		if loc, ok := result["_id"].(string); ok && loc != "" {
			// Extract subfolder name from location
			if location == "/" {
				// For root, take the first part before /
				parts := strings.Split(strings.TrimPrefix(loc, "/"), "/")
				if len(parts) > 0 && parts[0] != "" {
					folders = append(folders, parts[0])
				}
			} else {
				// For specific location, extract the next level
				if strings.HasPrefix(loc, location) {
					remaining := strings.TrimPrefix(loc, location)
					if remaining != "" {
						parts := strings.Split(remaining, "/")
						if len(parts) > 0 && parts[0] != "" {
							folders = append(folders, parts[0])
						}
					}
				}
			}
		}
	}

	// Remove duplicates
	uniqueFolders := make([]string, 0)
	seen := make(map[string]bool)
	for _, folder := range folders {
		if !seen[folder] {
			seen[folder] = true
			uniqueFolders = append(uniqueFolders, folder)
		}
	}

	return uniqueFolders, nil
}

// GetItemByID retrieves a convert item by its ID
func (r *ConvertItemsMongoRepository) GetItemByID(ctx context.Context, id primitive.ObjectID) (*models.ConvertItemMongo, error) {
	var item models.ConvertItemMongo
	err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&item)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("convert item not found")
		}
		return nil, fmt.Errorf("failed to find convert item: %w", err)
	}

	return &item, nil
}

// TestConnection tests the MongoDB connection for convert_items collection
func (r *ConvertItemsMongoRepository) TestConnection(ctx context.Context) (int64, error) {
	count, err := r.collection.CountDocuments(ctx, bson.M{})
	if err != nil {
		return 0, fmt.Errorf("failed to count convert_items documents: %w", err)
	}
	return count, nil
}
