package repository

import (
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"showfer-web/models"
	"showfer-web/service/logger"
	"strconv"
	"strings"
	"time"
)

type File struct {
	ID          string         `json:"id"`
	FileName    string         `json:"fileName"`
	Duration    float64        `json:"duration"`
	Episode     sql.NullString `json:"episode"`
	Name        string         `json:"name"`
	Description sql.NullString `json:"description"`
}

type Folder struct {
	Folder  string    `json:"folder"`
	Path    string    `json:"path"`
	Files   []File    `json:"files"`
	Folders []*Folder `json:"folders"`
}

type FileRow struct {
	ID          string
	FileName    string
	Duration    float64
	Episode     sql.NullString
	Name        string
	Description sql.NullString
	Location    string
}

// NullableString is a helper type for handling NULL string values from the database
type NullableString struct {
	sql.NullString
}

// GetValue returns the string value or an empty string if NULL
func (ns NullableString) GetValue() string {
	if ns.Valid {
		return ns.String
	}
	return ""
}

// FilesRepository handles database operations for files
type FilesRepository struct {
	db *sql.DB
}

// NewFilesRepository creates a new FilesRepository
func NewFilesRepository(db *sql.DB) *FilesRepository {
	return &FilesRepository{
		db: db,
	}
}

// GetDB returns the database connection
func (r *FilesRepository) GetDB() *sql.DB {
	return r.db
}

// ListConvertItems gets all convert items with pagination
func (r *FilesRepository) ListConvertItems(pagination models.Pagination) (models.ConvertItemListResult, error) {
	var total int
	err := r.db.QueryRow(`SELECT COUNT(*) FROM convert_items`).Scan(&total)
	if err != nil {
		return models.ConvertItemListResult{}, err
	}

	rows, err := r.db.Query(`
		SELECT id, filename, location, duration, status, size, storage_type, created_at, updated_at, 
		       c_location, name, description, episode, width, height, fps, video_codec, audio_codec, 
		       bitrate, recorder_id, codec_settings_version
		FROM convert_items
		ORDER BY updated_at DESC
		LIMIT $1
		OFFSET $2
	`, pagination.Limit, (pagination.Page-1)*pagination.Limit)
	if err != nil {
		return models.ConvertItemListResult{}, err
	}
	defer rows.Close()

	var items []models.ConvertItem
	for rows.Next() {
		var item models.ConvertItem
		var cLocation NullableString
		var name, description, episode sql.NullString
		var width, height, bitrate, recorderID, codecSettingsVersion sql.NullInt64
		var fps sql.NullFloat64
		var videoCodec, audioCodec sql.NullString

		err := rows.Scan(
			&item.ID, &item.Filename, &item.Location, &item.Duration, &item.Status, &item.Size,
			&item.StorageType, &item.CreatedAt, &item.UpdatedAt, &cLocation, &name,
			&description, &episode, &width, &height, &fps, &videoCodec, &audioCodec,
			&bitrate, &recorderID, &codecSettingsVersion,
		)
		if err != nil {
			return models.ConvertItemListResult{}, err
		}

		// Handle NULL values
		item.CLocation = cLocation.GetValue()
		if name.Valid {
			item.Name = name.String
		}
		if description.Valid {
			item.Description = description.String
		}
		if episode.Valid {
			item.Episode = episode.String
		}
		if width.Valid {
			w := int(width.Int64)
			item.Width = &w
		}
		if height.Valid {
			h := int(height.Int64)
			item.Height = &h
		}
		if fps.Valid {
			item.FPS = &fps.Float64
		}
		if videoCodec.Valid {
			item.VideoCodec = &videoCodec.String
		}
		if audioCodec.Valid {
			item.AudioCodec = &audioCodec.String
		}
		if bitrate.Valid {
			b := int(bitrate.Int64)
			item.Bitrate = &b
		}
		if recorderID.Valid {
			r := int(recorderID.Int64)
			item.RecorderID = &r
		}
		if codecSettingsVersion.Valid {
			v := int(codecSettingsVersion.Int64)
			item.CodecSettingsVersion = &v
		}

		items = append(items, item)
	}

	totalPages := total / pagination.Limit
	if total%pagination.Limit > 0 {
		totalPages++
	}

	return models.ConvertItemListResult{
		Items:      items,
		TotalItems: total,
		TotalPages: totalPages,
		Page:       pagination.Page,
		Limit:      pagination.Limit,
	}, nil
}

// ConvertItemsByFolder gets convert items by folder
func (r *FilesRepository) ConvertItemsByFolder(location string) ([]models.ConvertItem, error) {
	rows, err := r.db.Query(`
		SELECT id, filename, location, duration, status, size, storage_type, created_at, updated_at, 
		       c_location, name, description, episode, width, height, fps, video_codec, audio_codec, 
		       bitrate, recorder_id, codec_settings_version
		FROM convert_items
		WHERE location = $1
		ORDER BY updated_at DESC
	`, location)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var items []models.ConvertItem
	for rows.Next() {
		var item models.ConvertItem
		var cLocation NullableString
		var name, description, episode sql.NullString
		var width, height, bitrate, recorderID, codecSettingsVersion sql.NullInt64
		var fps sql.NullFloat64
		var videoCodec, audioCodec sql.NullString

		err := rows.Scan(
			&item.ID, &item.Filename, &item.Location, &item.Duration, &item.Status, &item.Size,
			&item.StorageType, &item.CreatedAt, &item.UpdatedAt, &cLocation, &name,
			&description, &episode, &width, &height, &fps, &videoCodec, &audioCodec,
			&bitrate, &recorderID, &codecSettingsVersion,
		)
		if err != nil {
			return nil, err
		}

		// Handle NULL values
		item.CLocation = cLocation.GetValue()
		if name.Valid {
			item.Name = name.String
		}
		if description.Valid {
			item.Description = description.String
		}
		if episode.Valid {
			item.Episode = episode.String
		}
		if width.Valid {
			w := int(width.Int64)
			item.Width = &w
		}
		if height.Valid {
			h := int(height.Int64)
			item.Height = &h
		}
		if fps.Valid {
			item.FPS = &fps.Float64
		}
		if videoCodec.Valid {
			item.VideoCodec = &videoCodec.String
		}
		if audioCodec.Valid {
			item.AudioCodec = &audioCodec.String
		}
		if bitrate.Valid {
			b := int(bitrate.Int64)
			item.Bitrate = &b
		}
		if recorderID.Valid {
			r := int(recorderID.Int64)
			item.RecorderID = &r
		}
		if codecSettingsVersion.Valid {
			v := int(codecSettingsVersion.Int64)
			item.CodecSettingsVersion = &v
		}

		items = append(items, item)
	}

	return items, nil
}

// GetSubfolders gets subfolders by location
func (r *FilesRepository) GetSubfolders(location string) ([]string, error) {
	rows, err := r.db.Query(`
		SELECT DISTINCT subfolder
		FROM (
			SELECT
				CASE
					WHEN STRPOS(SUBSTR(location, LENGTH($1) + 1), '/') > 0 THEN
						SUBSTR(SUBSTR(location, LENGTH($2) + 1), 1, STRPOS(SUBSTR(location, LENGTH($3) + 1), '/') - 1)
					ELSE
						SUBSTR(location, LENGTH($4) + 1)
				END AS subfolder
			FROM convert_items
			WHERE location LIKE $5 || '%' AND location != $6
		) AS subfolders_query
		WHERE subfolder != ''
	`, location, location, location, location, location, location)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var subfolders []string
	for rows.Next() {
		var subfolder string
		if err := rows.Scan(&subfolder); err != nil {
			return nil, err
		}
		subfolders = append(subfolders, subfolder)
	}
	return subfolders, nil
}

// GetConvertItemById gets a convert item by ID
func (r *FilesRepository) GetConvertItemById(id int64) (models.ConvertItem, error) {
	row := r.db.QueryRow(`
		SELECT id, filename, location, duration, status, size, storage_type, created_at, updated_at, 
		       c_location, name, description, episode, width, height, fps, video_codec, audio_codec, 
		       bitrate, recorder_id, codec_settings_version
		FROM convert_items
		WHERE id = $1
		LIMIT 1
	`, id)

	var item models.ConvertItem
	var cLocation NullableString
	var name, description, episode sql.NullString
	var width, height, bitrate, recorderID, codecSettingsVersion sql.NullInt64
	var fps sql.NullFloat64
	var videoCodec, audioCodec sql.NullString

	err := row.Scan(
		&item.ID, &item.Filename, &item.Location, &item.Duration, &item.Status, &item.Size,
		&item.StorageType, &item.CreatedAt, &item.UpdatedAt, &cLocation, &name,
		&description, &episode, &width, &height, &fps, &videoCodec, &audioCodec,
		&bitrate, &recorderID, &codecSettingsVersion,
	)
	if err != nil {
		return models.ConvertItem{}, err
	}

	// Handle NULL values
	item.CLocation = cLocation.GetValue()
	if name.Valid {
		item.Name = name.String
	}
	if description.Valid {
		item.Description = description.String
	}
	if episode.Valid {
		item.Episode = episode.String
	}
	if width.Valid {
		w := int(width.Int64)
		item.Width = &w
	}
	if height.Valid {
		h := int(height.Int64)
		item.Height = &h
	}
	if fps.Valid {
		item.FPS = &fps.Float64
	}
	if videoCodec.Valid {
		item.VideoCodec = &videoCodec.String
	}
	if audioCodec.Valid {
		item.AudioCodec = &audioCodec.String
	}
	if bitrate.Valid {
		b := int(bitrate.Int64)
		item.Bitrate = &b
	}
	if recorderID.Valid {
		r := int(recorderID.Int64)
		item.RecorderID = &r
	}
	if codecSettingsVersion.Valid {
		v := int(codecSettingsVersion.Int64)
		item.CodecSettingsVersion = &v
	}

	return item, nil
}

// CreateConvertItem creates a new convert item
func (r *FilesRepository) CreateConvertItem(item models.ConvertItem) (int64, error) {
	var id int64
	err := r.db.QueryRow(`
		INSERT INTO convert_items
			(filename, name, location, duration, status, size, storage_type, created_at, updated_at, c_location, recorder_id, video_codec, audio_codec)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
		RETURNING id
	`,
		item.Filename,
		item.Name,
		item.Location,
		item.Duration,
		item.Status,
		item.Size,
		item.StorageType,
		item.CreatedAt,
		item.UpdatedAt,
		item.CLocation,
		item.RecorderID,
		item.VideoCodec,
		item.AudioCodec,
	).Scan(&id)

	if err != nil {
		return 0, err
	}

	return id, nil
}

// UpdateConvertItem updates an existing convert item
func (r *FilesRepository) UpdateConvertItem(item models.ConvertItem) error {
	_, err := r.db.Exec(`
		UPDATE convert_items 
		SET filename = $1, location = $2, duration = $3, name = $4, description = $5, episode = $6
		WHERE id = $7
	`, item.Filename, item.Location, item.Duration, item.Name, item.Description, item.Episode, item.ID)

	return err
}

// UpdateConvertItemFilename updates only the filename of a convert item
func (r *FilesRepository) UpdateConvertItemFilename(id int64, filename string) error {
	_, err := r.db.Exec(`
		UPDATE convert_items 
		SET filename = $1
		WHERE id = $2
	`, filename, id)

	return err
}

// UpdateFileLocation updates the location and c_location of a convert item
func (r *FilesRepository) UpdateFileLocation(id int64, oldLocation string, newLocation string) error {
	// Start a transaction
	tx, err := r.db.Begin()
	if err != nil {
		logger.Error("Failed to begin transaction: %v", err)
		return err
	}

	// Defer rollback in case of error - this will be a no-op if the transaction is committed
	defer tx.Rollback()

	// Get the file to update
	var filename string
	err = tx.QueryRow("SELECT filename FROM convert_items WHERE id = $1", id).Scan(&filename)
	if err != nil {
		logger.Error("Failed to get filename: %v", err)
		return err
	}

	// Update convert_items table with new location and c_location
	now := time.Now().UTC().Format(time.RFC3339)
	_, err = tx.Exec(`
		UPDATE convert_items
		SET location = $1, c_location = $2, updated_at = $3
		WHERE id = $4
	`,
		newLocation,
		newLocation, // Update c_location to match location
		now,
		id,
	)

	if err != nil {
		logger.Error("Failed to update convert_items table: %v", err)
		return err
	}

	// Update content_analytics table
	// Old content path
	oldContentPath := oldLocation + filename

	// New content path
	newContentPath := newLocation + filename

	_, err = tx.Exec(`
		UPDATE content_analytics
		SET content_path = $1
		WHERE content_path = $2
	`,
		newContentPath,
		oldContentPath,
	)

	if err != nil {
		logger.Error("Failed to update content_analytics table: %v", err)
		// Continue anyway, as this is not critical for file moving
		// But log the error for debugging purposes
	} else {
		logger.Log("Updated content_analytics table for file: %s -> %s", oldContentPath, newContentPath)
	}

	// Update schedules that reference this file
	// First, get all schedules
	rows, err := tx.Query("SELECT id, regular_days, special_days, fillers FROM schedules")
	if err != nil {
		logger.Error("Failed to get schedules: %v", err)
		// Continue anyway, as we can still move the file
	} else {
		defer rows.Close()

		var schedulesToUpdate []int64

		for rows.Next() {
			var id int64
			var regularDays, specialDays, fillers string

			err := rows.Scan(&id, &regularDays, &specialDays, &fillers)
			if err != nil {
				logger.Error("Failed to scan schedule: %v", err)
				continue
			}

			// Check if the file is referenced in regular days
			updatedRegularDays, regularChanged, err := updateFilePathsInJSON(regularDays, oldContentPath, newContentPath)
			if err != nil {
				logger.Error("Failed to update regular days: %v", err)
				continue
			}

			// Check if the file is referenced in special days
			updatedSpecialDays, specialChanged, err := updateFilePathsInJSON(specialDays, oldContentPath, newContentPath)
			if err != nil {
				logger.Error("Failed to update special days: %v", err)
				continue
			}

			// Check if the file is referenced in fillers
			updatedFillers, fillersChanged, err := updateFilePathsInJSON(fillers, oldContentPath, newContentPath)
			if err != nil {
				logger.Error("Failed to update fillers: %v", err)
				continue
			}

			// If any changes were made, update the schedule
			if regularChanged || specialChanged || fillersChanged {
				_, err := tx.Exec(`
					UPDATE schedules
					SET regular_days = $1, special_days = $2, fillers = $3, updated_at = $4
					WHERE id = $5
				`,
					updatedRegularDays,
					updatedSpecialDays,
					updatedFillers,
					now,
					id,
				)

				if err != nil {
					logger.Error("Failed to update schedule %d: %v", id, err)
					continue
				}

				schedulesToUpdate = append(schedulesToUpdate, id)
				logger.Log("Updated schedule %d with new file path", id)
			}
		}

		// If any schedules were updated, we need to restart them
		if len(schedulesToUpdate) > 0 {
			logger.Log("Schedules updated with new file paths: %v", schedulesToUpdate)
			// Note: In a real implementation, we would restart the schedulers here
			// But for now, we'll just log the schedules that need to be restarted
		}
	}

	// Commit the transaction
	err = tx.Commit()
	if err != nil {
		logger.Error("Failed to commit transaction: %v", err)
		return err
	}

	return nil
}

// DeleteFile deletes a file from the database and disk
func (r *FilesRepository) DeleteFile(id int64, filePath string, baseDir string) error {
	// Delete from database
	_, err := r.db.Exec("DELETE FROM convert_items WHERE id = $1", id)
	if err != nil {
		return err
	}

	// Delete from disk
	fullPath := filepath.Join(baseDir, filePath)
	err = os.Remove(fullPath)
	if err != nil && !os.IsNotExist(err) {
		logger.Error("Failed to delete file from disk: %v", err)
		return err
	}

	return nil
}

// GetSchedulesUsingFile returns a list of schedules that use the specified file
func (r *FilesRepository) GetSchedulesUsingFile(filePath string) ([]models.Schedule, error) {
	// Query all schedules
	rows, err := r.db.Query("SELECT id, name, short_id, icon, timezone, output_url, network_interface, regular_days, special_days, fillers FROM schedules")
	if err != nil {
		logger.Error("Failed to query schedules: %v", err)
		return nil, err
	}
	defer rows.Close()

	var schedules []models.Schedule

	// Process each schedule
	for rows.Next() {
		var id int64
		var name, shortID, icon, timezone, outputUrl, networkInterface string
		var regularDays, specialDays, fillers string

		err := rows.Scan(&id, &name, &shortID, &icon, &timezone, &outputUrl, &networkInterface, &regularDays, &specialDays, &fillers)
		if err != nil {
			logger.Error("Failed to scan schedule row: %v", err)
			continue
		}

		// Check if the file is used in this schedule
		fileUsed := false

		// Check regular_days
		if regularDays != "" && strings.Contains(regularDays, filePath) {
			fileUsed = true
		}

		// Check special_days
		if !fileUsed && specialDays != "" && strings.Contains(specialDays, filePath) {
			fileUsed = true
		}

		// Check fillers
		if !fileUsed && fillers != "" && strings.Contains(fillers, filePath) {
			fileUsed = true
		}

		if fileUsed {
			// Parse the JSON fields
			var regularDaysData []models.Day
			if regularDays != "" {
				err = json.Unmarshal([]byte(regularDays), &regularDaysData)
				if err != nil {
					logger.Error("Failed to unmarshal regular_days for schedule %d: %v", id, err)
					regularDaysData = []models.Day{}
				}
			}

			var specialDaysData []models.Day
			if specialDays != "" {
				err = json.Unmarshal([]byte(specialDays), &specialDaysData)
				if err != nil {
					logger.Error("Failed to unmarshal special_days for schedule %d: %v", id, err)
					specialDaysData = []models.Day{}
				}
			}

			var fillersData models.Fillers
			if fillers != "" {
				err = json.Unmarshal([]byte(fillers), &fillersData)
				if err != nil {
					logger.Error("Failed to unmarshal fillers for schedule %d: %v", id, err)
					fillersData = models.Fillers{}
				}
			}

			// Create the schedule object
			schedule := models.Schedule{
				ID:               id,
				Name:             name,
				ShortID:          shortID,
				Icon:             icon,
				Timezone:         timezone,
				OutputUrl:        outputUrl,
				NetworkInterface: networkInterface,
				RegularDays:      regularDaysData,
				SpecialDays:      specialDaysData,
				Fillers:          fillersData,
			}

			schedules = append(schedules, schedule)
		}
	}

	return schedules, nil
}

// RenameFile renames a file on disk and updates the database
func (r *FilesRepository) RenameFile(file models.ConvertItem, newFilename string, baseDir string) error {
	// Get file extension
	oldExt := filepath.Ext(file.Filename)

	// Ensure new filename has the same extension
	if filepath.Ext(newFilename) == "" {
		newFilename = newFilename + oldExt
	}

	// Construct old and new file paths
	oldPath := filepath.Join(baseDir, file.Location, file.Filename)
	newPath := filepath.Join(baseDir, file.Location, newFilename)

	// Check if new file already exists
	if _, err := os.Stat(newPath); err == nil {
		return fmt.Errorf("a file with the name %s already exists", newFilename)
	}

	//// Old file path in the JSON
	//oldFilePath := file.Location + file.Filename

	//// Get schedules that use this file before making any changes
	//// We'll need this later to restart the scheduler
	//affectedSchedules, err := r.GetSchedulesUsingFile(oldFilePath)
	//if err != nil {
	//	logger.Error("Failed to get schedules using file: %v", err)
	//	// Continue anyway, as this is not critical
	//}

	// Start a transaction
	tx, err := r.db.Begin()
	if err != nil {
		logger.Error("Failed to start transaction: %v", err)
		return err
	}

	// Defer rollback in case of error - this will be a no-op if the transaction is committed
	defer tx.Rollback()

	// Rename file on disk
	err = os.Rename(oldPath, newPath)
	if err != nil {
		logger.Error("Failed to rename file on disk: %v", err)
		return err
	}

	// Update convert_items table
	now := time.Now().UTC().Format(time.RFC3339)
	_, err = tx.Exec(`
		UPDATE convert_items
		SET filename = $1, updated_at = $2
		WHERE id = $3
	`,
		newFilename,
		now,
		file.ID,
	)

	if err != nil {
		logger.Error("Failed to update convert_items table: %v", err)
		return err
	}

	// Update content_analytics table
	// Old content path
	oldContentPath := file.Location + file.Filename

	// New content path and name
	newContentPath := file.Location + newFilename
	newContentName := newFilename

	_, err = tx.Exec(`
		UPDATE content_analytics
		SET content_path = $1, content_name = $2
		WHERE content_path = $3
	`,
		newContentPath,
		newContentName,
		oldContentPath,
	)

	if err != nil {
		logger.Error("Failed to update content_analytics table: %v", err)
		// Continue anyway, as this is not critical for file renaming
		// But log the error for debugging purposes
	} else {
		logger.Log("Updated content_analytics table for file: %s -> %s", oldContentPath, newContentPath)
	}

	// Commit the transaction
	err = tx.Commit()
	if err != nil {
		logger.Error("Failed to commit transaction: %v", err)
		return err
	}

	return nil
}

// updateFilePathsInJSON updates file paths in JSON data
// Returns the updated JSON string, a boolean indicating if any changes were made, and an error if any
func updateFilePathsInJSON(jsonData string, oldPath string, newPath string) (string, bool, error) {
	logger.Log("Updating file paths in JSON: oldPath=%s, newPath=%s", oldPath, newPath)
	logger.Log("Original JSON: %s", jsonData)

	var data interface{}

	// Parse the JSON data
	err := json.Unmarshal([]byte(jsonData), &data)
	if err != nil {
		logger.Error("Failed to parse JSON: %v", err)
		return "", false, fmt.Errorf("failed to parse JSON: %v", err)
	}

	// Track if any changes were made
	changed := false

	// Update file paths in the parsed data
	data, changed = updatePathsInData(data, oldPath, newPath)

	// If no changes were made, return the original JSON
	if !changed {
		logger.Log("No changes made to JSON")
		return jsonData, false, nil
	}

	// Marshal the updated data back to JSON
	updatedJSON, err := json.Marshal(data)
	if err != nil {
		logger.Error("Failed to marshal JSON: %v", err)
		return "", false, fmt.Errorf("failed to marshal JSON: %v", err)
	}

	logger.Log("Updated JSON: %s", string(updatedJSON))
	return string(updatedJSON), true, nil
}

// updatePathsInData recursively updates file paths in parsed JSON data
// Returns the updated data and a boolean indicating if any changes were made
func updatePathsInData(data interface{}, oldPath string, newPath string) (interface{}, bool) {
	// Track if any changes were made
	changed := false

	switch v := data.(type) {
	case map[string]interface{}:
		// Check if this is a file object with a path
		if path, ok := v["path"].(string); ok {
			logger.Log("Found path: %s, comparing with oldPath: %s", path, oldPath)
			if path == oldPath {
				logger.Log("Updating path from %s to %s", path, newPath)
				v["path"] = newPath
				changed = true
			}
		}

		// Recursively process all fields
		for key, value := range v {
			logger.Log("Processing field: %s", key)
			updatedValue, fieldChanged := updatePathsInData(value, oldPath, newPath)
			if fieldChanged {
				v[key] = updatedValue
				changed = true
			}
		}
		return v, changed

	case []interface{}:
		// Recursively process all elements in the array
		logger.Log("Processing array with %d elements", len(v))
		for i, item := range v {
			updatedItem, itemChanged := updatePathsInData(item, oldPath, newPath)
			if itemChanged {
				v[i] = updatedItem
				changed = true
			}
		}
		return v, changed

	case string:
		// Check if this is a direct path string
		logger.Log("Checking string value: %s", v)
		if v == oldPath {
			logger.Log("Updating string from %s to %s", v, newPath)
			return newPath, true
		}
		return v, false

	default:
		// For other types, no changes needed
		logger.Log("Skipping type: %T", v)
		return data, false
	}
}

// FindConvertItemByFilenameAndLocation finds a convert item by filename and location
func (r *FilesRepository) FindConvertItemByFilenameAndLocation(filename string, location string) (models.ConvertItem, error) {
	row := r.db.QueryRow(`
		SELECT id, filename, location, duration, status, size, storage_type, created_at, updated_at, c_location, name, description, episode
		FROM convert_items
		WHERE filename = $1 AND location = $2
		LIMIT 1
	`, filename, location)

	var item models.ConvertItem
	var cLocation NullableString
	var name, description, episode sql.NullString

	err := row.Scan(
		&item.ID, &item.Filename, &item.Location, &item.Duration, &item.Status, &item.Size,
		&item.StorageType, &item.CreatedAt, &item.UpdatedAt, &cLocation, &name,
		&description, &episode,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return models.ConvertItem{}, errors.New("file not found")
		}
		return models.ConvertItem{}, err
	}

	// Handle NULL values
	item.CLocation = cLocation.GetValue()
	if name.Valid {
		item.Name = name.String
	}
	if description.Valid {
		item.Description = description.String
	}
	if episode.Valid {
		item.Episode = episode.String
	}

	return item, nil
}

// GetConvertItemsByStatus gets convert items by status
func (r *FilesRepository) GetConvertItemsByStatus(status int) ([]models.ConvertItem, error) {
	rows, err := r.db.Query(`
		SELECT id, filename, location, duration, status, size, storage_type, created_at, updated_at, c_location, name, description, episode
		FROM convert_items
		WHERE status = $1
		ORDER BY created_at ASC
	`, status)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var items []models.ConvertItem
	for rows.Next() {
		var item models.ConvertItem
		var cLocation NullableString
		var name, description, episode sql.NullString

		err := rows.Scan(
			&item.ID, &item.Filename, &item.Location, &item.Duration, &item.Status, &item.Size,
			&item.StorageType, &item.CreatedAt, &item.UpdatedAt, &cLocation, &name,
			&description, &episode,
		)
		if err != nil {
			return nil, err
		}

		// Handle NULL values
		item.CLocation = cLocation.GetValue()
		if name.Valid {
			item.Name = name.String
		}
		if description.Valid {
			item.Description = description.String
		}
		if episode.Valid {
			item.Episode = episode.String
		}

		items = append(items, item)
	}

	return items, nil
}

// GetFilesByRecorderID gets files associated with a recorder that are in queue or being transcoded
func (r *FilesRepository) GetFilesByRecorderID(recorderID int64) ([]models.ConvertItem, error) {
	// Get files in the recordings directory that are in queue or being transcoded
	rows, err := r.db.Query(`
		SELECT id, filename, location, duration, status, size, storage_type, created_at, updated_at, c_location, name, description, episode
		FROM convert_items
		WHERE location = '/recordings/'
		AND (status = $1 OR status = $2)
		AND filename LIKE $3
		ORDER BY created_at ASC
	`, int(models.FileStatusQueue), int(models.FileStatusProcessing), "rec_"+strconv.FormatInt(recorderID, 10)+"%")

	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var items []models.ConvertItem
	for rows.Next() {
		var item models.ConvertItem
		var cLocation NullableString
		var name, description, episode sql.NullString

		err := rows.Scan(
			&item.ID, &item.Filename, &item.Location, &item.Duration, &item.Status, &item.Size,
			&item.StorageType, &item.CreatedAt, &item.UpdatedAt, &cLocation, &name,
			&description, &episode,
		)
		if err != nil {
			return nil, err
		}

		// Handle NULL values
		item.CLocation = cLocation.GetValue()
		if name.Valid {
			item.Name = name.String
		}
		if description.Valid {
			item.Description = description.String
		}
		if episode.Valid {
			item.Episode = episode.String
		}

		items = append(items, item)
	}

	return items, nil
}

// ChangeStatus changes the status of a convert item
func (r *FilesRepository) ChangeStatus(id int64, status int) error {
	_, err := r.db.Exec("UPDATE convert_items SET status=$1 WHERE id=$2", status, id)
	return err
}

// GetDurationById return file duration or 0 from the database by id
func (r *FilesRepository) GetDurationById(id int64) (float64, error) {
	var duration sql.NullFloat64

	err := r.db.QueryRow(`
		SELECT duration
		FROM convert_items
		WHERE id = $1
	`, id).Scan(&duration)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return 0, nil
		}
		return 0, err
	}

	if duration.Valid {
		return duration.Float64, nil
	}
	return 0, nil
}

func (r *FilesRepository) FindByPath(file string) (models.ConvertItem, error) {
	dir := filepath.Dir(file)
	if !strings.HasSuffix(dir, string(os.PathSeparator)) {
		dir += string(os.PathSeparator)
	}

	row := r.db.QueryRow(`
		SELECT id, duration, filename, location, name, description, episode
		FROM convert_items
		WHERE filename = $1 AND location = $2
		LIMIT 1
	`,
		filepath.Base(file),
		dir,
	)

	var item models.ConvertItem
	var name, description, episode sql.NullString

	err := row.Scan(&item.ID, &item.Duration, &item.Filename, &item.Location, &name, &description, &episode)
	if err != nil {
		return models.ConvertItem{}, err
	}

	if name.Valid {
		item.Name = name.String
	}
	if description.Valid {
		item.Description = description.String
	}
	if episode.Valid {
		item.Episode = episode.String
	}

	return item, nil
}

func (r *FilesRepository) FindByFolder(folder string) []models.ConvertItem {
	if !strings.HasSuffix(folder, string(os.PathSeparator)) {
		folder += string(os.PathSeparator)
	}

	rows, _ := r.db.Query(`
		SELECT id, duration, filename, location, name, description, episode
		FROM convert_items
		WHERE location = $1
	`,
		folder,
	)

	defer rows.Close()

	var data []models.ConvertItem
	for rows.Next() {
		var item models.ConvertItem
		var name, description, episode sql.NullString

		err := rows.Scan(&item.ID, &item.Duration, &item.Filename, &item.Location, &name, &description, &episode)
		if err != nil {
			logger.Error("Failed to scan: %v", err)
			continue
		}

		if name.Valid {
			item.Name = name.String
		}
		if description.Valid {
			item.Description = description.String
		}
		if episode.Valid {
			item.Episode = episode.String
		}

		data = append(data, item)
	}

	return data
}

func (r *FilesRepository) FilesToNestedStructure() *Folder {
	rows, _ := r.db.Query(`
		SELECT id, filename, duration, episode, name, description, location
		FROM convert_items
		WHERE status = $1;
	`, int(models.FileStatusSuccess))

	defer rows.Close()

	var fileRows []FileRow

	for rows.Next() {
		var r FileRow
		if err := rows.Scan(&r.ID, &r.FileName, &r.Duration, &r.Episode, &r.Name, &r.Description, &r.Location); err != nil {
			logger.Error(err.Error())
		}
		fileRows = append(fileRows, r)
	}

	return r.resultByGroup(fileRows)
}

func (r *FilesRepository) FilesToNestedStructureFilterByDuration(minDuration, maxDuration int) *Folder {

	rows, _ := r.db.Query(`
		SELECT id, filename, duration, episode, name, description, location
		FROM convert_items
		WHERE status = $1 AND duration > $2 AND duration <= $3
	`, int(models.FileStatusSuccess), minDuration, maxDuration)

	defer rows.Close()
	var fileRows []FileRow

	for rows.Next() {
		var r FileRow
		if err := rows.Scan(&r.ID, &r.FileName, &r.Duration, &r.Episode, &r.Name, &r.Description, &r.Location); err != nil {
			logger.Error(err.Error())
		}
		fileRows = append(fileRows, r)
	}

	return r.resultByGroup(fileRows)
}

func (r *FilesRepository) resultByGroup(fileRows []FileRow) *Folder {
	root := &Folder{
		Folder:  "",
		Path:    "/",
		Files:   []File{},
		Folders: []*Folder{},
	}

	for _, row := range fileRows {
		path := strings.Trim(row.Location, "/")
		parts := []string{}
		if path != "" {
			parts = strings.Split(path, "/")
		}

		file := File{
			ID:          row.ID,
			FileName:    row.FileName,
			Duration:    row.Duration,
			Episode:     row.Episode,
			Name:        row.Name,
			Description: row.Description,
		}

		r.addPathToStructure(root, parts, &file)
	}

	return root
}

func (r *FilesRepository) addPathToStructure(current *Folder, parts []string, file *File) {
	if len(parts) == 0 {
		current.Files = append(current.Files, *file)
		return
	}

	part := parts[0]

	var next *Folder
	for _, folder := range current.Folders {
		if folder.Folder == part {
			next = folder
			break
		}
	}

	if next == nil {
		next = &Folder{
			Folder:  part,
			Path:    strings.TrimRight(current.Path, "/") + "/" + part,
			Files:   []File{},
			Folders: []*Folder{},
		}
		current.Folders = append(current.Folders, next)
	}

	r.addPathToStructure(next, parts[1:], file)
}
