package repository

import (
	"database/sql"
	"showfer-web/service/logger"
)

// MigrateTables updates the database schema
func MigrateTables(db *sql.DB) error {
	// Check if convert_items table has the required columns
	if err := migrateConvertItemsTable(db); err != nil {
		return err
	}

	// Check if users table has the required columns
	if err := migrateUsersTable(db); err != nil {
		return err
	}

	// Check if recorders table has the required columns
	if err := migrateRecordersTable(db); err != nil {
		return err
	}

	// Check if content_analytics table has the required indexes
	if err := migrateContentAnalyticsTable(db); err != nil {
		return err
	}

	return nil
}

// migrateConvertItemsTable adds missing columns to the convert_items table
func migrateConvertItemsTable(db *sql.DB) error {
	// Check if status column exists
	var statusExists bool
	err := db.QueryRow(`
		SELECT EXISTS (
			SELECT 1 FROM information_schema.columns 
			WHERE table_name = 'convert_items' AND column_name = 'status'
		)
	`).Scan(&statusExists)
	if err != nil {
		logger.Error("Failed to check if status column exists: %v", err)
		return err
	}

	// Add status column if it doesn't exist
	if !statusExists {
		_, err := db.Exec(`ALTER TABLE convert_items ADD COLUMN status INTEGER DEFAULT 0`)
		if err != nil {
			logger.Error("Failed to add status column: %v", err)
			return err
		}
		logger.Log("Added status column to convert_items table")
	}

	// Check if storage_type column exists
	var storageTypeExists bool
	err = db.QueryRow(`
		SELECT EXISTS (
			SELECT 1 FROM information_schema.columns 
			WHERE table_name = 'convert_items' AND column_name = 'storage_type'
		)
	`).Scan(&storageTypeExists)
	if err != nil {
		logger.Error("Failed to check if storage_type column exists: %v", err)
		return err
	}

	// Add storage_type column if it doesn't exist
	if !storageTypeExists {
		_, err := db.Exec(`ALTER TABLE convert_items ADD COLUMN storage_type INTEGER DEFAULT 0`)
		if err != nil {
			logger.Error("Failed to add storage_type column: %v", err)
			return err
		}
		logger.Log("Added storage_type column to convert_items table")
	}

	// Check if c_location column exists
	var cLocationExists bool
	err = db.QueryRow(`
		SELECT EXISTS (
			SELECT 1 FROM information_schema.columns 
			WHERE table_name = 'convert_items' AND column_name = 'c_location'
		)
	`).Scan(&cLocationExists)
	if err != nil {
		logger.Error("Failed to check if c_location column exists: %v", err)
		return err
	}

	// Add c_location column if it doesn't exist
	if !cLocationExists {
		_, err := db.Exec(`ALTER TABLE convert_items ADD COLUMN c_location TEXT`)
		if err != nil {
			logger.Error("Failed to add c_location column: %v", err)
			return err
		}
		logger.Log("Added c_location column to convert_items table")
	}

	// Check if name column exists
	var nameExists bool
	err = db.QueryRow(`
		SELECT EXISTS (
			SELECT 1 FROM information_schema.columns 
			WHERE table_name = 'convert_items' AND column_name = 'name'
		)
	`).Scan(&nameExists)
	if err != nil {
		logger.Error("Failed to check if name column exists: %v", err)
		return err
	}

	// Add name column if it doesn't exist
	if !nameExists {
		_, err := db.Exec(`ALTER TABLE convert_items ADD COLUMN name TEXT`)
		if err != nil {
			logger.Error("Failed to add name column: %v", err)
			return err
		}
		// Update name column with filename values
		_, err = db.Exec(`UPDATE convert_items SET name = filename WHERE name IS NULL`)
		if err != nil {
			logger.Error("Failed to update name column: %v", err)
			return err
		}
		logger.Log("Added name column to convert_items table")
	}

	// Check if episode column exists
	var episodeExists bool
	err = db.QueryRow(`
		SELECT EXISTS (
			SELECT 1 FROM information_schema.columns 
			WHERE table_name = 'convert_items' AND column_name = 'episode'
		)
	`).Scan(&episodeExists)
	if err != nil {
		logger.Error("Failed to check if episode column exists: %v", err)
		return err
	}

	// Add episode column if it doesn't exist
	if !episodeExists {
		_, err := db.Exec(`ALTER TABLE convert_items ADD COLUMN episode TEXT`)
		if err != nil {
			logger.Error("Failed to add episode column: %v", err)
			return err
		}
		logger.Log("Added episode column to convert_items table")
	}

	// Create indexes if they don't exist
	_, err = db.Exec(`
		CREATE INDEX IF NOT EXISTS idx_convert_items_location ON convert_items(location);
		CREATE INDEX IF NOT EXISTS idx_convert_items_filename_location ON convert_items(filename, location);
	`)
	if err != nil {
		logger.Error("Failed to create indexes: %v", err)
		return err
	}

	return nil
}

// migrateUsersTable adds missing columns to the users table
func migrateUsersTable(db *sql.DB) error {
	// Check if status column exists
	var statusExists bool
	err := db.QueryRow(`
		SELECT EXISTS (
			SELECT 1 FROM information_schema.columns 
			WHERE table_name = 'users' AND column_name = 'status'
		)
	`).Scan(&statusExists)
	if err != nil {
		logger.Error("Failed to check if status column exists in users table: %v", err)
		return err
	}

	// Add status column if it doesn't exist
	if !statusExists {
		_, err := db.Exec(`ALTER TABLE users ADD COLUMN status TEXT NOT NULL DEFAULT 'approved'`)
		if err != nil {
			logger.Error("Failed to add status column to users table: %v", err)
			return err
		}
		logger.Log("Added status column to users table")
	}

	return nil
}

// migrateRecordersTable adds missing columns to the recorders table
func migrateRecordersTable(db *sql.DB) error {
	// Check if network_interface column exists
	var networkInterfaceExists bool
	err := db.QueryRow(`
		SELECT EXISTS (
			SELECT 1 FROM information_schema.columns 
			WHERE table_name = 'recorders' AND column_name = 'network_interface'
		)
	`).Scan(&networkInterfaceExists)
	if err != nil {
		logger.Error("Failed to check if network_interface column exists in recorders table: %v", err)
		return err
	}

	// Add network_interface column if it doesn't exist
	if !networkInterfaceExists {
		_, err := db.Exec(`ALTER TABLE recorders ADD COLUMN network_interface TEXT DEFAULT ''`)
		if err != nil {
			logger.Error("Failed to add network_interface column to recorders table: %v", err)
			return err
		}
		logger.Log("Added network_interface column to recorders table")
	}

	// Check if source_ip column exists
	var sourceIpExists bool
	err = db.QueryRow(`
		SELECT EXISTS (
			SELECT 1 FROM information_schema.columns 
			WHERE table_name = 'recorders' AND column_name = 'source_ip'
		)
	`).Scan(&sourceIpExists)
	if err != nil {
		logger.Error("Failed to check if source_ip column exists in recorders table: %v", err)
		return err
	}

	// Add source_ip column if it doesn't exist
	if !sourceIpExists {
		_, err := db.Exec(`ALTER TABLE recorders ADD COLUMN source_ip TEXT DEFAULT ''`)
		if err != nil {
			logger.Error("Failed to add source_ip column to recorders table: %v", err)
			return err
		}
		logger.Log("Added source_ip column to recorders table")
	}

	// Check if service_id column exists
	var serviceIdExists bool
	err = db.QueryRow(`
		SELECT EXISTS (
			SELECT 1 FROM information_schema.columns 
			WHERE table_name = 'recorders' AND column_name = 'service_id'
		)
	`).Scan(&serviceIdExists)
	if err != nil {
		logger.Error("Failed to check if service_id column exists in recorders table: %v", err)
		return err
	}

	// Add service_id column if it doesn't exist
	if !serviceIdExists {
		_, err := db.Exec(`ALTER TABLE recorders ADD COLUMN service_id INTEGER DEFAULT NULL`)
		if err != nil {
			logger.Error("Failed to add service_id column to recorders table: %v", err)
			return err
		}
		logger.Log("Added service_id column to recorders table")
	}

	return nil
}

// migrateContentAnalyticsTable adds missing indexes to the content_analytics table
func migrateContentAnalyticsTable(db *sql.DB) error {
	// Check if the composite index exists
	var indexExists bool
	err := db.QueryRow(`
		SELECT EXISTS (
			SELECT 1 FROM pg_indexes 
			WHERE tablename = 'content_analytics' AND indexname = 'idx_content_analytics_schedule_played_at'
		)
	`).Scan(&indexExists)
	if err != nil {
		logger.Error("Failed to check if composite index exists in content_analytics table: %v", err)
		return err
	}

	// Create the composite index if it doesn't exist
	if !indexExists {
		_, err := db.Exec(`
			CREATE INDEX IF NOT EXISTS idx_content_analytics_schedule_played_at 
			ON content_analytics(schedule_id, played_at)
		`)
		if err != nil {
			logger.Error("Failed to create composite index on content_analytics table: %v", err)
			return err
		}
		logger.Log("Added composite index to content_analytics table")
	}

	return nil
}
