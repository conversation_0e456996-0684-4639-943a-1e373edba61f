package scheduler

import (
	"fmt"
	"path/filepath"
	"showfer-web/models"
	"showfer-web/repository"
	"showfer-web/service/logger"
	recorder "showfer-web/service/recorder"
	"strconv"
	"strings"
	"sync"
	"time"
)

// SchedulerService handles scheduled recordings
type SchedulerService struct {
	recorderRepo *repository.RecorderRepository
	ticker       *time.Ticker
	stopChan     chan bool
	mutex        sync.RWMutex
	running      bool
}

// NewSchedulerService creates a new scheduler service
func NewSchedulerService(recorderRepo *repository.RecorderRepository) *SchedulerService {
	return &SchedulerService{
		recorderRepo: recorderRepo,
		stopChan:     make(chan bool),
	}
}

// Start begins the scheduler service
func (s *SchedulerService) Start() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.running {
		logger.Log("Scheduler service is already running")
		return
	}

	s.running = true
	s.ticker = time.NewTicker(30 * time.Second) // Check every 30 seconds

	go s.run()
	logger.Log("Scheduler service started")
}

// Stop stops the scheduler service
func (s *SchedulerService) Stop() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.running {
		return
	}

	s.running = false
	s.stopChan <- true
	if s.ticker != nil {
		s.ticker.Stop()
	}
	logger.Log("Scheduler service stopped")
}

// run is the main loop of the scheduler
func (s *SchedulerService) run() {
	for {
		select {
		case <-s.ticker.C:
			s.checkScheduledRecordings()
		case <-s.stopChan:
			return
		}
	}
}

// checkScheduledRecordings checks for recordings that should be started
func (s *SchedulerService) checkScheduledRecordings() {
	// Get all scheduled recordings that are stopped
	pagination := models.Pagination{Page: 1, Limit: 1000} // Get all recordings
	result, err := s.recorderRepo.GetRecorders(pagination)
	if err != nil {
		logger.Error("Failed to get recorders for scheduling: %v", err)
		return
	}

	now := time.Now()
	
	for _, rec := range result.Items {
		// Skip if not scheduled or already running
		if !rec.IsScheduled || rec.Status != "stopped" || rec.ScheduledStartTime == nil {
			continue
		}

		// Parse the scheduled start time
		scheduledTime, err := time.Parse("2006-01-02T15:04:05Z07:00", *rec.ScheduledStartTime)
		if err != nil {
			logger.Error("Failed to parse scheduled start time for recorder %d: %v", rec.ID, err)
			continue
		}

		// Check if it's time to start (within 30 seconds window)
		if now.After(scheduledTime) && now.Before(scheduledTime.Add(30*time.Second)) {
			logger.Log("Starting scheduled recording for recorder %d", rec.ID)
			s.startScheduledRecording(rec)
		}
	}
}

// startScheduledRecording starts a scheduled recording
func (s *SchedulerService) startScheduledRecording(rec models.Recorder) {
	// Update the status in the database first
	err := s.recorderRepo.StartRecorder(rec.ID)
	if err != nil {
		logger.Error("Failed to update recorder status in database for recorder %d: %v", rec.ID, err)
		return
	}

	// Get the recordings directory
	recordingsDir := filepath.Join("./data", "recordings")

	// Parse duration string to seconds
	var durationSeconds int

	// Check if the duration is in HH:MM:SS format
	if strings.Contains(rec.Duration, ":") {
		durationParts := strings.Split(rec.Duration, ":")
		if len(durationParts) != 3 {
			logger.Error("Invalid duration format for recorder %d, expected HH:MM:SS", rec.ID)
			_ = s.recorderRepo.StopRecorder(rec.ID)
			return
		}

		hours, err := strconv.Atoi(durationParts[0])
		if err != nil {
			logger.Error("Invalid hours in duration for recorder %d", rec.ID)
			_ = s.recorderRepo.StopRecorder(rec.ID)
			return
		}

		minutes, err := strconv.Atoi(durationParts[1])
		if err != nil {
			logger.Error("Invalid minutes in duration for recorder %d", rec.ID)
			_ = s.recorderRepo.StopRecorder(rec.ID)
			return
		}

		seconds, err := strconv.Atoi(durationParts[2])
		if err != nil {
			logger.Error("Invalid seconds in duration for recorder %d", rec.ID)
			_ = s.recorderRepo.StopRecorder(rec.ID)
			return
		}

		durationSeconds = hours*3600 + minutes*60 + seconds
	} else {
		// Try to parse as a direct number of seconds
		seconds, err := strconv.Atoi(rec.Duration)
		if err != nil {
			logger.Error("Invalid duration format for recorder %d, expected either HH:MM:SS or number of seconds", rec.ID)
			_ = s.recorderRepo.StopRecorder(rec.ID)
			return
		}
		durationSeconds = seconds
	}

	// Prepare the RTP URL with network interface if specified
	rtpURL := rec.Input
	if rec.NetworkInterface != "" {
		// Check if the URL already has query parameters
		if strings.Contains(rtpURL, "?") {
			rtpURL += "&iface=" + rec.NetworkInterface
		} else {
			rtpURL += "?iface=" + rec.NetworkInterface
		}
		logger.Log("Using network interface %s for scheduled recorder %d", rec.NetworkInterface, rec.ID)
	}

	// Start the recording process
	err = recorder.StartRecording(
		rec.ID,
		rtpURL,
		recordingsDir,
		durationSeconds,
		rec.VCodec,
		rec.ACodec,
		rec.Resolution,
		fmt.Sprintf("%.2f", rec.FPS),
		rec.SampleRate,
		rec.VBitrate,
		rec.ABitrate,
		rec.MaxVBitrate,
		0, // Service ID - 0 means record all services
		rec.SourceIP, // Source IP from recorder configuration
	)

	if err != nil {
		// If starting the recording fails, revert the database status
		logger.Error("Failed to start scheduled recording for recorder %d: %v", rec.ID, err)
		_ = s.recorderRepo.StopRecorder(rec.ID)
		return
	}

	logger.Log("Successfully started scheduled recording for recorder %d", rec.ID)
}
