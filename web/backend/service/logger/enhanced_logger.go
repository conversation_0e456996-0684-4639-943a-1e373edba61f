package logger

import (
	"encoding/json"
	"fmt"
	"runtime"
	"showfer-web/models"
	"strings"
	"time"
)

// LogsRepository interface to avoid import cycles
type LogsRepository interface {
	CreateLogEntry(entry *models.LogEntry) error
}

// EnhancedLogger provides both file and database logging capabilities
type EnhancedLogger struct {
	logsRepo LogsRepository
	enabled  bool
}

var enhancedLogger *EnhancedLogger

// LogContext contains contextual information for logging
type LogContext struct {
	UserID    *int64
	RequestID *string
	Source    string
	Metadata  map[string]interface{}
}

// InitEnhancedLogger initializes the enhanced logger with database support
func InitEnhancedLogger(logsRepo LogsRepository) {
	enhancedLogger = &EnhancedLogger{
		logsRepo: logsRepo,
		enabled:  true,
	}
}

// GetEnhancedLogger returns the enhanced logger instance
func GetEnhancedLogger() *EnhancedLogger {
	return enhancedLogger
}

// logToDatabase logs an entry to the database
func (el *EnhancedLogger) logToDatabase(level models.LogLevel, message string, ctx *LogContext) {
	if el == nil || !el.enabled || el.logsRepo == nil {
		return
	}

	// Get caller information for source if not provided
	source := "unknown"
	if ctx != nil && ctx.Source != "" {
		source = ctx.Source
	} else {
		// Get caller info
		if pc, _, line, ok := runtime.Caller(3); ok {
			funcName := runtime.FuncForPC(pc).Name()
			// Extract just the package and function name
			parts := strings.Split(funcName, "/")
			if len(parts) > 0 {
				lastPart := parts[len(parts)-1]
				source = fmt.Sprintf("%s:%d", lastPart, line)
			}
		}
	}

	// Prepare metadata
	var metadataJSON *string
	if ctx != nil && len(ctx.Metadata) > 0 {
		if jsonBytes, err := json.Marshal(ctx.Metadata); err == nil {
			metadataStr := string(jsonBytes)
			metadataJSON = &metadataStr
		}
	}

	// Create log entry
	entry := &models.LogEntry{
		Timestamp: time.Now(),
		Level:     level,
		Message:   message,
		Source:    source,
		Metadata:  metadataJSON,
	}

	if ctx != nil {
		entry.UserID = ctx.UserID
		entry.RequestID = ctx.RequestID
	}

	// Log to database (non-blocking)
	go func() {
		if err := el.logsRepo.CreateLogEntry(entry); err != nil {
			// Fallback to file logging if database fails
			Error("Failed to log to database: %v", err)
		}
	}()
}

// LogWithContext logs an informational message with context
func LogWithContext(ctx *LogContext, format string, v ...interface{}) {
	message := fmt.Sprintf(format, v...)
	
	// Log to file (existing behavior)
	Log(format, v...)
	
	// Log to database
	if enhancedLogger != nil {
		enhancedLogger.logToDatabase(models.LogLevelInfo, message, ctx)
	}
}

// WarnWithContext logs a warning message with context
func WarnWithContext(ctx *LogContext, format string, v ...interface{}) {
	message := fmt.Sprintf(format, v...)
	
	// Log to file (existing behavior)
	Warn(format, v...)
	
	// Log to database
	if enhancedLogger != nil {
		enhancedLogger.logToDatabase(models.LogLevelWarn, message, ctx)
	}
}

// ErrorWithContext logs an error message with context
func ErrorWithContext(ctx *LogContext, format string, v ...interface{}) {
	message := fmt.Sprintf(format, v...)
	
	// Log to file (existing behavior)
	Error(format, v...)
	
	// Log to database
	if enhancedLogger != nil {
		enhancedLogger.logToDatabase(models.LogLevelError, message, ctx)
	}
}

// DebugWithContext logs a debug message with context
func DebugWithContext(ctx *LogContext, format string, v ...interface{}) {
	message := fmt.Sprintf(format, v...)
	
	// Debug logs only go to database for now
	if enhancedLogger != nil {
		enhancedLogger.logToDatabase(models.LogLevelDebug, message, ctx)
	}
}

// Enhanced convenience functions that automatically detect source

// LogInfo logs an informational message with automatic source detection
func LogInfo(format string, v ...interface{}) {
	LogWithContext(nil, format, v...)
}

// LogWarn logs a warning message with automatic source detection
func LogWarn(format string, v ...interface{}) {
	WarnWithContext(nil, format, v...)
}

// LogError logs an error message with automatic source detection
func LogError(format string, v ...interface{}) {
	ErrorWithContext(nil, format, v...)
}

// LogDebug logs a debug message with automatic source detection
func LogDebug(format string, v ...interface{}) {
	DebugWithContext(nil, format, v...)
}

// User-specific logging functions

// LogUserAction logs a user action with user context
func LogUserAction(userID int64, action string, metadata map[string]interface{}) {
	ctx := &LogContext{
		UserID:   &userID,
		Source:   "user_action",
		Metadata: metadata,
	}
	LogWithContext(ctx, "User action: %s", action)
}

// LogAPIRequest logs an API request with request context
func LogAPIRequest(userID *int64, requestID, method, path string, statusCode int, duration time.Duration) {
	metadata := map[string]interface{}{
		"method":      method,
		"path":        path,
		"status_code": statusCode,
		"duration_ms": duration.Milliseconds(),
	}
	
	ctx := &LogContext{
		UserID:    userID,
		RequestID: &requestID,
		Source:    "api_request",
		Metadata:  metadata,
	}
	
	LogWithContext(ctx, "API %s %s - %d (%dms)", method, path, statusCode, duration.Milliseconds())
}

// LogSystemEvent logs a system event
func LogSystemEvent(event string, metadata map[string]interface{}) {
	ctx := &LogContext{
		Source:   "system",
		Metadata: metadata,
	}
	LogWithContext(ctx, "System event: %s", event)
}

// LogRecorderEvent logs a recorder-related event
func LogRecorderEvent(recorderID int, event string, metadata map[string]interface{}) {
	if metadata == nil {
		metadata = make(map[string]interface{})
	}
	metadata["recorder_id"] = recorderID
	
	ctx := &LogContext{
		Source:   "recorder",
		Metadata: metadata,
	}
	LogWithContext(ctx, "Recorder %d: %s", recorderID, event)
}

// LogFileEvent logs a file-related event
func LogFileEvent(filename, event string, metadata map[string]interface{}) {
	if metadata == nil {
		metadata = make(map[string]interface{})
	}
	metadata["filename"] = filename
	
	ctx := &LogContext{
		Source:   "file_manager",
		Metadata: metadata,
	}
	LogWithContext(ctx, "File %s: %s", filename, event)
}

// LogSchedulerEvent logs a scheduler-related event
func LogSchedulerEvent(scheduleID int64, event string, metadata map[string]interface{}) {
	if metadata == nil {
		metadata = make(map[string]interface{})
	}
	metadata["schedule_id"] = scheduleID
	
	ctx := &LogContext{
		Source:   "scheduler",
		Metadata: metadata,
	}
	LogWithContext(ctx, "Schedule %d: %s", scheduleID, event)
}

// SetEnabled enables or disables database logging
func (el *EnhancedLogger) SetEnabled(enabled bool) {
	if el != nil {
		el.enabled = enabled
	}
}

// IsEnabled returns whether database logging is enabled
func (el *EnhancedLogger) IsEnabled() bool {
	return el != nil && el.enabled
}
