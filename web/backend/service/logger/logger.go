package logger

import (
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"time"
)

var (
	infoLogger  *log.Logger
	warnLogger  *log.Logger
	errorLogger *log.Logger
)

// Init initializes the logger
func Init() {
	// Create logs directory if it doesn't exist
	logsDir := "./logs"
	if err := os.Mkdir<PERSON>ll(logsDir, 0755); err != nil {
		log.Fatalf("Failed to create logs directory: %v", err)
	}

	// Create log files
	currentTime := time.Now().Format("2006-01-02")
	infoLogFile, err := os.OpenFile(
		filepath.Join(logsDir, fmt.Sprintf("info_%s.log", currentTime)),
		os.O_APPEND|os.O_CREATE|os.O_WRONLY,
		0644,
	)
	if err != nil {
		log.Fatalf("Failed to open info log file: %v", err)
	}

	errorLogFile, err := os.OpenFile(
		filepath.Join(logsDir, fmt.Sprintf("error_%s.log", currentTime)),
		os.O_APPEND|os.O_CREATE|os.O_WRONLY,
		0644,
	)
	if err != nil {
		log.Fatalf("Failed to open error log file: %v", err)
	}

	// Initialize loggers
	infoLogger = log.New(infoLogFile, "INFO: ", log.Ldate|log.Ltime|log.Lshortfile)
	warnLogger = log.New(infoLogFile, "WARN: ", log.Ldate|log.Ltime|log.Lshortfile)
	errorLogger = log.New(errorLogFile, "ERROR: ", log.Ldate|log.Ltime|log.Lshortfile)

	// Also log to console
	multiInfoWriter := io.MultiWriter(os.Stdout, infoLogFile)
	multiWarnWriter := io.MultiWriter(os.Stdout, infoLogFile)
	multiErrorWriter := io.MultiWriter(os.Stderr, errorLogFile)
	infoLogger.SetOutput(multiInfoWriter)
	warnLogger.SetOutput(multiWarnWriter)
	errorLogger.SetOutput(multiErrorWriter)

	Log("Logger initialized")
}

// Log logs an informational message
func Log(format string, v ...interface{}) {
	if infoLogger == nil {
		infoLogger = log.New(os.Stdout, "INFO: ", log.Ldate|log.Ltime|log.Lshortfile)
	}
	infoLogger.Printf(format, v...)
}

// Warn logs a warning message
func Warn(format string, v ...interface{}) {
	if warnLogger == nil {
		warnLogger = log.New(os.Stdout, "WARN: ", log.Ldate|log.Ltime|log.Lshortfile)
	}
	warnLogger.Printf(format, v...)
}

// Error logs an error message
func Error(format string, v ...interface{}) {
	if errorLogger == nil {
		errorLogger = log.New(os.Stderr, "ERROR: ", log.Ldate|log.Ltime|log.Lshortfile)
	}
	errorLogger.Printf(format, v...)
}

// ErrorWithReturn logs an error message and returns an error
func ErrorWithReturn(format string, v ...interface{}) error {
	Error(format, v...)
	return fmt.Errorf(format, v...)
}
