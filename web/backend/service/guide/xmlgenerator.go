package guide

import (
	"encoding/xml"
	"fmt"
	"os"
	"regexp"
	"showfer-web/models"
	"strconv"
	"strings"
	"time"
)

const (
	SubFolder         = "epg"
	SourceInfoUrl     = ""              // TODO: needs to add dynamic settings
	GeneratorInfoName = "Showfer Media" // TODO: needs to add dynamic settings (company name)
	SystemEpisodeAttr = "xmltv_ns"
	Lang              = "en"
)

type XmlGenerator struct {
	baseDir string
}

func NewXmlGenerator(baseDir string) *XmlGenerator {
	return &XmlGenerator{baseDir: baseDir}
}

func (g *XmlGenerator) Generate(guide models.Guide, schedule models.Schedule) error {
	loc, _ := time.LoadLocation(schedule.Timezone)
	_, utcOffset := time.Now().In(loc).Zone()

	bk := models.Tv{
		SourceInfoUrl:     SourceInfoUrl,
		SourceInfoName:    schedule.Name,
		GeneratorInfoName: GeneratorInfoName,
		Channel: models.Channel{
			Id:          schedule.Name,
			DisplayName: schedule.Name,
			Icon:        ternary(len(schedule.Icon) > 0, schedule.Icon, ""),
		},
	}

	for _, element := range guide.Elements {
		startEl, _ := time.Parse("2006-01-02T15:04:05Z", element.Start)
		utcTimeStart := startEl.Add(-time.Duration(utcOffset) * time.Second)

		endEl, _ := time.Parse("2006-01-02T15:04:05Z", element.End)
		utcTimeEnd := endEl.Add(-time.Duration(utcOffset) * time.Second)

		var episodeNumber any //If specify the document type.Episode Number, XML will show the episode-num field with an empty value
		if g.episodeToInt(element.File.Episode) > 0 {
			episodeNumber = models.EpisodeNumber{
				Value:  element.File.Episode,
				System: SystemEpisodeAttr,
			}
		}

		re := regexp.MustCompile(`[&./\\#,+()$~%'":*?<>^{}]`)
		title := re.ReplaceAllString(element.Title, "")
		desc := re.ReplaceAllString(element.Description, "")

		program := models.Program{
			Title:   title,
			Start:   utcTimeStart.Format("20060102150405 +0000"),
			Stop:    utcTimeEnd.Format("20060102150405 +0000"),
			Date:    startEl.Format("20060102"),
			Channel: schedule.Name,
			SubTitle: models.SubTitle{
				Value: subStr(element.Title, 0, 35),
				Lang:  Lang,
			},
			Desc: models.Desc{
				Value: ternary(len(desc) > 0, desc, title),
				Lang:  Lang,
			},
			EpisodeNumber: episodeNumber,
		}
		bk.Program = append(bk.Program, program)
	}

	epgFolder := fmt.Sprintf("%s/%s", g.baseDir, SubFolder)
	if _, err := os.Stat(epgFolder); os.IsNotExist(err) {
		err := os.Mkdir(epgFolder, 0755)
		if err != nil {
			return fmt.Errorf("error creating EPG folder: %v", err)
		}
	}

	xmlFile, err := os.Create(fmt.Sprintf("%s/%s.xml", epgFolder, schedule.ShortID))
	if err != nil {
		return fmt.Errorf("error creating XML file: %v", err)
	}

	encoder := xml.NewEncoder(xmlFile)
	encoder.Indent("", "\t")
	err = encoder.Encode(&bk)
	if err != nil {
		return fmt.Errorf("error encoding XML to file: %v", err)
	}

	return nil
}

func (g *XmlGenerator) episodeToInt(episodeNum string) int {
	re := regexp.MustCompile("\\d+")
	str := strings.Join(re.FindAllString(episodeNum, -1), "")
	episode, _ := strconv.Atoi(str)

	return episode
}

func ternary[T any](condition bool, first T, second T) T {
	if condition {
		return first
	} else {
		return second
	}
}

func subStr(input string, start int, length int) string {
	asRunes := []rune(input)

	if start >= len(asRunes) {
		return ""
	}

	if start+length > len(asRunes) {
		length = len(asRunes) - start
	}

	return string(asRunes[start : start+length])
}
