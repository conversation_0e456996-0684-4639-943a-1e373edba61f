package guide

import (
	"database/sql"
	"errors"
	"fmt"
	"math/rand"
	"regexp"
	"showfer-web/models"
	"showfer-web/repository"
	"showfer-web/service/logger"
	"showfer-web/service/playout"
	"slices"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"
)

const (
	ConnectionType = "connection"
	FillerType     = "filler"
	FileType       = "file"
	MinuteOffset   = 10
)

type GuideGenerator struct {
	scheduleRepository *repository.ScheduleRepository
	fileRepo           *repository.FilesRepository
	guideRepo          *repository.GuideRepository
	historyRepo        *repository.HistoryRepository
	schedule           models.Schedule
	usedElements       map[string][]models.History
	mutex              sync.Mutex
	xmlGenerator       *XmlGenerator
	playout            *playout.Playout
}

func NewGuideGenerator(db *sql.DB, playout *playout.Playout) *GuideGenerator {
	return &GuideGenerator{
		scheduleRepository: repository.NewScheduleRepository(db),
		fileRepo:           repository.NewFilesRepository(db),
		guideRepo:          repository.NewGuideRepository(db),
		historyRepo:        repository.NewHistoryRepository(db),
		xmlGenerator:       NewXmlGenerator(playout.BaseDir),
		playout:            playout,
	}
}

func (g *GuideGenerator) UpdateAllGuides() error {
	result, err := g.scheduleRepository.ListSchedules(models.Pagination{
		Page:  1,
		Limit: 100,
	})
	if err != nil {
		return err
	}

	for _, schedule := range result.Items {
		err = g.Generate(schedule, false)
		if err != nil {
			logger.Error("Failed generate guide: %v", err)
			continue
		}
	}

	return nil
}

func (g *GuideGenerator) Generate(schedule models.Schedule, force bool) error {
	g.schedule = schedule
	g.usedElements = make(map[string][]models.History)

	var count int
	var dayNumber int
	var startTime time.Time
	var nowWithMinuteOffset time.Time
	var currentTimeBlock time.Time
	var currentLastElementTime time.Time

	itemDuration := 0

	loc, _ := time.LoadLocation(schedule.Timezone)
	_, offset := time.Now().In(loc).Zone()

	utcLoc, _ := time.LoadLocation("UTC")
	now := time.Now().In(utcLoc).Add(time.Duration(offset) * time.Second)

	nowWithMinuteOffset = now.Add(MinuteOffset * time.Minute)
	dayNumber = int(nowWithMinuteOffset.Weekday())

	isNewGuide := false
	guide, err := g.guideRepo.FindGuideByScheduleID(schedule.ID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			isNewGuide = true
		} else {
			return fmt.Errorf("failed to find guide: %v", err)
		}
	}

	if isNewGuide || force || len(guide.Elements) == 0 {
		if isNewGuide {
			guide.ScheduleId = schedule.ID
		}
		count = 6 // today + 6 days = 7 days

		startTime = time.Date(nowWithMinuteOffset.Year(), nowWithMinuteOffset.Month(), nowWithMinuteOffset.Day(), 0, 0, 0, 0, now.Location())
		currentLastElementTime = startTime

		if int(nowWithMinuteOffset.Weekday()) == dayNumber && force {
			currentTimeBlock = g.getCurrentTimeBlock(nowWithMinuteOffset)
			guide.Elements = arrayFilter(guide.Elements, func(element models.Element) bool {
				startEl, _ := time.Parse("2006-01-02T15:04:05Z", element.Start)
				return startEl.Unix() < currentTimeBlock.Unix()
			})
			guide.Elements = arrayFilter(guide.Elements, func(element models.Element) bool {
				return element.Type != FillerType

			})
			if len(guide.Elements) > 0 {
				for _, element := range guide.Elements {
					g.addedUsedElements(element)
				}
				lastEl := guide.Elements[len(guide.Elements)-1]
				endLast, _ := time.Parse("2006-01-02T15:04:05Z", lastEl.End)
				currentLastElementTime = endLast
				startTime = currentLastElementTime
				if currentLastElementTime.Unix() > currentTimeBlock.Unix() {
					currentTimeBlock = currentLastElementTime
				}

				endLastElement := time.Date(nowWithMinuteOffset.Year(), nowWithMinuteOffset.Month(), nowWithMinuteOffset.Day(), 0, 0, 0, 0, now.Location())
				for _, element := range guide.Elements {
					startEl, _ := time.Parse("2006-01-02T15:04:05Z", element.Start)
					endEl, _ := time.Parse("2006-01-02T15:04:05Z", element.End)
					if startEl.Unix() > endLastElement.Unix() {
						filler := g.createFiller(endLastElement, startEl)
						guide.Elements = append(guide.Elements, filler)
					}
					endLastElement = endEl

				}

				sort.Slice(guide.Elements, func(i, j int) bool {
					first, _ := time.Parse("2006-01-02T15:04:05Z", guide.Elements[i].Start)
					second, _ := time.Parse("2006-01-02T15:04:05Z", guide.Elements[j].Start)

					return first.Unix() < second.Unix()
				})
			}
		}

	} else {
		lastEl := guide.Elements[len(guide.Elements)-1]
		startTime, _ = time.Parse("2006-01-02T15:04:05Z", lastEl.End)

		for _, element := range guide.Elements {
			g.addedUsedElements(element)
		}

		diff := startTime.Sub(time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, startTime.Location()))
		diffDays := int(diff.Hours() / 24)

		if diffDays >= 4 { // Already created
			return g.xmlGenerator.Generate(guide, schedule)
		}

		count = 4 - diffDays
		dayNumber = dayNumber + diffDays
		if dayNumber > 6 { // Next week
			dayNumber = dayNumber - 7
		}
	}
	for number := 0; number <= count; number++ {
		day := schedule.RegularDays[dayNumber]

		specialDayIndex := slices.IndexFunc(schedule.SpecialDays, func(day models.Day) bool {
			dayDate, _ := time.Parse(time.RFC3339, day.Date)
			return dayDate.Format("2006-02-01") == startTime.Format("2006-02-01")
		})

		if specialDayIndex != -1 {
			day = schedule.SpecialDays[specialDayIndex]
		}

		sort.Slice(day.Items, func(i, j int) bool {
			first, _ := time.Parse("2006-01-02T15:04:05Z", day.Items[i].Start)
			second, _ := time.Parse("2006-01-02T15:04:05Z", day.Items[j].Start)

			return first.Unix() < second.Unix()
		})

		for _, item := range day.Items {
			start, _ := time.Parse("2006-01-02T15:04:05Z", item.Start)
			end, _ := time.Parse("2006-01-02T15:04:05Z", item.End)

			if int(nowWithMinuteOffset.Weekday()) == dayNumber && force && !isNewGuide {
				if start.Unix() < currentTimeBlock.Unix() && end.Unix() <= currentTimeBlock.Unix() {
					continue
				} else if start.Unix() < currentTimeBlock.Unix() && end.Unix() > currentTimeBlock.Unix() {
					if currentTimeBlock.Unix() > currentLastElementTime.Unix() {
						element := g.createFiller(currentLastElementTime, currentTimeBlock)
						guide.Elements = append(guide.Elements, element)
					}
					if item.Type == "connection" {
						element := g.createConnectionElement(item, currentTimeBlock, end)
						guide.Elements = append(guide.Elements, element)
					} else {
						elements := g.getElements(item, currentTimeBlock, end, 0, &itemDuration)
						guide.Elements = append(guide.Elements, elements...)
					}
					startTime = end
				} else if start.Unix() >= currentTimeBlock.Unix() {
					if start.Unix() > startTime.Unix() {
						element := g.createFiller(startTime, start)
						guide.Elements = append(guide.Elements, element)
						startTime = start
					}
					if item.Type == "connection" {
						element := g.createConnectionElement(item, start, end)
						guide.Elements = append(guide.Elements, element)
					} else {
						elements := g.getElements(item, start, end, 0, &itemDuration)
						guide.Elements = append(guide.Elements, elements...)
					}
					startTime = end
				}
			} else {
				endDay := startTime.Day()
				if start.Day() != end.Day() {
					endDay = startTime.Day() + 1
				}
				start = time.Date(startTime.Year(), startTime.Month(), startTime.Day(), start.Hour(), start.Minute(), 0, 0, start.Location())
				end = time.Date(startTime.Year(), startTime.Month(), endDay, end.Hour(), end.Minute(), 0, 0, end.Location())

				if start.Unix() > startTime.Unix() {
					element := g.createFiller(startTime, start)
					guide.Elements = append(guide.Elements, element)
					startTime = start
				}

				if item.Type == "connection" {
					element := g.createConnectionElement(item, start, end)
					guide.Elements = append(guide.Elements, element)
				} else {
					elements := g.getElements(item, start, end, 0, &itemDuration)
					guide.Elements = append(guide.Elements, elements...)
				}

				startTime = end
			}
		}

		if len(day.Items) == 0 {
			guide.Elements = arrayFilter(guide.Elements, func(element models.Element) bool {
				startEl, _ := time.Parse("2006-01-02T15:04:05Z", element.Start)
				return startEl.Unix() != startTime.Unix()
			})
			element := g.createFiller(startTime, startTime.Add(24*time.Hour))
			guide.Elements = append(guide.Elements, element)
			startTime = startTime.Add(24 * time.Hour)
		}

		if int(startTime.Weekday()) == dayNumber {
			endOfDay := time.Date(startTime.Year(), startTime.Month(), startTime.Day()+1, 0, 0, 0, 0, utcLoc)
			guide.Elements = arrayFilter(guide.Elements, func(element models.Element) bool {
				startEl, _ := time.Parse("2006-01-02T15:04:05Z", element.Start)
				return startEl.Unix() != startTime.Unix()
			})
			element := g.createFiller(startTime, endOfDay)
			guide.Elements = append(guide.Elements, element)

			startTime = endOfDay
		}

		dayNumber++
		if dayNumber > 6 {
			dayNumber = 0
		}
	}

	checkTime := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	// Clear old guide elements
	guide.Elements = arrayFilter(guide.Elements, func(element models.Element) bool {
		endTime, _ := time.Parse("2006-01-02T15:04:05Z", element.End)
		return checkTime.Sub(endTime).Hours() < (2 * 24) // Last 3 days
	})

	// Clear old special days
	schedule.SpecialDays = arrayFilter(schedule.SpecialDays, func(day models.Day) bool {
		dayDate, _ := time.Parse(time.RFC3339, day.Date)
		return checkTime.Sub(dayDate).Hours() < (8 * 24) // Last week
	})
	if isNewGuide {
		_, err := g.guideRepo.CreateGuide(guide)
		if err != nil {
			logger.Error("Failed to create guide: ", err)
			return err
		}
	} else {
		err := g.guideRepo.UpdateGuide(guide)
		if err != nil {
			logger.Error("Failed to update guide: ", err)
			return err
		}
	}

	err = g.playout.Run(schedule)
	if err != nil {
		logger.Error("Failed to run playout: %v", err)
	}

	return g.xmlGenerator.Generate(guide, schedule)
}

func (g *GuideGenerator) ReplaceProgramWithNewFile(
	guide models.Guide,
	changeProgram models.Element,
	newProgramFile models.NewProgram,
	schedule models.Schedule,
) (models.Guide, error) {
	convertItem, err := g.fileRepo.GetConvertItemById(newProgramFile.Id)
	if err != nil {
		logger.Error("Failed find new file: %v", err)
		return guide, err
	}
	minDuration, maxDuration := g.GetMaxAndMinFileDuration(int(convertItem.Duration))
	if int(convertItem.Duration) <= minDuration || int(convertItem.Duration) > maxDuration {
		logger.Error("Wrong new file duration: %v", err)
		return guide, errors.New("wrong new file duration")
	}

	for i, elem := range guide.Elements {
		if elem.Start == changeProgram.Start && elem.End == changeProgram.End && elem.File.FileId == changeProgram.File.FileId {
			guide.Elements[i].Title = convertItem.Name
			guide.Elements[i].File = models.File{
				FileId:  convertItem.ID,
				Folder:  convertItem.Location,
				Episode: convertItem.Episode,
			}
			break
		}
	}
	err = g.xmlGenerator.Generate(guide, schedule)
	if err != nil {
		logger.Error("Failed generate xml: %v", err)
		return guide, err
	}
	return guide, nil
}

func (g *GuideGenerator) GetMaxAndMinFileDuration(duration int) (int, int) {
	const interval = 1800

	adjusted := duration - 1
	if adjusted < 0 {
		adjusted = 0
	}

	lower := (adjusted / interval) * interval
	upper := lower + interval

	return lower, upper
}

func (g *GuideGenerator) addedUsedElements(element models.Element) {
	g.mutex.Lock()
	defer g.mutex.Unlock()

	if element.Type == FileType {
		localUsedElements := g.usedElements
		localUsedElements[element.File.Folder] = append(localUsedElements[element.File.Folder], models.History{
			ScheduleId: g.schedule.ID,
			Folder:     element.File.Folder,
			FileId:     element.File.FileId,
			Episode:    g.episodeToInt(element.File.Episode),
		})

		g.usedElements = localUsedElements
	}
}

func (g *GuideGenerator) createFileElements(
	itemDuration *int,
	start time.Time,
	end time.Time,
	files []models.ConvertItem,
	fillers models.Fillers,
) []models.Element {
	var elements []models.Element
	duration := end.Sub(start)

	*itemDuration = 0
	return g.getRandElementsFromList(itemDuration, duration, start, files, elements, fillers)
}

func (g *GuideGenerator) getRandElementsFromList(
	itemDuration *int,
	duration time.Duration,
	start time.Time,
	files []models.ConvertItem,
	elements []models.Element,
	fillers models.Fillers,
) []models.Element {
	var file models.ConvertItem
	episodes := arrayFilter(files, func(item models.ConvertItem) bool {
		return g.episodeToInt(item.Episode) > 0 && int(item.Duration) <= int(duration.Seconds())
	})

	files = arrayFilter(files, func(item models.ConvertItem) bool {
		return int(item.Duration) <= int(duration.Seconds())
	})

	if len(episodes) == 0 {
		if len(files) == 0 && duration.Seconds() >= 30 {
			*itemDuration = int(duration.Seconds())
			return elements
		}
		file = files[rand.Intn(len(files))]
	} else {
		sort.Slice(episodes, func(i, j int) bool {
			return g.episodeToInt(episodes[i].Episode) < g.episodeToInt(episodes[j].Episode)
		})

		file = episodes[0]
	}

	elementDuration := 1800 - (int(file.Duration) % 1800) + int(file.Duration)
	end := start.Add(time.Duration(elementDuration) * time.Second)

	element := models.Element{
		Type:        FileType,
		Title:       file.Name,
		Description: file.Description,
		File: models.File{
			FileId:  file.ID,
			Folder:  file.Location,
			Episode: file.Episode,
		},
		Start:   start.Format("2006-01-02T15:04:05Z"),
		End:     end.Format("2006-01-02T15:04:05Z"),
		Fillers: fillers,
	}
	elements = append(elements, element)
	g.addedUsedElements(element)

	files = arrayFilter(files, func(item models.ConvertItem) bool {
		return item.ID != file.ID
	})

	if (int(duration.Seconds()) - elementDuration) > 0 {
		seconds := int(duration.Seconds()) - elementDuration
		duration = time.Duration(seconds * int(time.Second))

		return g.getRandElementsFromList(itemDuration, duration, end, files, elements, fillers)
	}

	return elements
}

func (g *GuideGenerator) createConnectionElement(item models.Item, start time.Time, end time.Time) models.Element {
	return models.Element{
		Type:        ConnectionType,
		Title:       item.Name,
		Description: item.Description,
		Connection: models.Connection{
			Type:       item.Connection,
			Link:       item.Link,
			Port:       item.Port,
			Mode:       item.Mode,
			ExpireDate: item.ExpireDate,
			ExpireTime: item.ExpireTime,
		},
		Start: start.Format("2006-01-02T15:04:05Z"),
		End:   end.Format("2006-01-02T15:04:05Z"),
	}
}

func (g *GuideGenerator) createFiller(start time.Time, end time.Time) models.Element {
	return models.Element{
		Type:        FillerType,
		Title:       "Filler",
		Description: "",
		Start:       start.Format("2006-01-02T15:04:05Z"),
		End:         end.Format("2006-01-02T15:04:05Z"),
	}
}

func (g *GuideGenerator) findConvertItems(item models.Item) []models.ConvertItem {
	g.mutex.Lock()
	defer g.mutex.Unlock()

	var files []models.ConvertItem

	localUsedElements := g.usedElements

	for _, file := range item.Files {
		convertItem, err := g.fileRepo.GetConvertItemById(file.ID)
		if err != nil {
			logger.Error("Failed to find convertItem by id %s: %v", err)
			continue
		}
		files = append(files, convertItem)
	}

	for _, folder := range item.Folders {
		if _, has := localUsedElements[folder]; !has {
			usedElements, err := g.historyRepo.FindAllByScheduleAndFolder(g.schedule.ID, folder)
			if err != nil {
				logger.Error("Failed to find usedElements by path: %v", err)
				continue
			}

			localUsedElements[folder] = usedElements
		}

		convertItems := g.fileRepo.FindByFolder(folder)
		items := arrayFilter(convertItems, func(item models.ConvertItem) bool {
			if _, has := localUsedElements[item.Location]; has {
				return !contains(localUsedElements[item.Location], item)
			}
			return true
		})

		if len(items) == 0 {
			items = convertItems
			localUsedElements[folder] = nil
			err := g.historyRepo.DeleteByScheduleAndFolder(g.schedule.ID, folder)
			if err != nil {
				logger.Error("Failed to delete usedElements by path: %v", err)
				continue
			}
		}

		files = append(files, items...)
	}

	g.usedElements = localUsedElements

	return files
}

func (g *GuideGenerator) episodeToInt(episodeNum string) int {
	re := regexp.MustCompile("\\d+")
	str := strings.Join(re.FindAllString(episodeNum, -1), "")
	episode, _ := strconv.Atoi(str)

	return episode
}

func (g *GuideGenerator) getCurrentTimeBlock(currentTime time.Time) time.Time {
	if currentTime.Minute() > 30 {
		currentTime = currentTime.Add(time.Hour).Truncate(time.Hour)
	} else {
		currentTime = currentTime.Truncate(time.Hour).Add(30 * time.Minute)
	}

	return currentTime
}

func (g *GuideGenerator) getElements(
	item models.Item,
	start time.Time,
	end time.Time,
	remainDuration int,
	itemDuration *int,
) []models.Element {
	files := g.findConvertItems(item)
	elements := g.createFileElements(itemDuration, start, end, files, item.Fillers)
	localItemDuration := *itemDuration
	if localItemDuration > 30 {
		startPoint := end.Add(-time.Duration(localItemDuration) * time.Second)
		if localItemDuration == remainDuration {
			elements = append(elements, g.createFiller(startPoint, end))
		} else {
			elements = append(elements, g.getElements(
				item,
				startPoint,
				end,
				localItemDuration,
				itemDuration,
			)...)
		}
	}

	return elements
}

func arrayFilter[Data any](array []Data, callback func(Data) bool) (response []Data) {
	for _, value := range array {
		if callback(value) {
			response = append(response, value)
		}
	}
	return
}

func contains(history []models.History, item models.ConvertItem) bool {
	for _, h := range history {
		if h.FileId == item.ID {
			return true
		}
	}

	return false
}
