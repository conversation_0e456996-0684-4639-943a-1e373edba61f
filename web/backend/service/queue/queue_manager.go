package queue

import (
	"context"
	"showfer-web/models"
	"showfer-web/repository"
	"showfer-web/service/logger"
	"showfer-web/service/recorder"
	"showfer-web/service/transcoder"
	"sync"
)

type QueueManager struct {
	queue             []int64
	mutex             sync.Mutex
	stopRequested     bool
	filesRepo         *repository.FilesRepository
	codecRepo         *repository.CodecSettingsRepository
	statusManager     *recorder.RecorderStatusManager
	baseDir           string
	taskChan          chan int64
	activeWorkers     int
	workerCount       int
	workerCancelFuncs []context.CancelFunc
	workerControlMux  sync.Mutex
}

var instance *QueueManager
var once sync.Once

// GetInstance returns the singleton instance of QueueManager
func GetInstance() *QueueManager {
	once.Do(func() {
		instance = &QueueManager{
			queue:             make([]int64, 0),
			taskChan:          make(chan int64, 100),
			stopRequested:     false,
			workerCancelFuncs: make([]context.CancelFunc, 0),
		}
	})
	return instance
}

// Init initializes the queue manager with dependencies and launches workers
func (qm *QueueManager) Init(
	filesRepo *repository.FilesRepository,
	codecRepo *repository.CodecSettingsRepository,
	generalSettingRepo *repository.GeneralSettingsRepository,
	statusManager *recorder.RecorderStatusManager,
	baseDir string) {
	qm.filesRepo = filesRepo
	qm.codecRepo = codecRepo
	qm.statusManager = statusManager
	qm.baseDir = baseDir

	settings, err := generalSettingRepo.GetGeneralSettings()
	if err != nil {
		logger.Error("Failed to get general settings: %v", err)
		qm.AdjustWorkers(3) // fallback
	} else {
		qm.AdjustWorkers(settings.TranscoderThreads)
	}

	qm.loadQueuedItems()
}

func (qm *QueueManager) AdjustWorkers(desiredCount int) {
	qm.workerControlMux.Lock()
	defer qm.workerControlMux.Unlock()

	if desiredCount < 1 {
		desiredCount = 1
	}

	currentCount := qm.workerCount

	if desiredCount == currentCount {
		return
	}

	if desiredCount < currentCount {
		for i := 0; i < currentCount-desiredCount; i++ {
			idx := len(qm.workerCancelFuncs) - 1
			cancel := qm.workerCancelFuncs[idx]
			cancel()
			qm.workerCancelFuncs = qm.workerCancelFuncs[:idx]
			qm.workerCount--
		}
		logger.Log("Reduced workers to %d", qm.workerCount)
		return
	}

	for i := 0; i < desiredCount-currentCount; i++ {
		ctx, cancel := context.WithCancel(context.Background())
		qm.workerCancelFuncs = append(qm.workerCancelFuncs, cancel)
		go qm.worker(ctx)
		qm.workerCount++
	}
	logger.Log("Increased workers to %d", qm.workerCount)
}

// loadQueuedItems loads any items with QUEUE status from the database
func (qm *QueueManager) loadQueuedItems() {
	items, err := qm.filesRepo.GetConvertItemsByStatus(int(models.FileStatusQueue))
	if err != nil {
		logger.Error("Failed to load queued items: %v", err)
	}
	processingItems, err := qm.filesRepo.GetConvertItemsByStatus(int(models.FileStatusProcessing))
	if err != nil {
		return
	}

	if len(processingItems) > 0 {
		items = append(items, processingItems...)
	}

	/*qm.mutex.Lock()
	defer qm.mutex.Unlock()*/

	for _, item := range items {
		qm.AddToQueue(item.ID)
	}
}

// AddToQueue adds a convert item to the queue and dispatches it to a worker
func (qm *QueueManager) AddToQueue(convertItemID int64) {
	qm.mutex.Lock()
	for _, id := range qm.queue {
		if id == convertItemID {
			qm.mutex.Unlock()
			logger.Log("Item %d is already in the queue", convertItemID)
			return
		}
	}
	qm.queue = append(qm.queue, convertItemID)
	qm.mutex.Unlock()

	item, err := qm.filesRepo.GetConvertItemById(convertItemID)
	if err != nil {
		logger.Error("Failed to get convert item %d: %v", convertItemID, err)
		return
	}

	if item.Status != int(models.FileStatusQueue) {
		err = qm.filesRepo.ChangeStatus(convertItemID, int(models.FileStatusQueue))
		if err != nil {
			logger.Error("Failed to update status for item %d: %v", convertItemID, err)
			return
		}
	}

	go func() { qm.taskChan <- convertItemID }()
	logger.Log("Added item to queue and dispatched to worker: %d", convertItemID)
}

func (qm *QueueManager) worker(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			logger.Log("Worker shutting down")
			return
		case convertItemID := <-qm.taskChan:
			qm.incrementWorkers()
			qm.processItem(convertItemID)
			qm.decrementWorkers()
		}
	}
}

// processItem processes a single item
func (qm *QueueManager) processItem(convertItemID int64) {
	logger.Log("-------------- Debug: Queue processing item: %d -----------------", convertItemID)
	t := transcoder.NewTranscoder(qm.filesRepo, qm.codecRepo, qm.statusManager, qm.baseDir, qm.filesRepo.GetDB())
	logger.Log("-------------- Debug: Created transcoder, calling TranscodeFile for item: %d -----------------", convertItemID)
	err := t.TranscodeFile(convertItemID)
	if err != nil {
		logger.Error("-------------- Debug: Failed to transcode file %d: %v -----------------", convertItemID, err)
	} else {
		logger.Log("-------------- Debug: Successfully completed transcode for file %d -----------------", convertItemID)
	}
}

// incrementWorkers safely increases the count of active workers
func (qm *QueueManager) incrementWorkers() {
	qm.mutex.Lock()
	qm.activeWorkers++
	qm.mutex.Unlock()
}

// decrementWorkers safely decreases the count of active workers
func (qm *QueueManager) decrementWorkers() {
	qm.mutex.Lock()
	qm.activeWorkers--
	qm.mutex.Unlock()
}

// Stop stops the queue manager
func (qm *QueueManager) Stop() {
	qm.mutex.Lock()
	qm.stopRequested = true
	close(qm.taskChan)
	for _, cancel := range qm.workerCancelFuncs {
		cancel()
	}
	qm.workerCancelFuncs = nil
	qm.workerCount = 0
	qm.mutex.Unlock()
}

// GetQueueLength returns the current length of the queue
func (qm *QueueManager) GetQueueLength() int {
	qm.mutex.Lock()
	defer qm.mutex.Unlock()
	return len(qm.queue)
}

// IsProcessing returns true if there are active workers
func (qm *QueueManager) IsProcessing() bool {
	qm.mutex.Lock()
	defer qm.mutex.Unlock()
	return qm.activeWorkers > 0
}

// ClearQueue removes all items from the queue and resets their status to Success
func (qm *QueueManager) ClearQueue() {
	qm.mutex.Lock()
	defer qm.mutex.Unlock()
	
	logger.Log("-------------- Debug: Clearing queue with %d items -----------------", len(qm.queue))
	
	// Reset status of all queued items back to Success
	for _, itemID := range qm.queue {
		err := qm.filesRepo.ChangeStatus(itemID, int(models.FileStatusSuccess))
		if err != nil {
			logger.Error("Failed to reset status for item %d: %v", itemID, err)
		} else {
			logger.Log("Reset status of item %d to Success", itemID)
		}
	}
	
	// Clear the queue
	qm.queue = make([]int64, 0)
	logger.Log("-------------- Debug: Queue cleared -----------------")
}
