package files

import (
	"database/sql"
	"log"
	"showfer-web/models"
	"showfer-web/repository"
	"sync"
	"time"
)

// FileStatusManager handles the status updates for files
type FileStatusManager struct {
	db           *sql.DB
	filesRepo    *repository.FilesRepository
	statusMutex  sync.Mutex
	clients      map[*Client]bool
	clientsMux   sync.Mutex
	broadcast    chan models.FileStatusUpdate
	stopChecker  chan struct{}
	checkPeriod  time.Duration
	lastStatuses map[int64]int // Map of file ID to last known status
}

// Client represents a connected WebSocket client
type Client struct {
	ID   string
	Send chan models.FileStatusUpdate
}

var fileStatusManager *FileStatusManager

// InitFileStatusManager initializes the file status manager
func InitFileStatusManager(db *sql.DB) {
	fileStatusManager = &FileStatusManager{
		db:           db,
		filesRepo:    repository.NewFilesRepository(db),
		clients:      make(map[*Client]bool),
		broadcast:    make(chan models.FileStatusUpdate),
		stopChecker:  make(chan struct{}),
		checkPeriod:  5 * time.Second, // Check every 5 seconds
		lastStatuses: make(map[int64]int),
	}

	// Start the broadcaster
	go fileStatusManager.broadcaster()

	// Start the periodic status checker
	go fileStatusManager.startStatusChecker()
}

// GetFileStatusManager returns the singleton instance of the file status manager
func GetFileStatusManager() *FileStatusManager {
	return fileStatusManager
}

// broadcaster handles broadcasting status updates to all connected clients
func (m *FileStatusManager) broadcaster() {
	for {
		// Wait for a message to broadcast
		update := <-m.broadcast

		// Send to all clients
		m.clientsMux.Lock()
		for client := range m.clients {
			select {
			case client.Send <- update:
				// Message sent successfully
			default:
				// Client is not receiving messages, remove it
				close(client.Send)
				delete(m.clients, client)
			}
		}
		m.clientsMux.Unlock()
	}
}

// RegisterClient registers a new WebSocket client
func (m *FileStatusManager) RegisterClient(client *Client) {
	m.clientsMux.Lock()
	defer m.clientsMux.Unlock()
	m.clients[client] = true
}

// UnregisterClient unregisters a WebSocket client
func (m *FileStatusManager) UnregisterClient(client *Client) {
	m.clientsMux.Lock()
	defer m.clientsMux.Unlock()
	if _, ok := m.clients[client]; ok {
		close(client.Send)
		delete(m.clients, client)
	}
}

// startStatusChecker starts the periodic status checker
func (m *FileStatusManager) startStatusChecker() {
	ticker := time.NewTicker(m.checkPeriod)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			m.checkFileStatuses()
		case <-m.stopChecker:
			return
		}
	}
}

// checkFileStatuses checks for file status changes
func (m *FileStatusManager) checkFileStatuses() {
	m.statusMutex.Lock()
	defer m.statusMutex.Unlock()

	// Get all files from the database
	pagination := models.Pagination{
		Page:  1,
		Limit: 1000, // Get a large number of files
	}
	result, err := m.filesRepo.ListConvertItems(pagination)
	if err != nil {
		log.Printf("Failed to get convert items: %v", err)
		return
	}

	// Check for status changes
	for _, item := range result.Items {
		lastStatus, exists := m.lastStatuses[item.ID]
		
		// If this is the first time we're seeing this file or the status has changed
		if !exists || lastStatus != item.Status {
			// Update the last known status
			m.lastStatuses[item.ID] = item.Status
			
			// Only broadcast if this isn't the first time we're seeing this file
			if exists {
				// Broadcast the status update
				m.broadcast <- models.FileStatusUpdate{
					ID:     item.ID,
					Status: item.Status,
				}
			}
		}
	}
}

// Stop stops the file status manager
func (m *FileStatusManager) Stop() {
	close(m.stopChecker)
}
