package recorder

import (
	"database/sql"
	"showfer-web/models"
	"showfer-web/repository"
	"showfer-web/service/interfaces"
	"showfer-web/service/logger"
	"strings"
	"sync"
	"time"
)

// RecorderStatusManager handles the status updates for recorders
type RecorderStatusManager struct {
	db            *sql.DB
	queueManager  interfaces.QueueManagerInterface
	statusMutex   sync.Mutex
	statusUpdates map[int]bool // Tracks which recorders have pending status updates
	recorderRepo  *repository.RecorderRepository
	filesRepo     FilesRepository
	// WebSocket clients
	clients    map[*Client]bool
	clientsMux sync.Mutex
	broadcast  chan models.RecorderStatusUpdate

	// RTP URL validation
	stopValidation chan struct{}
	// Track ts_sync status changes to avoid multiple status updates
	tsSyncStatus map[int]bool // Map of recorder ID to ts_sync status
	tsSyncMutex  sync.Mutex
}

var statusManager *RecorderStatusManager

// Client represents a connected WebSocket client
type Client struct {
	ID   string
	Send chan models.RecorderStatusUpdate
}

// InitStatusManager initializes the recorder status manager
func InitStatusManager(db *sql.DB, queue interfaces.QueueManagerInterface) {
	statusManager = &RecorderStatusManager{
		db:             db,
		queueManager:   queue,
		statusUpdates:  make(map[int]bool),
		recorderRepo:   repository.NewRecorderRepository(db),
		filesRepo:      repository.NewFilesRepository(db),
		clients:        make(map[*Client]bool),
		broadcast:      make(chan models.RecorderStatusUpdate),
		stopValidation: make(chan struct{}),
		tsSyncStatus:   make(map[int]bool),
	}

	// Start the broadcaster
	go statusManager.broadcaster()

	// Only check TS sync for active recorders, not joined ones
	go statusManager.startRtpUrlValidation()
}

// GetStatusManager returns the singleton instance of the status manager
func GetStatusManager() *RecorderStatusManager {
	return statusManager
}

// broadcaster handles broadcasting status updates to all connected clients
func (m *RecorderStatusManager) broadcaster() {
	for {
		// Wait for a message to broadcast
		update := <-m.broadcast

		// Send to all clients
		m.clientsMux.Lock()
		for client := range m.clients {
			select {
			case client.Send <- update:
				// Message sent successfully
			default:
				// Client is not receiving messages, remove it
				close(client.Send)
				delete(m.clients, client)
			}
		}
		m.clientsMux.Unlock()
	}
}

// RegisterClient registers a new WebSocket client
func (m *RecorderStatusManager) RegisterClient(client *Client) {
	m.clientsMux.Lock()
	defer m.clientsMux.Unlock()
	m.clients[client] = true
}

// UnregisterClient unregisters a WebSocket client
func (m *RecorderStatusManager) UnregisterClient(client *Client) {
	m.clientsMux.Lock()
	defer m.clientsMux.Unlock()
	if _, ok := m.clients[client]; ok {
		close(client.Send)
		delete(m.clients, client)
	}
}

// UpdateRecorderStatus updates the status of a recorder in the database
func (m *RecorderStatusManager) UpdateRecorderStatus(id int, status string) error {
	m.statusMutex.Lock()
	defer m.statusMutex.Unlock()

	// Check if we already have a pending update for this recorder
	if _, exists := m.statusUpdates[id]; exists {
		return nil // Skip duplicate updates
	}

	// Mark this recorder as having a pending update
	m.statusUpdates[id] = true

	// The "failed" status is set when:
	// 1. There's an actual error in the recording process
	// 2. TS sync is lost during active recording

	// Update the status in the database
	var err error
	switch status {
	case "running", "recording": // Both "running" and "recording" map to StartRecorder
		err = m.recorderRepo.StartRecorder(id)
	case "stopped":
		err = m.recorderRepo.StopRecorder(id)
	case "completed":
		err = m.recorderRepo.CompleteRecorder(id)
		// If the recording completed successfully, also reset scheduling fields for scheduled recordings
		if err == nil {
			// Get the recorder to check if it was scheduled
			recorder, getErr := m.recorderRepo.GetRecorderByID(id)
			if getErr == nil && recorder.IsScheduled {
				logger.Log("Resetting scheduling fields for completed scheduled recorder %d", id)
				resetErr := m.recorderRepo.ResetScheduling(id)
				if resetErr != nil {
					logger.Error("Failed to reset scheduling for recorder %d: %v", id, resetErr)
				}
			}
		}
	case "failed":
		err = m.recorderRepo.FailRecorder(id)
	case "transcoding":
		// For transcoding status, we keep the recorder as "running" in the database
		// but broadcast the "transcoding" status to the frontend
		err = m.recorderRepo.StartRecorder(id)
	default:
		logger.Error("Unknown recorder status: %s", status)
		delete(m.statusUpdates, id)
		return nil
	}

	if err != nil {
		logger.Error("Failed to update recorder status: %v", err)
		delete(m.statusUpdates, id)
		return err
	}

	// Broadcast the status update to all connected clients
	m.broadcast <- models.RecorderStatusUpdate{
		ID:     id,
		Status: status,
	}

	// Remove from pending updates
	delete(m.statusUpdates, id)
	return nil
}

func (m *RecorderStatusManager) NotifyRecorderStopped(id int, completed bool) {
	// Update the database status based on whether the recorder completed successfully
	// The 'completed' parameter indicates whether both recording (GStreamer) and transcoding (FFmpeg) completed successfully
	if !completed {
		err := m.UpdateRecorderStatus(id, "stopped")
		if err != nil {
			logger.Log("Update recorder status to stopped")
		} else {
			logger.Error("DEBUG: Recording did not complete successfully, not adding to file manager")
		}
	} else {
		outputFile := getLastRecordingFilePath(id)
		if outputFile == "" {
			logger.Error("Failed to get output file path for recorder %d", id)
			return
		}

		// Add the recording to the file manager
		_, err := AddRecordingToFileManager(outputFile, m.filesRepo, id, m.queueManager, m.recorderRepo)
		if err != nil {
			logger.Error("Failed to add recording to file manager: %v", err)
		} else {
			err := statusManager.UpdateRecorderStatus(id, "transcoding")
			if err != nil {
				logger.Log("DEBUG: Recorder %d is in transcoding phase, setting status to 'transcoding'", id)
			} else {
				logger.Error("Failed to update recorder status: %v", err)
			}
		}
	}

}

// NotifyRecorderFailed is called when a recorder fails
func (m *RecorderStatusManager) NotifyRecorderFailed(id int, errorMsg string) {
	// First, try to stop the recording process
	// This ensures that the recorder process is terminated before updating the status
	logger.Log("Attempting to stop recorder %d process before marking as failed", id)
	err := StopRecording(id)
	if err != nil {
		// Check if the error is because the recorder is not active
		if err.Error() != "" && !strings.Contains(err.Error(), "no active recorder") {
			// Log other errors but continue with the status update
			logger.Error("Failed to stop recorder %d process: %v", id, err)
		} else {
			logger.Log("Recorder %d is not active or already stopped, proceeding to mark as failed", id)
		}
	} else {
		logger.Log("Successfully stopped recorder %d process", id)
	}

	// Now update the status in the database
	err = m.UpdateRecorderStatus(id, "failed")
	if err != nil {
		logger.Error("Failed to update recorder status after failure: %v", err)
	} else {
		logger.Log("Recorder %d failed: %s", id, errorMsg)
	}
}

// startRtpUrlValidation starts a goroutine that periodically checks if RTP URLs are working
// and updates the ts_sync status accordingly for both active and joined recorders
func (m *RecorderStatusManager) startRtpUrlValidation() {
	logger.Log("Starting periodic RTP URL validation for both active and joined recorders")

	// Check every 10 seconds to reduce overhead
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			// Get all recorders (both active and joined)
			statuses := GetRecorderStatus()
			activeRecorderCount := 0
			joinedRecorderCount := 0

			// Count active and joined recorders
			for _, status := range statuses {
				if status.IsActive {
					activeRecorderCount++
				} else if status.IsJoined {
					joinedRecorderCount++
				}
			}

			if activeRecorderCount > 0 || joinedRecorderCount > 0 {
				logger.Log("Checking %d active recorders and %d joined recorders for RTP URL validation",
					activeRecorderCount, joinedRecorderCount)
			}

			// Check each recorder
			for _, status := range statuses {
				// Check both active and joined recorders
				if !status.IsActive && !status.IsJoined {
					continue
				}

				// Skip recorders that are in transcoding phase or have completed recording
				recorderMutex.Lock()
				if activeRecorder, ok := activeRecorders[status.ID]; ok {
					// Check if the recorder is in transcoding phase
					// A recorder is in transcoding phase only if recording has completed but transcoding is still in progress
					activeRecorder.streamInfoMutex.Lock()
					if activeRecorder.UsingTsOutput && activeRecorder.FfmpegOutputFile != "" &&
						activeRecorder.RecordingCompleted && !activeRecorder.CompletedSuccessfully {
						logger.Log("Skipping RTP URL validation for recorder %d as it's in transcoding phase", status.ID)
						activeRecorder.streamInfoMutex.Unlock()
						recorderMutex.Unlock()
						continue
					}
					activeRecorder.streamInfoMutex.Unlock()

					// Check if recording has completed and transcoding has also completed
					activeRecorder.streamInfoMutex.Lock()
					if activeRecorder.RecordingCompleted && activeRecorder.CompletedSuccessfully {
						logger.Log("Skipping RTP URL validation for recorder %d as recording and transcoding have completed", status.ID)
						activeRecorder.streamInfoMutex.Unlock()
						recorderMutex.Unlock()
						continue
					}
					activeRecorder.streamInfoMutex.Unlock()
				}
				recorderMutex.Unlock()

				// Process each recorder in a separate goroutine
				go func(recorderID int, rtpURL string, currentTsSync bool, isActive bool, isJoined bool) {
					recorderType := "active"
					if !isActive && isJoined {
						recorderType = "joined"
					}

					logger.Log("Starting RTP URL validation for %s recorder %d: %s (current ts_sync: %v)",
						recorderType, recorderID, rtpURL, currentTsSync)

					// Make three attempts to check the RTP URL before setting TS sync to false
					isWorking := false
					maxAttempts := 3

					// Get the current TS sync status
					m.tsSyncMutex.Lock()
					prevTsSync, exists := m.tsSyncStatus[recorderID]
					m.tsSyncMutex.Unlock()

					// Only make multiple attempts if the current TS sync is true
					if exists && prevTsSync {
						// If TS sync is currently true, make multiple attempts before setting to false
						for attempt := 1; attempt <= maxAttempts; attempt++ {
							logger.Log("Checking RTP URL for %s recorder %d (attempt %d of %d)", recorderType, recorderID, attempt, maxAttempts)
							attemptResult := CheckRtpUrlIsWorking(rtpURL)

							if attemptResult {
								// If any attempt succeeds, consider the URL working
								isWorking = true
								logger.Log("RTP URL check succeeded on attempt %d for %s recorder %d", attempt, recorderType, recorderID)
								break
							}

							// If this isn't the last attempt, wait a short time before trying again
							if attempt < maxAttempts {
								logger.Log("RTP URL check failed on attempt %d for %s recorder %d, trying again...", attempt, recorderType, recorderID)
								time.Sleep(500 * time.Millisecond)
							} else {
								logger.Log("RTP URL check failed on all %d attempts for %s recorder %d", maxAttempts, recorderType, recorderID)
							}
						}
					} else {
						// If TS sync is already false or unknown, just make one attempt to see if it's working
						isWorking = CheckRtpUrlIsWorking(rtpURL)
					}

					logger.Log("RTP URL validation final result for %s recorder %d: %s is %s",
						recorderType, recorderID, rtpURL, map[bool]string{true: "working", false: "not working"}[isWorking])

					// Get the latest ts_sync status
					m.tsSyncMutex.Lock()
					latestTsSync, existsInMap := m.tsSyncStatus[recorderID]
					statusChanged := !existsInMap || latestTsSync != isWorking
					m.tsSyncStatus[recorderID] = isWorking
					m.tsSyncMutex.Unlock()

					// Update the ts_sync status in the recorder (active or joined)
					recorderMutex.Lock()

					if isActive {
						// Handle active recorder
						if activeRecorder, ok := activeRecorders[recorderID]; ok {
							// Only update if the status is different
							if activeRecorder.TsSync != isWorking {
								logger.Log("Updating ts_sync for active recorder %d from %v to %v",
									recorderID, activeRecorder.TsSync, isWorking)
								activeRecorder.updateTsSync(isWorking)

								// Mark recorders as failed when TS sync is lost during recording
								if !isWorking && statusChanged && latestTsSync {
									logger.Log("TS sync lost for active recorder %d after multiple failed attempts, marking as failed", recorderID)
									// Release the mutex before calling NotifyRecorderFailed to avoid deadlock
									recorderMutex.Unlock()
									// Mark the recorder as failed
									m.NotifyRecorderFailed(recorderID, "TS sync lost during recording after multiple failed attempts")
									return
								}
							}
						}
					} else if isJoined {
						// Handle joined recorder
						if joinedRecorder, ok := joinedRecorders[recorderID]; ok {
							// Only update if the status is different
							joinedRecorder.streamInfoMutex.Lock()
							if joinedRecorder.TsSync != isWorking {
								logger.Log("Updating ts_sync for joined recorder %d from %v to %v",
									recorderID, joinedRecorder.TsSync, isWorking)
								joinedRecorder.TsSync = isWorking
							}
							joinedRecorder.streamInfoMutex.Unlock()
						}
					}

					recorderMutex.Unlock()

					// Log status changes
					if statusChanged {
						if !isWorking && latestTsSync {
							logger.Log("TS sync lost for %s recorder %d after multiple failed attempts (RTP URL: %s)",
								recorderType, recorderID, rtpURL)
						} else if isWorking && !latestTsSync {
							logger.Log("TS sync established for %s recorder %d (RTP URL: %s)",
								recorderType, recorderID, rtpURL)
						}
					}
				}(status.ID, status.RtpURL, status.TsSync, status.IsActive, status.IsJoined)
			}

		case <-m.stopValidation:
			logger.Log("Stopping periodic RTP URL validation")
			return
		}
	}
}

// StopRtpUrlValidation stops the periodic RTP URL validation
func (m *RecorderStatusManager) StopRtpUrlValidation() {
	close(m.stopValidation)
}
