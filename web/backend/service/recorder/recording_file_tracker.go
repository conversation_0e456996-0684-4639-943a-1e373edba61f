package recorder

import (
	"log"
	"sync"
)

// Map to store the last output file path for each recorder
var (
	lastRecordingFiles     = make(map[int]string)
	lastRecordingFilesMutex = &sync.Mutex{}
)

// setLastRecordingFilePath stores the output file path for a recorder
func setLastRecordingFilePath(id int, filePath string) {
	lastRecordingFilesMutex.Lock()
	defer lastRecordingFilesMutex.Unlock()

	lastRecordingFiles[id] = filePath
	log.Printf("DEBUG: Stored output file path for recorder %d: %s", id, filePath)
}

// getLastRecordingFilePath retrieves the output file path for a recorder
func getLastRecordingFilePath(id int) string {
	lastRecordingFilesMutex.Lock()
	defer lastRecordingFilesMutex.Unlock()

	filePath, exists := lastRecordingFiles[id]
	if !exists {
		log.Printf("DEBUG: No output file path found for recorder %d", id)
		return ""
	}

	log.Printf("DEBUG: Retrieved output file path for recorder %d: %s", id, filePath)
	return filePath
}
