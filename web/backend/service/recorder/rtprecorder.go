package recorder

import (
	"context"
	"fmt"
	"log"
	"net"
	"os"
	"os/exec"
	"path/filepath"
	"showfer-web/models"
	"showfer-web/service/detector"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/go-gst/go-gst/gst"
)

type EncodingParams struct {
	VideoCodec   string
	AudioCodec   string
	Resolution   string
	FPS          string
	SampleRate   string
	VideoBitrate string
	AudioBitrate string
	Preset       string
	Profile      string
}

const (
	DEFAULT_VIDEO_CODEC   = "x264enc" // Changed from avenc_h264
	DEFAULT_AUDIO_CODEC   = "aac"     // Using AAC for web compatibility
	DEFAULT_RESOLUTION    = "1280x720"
	DEFAULT_FPS           = "30"
	DEFAULT_SAMPLE_RATE   = "48000" // Changed to 48kHz for better AC3 compatibility
	DEFAULT_VIDEO_BITRATE = "2M"
	DEFAULT_AUDIO_BITRATE = "192k"   // Increased for better audio quality
	MAX_VIDEO_BITRATE     = 20000000 // 20 Mbps in bits/second
)

// StreamInfo holds information about a stream
type StreamInfo struct {
	TsSync    bool
	Services  []models.ServiceInfo
	VideoInfo models.VideoInfo
	AudioInfo models.AudioInfo
	ServiceID int // ID of the specific service to record (0 means record all services)
}

// Maps to keep track of active and joined recorders
var (
	activeRecorders = make(map[int]*RtpToMp4Converter)
	joinedRecorders = make(map[int]*RtpToMp4Converter) // Recorders that are joined but not recording
	recorderMutex   = &sync.Mutex{}
)

func parseBitrate(bitrate string) (int, error) {
	multiplier := 1
	value := strings.TrimRight(bitrate, "kKmMgG")

	switch bitrate[len(bitrate)-1] {
	case 'k', 'K':
		multiplier = 1000
	case 'm', 'M':
		multiplier = 1000000
	case 'g', 'G':
		multiplier = 1000000000
	}

	numValue, err := strconv.Atoi(value)
	if err != nil {
		return 0, fmt.Errorf("invalid bitrate format: %s", bitrate)
	}

	return numValue * multiplier, nil
}

func validateBitrate(bitrate string) error {
	bits, err := parseBitrate(bitrate)
	if err != nil {
		return err
	}

	if bits > MAX_VIDEO_BITRATE {
		return fmt.Errorf("video bitrate exceeds maximum allowed value of 20Mbps")
	}

	return nil
}

func getDefaultParams() *EncodingParams {
	return &EncodingParams{
		VideoCodec:   DEFAULT_VIDEO_CODEC,
		AudioCodec:   DEFAULT_AUDIO_CODEC,
		Resolution:   DEFAULT_RESOLUTION,
		FPS:          DEFAULT_FPS,
		SampleRate:   DEFAULT_SAMPLE_RATE,
		VideoBitrate: DEFAULT_VIDEO_BITRATE,
		AudioBitrate: DEFAULT_AUDIO_BITRATE,
		Preset:       "medium",
		Profile:      "high",
	}
}

type RtpToMp4Converter struct {
	ID        int
	RtpURL    string
	OutputDir string
	Duration  int
	Params    *EncodingParams
	Pipeline  *gst.Pipeline
	Done      chan struct{} // Channel to signal completion
	StopChan  chan struct{} // Channel to signal stop request
	StartTime time.Time     // When the recording started

	// Stream information
	TsSync    bool
	Services  []models.ServiceInfo
	VideoInfo models.VideoInfo
	AudioInfo models.AudioInfo
	ServiceID int // ID of the specific service to record (0 means record all services)

	// Completion status
	CompletedSuccessfully bool

	// Mutex to protect stream information
	streamInfoMutex sync.Mutex

	// New fields from the improved implementation
	UsingTsOutput      bool   // Flag to indicate if we're using TS output as fallback
	FfmpegOutputFile   string // Path to the output MP4 file when using FFmpeg fallback
	TsFilePath         string // Path to the temporary TS file
	RecordingCompleted bool   // Flag to indicate if recording has completed (to skip RTP URL checking)
	NetworkInterface   string // Network interface to use for receiving RTP stream

	// Iptables support for source IP filtering
	IptablesManager *IptablesManager // Manager for iptables rules
	UsingIptables   bool             // Flag to indicate if iptables filtering is active
}

// IPInfo contains information about an RTP URL's IP address and port
type IPInfo struct {
	Address     string
	Port        int
	IsMulticast bool
	Interface   string // Network interface to use for receiving RTP stream
	LocalAddr   string // Local address to bind to for receiving RTP stream
	SourceIP    string // Source IP address to filter by (optional)
}

func parseRtpURL(url string) (*IPInfo, error) {
	// Remove rtp:// prefix
	urlWithoutPrefix := strings.TrimPrefix(url, "rtp://")

	// Check for query parameters (for network interface)
	var queryParams string
	var baseURL string

	if strings.Contains(urlWithoutPrefix, "?") {
		parts := strings.SplitN(urlWithoutPrefix, "?", 2)
		baseURL = parts[0]
		queryParams = parts[1]
	} else {
		baseURL = urlWithoutPrefix
	}

	// Split by first slash to separate address:port from path
	parts := strings.SplitN(baseURL, "/", 2)
	if len(parts) == 0 {
		return nil, fmt.Errorf("invalid RTP URL format. Expected rtp://ip:port[/path][?iface=eth0&sourceip=*******]")
	}

	// Split address and port
	addrParts := strings.Split(parts[0], ":")
	if len(addrParts) != 2 {
		return nil, fmt.Errorf("invalid RTP URL format. Expected rtp://ip:port[/path][?iface=eth0&sourceip=*******]")
	}

	ip := addrParts[0]
	port, err := strconv.Atoi(addrParts[1])
	if err != nil {
		return nil, fmt.Errorf("invalid port number: %v", err)
	}

	// Validate IP address
	ipAddr := net.ParseIP(ip)
	if ipAddr == nil {
		return nil, fmt.Errorf("invalid IP address: %s", ip)
	}

	// Check if it's a multicast address
	isMulticast := false
	if ipAddr.To4() != nil {
		// IPv4 multicast: ********* to ***************
		isMulticast = ipAddr.To4()[0] >= 224 && ipAddr.To4()[0] <= 239
	} else {
		// IPv6 multicast: starts with ff00::/8
		isMulticast = ipAddr.To16()[0] == 0xff
	}

	// Parse query parameters to get network interface, localaddr, and sourceip
	networkInterface := ""
	localAddr := ""
	sourceIP := ""
	if queryParams != "" {
		params := strings.Split(queryParams, "&")
		for _, param := range params {
			if strings.HasPrefix(param, "iface=") {
				networkInterface = strings.TrimPrefix(param, "iface=")
			} else if strings.HasPrefix(param, "localaddr=") {
				localAddr = strings.TrimPrefix(param, "localaddr=")
			} else if strings.HasPrefix(param, "sourceip=") {
				sourceIP = strings.TrimPrefix(param, "sourceip=")
				// Validate source IP if provided
				if sourceIP != "" {
					if net.ParseIP(sourceIP) == nil {
						return nil, fmt.Errorf("invalid source IP address: %s", sourceIP)
					}
				}
			}
		}
	}

	log.Printf("Parsed RTP URL: %s:%d (multicast: %v, interface: %s, localaddr: %s, sourceip: %s)",
		ip, port, isMulticast, networkInterface, localAddr, sourceIP)

	return &IPInfo{
		Address:     ip,
		Port:        port,
		IsMulticast: isMulticast,
		Interface:   networkInterface,
		LocalAddr:   localAddr,
		SourceIP:    sourceIP,
	}, nil
}

// Helper function to check if a GStreamer plugin is available
func isPluginAvailable(pluginName string) bool {
	// Use gst-inspect-1.0 to check if the plugin is available
	cmd := exec.Command("gst-inspect-1.0", pluginName)
	err := cmd.Run()
	if err != nil {
		log.Printf("Plugin %s not found: %v", pluginName, err)
		return false
	}
	log.Printf("Plugin %s found via gst-inspect-1.0", pluginName)
	return true
}

// isGPUAvailable checks if an NVIDIA GPU is available on the system
// and if the necessary GStreamer NVIDIA plugins are installed
func isGPUAvailable() bool {
	// First check if nvidia-smi command exists and runs successfully
	cmd := exec.Command("nvidia-smi")
	err := cmd.Run()
	if err != nil {
		log.Printf("GPU not detected: %v", err)
		return false
	}

	// Then check if the required NVIDIA GStreamer plugins are available
	nvPlugins := []string{"nvh264enc", "nvenc", "nvdec"}
	for _, plugin := range nvPlugins {
		if isPluginAvailable(plugin) {
			log.Printf("NVIDIA GPU detected and %s plugin is available, will use hardware acceleration", plugin)
			return true
		}
	}

	log.Printf("NVIDIA GPU detected but required GStreamer plugins are not available")
	log.Printf("Please install the NVIDIA GStreamer plugins with: sudo apt-get install nvidia-container-toolkit gstreamer1.0-plugins-bad")
	return false
}

// Helper function to convert GStreamer state to string
func stateToString(state gst.State) string {
	switch state {
	case gst.State(gst.VoidPending):
		return "VOID_PENDING"
	case gst.State(gst.StateNull):
		return "NULL"
	case gst.State(gst.StateReady):
		return "READY"
	case gst.State(gst.StatePaused):
		return "PAUSED"
	case gst.State(gst.StatePlaying):
		return "PLAYING"
	default:
		return "UNKNOWN"
	}
}

// Helper function to parse resolution string and handle interlaced/progressive formats
func parseResolution(resolution string) (string, bool) {
	// Check if resolution has 'i' suffix (interlaced)
	isInterlaced := strings.HasSuffix(resolution, "i")

	// Check if resolution has 'p' suffix (progressive)
	isProgressive := strings.HasSuffix(resolution, "p")

	// Remove the suffix for GStreamer/FFmpeg
	cleanResolution := resolution
	if isInterlaced {
		cleanResolution = strings.TrimSuffix(resolution, "i")
	} else if isProgressive {
		cleanResolution = strings.TrimSuffix(resolution, "p")
	}

	return cleanResolution, isInterlaced
}

// Helper function to check if FFmpeg is available
func isFFmpegAvailable() bool {
	cmd := exec.Command("ffmpeg", "-version")
	err := cmd.Run()
	if err != nil {
		log.Printf("FFmpeg not found: %v", err)
		return false
	}
	log.Printf("FFmpeg found")
	return true
}

// ParseRtpURL is an exported version of parseRtpURL that validates and parses an RTP URL
func ParseRtpURL(url string) (*IPInfo, error) {
	return parseRtpURL(url)
}

// CheckRtpUrlIsWorking checks if an RTP URL is accessible using a direct UDP connection
// Returns true if the URL is working, false otherwise
func CheckRtpUrlIsWorking(rtpURL string) bool {
	// Parse the RTP URL to get IP and port
	ipInfo, err := parseRtpURL(rtpURL)
	if err != nil {
		log.Printf("Failed to parse RTP URL %s: %v", rtpURL, err)
		return false
	}

	log.Printf("Checking RTP URL: %s:%d (multicast: %v)", ipInfo.Address, ipInfo.Port, ipInfo.IsMulticast)

	// Create a UDP connection to check if the stream is available
	var conn *net.UDPConn
	var localAddr *net.UDPAddr
	var remoteAddr *net.UDPAddr

	// Set up the remote address
	remoteAddr, err = net.ResolveUDPAddr("udp", fmt.Sprintf("%s:%d", ipInfo.Address, ipInfo.Port))
	if err != nil {
		log.Printf("Failed to resolve remote address %s:%d: %v", ipInfo.Address, ipInfo.Port, err)
		return false
	}

	// Create the UDP connection
	if ipInfo.IsMulticast {
		// For multicast, we need to join the multicast group
		// First, find the interface if specified
		var ifi *net.Interface
		if ipInfo.Interface != "" {
			ifi, err = net.InterfaceByName(ipInfo.Interface)
			if err != nil {
				log.Printf("Failed to find interface %s: %v", ipInfo.Interface, err)
				// Continue without specifying interface
				ifi = nil
			}
		}

		// Create a UDP connection for multicast
		conn, err = net.ListenMulticastUDP("udp", ifi, remoteAddr)
		if err != nil {
			log.Printf("Failed to create multicast UDP connection: %v", err)
			return false
		}
	} else {
		// For unicast, create a regular UDP connection
		localAddr = &net.UDPAddr{IP: net.IPv4zero, Port: 0}
		conn, err = net.ListenUDP("udp", localAddr)
		if err != nil {
			log.Printf("Failed to create UDP connection: %v", err)
			return false
		}
	}
	defer conn.Close()

	// Set a read deadline
	conn.SetReadDeadline(time.Now().Add(500 * time.Millisecond))

	// Try to read some data from the stream
	buffer := make([]byte, 4096)
	bytesRead := 0

	// Try to read data
	n, _, err := conn.ReadFromUDP(buffer)
	if err != nil {
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			log.Printf("Timeout reading from UDP stream %s:%d", ipInfo.Address, ipInfo.Port)
		} else {
			log.Printf("Error reading from UDP stream %s:%d: %v", ipInfo.Address, ipInfo.Port, err)
		}
	} else {
		bytesRead += n
		log.Printf("Read %d bytes from UDP stream %s:%d", n, ipInfo.Address, ipInfo.Port)
	}

	// If we read any data, the stream is working
	isWorking := bytesRead > 0

	if isWorking {
		log.Printf("RTP URL %s is working (received %d bytes)", rtpURL, bytesRead)
	} else {
		log.Printf("RTP URL %s is not working (no data received)", rtpURL)
	}

	return isWorking
}

func listAvailableEncoders() []string {
	log.Printf("Checking available video encoders...")

	// Check if GPU is available
	gpuAvailable := isGPUAvailable()

	// Define encoder lists based on GPU availability
	var encoders []string

	if gpuAvailable {
		// If GPU is available, prioritize NVIDIA encoders
		log.Printf("GPU detected, prioritizing NVIDIA encoders")
		encoders = []string{
			"nvh264enc",    // NVIDIA GPU encoder (highest priority when GPU is available)
			"x264enc",      // Software encoder (fallback)
			"vaapih264enc", // Intel/AMD GPU encoder
			"avenc_h264",   // FFmpeg encoder
			"openh264enc",  // Cisco OpenH264 encoder
			"v4l2h264enc",  // Video4Linux encoder
			"omxh264enc",   // OpenMAX encoder
		}
	} else {
		// If no GPU is available, prioritize CPU encoders
		log.Printf("No GPU detected, prioritizing CPU encoders")
		encoders = []string{
			"x264enc",      // Software encoder (highest priority when no GPU)
			"vaapih264enc", // Intel/AMD GPU encoder
			"avenc_h264",   // FFmpeg encoder
			"openh264enc",  // Cisco OpenH264 encoder
			"v4l2h264enc",  // Video4Linux encoder
			"omxh264enc",   // OpenMAX encoder
			"nvh264enc",    // NVIDIA GPU encoder (lowest priority when no GPU detected)
		}
	}

	available := []string{}
	for _, encoder := range encoders {
		log.Printf("Testing video encoder: %s", encoder)
		if isPluginAvailable(encoder) {
			log.Printf("Video encoder %s is available", encoder)
			available = append(available, encoder)
		} else {
			log.Printf("Video encoder %s is not available", encoder)
		}
	}

	// Also check for audio encoders/parsers
	log.Printf("Checking available audio encoders...")
	audioEncoders := []string{
		"ac3parse",
		"a52dec",
		"avdec_ac3",
		"avdec_ac3_fixed",
	}

	for _, encoder := range audioEncoders {
		log.Printf("Testing audio encoder/parser: %s", encoder)
		if isPluginAvailable(encoder) {
			log.Printf("Audio encoder/parser %s is available", encoder)
		} else {
			log.Printf("Audio encoder/parser %s is not available", encoder)
		}
	}

	// Check for GPU-accelerated decoders if GPU is available
	if gpuAvailable {
		log.Printf("Checking GPU-accelerated decoders...")
		gpuDecoders := []string{
			"nvdec",
			"nvh264dec",
		}

		for _, decoder := range gpuDecoders {
			log.Printf("Testing GPU decoder: %s", decoder)
			if isPluginAvailable(decoder) {
				log.Printf("GPU decoder %s is available", decoder)
			} else {
				log.Printf("GPU decoder %s is not available", decoder)
			}
		}
	}

	return available
}

func NewRtpToMp4Converter(id int, rtpURL, outputDir string, duration int, params *EncodingParams, serviceID int) (*RtpToMp4Converter, error) {
	if params == nil {
		params = getDefaultParams()
	}

	// Validate video bitrate
	if err := validateBitrate(params.VideoBitrate); err != nil {
		return nil, err
	}

	// Initialize GStreamer
	log.Printf("Initializing GStreamer...")
	gst.Init(nil)

	// List available encoders
	log.Printf("Checking GStreamer installation...")

	// Try a simple pipeline first
	log.Printf("Testing basic GStreamer functionality...")
	testPipeline, err := gst.NewPipelineFromString("videotestsrc ! fakesink")
	if err != nil {
		return nil, fmt.Errorf("basic GStreamer test failed: %v\nPlease check GStreamer installation", err)
	}
	testPipeline.SetState(gst.StateNull)

	availableEncoders := listAvailableEncoders()
	if len(availableEncoders) == 0 {
		// Run gst-inspect-1.0 to get more information
		log.Printf("Running gst-inspect-1.0 to check available plugins...")
		output, err := exec.Command("gst-inspect-1.0").Output()
		if err != nil {
			log.Printf("Failed to run gst-inspect-1.0: %v", err)
		} else {
			log.Printf("Available GStreamer plugins:\n%s", string(output))
		}

		return nil, fmt.Errorf("no H.264 encoders found. Please install one of these packages:\n" +
			"For software encoding:\n" +
			"  sudo apt-get install gstreamer1.0-plugins-ugly (for x264enc)\n" +
			"  sudo apt-get install gstreamer1.0-libav (for avenc_h264)\n" +
			"For hardware encoding:\n" +
			"  sudo apt-get install gstreamer1.0-vaapi (for vaapih264enc)\n" +
			"  sudo apt-get install nvidia-container-toolkit (for nvenc)")
	}

	// Use the first available encoder
	params.VideoCodec = availableEncoders[0]
	log.Printf("Using video encoder: %s", params.VideoCodec)

	// Parse RTP URL to extract network interface and source IP
	ipInfo, err := parseRtpURL(rtpURL)
	if err != nil {
		log.Printf("Warning: Failed to parse RTP URL for network interface extraction: %v", err)
	}

	networkInterface := ""
	if ipInfo != nil {
		networkInterface = ipInfo.Interface
	}

	converter := &RtpToMp4Converter{
		ID:        id,
		RtpURL:    rtpURL,
		OutputDir: outputDir,
		Duration:  duration,
		Params:    params,
		Done:      make(chan struct{}),
		StopChan:  make(chan struct{}),
		// Initialize stream information
		TsSync:   false,
		Services: []models.ServiceInfo{},
		VideoInfo: models.VideoInfo{
			Resolution: "Unknown",
			Codec:      "Unknown",
		},
		AudioInfo: models.AudioInfo{
			Channels: 0,
			Codec:    "Unknown",
		},
		ServiceID: serviceID, // Set the service ID (0 means record all services)
		// Initialize new fields
		UsingTsOutput:    false,
		FfmpegOutputFile: "",
		TsFilePath:       "",
		NetworkInterface: networkInterface, // Set the network interface from RTP URL
		// Initialize iptables fields
		IptablesManager: nil,
		UsingIptables:   false,
	}

	// Initialize iptables if source IP is provided
	if ipInfo != nil && ipInfo.SourceIP != "" {
		log.Printf("Source IP filtering requested: %s", ipInfo.SourceIP)

		iptablesManager := NewIptablesManager(id)
		
		if iptablesManager.isIptablesAvailable() {
			// Set up iptables rules for source IP filtering
			err := iptablesManager.addSourceIPFilter(ipInfo.SourceIP, ipInfo.Address, ipInfo.Port)
			if err != nil {
				log.Printf("Warning: Failed to set up iptables filtering: %v", err)
				log.Printf("Continuing without iptables filtering")
			} else {
				converter.IptablesManager = iptablesManager
				converter.UsingIptables = true
				log.Printf("Successfully set up iptables filtering for source IP: %s", ipInfo.SourceIP)
			}
		} else {
			log.Printf("Warning: iptables not available for source IP filtering")
			log.Printf("Continuing without iptables filtering")
		}
	}

	return converter, nil
}

func (c *RtpToMp4Converter) buildUdpSrcElement(ipInfo *IPInfo) string {
	// Base parameters for udpsrc
	var params string
	if ipInfo.IsMulticast {
		params = fmt.Sprintf(`multicast-group=%s port=%d auto-multicast=true buffer-size=2097152`,
			ipInfo.Address, ipInfo.Port)
		// Add multicast-iface parameter if a network interface is specified
		if ipInfo.Interface != "" {
			params += fmt.Sprintf(` multicast-iface=%s`, ipInfo.Interface)
			log.Printf("Using network interface %s for multicast reception", ipInfo.Interface)
		}
	} else {
		params = fmt.Sprintf(`address=%s port=%d buffer-size=2097152`,
			ipInfo.Address, ipInfo.Port)
	}
	// Add local-address parameter if specified
	if ipInfo.LocalAddr != "" {
		params += fmt.Sprintf(` local-address=%s`, ipInfo.LocalAddr)
		log.Printf("Using local address %s for RTP reception", ipInfo.LocalAddr)
	}
	return fmt.Sprintf(`
		udpsrc %s
	`, params)
}

// updateStreamInfo updates the stream information based on GStreamer element properties
func (c *RtpToMp4Converter) updateStreamInfo(tsSync bool, services []models.ServiceInfo, videoInfo models.VideoInfo, audioInfo models.AudioInfo) {
	c.streamInfoMutex.Lock()
	defer c.streamInfoMutex.Unlock()

	c.TsSync = tsSync

	if len(services) > 0 {
		c.Services = services
	}

	if videoInfo.Resolution != "" && videoInfo.Resolution != "Unknown" {
		c.VideoInfo.Resolution = videoInfo.Resolution
	}

	if videoInfo.Codec != "" && videoInfo.Codec != "Unknown" {
		c.VideoInfo.Codec = videoInfo.Codec
	}

	if audioInfo.Channels > 0 {
		c.AudioInfo.Channels = audioInfo.Channels
	}

	if audioInfo.Codec != "" && audioInfo.Codec != "Unknown" {
		c.AudioInfo.Codec = audioInfo.Codec
	}
}

// updateTsSync updates only the ts_sync status
func (c *RtpToMp4Converter) updateTsSync(tsSync bool) {
	c.streamInfoMutex.Lock()
	defer c.streamInfoMutex.Unlock()

	// Only update if the status is changing
	if c.TsSync != tsSync {
		log.Printf("Updating TS sync status for recorder %d from %v to %v", c.ID, c.TsSync, tsSync)
		c.TsSync = tsSync
	}
}

func (c *RtpToMp4Converter) buildPipeline() error {
	ipInfo, err := parseRtpURL(c.RtpURL)
	if err != nil {
		return err
	}

	timestamp := time.Now().Format("20060102_150405")
	connType := "unicast"
	if ipInfo.IsMulticast {
		connType = "multicast"
	}

	// Parse resolution and handle interlaced/progressive formats
	cleanResolution, isInterlaced := parseResolution(c.Params.Resolution)
	log.Printf("Resolution: %s (clean: %s, interlaced: %v)",
		c.Params.Resolution, cleanResolution, isInterlaced)

	outputFile := filepath.Join(c.OutputDir,
		fmt.Sprintf("%s_%s_%s_%d_%s_%s.mp4",
			timestamp, connType, ipInfo.Address, ipInfo.Port,
			c.Params.Resolution, c.Params.VideoBitrate))

	// Try different GStreamer pipeline approaches in sequence
	var pipeline *gst.Pipeline
	var pipelineErr error

	// Convert bitrate from string with suffix (like "1000k") to integer value for encoders
	// x264enc doesn't accept the 'k' suffix for bitrate values
	videoBitrateValue, err := parseBitrate(c.Params.VideoBitrate)
	if err != nil {
		log.Printf("Warning: Failed to parse video bitrate '%s', using default: %v", c.Params.VideoBitrate, err)
		videoBitrateValue = 1000000 // Default to 1Mbps if parsing fails
	}

	// Check if we're using NVIDIA GPU encoder
	isNvidiaEncoder := strings.HasPrefix(c.Params.VideoCodec, "nv")

	// Double-check that the specified encoder is actually available
	encoderAvailable := isPluginAvailable(c.Params.VideoCodec)
	if !encoderAvailable {
		log.Printf("Warning: Specified encoder %s is not available. Falling back to x264enc.", c.Params.VideoCodec)
		c.Params.VideoCodec = "x264enc" // Fall back to x264enc
		isNvidiaEncoder = false
	}

	// For NVIDIA encoders, we need to use different parameters
	// nvh264enc uses bits/sec instead of kbits/sec and has different property names
	var encoderParams string
	if isNvidiaEncoder {
		// NVIDIA encoder parameters (nvh264enc)
		// Convert bitrate to bits/sec (nvenc uses bits/sec)
		nvBitrate := videoBitrateValue

		// Add videoconvert before nvh264enc to ensure proper format conversion
		encoderParams = fmt.Sprintf(`videoconvert ! %s bitrate=%d rc-mode=1 preset=2 zerolatency=true`,
			c.Params.VideoCodec, nvBitrate)

		log.Printf("Using NVIDIA encoder with videoconvert: %s", encoderParams)
	} else {
		// Standard x264enc parameters
		encoderParams = fmt.Sprintf(`%s tune=zerolatency bitrate=%d speed-preset=%s profile=%s`,
			c.Params.VideoCodec, videoBitrateValue, c.Params.Preset, c.Params.Profile)
		
		log.Printf("Using standard encoder: %s", encoderParams)
	}

	// FFmpeg if available
	if isFFmpegAvailable() {
		log.Printf("All GStreamer pipelines failed, trying FFmpeg fallback...")

		// Use project base path for temporary directory
		tmpDir := filepath.Join(".", "data", "tmp")

		// Ensure the directory exists
		if err := os.MkdirAll(tmpDir, 0755); err != nil {
			return fmt.Errorf("failed to create temporary directory: %v", err)
		}

		// Create a unique TS file path for this recording
		tsFilePath := fmt.Sprintf("%s/stream_%s_%d_%d_%d.ts", tmpDir, ipInfo.Address, ipInfo.Port, c.ID, time.Now().UnixNano())

		// Create a simple pipeline that just passes the UDP stream to FFmpeg
		ffmpegPipelineStr := fmt.Sprintf(`
			%s ! queue max-size-bytes=2097152 ! tsparse ! filesink location=%s sync=false
		`, c.buildUdpSrcElement(ipInfo), tsFilePath)

		log.Printf("Creating GStreamer pipeline with configuration")
		log.Printf("Pipeline string: %s", ffmpegPipelineStr)
		pipeline, pipelineErr = gst.NewPipelineFromString(ffmpegPipelineStr)

		if pipelineErr == nil {
			log.Printf("Pipeline creation SUCCESSFUL")
			// Set flag to indicate we're using TS output as fallback
			c.UsingTsOutput = true

			// Store the output file path for later FFmpeg conversion
			c.FfmpegOutputFile = outputFile

			// Store the TS file path
			c.TsFilePath = tsFilePath

			log.Printf("Using TS recording mode. TS file: %s, Final MP4: %s", tsFilePath, outputFile)
		} else {
			log.Printf("Pipeline creation FAILED: %v", pipelineErr)
		}
	}

	c.Pipeline = pipeline
	log.Printf("Successfully created pipeline")

	// Handle pipeline messages with improved error handling
	bus := c.Pipeline.GetBus()
	go func() {
		tsSync := false
		services := []models.ServiceInfo{}

		for {
			msg := bus.TimedPopFiltered(gst.ClockTimeNone,
				gst.MessageType(gst.MessageError|gst.MessageEOS|gst.MessageStateChanged|gst.MessageWarning))

			if msg == nil {
				continue
			}

			switch msg.Type() {
			case gst.MessageType(gst.MessageError):
				err := msg.ParseError()
				log.Printf("PIPELINE ERROR: %v", err)

				// Try to get more detailed error information
				element := msg.Source()
				if element != "" {
					log.Printf("Error from element: %s", element)
				}

				select {
				case <-c.Done:
					// Channel already closed
				default:
					close(c.Done)
				}
			case gst.MessageType(gst.MessageWarning):
				warn := msg.ParseWarning()
				log.Printf("PIPELINE WARNING: %v", warn)
				
				// Check for TS corruption warnings and try to recover
				warnStr := warn.Error()
				if strings.Contains(warnStr, "corrupt") || strings.Contains(warnStr, "PES packet size mismatch") {
					log.Printf("DETECTED TS CORRUPTION WARNING - implementing recovery strategy")
					// Log the warning but don't stop the pipeline - let it continue with error recovery
					element := msg.Source()
					if element != "" {
						log.Printf("Corruption warning from element: %s", element)
					}
				}
			case gst.MessageType(gst.MessageEOS):
				log.Printf("END OF STREAM reached after %d seconds", c.Duration)
				select {
				case <-c.Done:
					// Channel already closed
				default:
					close(c.Done)
				}
			case gst.MessageType(gst.MessageStateChanged):
				if msg.Source() == c.Pipeline.GetName() {
					oldState, newState := msg.ParseStateChanged()
					log.Printf("Pipeline state changed from %s to %s",
						stateToString(oldState), stateToString(newState))

					if newState == gst.StatePlaying {
						log.Printf("Pipeline is PLAYING. Recording for %d seconds", c.Duration)

						// After pipeline starts playing, detect real services from the RTP stream
						tsSync = true
						c.updateStreamInfo(tsSync, services, models.VideoInfo{}, models.AudioInfo{})

						// Detect real services using the detector package
						go func() {
							time.Sleep(3 * time.Second) // Give the stream time to stabilize

							log.Printf("Starting real service detection for RTP URL: %s", c.RtpURL)

							// Set up detection options
							options := detector.DefaultDetectionOptions()
							options.Timeout = 10

							// Use network interface if specified
							if c.NetworkInterface != "" {
								options.Interface = c.NetworkInterface
							}

							// Detect services
							streamInfo, err := detector.DetectServices(c.RtpURL, options)
							if err != nil {
								log.Printf("Service detection failed: %v", err)
								// Use fallback service info on error with proper audio tracks
								errorService := models.ServiceInfo{
									ServiceID:   1,
									ServiceName: "Detected Service (Error)",
									AudioTracks: []models.AudioInfo{
										{
											Channels: 2,
											Codec:    "Unknown",
										},
									},
								}
								services = append(services, errorService)
								c.updateStreamInfo(tsSync, services, models.VideoInfo{}, models.AudioInfo{})
								return
							}

							// Update with detected services
							if streamInfo.TsSync && len(streamInfo.Services) > 0 {
								log.Printf("Successfully detected %d services", len(streamInfo.Services))
								services = streamInfo.Services

								// Update video and audio info with detected values
								var videoInfo models.VideoInfo
								var audioInfo models.AudioInfo

								if len(streamInfo.VideoInfo) > 0 {
									videoInfo = streamInfo.VideoInfo[0] // Use first video stream
								}

								if len(streamInfo.AudioInfo) > 0 {
									audioInfo = streamInfo.AudioInfo[0] // Use first audio stream
								}

								c.updateStreamInfo(tsSync, services, videoInfo, audioInfo)
							} else {
								log.Printf("No services detected or TS sync failed")
								// Use fallback service info with proper audio tracks
								fallbackService := models.ServiceInfo{
									ServiceID:   1,
									ServiceName: "No Services Detected",
									AudioTracks: []models.AudioInfo{
										{
											Channels: 2,
											Codec:    "Unknown",
										},
									},
								}
								services = append(services, fallbackService)
								c.updateStreamInfo(tsSync, services, models.VideoInfo{}, models.AudioInfo{})
							}
						}()
					}
				}
			}
		}
	}()

	return nil
}

func (c *RtpToMp4Converter) Convert() error {
	log.Printf("Starting conversion process for recorder ID %d", c.ID)
	log.Printf("RTP URL: %s", c.RtpURL)
	log.Printf("Duration: %d seconds", c.Duration)

	// Build the pipeline with error handling
	log.Printf("Building GStreamer pipeline")
	if err := c.buildPipeline(); err != nil {
		log.Printf("Pipeline build FAILED: %v", err)
		return fmt.Errorf("pipeline build failed: %v", err)
	}
	log.Printf("Pipeline built successfully")

	// Get output file path before starting (for progress reporting)
	log.Printf("Determining output file path")
	outputFile, err := c.getOutputFilePath()
	if err != nil {
		log.Printf("Failed to determine output file path: %v", err)
		return fmt.Errorf("failed to determine output file path: %v", err)
	}
	log.Printf("Output will be saved to: %s", outputFile)

	// Create a context with cancellation for coordinating shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Start pipeline with improved state change handling
	log.Printf("Setting pipeline to PLAYING state")
	stateChangeStart := time.Now()
	err = c.Pipeline.SetState(gst.State(gst.StatePlaying))
	if err != nil {
		log.Printf("Failed to set pipeline to PLAYING state: %v", err)
		return fmt.Errorf("failed to set pipeline to playing state: %v", err)
	}
	log.Printf("Pipeline state change command sent")

	// Wait for state change to complete with timeout
	timeout := time.NewTimer(10 * time.Second)
	defer timeout.Stop()

	// Create a goroutine to check state
	stateChangeDone := make(chan struct{})
	go func() {
		for {
			status, state := c.Pipeline.GetState(gst.State(gst.StatePlaying), gst.ClockTime(gst.ClockTimeNone))
			if state == gst.State(gst.StatePlaying) {
				close(stateChangeDone)
				return
			}
			if status == gst.StateChangeReturn(gst.StateChangeFailure) {
				log.Printf("Failed to change state to PLAYING")
				close(stateChangeDone)
				return
			}
			time.Sleep(100 * time.Millisecond)
		}
	}()

	// Wait for either state change or timeout
	select {
	case <-stateChangeDone:
		log.Printf("Pipeline reached PLAYING state after %v", time.Since(stateChangeStart))
	case <-timeout.C:
		log.Printf("WARNING - Timeout waiting for pipeline to reach PLAYING state")
	}

	// Set up a timer for the duration
	timer := time.NewTimer(time.Duration(c.Duration) * time.Second)
	defer timer.Stop()

	// Create a channel to signal when recording is completed
	recordingCompleted := make(chan struct{})

	// Start progress reporting and RTP URL checking in a separate goroutine
	go func() {
		ticker := time.NewTicker(10 * time.Second)
		defer ticker.Stop()

		startTime := time.Now()
		for {
			select {
			case <-ticker.C:
				elapsed := time.Since(startTime)
				progress := (elapsed.Seconds() / float64(c.Duration)) * 100
				if progress > 100 {
					progress = 100
				}

				// Check if file exists and report size
				if fileInfo, err := os.Stat(outputFile); err == nil {
					log.Printf("Progress: %.1f%% (%.1f of %d seconds) - File size: %.2f MB",
						progress, elapsed.Seconds(), c.Duration, float64(fileInfo.Size())/(1024*1024))
				} else {
					log.Printf("Progress: %.1f%% (%.1f of %d seconds) - File not created yet",
						progress, elapsed.Seconds(), c.Duration)
				}

				// Check if recording has completed
				select {
				case <-recordingCompleted:
					// Recording has completed, no need to check RTP URL anymore
					log.Printf("Recording completed, skipping RTP URL check")
					continue
				default:
					// Recording is still in progress, continue with RTP URL check
				}

				// Only check RTP URL during the actual recording phase (GStreamer),
				// not during transcoding (FFmpeg) or after recording is completed
				if !c.UsingTsOutput || c.FfmpegOutputFile == "" {
					// Make three attempts to check the RTP URL before setting TS sync to false
					isWorking := false
					maxAttempts := 3

					// Only make multiple attempts if the current TS sync is true
					c.streamInfoMutex.Lock()
					prevTsSync := c.TsSync
					c.streamInfoMutex.Unlock()

					if prevTsSync {
						// If TS sync is currently true, make multiple attempts before setting to false
						for attempt := 1; attempt <= maxAttempts; attempt++ {
							log.Printf("Checking RTP URL for recorder %d (attempt %d of %d)", c.ID, attempt, maxAttempts)
							attemptResult := CheckRtpUrlIsWorking(c.RtpURL)

							if attemptResult {
								// If any attempt succeeds, consider the URL working
								isWorking = true
								log.Printf("RTP URL check succeeded on attempt %d for recorder %d", attempt, c.ID)
								break
							}

							// If this isn't the last attempt, wait a short time before trying again
							if attempt < maxAttempts {
								log.Printf("RTP URL check failed on attempt %d for recorder %d, trying again...", attempt, c.ID)
								time.Sleep(500 * time.Millisecond)
							} else {
								log.Printf("RTP URL check failed on all %d attempts for recorder %d", maxAttempts, c.ID)
							}
						}
					} else {
						// If TS sync is already false, just make one attempt to see if it's back
						isWorking = CheckRtpUrlIsWorking(c.RtpURL)
					}

					// Update the TS sync status
					c.streamInfoMutex.Lock()
					prevTsSync = c.TsSync // Get the latest value
					c.TsSync = isWorking
					c.streamInfoMutex.Unlock()

					// If TS sync was lost, signal an error to stop the recording
					// This will only happen during the actual recording phase
					if !isWorking && prevTsSync {
						log.Printf("TS sync lost during recording for recorder %d after %d failed attempts, stopping recording", c.ID, maxAttempts)
						// Signal the main goroutine to stop
						select {
						case <-c.Done:
							// Channel already closed
						default:
							close(c.Done)
						}
						return
					} else if isWorking && !prevTsSync {
						log.Printf("TS sync restored during recording for recorder %d", c.ID)
					}
				} else if c.UsingTsOutput && c.FfmpegOutputFile != "" {
					// During transcoding phase, set ts_sync to true by default
					// Only set to false if an error occurs during transcoding
					c.streamInfoMutex.Lock()
					if !c.TsSync {
						log.Printf("Setting ts_sync to true during transcoding phase for recorder %d", c.ID)
						c.TsSync = true
					}
					c.streamInfoMutex.Unlock()
				}
			case <-ctx.Done():
				return
			}
		}
	}()

	// Flag to track if the recording completed successfully
	completedSuccessfully := false

	// Wait for either the timer to expire, an error/EOS message, or a signal
	log.Printf("Waiting for recording completion (timer: %d seconds)", c.Duration)
	select {
	case <-timer.C:
		log.Printf("Recording duration of %d seconds COMPLETED successfully", c.Duration)
		completedSuccessfully = true

		// Signal that recording is completed to stop RTP URL checking
		close(recordingCompleted)
		log.Printf("Signaled to stop RTP URL checking")

		// Set the RecordingCompleted flag to true
		c.streamInfoMutex.Lock()
		c.RecordingCompleted = true
		log.Printf("Set RecordingCompleted flag to true for recorder %d", c.ID)
		c.streamInfoMutex.Unlock()

		// Only send EOS if the recording completed successfully
		// Send EOS to properly finalize the MP4 file
		log.Printf("Sending EOS event to finalize the MP4 file...")
		if !c.Pipeline.SendEvent(gst.NewEOSEvent()) {
			log.Printf("Warning: Failed to send EOS event to pipeline")
		}

		// Wait for EOS to propagate with timeout
		eosTimeout := time.NewTimer(5 * time.Second)
		defer eosTimeout.Stop()

		select {
		case <-time.After(2 * time.Second): // Minimum wait time
			log.Printf("Minimum EOS propagation time elapsed")
		case <-eosTimeout.C:
			log.Printf("EOS propagation timeout reached")
		}
	case <-c.Done:
		log.Printf("Pipeline completed or encountered an ERROR")
	case <-c.StopChan:
		log.Printf("Received STOP request for recorder %d", c.ID)
	}

	// Cancel context to stop progress reporting
	cancel()

	// Now stop the pipeline with improved error handling
	log.Printf("Setting pipeline to NULL state")
	nullStateStart := time.Now()
	err = c.Pipeline.SetState(gst.State(gst.StateNull))
	if err != nil {
		log.Printf("WARNING - Failed to set pipeline to NULL state: %v", err)
	}

	// Wait for state change to complete with timeout
	nullTimeout := time.NewTimer(5 * time.Second)
	defer nullTimeout.Stop()

	// Create a goroutine to check state
	nullStateDone := make(chan struct{})
	go func() {
		for {
			status, state := c.Pipeline.GetState(gst.State(gst.StateNull), gst.ClockTime(gst.ClockTimeNone))
			if state == gst.State(gst.StateNull) {
				close(nullStateDone)
				return
			}
			if status == gst.StateChangeReturn(gst.StateChangeFailure) {
				log.Printf("Failed to change state to NULL")
				close(nullStateDone)
				return
			}
			time.Sleep(100 * time.Millisecond)
		}
	}()

	// Wait for either state change or timeout
	select {
	case <-nullStateDone:
		log.Printf("Pipeline reached NULL state after %v", time.Since(nullStateStart))
	case <-nullTimeout.C:
		log.Printf("WARNING - Timeout waiting for pipeline to reach NULL state")
	}

	log.Printf("Pipeline STOPPED successfully")

	// If we're using the FFmpeg fallback, run the conversion now
	if c.UsingTsOutput && c.FfmpegOutputFile != "" && c.TsFilePath != "" {
		log.Printf("Recording complete. Starting TS file processing")
		log.Printf("TS file path: %s", c.TsFilePath)
		log.Printf("Target MP4 file: %s", c.FfmpegOutputFile)

		// Update the status to "transcoding"
		if statusManager := GetStatusManager(); statusManager != nil {
			err := statusManager.UpdateRecorderStatus(c.ID, "transcoding")
			if err != nil {
				log.Printf("Failed to update recorder status to 'transcoding': %v", err)
				// Continue anyway, not a critical error
			} else {
				log.Printf("Updated recorder status to 'transcoding' for recorder %d", c.ID)
			}
		}

		// We're now in the transcoding phase, not the recording phase
		// The RecordingCompleted flag is already set to true, but CompletedSuccessfully is not yet set

		// Check if the TS file exists and get its size
		log.Printf("Checking TS file accessibility")
		tsFileInfo, err := os.Stat(c.TsFilePath)
		if err != nil {
			log.Printf("ERROR - Could not access TS file %s: %v", c.TsFilePath, err)
			// Set completion status to false since transcoding failed
			c.CompletedSuccessfully = false

			// Set ts_sync to false when TS file is not accessible
			c.streamInfoMutex.Lock()
			c.TsSync = false
			log.Printf("Setting ts_sync to false due to TS file access failure for recorder %d", c.ID)
			c.streamInfoMutex.Unlock()

			// Update the status to "failed" when TS file is not accessible
			if statusManager := GetStatusManager(); statusManager != nil {
				err := statusManager.UpdateRecorderStatus(c.ID, "failed")
				if err != nil {
					log.Printf("Failed to update recorder status to 'failed': %v", err)
					// Continue anyway, not a critical error
				} else {
					log.Printf("Updated recorder status to 'failed' for recorder %d", c.ID)
				}
			}

			return fmt.Errorf("TS file %s not accessible: %v", c.TsFilePath, err)
		}

		tsFileSize := tsFileInfo.Size()
		log.Printf("TS file size: %.2f MB", float64(tsFileSize)/(1024*1024))

		if tsFileSize == 0 {
			log.Printf("ERROR - TS file is EMPTY. No data was recorded")
			// Set completion status to false since transcoding failed
			c.CompletedSuccessfully = false

			// Set ts_sync to false when TS file is empty
			c.streamInfoMutex.Lock()
			c.TsSync = false
			log.Printf("Setting ts_sync to false due to empty TS file for recorder %d", c.ID)
			c.streamInfoMutex.Unlock()

			// Update the status to "failed" when TS file is empty
			if statusManager := GetStatusManager(); statusManager != nil {
				err := statusManager.UpdateRecorderStatus(c.ID, "failed")
				if err != nil {
					log.Printf("Failed to update recorder status to 'failed': %v", err)
					// Continue anyway, not a critical error
				} else {
					log.Printf("Updated recorder status to 'failed' for recorder %d", c.ID)
				}
			}

			// Clean up the empty TS file
			if err := os.Remove(c.TsFilePath); err != nil {
				log.Printf("Warning: Failed to remove empty TS file %s: %v", c.TsFilePath, err)
			} else {
				log.Printf("Removed empty TS file: %s", c.TsFilePath)
			}

			return fmt.Errorf("TS file is empty. No data was recorded")
		}

		log.Printf("Moving TS file to final location")
		err = os.Rename(c.TsFilePath, c.FfmpegOutputFile)
		if err != nil {
			log.Printf("ERROR - Could not move TS file to output location: %v", err)
			// Set completion status to false since transcoding failed
			c.CompletedSuccessfully = false

			// Set ts_sync to false when MP4 file is not accessible
			c.streamInfoMutex.Lock()
			c.TsSync = false
			log.Printf("Setting ts_sync to false due to TS file access failure for recorder %d", c.ID)
			c.streamInfoMutex.Unlock()

			// Update the status to "failed" when MP4 file is not accessible
			if statusManager := GetStatusManager(); statusManager != nil {
				err := statusManager.UpdateRecorderStatus(c.ID, "failed")
				if err != nil {
					log.Printf("Failed to update recorder status to 'failed': %v", err)
					// Continue anyway, not a critical error
				} else {
					log.Printf("Updated recorder status to 'failed' for recorder %d", c.ID)
				}
			}

			// Clean up the TS file even if TS verification failed
			if err := os.Remove(c.TsFilePath); err != nil {
				log.Printf("Warning: Failed to remove TS file %s after failed TS verification: %v", c.TsFilePath, err)
			} else {
				log.Printf("Removed TS file after failed TS verification: %s", c.TsFilePath)
			}

			return fmt.Errorf("TS file not accessible after transcoding: %v", err)
		}

		log.Printf("TS file successfully moved to final location")
		c.CompletedSuccessfully = true

		c.streamInfoMutex.Lock()
		c.TsSync = true
		log.Printf("Setting ts_sync to TRUE after successful file processing")
		c.streamInfoMutex.Unlock()

	} else {
		c.CompletedSuccessfully = completedSuccessfully

	}

	// Store the output file path for later use
	setLastRecordingFilePath(c.ID, c.FfmpegOutputFile)

	// Clean up iptables rules if they were used
	if c.UsingIptables && c.IptablesManager != nil {
		log.Printf("Cleaning up iptables rules for stopped recorder %d", c.ID)
		if err := c.IptablesManager.removeAllRules(); err != nil {
			log.Printf("Warning: Failed to clean up iptables rules for stopped recorder %d: %v", c.ID, err)
			// Also try the backup cleanup method
			if backupErr := CheckAndCleanStaleChains(c.ID); backupErr != nil {
				log.Printf("Backup cleanup also failed for stopped recorder %d: %v", c.ID, backupErr)
			} else {
				log.Printf("Backup cleanup succeeded for stopped recorder %d", c.ID)
			}
		} else {
			log.Printf("Successfully cleaned up iptables rules for stopped recorder %d", c.ID)
		}
		c.UsingIptables = false
		c.IptablesManager = nil
	}

	// Verify the output file with improved error handling
	return c.verifyOutputFile(c.FfmpegOutputFile)
}

// Helper method to get output file path
func (c *RtpToMp4Converter) getOutputFilePath() (string, error) {
	// Try to find the filesink element to get the actual output path
	if c.Pipeline != nil {
		elements, err := c.Pipeline.GetElements()
		if err == nil {
			for _, element := range elements {
				if element.GetFactory().GetName() == "filesink" {
					// Get the "location" property
					location, err := element.GetProperty("location")
					if err == nil && location != nil {
						outputFile := location.(string)
						log.Printf("Found output file from filesink element: %s", outputFile)
						return outputFile, nil
					}
				}
			}
		} else {
			log.Printf("Warning: Could not get pipeline elements: %v", err)
		}
	}

	// If we couldn't get the path from the element, reconstruct it
	ipInfo, err := parseRtpURL(c.RtpURL)
	if err != nil {
		return "", err
	}

	timestamp := time.Now().Format("20060102_150405")
	connType := "unicast"
	if ipInfo.IsMulticast {
		connType = "multicast"
	}

	outputFile := filepath.Join(c.OutputDir,
		fmt.Sprintf("%s_%s_%s_%d_%s_%s.mp4",
			timestamp, connType, ipInfo.Address, ipInfo.Port,
			c.Params.Resolution, c.Params.VideoBitrate))

	log.Printf("Reconstructed output file path: %s", outputFile)
	return outputFile, nil
}

// Helper method to verify the output file
func (c *RtpToMp4Converter) verifyOutputFile(outputFile string) error {
	// Check if the file exists and verify it
	fileInfo, err := os.Stat(outputFile)
	if err != nil {
		log.Printf("Warning: MP4 file was not created or cannot be accessed: %v", err)
		// Set completion status to false since verification failed
		c.CompletedSuccessfully = false
		return fmt.Errorf("recording failed: output file was not created or cannot be accessed: %v", err)
	}

	fileSize := fileInfo.Size()
	log.Printf("MP4 file created: %s (Size: %.2f MB)", outputFile, float64(fileSize)/(1024*1024))

	if fileSize == 0 {
		log.Printf("Warning: MP4 file has zero size. No data was recorded.")
		// Set completion status to false since verification failed
		c.CompletedSuccessfully = false
		return fmt.Errorf("recording failed: output file has zero size")
	} else if fileSize < 1024 {
		log.Printf("Warning: MP4 file is very small (%.2f KB). It may be corrupted or contain no useful data.", float64(fileSize)/1024)
		// We don't fail here, just log a warning
	}

	// Try to use gst-discoverer to verify the file if available
	verificationFailed := false
	if isPluginAvailable("gst-discoverer-1.0") {
		log.Printf("Verifying MP4 file with gst-discoverer-1.0...")
		cmd := exec.Command("gst-discoverer-1.0", outputFile)
		output, err := cmd.CombinedOutput()
		if err != nil {
			log.Printf("Warning: MP4 file verification failed: %v\nOutput: %s", err, string(output))
			log.Printf("The file may still be playable in some media players.")
			verificationFailed = true
		} else {
			log.Printf("MP4 file verification successful. File should be playable in media players like VLC.")
		}
	} else {
		// Try to use ffprobe as an alternative if available
		if isFFmpegAvailable() {
			log.Printf("Verifying MP4 file with ffprobe...")
			cmd := exec.Command("ffprobe", "-v", "error", outputFile)
			output, err := cmd.CombinedOutput()
			if err != nil {
				log.Printf("Warning: MP4 file verification with ffprobe failed: %v\nOutput: %s", err, string(output))
				verificationFailed = true
			} else {
				log.Printf("MP4 file verification with ffprobe successful.")
			}
		}
	}

	// If verification failed, we don't consider it a complete failure
	// The file might still be usable, so we just log a warning
	if verificationFailed {
		log.Printf("Warning: File verification failed but the file might still be usable")
	}

	log.Printf("Conversion completed successfully. Output file: %s", outputFile)
	return nil
}

// StartRecording starts a new recording with the given parameters
func StartRecording(id int, rtpURL, outputDir string, durationSeconds int, vcodec, acodec, resolution, fps string, sampleRate int, vbitrate, abitrate, maxVbitrate int, serviceID int, sourceIP string) error {
	recorderMutex.Lock()
	defer recorderMutex.Unlock()

	// Check if a recorder with this ID already exists in active recorders
	if _, exists := activeRecorders[id]; exists {
		return fmt.Errorf("recorder with ID %d is already running", id)
	}

	// Check if the recorder is already joined
	joinedConverter, joinedExists := joinedRecorders[id]

	// Convert parameters to the format expected by the recorder
	// For bitrate, we need to ensure it's in the correct format
	// For resolution, we keep the 'i' suffix for interlaced video for display purposes
	// but handle it specially in the pipeline creation
	params := &EncodingParams{
		VideoCodec:   vcodec,
		AudioCodec:   acodec,
		Resolution:   resolution,
		FPS:          fps,
		SampleRate:   strconv.Itoa(sampleRate),
		VideoBitrate: fmt.Sprintf("%dk", vbitrate),
		AudioBitrate: fmt.Sprintf("%dk", abitrate),
		Preset:       "medium",
		Profile:      "high",
	}

	// Create output directory if it doesn't exist
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %v", err)
	}

	// Add sourceIP to RTP URL if provided and not already present
	finalRtpURL := rtpURL
	if sourceIP != "" && !strings.Contains(rtpURL, "sourceip=") {
		separator := "?"
		if strings.Contains(rtpURL, "?") {
			separator = "&"
		}
		finalRtpURL = fmt.Sprintf("%s%ssourceip=%s", rtpURL, separator, sourceIP)
		log.Printf("Added source IP filter to RTP URL: %s", finalRtpURL)
	}

	var converter *RtpToMp4Converter
	var err error

	if joinedExists {
		// Create a new converter with the same parameters but with a duration
		converter, err = NewRtpToMp4Converter(id, finalRtpURL, outputDir, durationSeconds, params, serviceID)
		if err != nil {
			return fmt.Errorf("failed to create converter: %v", err)
		}

		// Copy the stream information from the joined converter
		joinedConverter.streamInfoMutex.Lock()
		converter.TsSync = joinedConverter.TsSync
		converter.Services = joinedConverter.Services
		converter.VideoInfo = joinedConverter.VideoInfo
		converter.AudioInfo = joinedConverter.AudioInfo
		joinedConverter.streamInfoMutex.Unlock()

		// Remove from joined recorders
		delete(joinedRecorders, id)
	} else {
		// Create a new converter
		converter, err = NewRtpToMp4Converter(id, finalRtpURL, outputDir, durationSeconds, params, serviceID)
		if err != nil {
			return fmt.Errorf("failed to create converter: %v", err)
		}

		// Check RTP URL and set TS sync status based on the result
		log.Printf("Checking RTP URL %s for recorder %d", finalRtpURL, id)
		isWorking := CheckRtpUrlIsWorking(finalRtpURL)
		log.Printf("RTP URL check result for recorder %d: %s", id, map[bool]string{true: "working", false: "not working"}[isWorking])

		// Set the initial TS sync status
		converter.streamInfoMutex.Lock()
		converter.TsSync = isWorking // Set based on RTP URL check result
		converter.streamInfoMutex.Unlock()
	}

	// Set the start time
	converter.StartTime = time.Now()

	// Store the converter in the active recorders map
	activeRecorders[id] = converter

	// Update the status to "recording" when starting a recording
	if statusManager := GetStatusManager(); statusManager != nil {
		err := statusManager.UpdateRecorderStatus(id, "recording")
		if err != nil {
			log.Printf("Failed to update recorder status to 'recording': %v", err)
			// Continue anyway, not a critical error
		} else {
			log.Printf("Updated recorder status to 'recording' for recorder %d", id)
		}
	}

	// Start the converter in a goroutine
	go func() {
		var err error
		if err = converter.Convert(); err != nil {
			log.Printf("Recorder %d failed: %v", id, err)
		}

		// Lock the mutex to safely access the maps
		recorderMutex.Lock()

		// Get the completion status
		completedSuccessfully := converter.CompletedSuccessfully

		// Save the stream information
		streamInfo := &StreamInfo{
			TsSync:    converter.TsSync,
			Services:  converter.Services,
			VideoInfo: converter.VideoInfo,
			AudioInfo: converter.AudioInfo,
			ServiceID: converter.ServiceID,
		}

		// Check if the recorder is still in the active recorders map
		// It might have been removed already by StopRecording
		_, stillActive := activeRecorders[id]
		if stillActive {
			// Remove from active recorders if it's still there
			delete(activeRecorders, id)
			log.Printf("Recorder %d removed from activeRecorders map by completion goroutine", id)
		}

		// After successful transcoding, check RTP URL and get streaming info without initializing a full pipeline
		if completedSuccessfully && converter.UsingTsOutput && converter.FfmpegOutputFile != "" {
			log.Printf("Recording and transcoding completed successfully for recorder %d", id)
			log.Printf("Checking RTP URL %s after successful transcoding", converter.RtpURL)

			// Check RTP URL without initializing a full pipeline
			// Make three attempts to check the RTP URL before setting TS sync to false
			isWorking := false
			maxAttempts := 3

			for attempt := 1; attempt <= maxAttempts; attempt++ {
				log.Printf("Checking RTP URL for recorder %d after transcoding (attempt %d of %d)", id, attempt, maxAttempts)
				attemptResult := CheckRtpUrlIsWorking(converter.RtpURL)

				if attemptResult {
					// If any attempt succeeds, consider the URL working
					isWorking = true
					log.Printf("RTP URL check succeeded on attempt %d for recorder %d after transcoding", attempt, id)
					break
				}

				// If this isn't the last attempt, wait a short time before trying again
				if attempt < maxAttempts {
					log.Printf("RTP URL check failed on attempt %d for recorder %d after transcoding, trying again...", attempt, id)
					time.Sleep(500 * time.Millisecond)
				} else {
					log.Printf("RTP URL check failed on all %d attempts for recorder %d after transcoding", maxAttempts, id)
				}
			}

			log.Printf("Final RTP URL check result after transcoding for recorder %d: %s", id, map[bool]string{true: "working", false: "not working"}[isWorking])

			// Create a lightweight joined recorder entry with just the necessary information
			
			joinedConverter := &RtpToMp4Converter{
				ID:        id,
				RtpURL:    converter.RtpURL,
				OutputDir: converter.OutputDir,
				StartTime: time.Now(),
				// Set stream information based on the RTP URL check
				TsSync:    isWorking,
				Services:  streamInfo.Services,
				VideoInfo: streamInfo.VideoInfo,
				AudioInfo: streamInfo.AudioInfo,
				ServiceID: streamInfo.ServiceID,
				// Initialize channels and mutex
				Done:            make(chan struct{}),
				StopChan:        make(chan struct{}),
				streamInfoMutex: sync.Mutex{},
				// Mark as recording completed to prevent RTP URL checking
				RecordingCompleted: true,
			}

			// Store the lightweight recorder in the joined recorders map
			joinedRecorders[id] = joinedConverter

			// Update the status manager's TS sync status map
			if statusManager := GetStatusManager(); statusManager != nil {
				statusManager.tsSyncMutex.Lock()
				statusManager.tsSyncStatus[id] = isWorking
				statusManager.tsSyncMutex.Unlock()
			}

			log.Printf("Created lightweight joined recorder for %d after successful transcoding (TS sync: %v)", id, isWorking)
		} else {
			// Only auto-join if recording failed or was stopped manually
			log.Printf("Auto-joining recorder %d after recording", id)

			// Create a new converter for joined mode
			joinedConverter, err := NewRtpToMp4Converter(id, converter.RtpURL, converter.OutputDir, 0, converter.Params, converter.ServiceID)
			if err == nil {
				// Perform a new RTP URL check to get the current TS sync status
				log.Printf("Checking RTP URL %s for auto-joined recorder %d", converter.RtpURL, id)

				// Make three attempts to check the RTP URL before setting TS sync to false
				isWorking := false
				maxAttempts := 3

				for attempt := 1; attempt <= maxAttempts; attempt++ {
					log.Printf("Checking RTP URL for auto-joined recorder %d (attempt %d of %d)", id, attempt, maxAttempts)
					attemptResult := CheckRtpUrlIsWorking(converter.RtpURL)

					if attemptResult {
						// If any attempt succeeds, consider the URL working
						isWorking = true
						log.Printf("RTP URL check succeeded on attempt %d for auto-joined recorder %d", attempt, id)
						break
					}

					// If this isn't the last attempt, wait a short time before trying again
					if attempt < maxAttempts {
						log.Printf("RTP URL check failed on attempt %d for auto-joined recorder %d, trying again...", attempt, id)
						time.Sleep(500 * time.Millisecond)
					} else {
						log.Printf("RTP URL check failed on all %d attempts for auto-joined recorder %d", maxAttempts, id)
					}
				}

				log.Printf("Final RTP URL check result for auto-joined recorder %d: %s", id, map[bool]string{true: "working", false: "not working"}[isWorking])

				// Copy the stream information but use the new TS sync status
				joinedConverter.TsSync = isWorking // Use the new check result instead of copying
				joinedConverter.Services = streamInfo.Services
				joinedConverter.VideoInfo = streamInfo.VideoInfo
				joinedConverter.AudioInfo = streamInfo.AudioInfo

				// Set the start time
				joinedConverter.StartTime = time.Now()

				// Add to joined recorders
				joinedRecorders[id] = joinedConverter

				// Build and start the pipeline
				if err := joinedConverter.buildPipeline(); err == nil {
					joinedConverter.Pipeline.SetState(gst.StatePlaying)
					log.Printf("Recorder %d automatically joined after recording (TS sync: %v)", id, isWorking)
				} else {
					delete(joinedRecorders, id)
					log.Printf("Failed to auto-join recorder %d: %v", id, err)
				}
			}
		}

		recorderMutex.Unlock()

		// Notify the status manager that the recorder has stopped
		if statusManager := GetStatusManager(); statusManager != nil {
			if err != nil {
				// If there was an error, notify as failed
				statusManager.NotifyRecorderFailed(id, err.Error())
			} else {
				// Otherwise, use the completion status
				statusManager.NotifyRecorderStopped(id, completedSuccessfully)
			}
		}
	}()

	return nil
}

// StopRecording stops a recording with the given ID
func StopRecording(id int) error {
	recorderMutex.Lock()
	defer recorderMutex.Unlock()

	// Check if a recorder with this ID exists
	converter, exists := activeRecorders[id]
	if !exists {
		return fmt.Errorf("no active recorder with ID %d", id)
	}

	// Save the converter parameters before stopping it
	rtpURL := converter.RtpURL
	outputDir := converter.OutputDir

	// Save the stream information before stopping
	streamInfo := &StreamInfo{
		TsSync:    converter.TsSync,
		Services:  converter.Services,
		VideoInfo: converter.VideoInfo,
		AudioInfo: converter.AudioInfo,
		ServiceID: converter.ServiceID,
	}

	// Signal the converter to stop
	close(converter.StopChan)

	// Clean up iptables rules if they were used
	if converter.UsingIptables && converter.IptablesManager != nil {
		log.Printf("Cleaning up iptables rules for stopped recorder %d", id)
		if err := converter.IptablesManager.removeAllRules(); err != nil {
			log.Printf("Warning: Failed to clean up iptables rules for stopped recorder %d: %v", id, err)
			// Also try the backup cleanup method
			if backupErr := CheckAndCleanStaleChains(id); backupErr != nil {
				log.Printf("Backup cleanup also failed for stopped recorder %d: %v", id, backupErr)
			} else {
				log.Printf("Backup cleanup succeeded for stopped recorder %d", id)
			}
		} else {
			log.Printf("Successfully cleaned up iptables rules for stopped recorder %d", id)
		}
		converter.UsingIptables = false
		converter.IptablesManager = nil
	}

	// Immediately remove the recorder from the activeRecorders map
	// This prevents the "Cannot join a running recorder" error when trying to join it again
	delete(activeRecorders, id)

	// Log the removal
	log.Printf("Recorder %d removed from activeRecorders map", id)

	// After successful transcoding, check RTP URL and get streaming info without initializing a full pipeline
	if activeRecorder, ok := activeRecorders[id]; ok && activeRecorder.UsingTsOutput &&
		activeRecorder.FfmpegOutputFile != "" && activeRecorder.CompletedSuccessfully {
		log.Printf("Recording and transcoding completed successfully for recorder %d", id)
		log.Printf("Checking RTP URL %s after successful transcoding", rtpURL)

		// Check RTP URL without initializing a full pipeline
		// Make three attempts to check the RTP URL before setting TS sync to false
		isWorking := false
		maxAttempts := 3

		for attempt := 1; attempt <= maxAttempts; attempt++ {
			log.Printf("Checking RTP URL for recorder %d after transcoding (attempt %d of %d)", id, attempt, maxAttempts)
			attemptResult := CheckRtpUrlIsWorking(rtpURL)

			if attemptResult {
				// If any attempt succeeds, consider the URL working
				isWorking = true
				log.Printf("RTP URL check succeeded on attempt %d for recorder %d after transcoding", attempt, id)
				break
			}

			// If this isn't the last attempt, wait a short time before trying again
			if attempt < maxAttempts {
				log.Printf("RTP URL check failed on attempt %d for recorder %d after transcoding, trying again...", attempt, id)
				time.Sleep(500 * time.Millisecond)
			} else {
				log.Printf("RTP URL check failed on all %d attempts for recorder %d after transcoding", maxAttempts, id)
			}
		}

		log.Printf("Final RTP URL check result after transcoding for recorder %d: %s", id, map[bool]string{true: "working", false: "not working"}[isWorking])

		// Create a lightweight joined recorder entry with just the necessary information
		joinedConverter := &RtpToMp4Converter{
			ID:        id,
			RtpURL:    rtpURL,
			OutputDir: outputDir,
			StartTime: time.Now(),
			// Set stream information based on the RTP URL check
			TsSync:    isWorking,
			Services:  streamInfo.Services,
			VideoInfo: streamInfo.VideoInfo,
			AudioInfo: streamInfo.AudioInfo,
			ServiceID: streamInfo.ServiceID,
			// Initialize channels and mutex
			Done:            make(chan struct{}),
			StopChan:        make(chan struct{}),
			streamInfoMutex: sync.Mutex{},
			// Mark as recording completed to prevent RTP URL checking
			RecordingCompleted: true,
		}

		// Store the lightweight recorder in the joined recorders map
		joinedRecorders[id] = joinedConverter

		// Update the status manager's TS sync status map
		if statusManager := GetStatusManager(); statusManager != nil {
			statusManager.tsSyncMutex.Lock()
			statusManager.tsSyncStatus[id] = isWorking
			statusManager.tsSyncMutex.Unlock()
		}

		log.Printf("Created lightweight joined recorder for %d after successful transcoding (TS sync: %v)", id, isWorking)
	} else {
		// Automatically join the recorder after stopping to continue monitoring its status
		// Check if it's already in the joined recorders map
		if _, alreadyJoined := joinedRecorders[id]; !alreadyJoined {
			// Perform a new RTP URL check to get the current TS sync status
			log.Printf("Checking RTP URL %s for auto-joined recorder %d after stopping", rtpURL, id)

			// Make three attempts to check the RTP URL before setting TS sync to false
			isWorking := false
			maxAttempts := 3

			for attempt := 1; attempt <= maxAttempts; attempt++ {
				log.Printf("Checking RTP URL for auto-joined recorder %d after stopping (attempt %d of %d)", id, attempt, maxAttempts)
				attemptResult := CheckRtpUrlIsWorking(rtpURL)

				if attemptResult {
					// If any attempt succeeds, consider the URL working
					isWorking = true
					log.Printf("RTP URL check succeeded on attempt %d for auto-joined recorder %d after stopping", attempt, id)
					break
				}

				// If this isn't the last attempt, wait a short time before trying again
				if attempt < maxAttempts {
					log.Printf("RTP URL check failed on attempt %d for auto-joined recorder %d after stopping, trying again...", attempt, id)
					time.Sleep(500 * time.Millisecond)
				} else {
					log.Printf("RTP URL check failed on all %d attempts for auto-joined recorder %d after stopping", maxAttempts, id)
				}
			}

			log.Printf("Final RTP URL check result for auto-joined recorder %d after stopping: %s", id, map[bool]string{true: "working", false: "not working"}[isWorking])

			// Create a lightweight joined recorder entry with just the necessary information
			joinedConverter := &RtpToMp4Converter{
				ID:        id,
				RtpURL:    rtpURL,
				OutputDir: outputDir,
				StartTime: time.Now(),
				// Copy the stream information but use the new TS sync status
				TsSync:    isWorking, // Use the new check result instead of copying
				Services:  streamInfo.Services,
				VideoInfo: streamInfo.VideoInfo,
				AudioInfo: streamInfo.AudioInfo,
				ServiceID: streamInfo.ServiceID,
				// Initialize channels and mutex
				Done:            make(chan struct{}),
				StopChan:        make(chan struct{}),
				streamInfoMutex: sync.Mutex{},
			}

			// Store the lightweight recorder in the joined recorders map
			joinedRecorders[id] = joinedConverter

			// Update the status manager's TS sync status map
			if statusManager := GetStatusManager(); statusManager != nil {
				statusManager.tsSyncMutex.Lock()
				statusManager.tsSyncStatus[id] = isWorking // Use the new check result
				statusManager.tsSyncMutex.Unlock()
			}

			log.Printf("Recorder %d automatically joined after stopping", id)
		} else {
			log.Printf("Recorder %d is already joined, no need to auto-join after stopping", id)
		}
	}

	// The status update will still be handled by the goroutine when it completes
	return nil
}

// GetActiveRecorders returns a list of active recorder IDs
func GetActiveRecorders() []int {
	recorderMutex.Lock()
	defer recorderMutex.Unlock()

	ids := make([]int, 0, len(activeRecorders))
	for id := range activeRecorders {
		ids = append(ids, id)
	}

	return ids
}


// JoinRecorder joins a stream without recording it
// It checks the RTP stream once to get stream info and set TS sync status
func JoinRecorder(id int, rtpURL, outputDir string, vcodec, acodec, resolution, fps string, sampleRate int, vbitrate, abitrate, maxVbitrate int) error {
	recorderMutex.Lock()
	defer recorderMutex.Unlock()

	// Check if a recorder with this ID already exists in active recorders
	if _, exists := activeRecorders[id]; exists {
		return fmt.Errorf("recorder with ID %d is already running", id)
	}

	// Check if a recorder with this ID already exists in joined recorders
	if _, exists := joinedRecorders[id]; exists {
		// If it's already joined, just return success
		return nil
	}

	// Check for and clean up any stale iptables rules that might interfere
	if err := CheckAndCleanStaleChains(id); err != nil {
		log.Printf("Warning: Failed to clean stale iptables chains for recorder %d: %v", id, err)
		// Don't fail the join operation, just log the warning
	}

	// Parse RTP URL to get additional context for debugging
	ipInfo, parseErr := parseRtpURL(rtpURL)
	if parseErr != nil {
		log.Printf("Warning: Failed to parse RTP URL %s: %v", rtpURL, parseErr)
	} else {
		// Check for any active iptables rules that might interfere with this destination
		if err := CheckAndCleanActiveRulesForDestination(ipInfo.Address, ipInfo.Port); err != nil {
			log.Printf("Warning: Failed to clean active iptables rules for destination %s:%d: %v", ipInfo.Address, ipInfo.Port, err)
		}
		
		// Quick verification test - try a single RTP check to see if cleanup resolved the issue
		verificationResult := CheckRtpUrlIsWorking(rtpURL)
		if verificationResult {
			log.Printf("RTP connectivity verified after iptables cleanup for recorder %d", id)
		}
	}

	// Check RTP URL and set TS sync status based on the result
	log.Printf("Checking RTP URL %s for joined recorder %d", rtpURL, id)

	// Make three attempts to check the RTP URL before setting TS sync to false
	isWorking := false
	maxAttempts := 3

	for attempt := 1; attempt <= maxAttempts; attempt++ {
		log.Printf("Checking RTP URL for joined recorder %d (attempt %d of %d)", id, attempt, maxAttempts)
		
		attemptResult := CheckRtpUrlIsWorking(rtpURL)

		if attemptResult {
			// If any attempt succeeds, consider the URL working
			isWorking = true
			log.Printf("RTP URL check succeeded on attempt %d for joined recorder %d", attempt, id)
			break
		}

		// If this isn't the last attempt, wait a short time before trying again
		if attempt < maxAttempts {
			log.Printf("RTP URL check failed on attempt %d for joined recorder %d, trying again...", attempt, id)
			time.Sleep(500 * time.Millisecond)
		} else {
			log.Printf("RTP URL check failed on all %d attempts for joined recorder %d", maxAttempts, id)
		}
	}

	log.Printf("Final RTP URL check result for joined recorder %d: %s", id, map[bool]string{true: "working", false: "not working"}[isWorking])

	// Initialize service variables
	var services []models.ServiceInfo
	var videoInfo models.VideoInfo
	var audioInfo models.AudioInfo
	serviceDetectionSuccessful := false

	if isWorking {
		log.Printf("Detecting real services for joined recorder %d", id)

		// Set up detection options
		options := detector.DefaultDetectionOptions()
		options.Timeout = 8 // Reasonable timeout for join operation

		// Extract network interface from RTP URL if present
		if strings.Contains(rtpURL, "iface=") {
			parts := strings.Split(rtpURL, "iface=")
			if len(parts) > 1 {
				ifacePart := strings.Split(parts[1], "&")[0]
				options.Interface = ifacePart
			}
		}

		// Detect services
		streamInfo, err := detector.DetectServices(rtpURL, options)
		if err == nil && streamInfo.TsSync && len(streamInfo.Services) > 0 {
			log.Printf("Successfully detected %d services for joined recorder %d", len(streamInfo.Services), id)
			services = streamInfo.Services
			if len(streamInfo.VideoInfo) > 0 {
				videoInfo = streamInfo.VideoInfo[0]
			}
			if len(streamInfo.AudioInfo) > 0 {
				audioInfo = streamInfo.AudioInfo[0]
			}
			serviceDetectionSuccessful = true
		} else {
			log.Printf("Service detection failed for joined recorder %d even though RTP URL check passed: %v", id, err)
			// This indicates potential network interface issue - RTP packets received but no valid services detected
			isWorking = false // Mark as not working if service detection fails
		}
	}

	// Set appropriate service info based on detection results
	if serviceDetectionSuccessful {
		// Services were successfully detected, keep the detected services
		log.Printf("Join successful for recorder %d with %d detected services", id, len(services))
	} else {
		// Either RTP URL is not working OR service detection failed
		// Determine the appropriate service name based on the failure type
		serviceName := "No Signal"
		if isWorking {
			// RTP check passed but service detection failed - likely interface/decode issue
			serviceName = "Unknown Service"
			log.Printf("RTP URL accessible but service detection failed - setting service name to 'Unknown Service'")
		} else {
			// RTP check failed - likely network/connectivity issue
			serviceName = "No Signal"
			log.Printf("RTP URL not accessible - setting service name to 'No Signal'")
		}

		services = []models.ServiceInfo{
			{
				ServiceID:       1,
				ServiceName:     serviceName,
				VideoCodec:      vcodec,
				AudioCodec:      acodec,
				VideoResolution: resolution,
				AudioChannels:   2,
				AudioTracks: []models.AudioInfo{
					{
						Channels: 2,
						Codec:    acodec,
					},
				},
			},
		}
		videoInfo = models.VideoInfo{
			Resolution: resolution,
			Codec:      vcodec,
		}
		audioInfo = models.AudioInfo{
				Channels: 2,
				Codec:    acodec,
			}
		
		// For debugging: Extract interface from URL to log
		if strings.Contains(rtpURL, "iface=") {
			parts := strings.Split(rtpURL, "iface=")
			if len(parts) > 1 {
				ifacePart := strings.Split(parts[1], "&")[0]
				log.Printf("Join failed for recorder %d using interface '%s' - check network interface configuration", id, ifacePart)
			}
		}
	}

	// Create a lightweight joined recorder entry with just the necessary information
	joinedConverter := &RtpToMp4Converter{
		ID:        id,
		RtpURL:    rtpURL,
		OutputDir: outputDir,
		StartTime: time.Now(),
		// Set stream information - use serviceDetectionSuccessful for TsSync to be more accurate
		TsSync:    serviceDetectionSuccessful, // Only true if both RTP URL works AND services detected
		Services:  services,                   // Use detected or fallback services
		VideoInfo: videoInfo,                  // Use detected or fallback video info
		AudioInfo: audioInfo,                  // Use detected or fallback audio info
		// Initialize channels and mutex
		Done:            make(chan struct{}),
		StopChan:        make(chan struct{}),
		streamInfoMutex: sync.Mutex{},
	}

	// Store the lightweight recorder in the joined recorders map
	joinedRecorders[id] = joinedConverter

	// Update the status manager's TS sync status map - use serviceDetectionSuccessful for accuracy
	if statusManager := GetStatusManager(); statusManager != nil {
		statusManager.tsSyncMutex.Lock()
		statusManager.tsSyncStatus[id] = serviceDetectionSuccessful // Use accurate detection result
		statusManager.tsSyncMutex.Unlock()
	}

	// Log final status
	if serviceDetectionSuccessful {
		log.Printf("Successfully joined recorder %d with working RTP stream and detected services", id)
	} else {
		log.Printf("Joined recorder %d but with issues - check RTP URL and network interface", id)
	}

	return nil
}

// UnjoinRecorder unjoins a stream
func UnjoinRecorder(id int) error {
	recorderMutex.Lock()
	defer recorderMutex.Unlock()

	// Check if a recorder with this ID exists in joined recorders
	_, exists := joinedRecorders[id]
	if !exists {
		return fmt.Errorf("no joined recorder with ID %d", id)
	}

	// Remove the recorder from the joined recorders map
	delete(joinedRecorders, id)

	// Remove from status manager's TS sync status map
	if statusManager := GetStatusManager(); statusManager != nil {
		statusManager.tsSyncMutex.Lock()
		delete(statusManager.tsSyncStatus, id)
		statusManager.tsSyncMutex.Unlock()
	}

	log.Printf("Recorder %d unjoined successfully", id)
	return nil
}

// GetRecorderStatus returns the status of all active and joined recorders
func GetRecorderStatus() []models.RecorderStatus {
	recorderMutex.Lock()
	defer recorderMutex.Unlock()

	// Calculate total number of recorders (active + joined)
	totalRecorders := len(activeRecorders) + len(joinedRecorders)
	statuses := make([]models.RecorderStatus, 0, totalRecorders)

	// Add active recorders
	for id, recorder := range activeRecorders {
		// Calculate elapsed time
		elapsedSeconds := 0
		if !recorder.StartTime.IsZero() {
			elapsedSeconds = int(time.Since(recorder.StartTime).Seconds())
		}

		// Lock the stream info mutex to safely access the stream information
		recorder.streamInfoMutex.Lock()

		statuses = append(statuses, models.RecorderStatus{
			ID:              id,
			RtpURL:          recorder.RtpURL,
			OutputDir:       recorder.OutputDir,
			ElapsedSeconds:  elapsedSeconds,
			DurationSeconds: recorder.Duration,
			IsActive:        true,
			IsJoined:        true, // Active recorders are also considered joined
			StartTime:       recorder.StartTime.Format(time.RFC3339),
			TsSync:          recorder.TsSync,
			Services:        recorder.Services,
			VideoInfo:       recorder.VideoInfo,
			AudioInfo:       recorder.AudioInfo,
		})

		recorder.streamInfoMutex.Unlock()
	}

	// Add joined recorders
	for id, recorder := range joinedRecorders {
		// Lock the stream info mutex to safely access the stream information
		recorder.streamInfoMutex.Lock()

		statuses = append(statuses, models.RecorderStatus{
			ID:              id,
			RtpURL:          recorder.RtpURL,
			OutputDir:       recorder.OutputDir,
			ElapsedSeconds:  0,     // No elapsed time for joined recorders
			DurationSeconds: 0,     // No duration for joined recorders
			IsActive:        false, // Not actively recording
			IsJoined:        true,  // This is a joined recorder
			StartTime:       recorder.StartTime.Format(time.RFC3339),
			TsSync:          recorder.TsSync,
			Services:        recorder.Services,
			VideoInfo:       recorder.VideoInfo,
			AudioInfo:       recorder.AudioInfo,
		})

		recorder.streamInfoMutex.Unlock()
	}

	return statuses
}

// Initialize GStreamer when the package is loaded
func init() {
	// Initialize GStreamer
	gst.Init(nil)
}
