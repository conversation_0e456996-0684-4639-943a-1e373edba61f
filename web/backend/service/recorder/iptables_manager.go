package recorder

import (
	"fmt"
	"log"
	"net"
	"os/exec"
	"strconv"
	"strings"
	"sync"
)

// IptablesRule represents a single iptables rule
type IptablesRule struct {
	Chain   string
	Rule    []string
	Comment string
	Applied bool
}

// IptablesManager manages iptables rules for source IP filtering
type IptablesManager struct {
	rules     []IptablesRule
	mutex     sync.Mutex
	chainName string
}

// NewIptablesManager creates a new iptables manager for a recorder
func NewIptablesManager(recorderID int) *IptablesManager {
	chainName := fmt.Sprintf("TRAFFIQ_REC_%d", recorderID)
	
	return &IptablesManager{
		rules:     make([]IptablesRule, 0),
		chainName: chainName,
	}
}

// isIptablesAvailable checks if iptables is available on the system
func (im *IptablesManager) isIptablesAvailable() bool {
	cmd := exec.Command("iptables", "--version")
	err := cmd.Run()
	if err != nil {
		log.Printf("iptables not available: %v", err)
		return false
	}

	// Check if we have permissions to run iptables
	cmd = exec.Command("iptables", "-L", "-n")
	err = cmd.Run()
	if err != nil {
		log.Printf("iptables permission check failed: %v", err)
		log.Printf("Make sure the application is run as root or has CAP_NET_ADMIN capability")
		return false
	}

	log.Printf("iptables is available and accessible")
	return true
}

// createCustomChain creates a custom chain for this recorder
func (im *IptablesManager) createCustomChain() error {
	// Use a more specific check with timeout
	checkCmd := exec.Command("timeout", "5", "iptables", "-L", im.chainName, "-n", "--line-numbers")
	_, err := checkCmd.CombinedOutput()
	
	if err == nil {
		log.Printf("Chain %s already exists", im.chainName)
		return nil
	}
	
	rule := IptablesRule{
		Chain:   "NEW_CHAIN",
		Rule:    []string{"-N", im.chainName},
		Comment: fmt.Sprintf("Custom chain for recorder %s", im.chainName),
		Applied: false,
	}

	// Use timeout for chain creation as well
	createCmd := exec.Command("timeout", "10", "iptables", "-N", im.chainName)
	createOutput, createErr := createCmd.CombinedOutput()
	
	if createErr != nil {
		// Check if the error is because chain already exists
		if strings.Contains(string(createOutput), "Chain already exists") || 
		   strings.Contains(createErr.Error(), "Chain already exists") ||
		   strings.Contains(string(createOutput), "already exists") {
			log.Printf("Chain %s already exists (created by another process)", im.chainName)
			return nil
		}
		
		return fmt.Errorf("failed to create chain %s: %v, output: %s", im.chainName, createErr, string(createOutput))
	}

	// Verify the chain was actually created
	verifyCmd := exec.Command("timeout", "5", "iptables", "-L", im.chainName, "-n")
	_, verifyErr := verifyCmd.CombinedOutput()
	
	if verifyErr != nil {
		log.Printf("Warning: Could not verify chain creation: %v", verifyErr)
		// Don't fail here, the chain might still be usable
	}

	rule.Applied = true
	im.rules = append(im.rules, rule)
	log.Printf("Created iptables chain: %s", im.chainName)

	return nil
}

// addSourceIPFilter adds an iptables rule to filter by source IP
func (im *IptablesManager) addSourceIPFilter(sourceIP string, destIP string, port int) error {
	im.mutex.Lock()
	defer im.mutex.Unlock()

	if !im.isIptablesAvailable() {
		return fmt.Errorf("iptables is not available")
	}

	// Validate source IP
	if net.ParseIP(sourceIP) == nil {
		return fmt.Errorf("invalid source IP: %s", sourceIP)
	}

	// Validate destination IP
	if net.ParseIP(destIP) == nil {
		return fmt.Errorf("invalid destination IP: %s", destIP)
	}

	// Create custom chain if it doesn't exist
	if err := im.createCustomChain(); err != nil {
		return fmt.Errorf("failed to create custom chain: %v", err)
	}

	// Rule 1: Jump to our custom chain for packets to the destination
	jumpRule := IptablesRule{
		Chain: "INPUT",
		Rule: []string{
			"-I", "INPUT",
			"-d", destIP,
			"-p", "udp",
			"--dport", strconv.Itoa(port),
			"-j", im.chainName,
			"-m", "comment",
			"--comment", fmt.Sprintf("Jump to %s for port %d", im.chainName, port),
		},
		Comment: fmt.Sprintf("Jump rule for %s:%d", destIP, port),
		Applied: false,
	}

	// Rule 2: In our custom chain, ACCEPT packets EXACTLY from the specified source IP
	acceptRule := IptablesRule{
		Chain: im.chainName,
		Rule: []string{
			"-A", im.chainName,
			"-s", sourceIP,        // EXACT source IP match
			"-j", "ACCEPT",        // ACCEPT these packets
			"-m", "comment",
			"--comment", fmt.Sprintf("Accept ONLY from source %s", sourceIP),
		},
		Comment: fmt.Sprintf("Accept rule for EXACT source %s", sourceIP),
		Applied: false,
	}

	// Rule 3: In our custom chain, DROP all other packets (from any other source IP)
	dropRule := IptablesRule{
		Chain: im.chainName,
		Rule: []string{
			"-A", im.chainName,
			"-j", "DROP",
			"-m", "comment",
			"--comment", fmt.Sprintf("Drop all others in %s", im.chainName),
		},
		Comment: "Drop rule for non-matching packets",
		Applied: false,
	}

	// Apply rules in order
	rules := []IptablesRule{jumpRule, acceptRule, dropRule}
	var appliedRules []IptablesRule

	for i, rule := range rules {
		log.Printf("Applying iptables rule: %v", rule.Rule)
		cmd := exec.Command("iptables", rule.Rule...)
		
		if err := cmd.Run(); err != nil {
			log.Printf("Failed to apply rule %d: %v", i+1, err)
			log.Printf("Rule was: %v", rule.Rule)

			// Rollback applied rules
			im.rollbackRules(len(appliedRules) - 1)
			return fmt.Errorf("failed to apply iptables rule %d: %v", i+1, err)
		}

		rule.Applied = true
		appliedRules = append(appliedRules, rule)
		log.Printf("Successfully applied iptables rule %d: %s", i+1, rule.Comment)
	}

	// Add all successfully applied rules to our list
	im.rules = append(im.rules, appliedRules...)

	log.Printf("Successfully added source IP filter: %s -> %s:%d", sourceIP, destIP, port)
	return nil
}

// rollbackRules removes rules up to a certain index (used for cleanup on error)
func (im *IptablesManager) rollbackRules(upToIndex int) {
	log.Printf("Rolling back iptables rules up to index %d", upToIndex)

	if upToIndex < 0 {
		return
	}

	// Remove rules in reverse order
	for i := upToIndex; i >= 0; i-- {
		if i >= len(im.rules) {
			continue
		}
		
		if !im.rules[i].Applied {
			continue
		}
		
		rule := im.rules[i]

		// Convert rule from -A/-I to -D
		var deleteRule []string
		for _, arg := range rule.Rule {
			if arg == "-A" || arg == "-I" {
				deleteRule = append(deleteRule, "-D")
			} else if arg == "-A" || arg == "-I" {
				// Skip the rule number for -I
				continue
			} else {
				deleteRule = append(deleteRule, arg)
			}
		}

		log.Printf("Rolling back rule: %v", deleteRule)
		
		cmd := exec.Command("iptables", deleteRule...)
		if err := cmd.Run(); err != nil {
			log.Printf("Warning: Failed to rollback rule %d: %v", i, err)
		} else {
			log.Printf("Successfully rolled back rule %d", i)
		}
	}

	// Clear the applied rules from our list
	if upToIndex >= 0 && upToIndex < len(im.rules) {
		im.rules = im.rules[:upToIndex+1]
	}
}

// removeAllRules removes all iptables rules created by this manager
func (im *IptablesManager) removeAllRules() error {
	im.mutex.Lock()
	defer im.mutex.Unlock()

	log.Printf("Removing all iptables rules for manager %s", im.chainName)

	totalRules := len(im.rules)

	if totalRules == 0 {
		return nil
	}

	// Remove rules in reverse order (last applied first)
	for i := len(im.rules) - 1; i >= 0; i-- {
		rule := im.rules[i]
		
		if !rule.Applied {
			continue
		}

		var deleteRule []string

		if rule.Chain == "NEW_CHAIN" {
			// For chain creation, we need to delete the chain
			deleteRule = []string{"-X", im.chainName}
		} else {
			// For regular rules, convert from -A/-I to -D
			deleteRule = make([]string, 0, len(rule.Rule))
			for j, arg := range rule.Rule {
				if arg == "-A" {
					deleteRule = append(deleteRule, "-D")
				} else if arg == "-I" {
					deleteRule = append(deleteRule, "-D")
					// Skip the position number for -I rules
					if j+1 < len(rule.Rule) {
						// Check if next argument is a number (position)
						if _, err := strconv.Atoi(rule.Rule[j+1]); err == nil {
							continue
						}
					}
				} else {
					deleteRule = append(deleteRule, arg)
				}
			}
		}

		log.Printf("Removing iptables rule: %v", deleteRule)
		cmd := exec.Command("iptables", deleteRule...)
		if err := cmd.Run(); err != nil {
			log.Printf("Warning: Failed to remove rule %d (%s): %v", i, rule.Comment, err)
		} else {
			log.Printf("Successfully removed rule %d: %s", i, rule.Comment)
		}
	}

	// Clear the rules list
	im.rules = make([]IptablesRule, 0)

	log.Printf("Completed removal of all iptables rules for manager %s", im.chainName)
	return nil
}

// CheckAndCleanStaleChains checks for and cleans up any stale iptables chains that might interfere with new operations
func CheckAndCleanStaleChains(recorderID int) error {
	chainName := fmt.Sprintf("TRAFFIQ_REC_%d", recorderID)
	
	// Check if iptables is available
	cmd := exec.Command("iptables", "--version")
	if err := cmd.Run(); err != nil {
		return nil
	}
	
	// Check if the chain exists
	checkCmd := exec.Command("timeout", "5", "iptables", "-L", chainName, "-n")
	_, err := checkCmd.CombinedOutput()
	
	if err != nil {
		// Chain doesn't exist, which is good
		return nil
	}
	
	log.Printf("Found existing chain %s, cleaning up stale rules", chainName)
	
	// Check for jump rules in INPUT chain that point to our custom chain
	inputCmd := exec.Command("iptables", "-L", "INPUT", "-n", "--line-numbers")
	inputOutput, inputErr := inputCmd.CombinedOutput()
	
	if inputErr == nil {
		inputStr := string(inputOutput)
		if strings.Contains(inputStr, chainName) {
			log.Printf("Found jump rules in INPUT chain pointing to %s", chainName)
		}
	}
	
	// Clean up the stale chain and its rules
	log.Printf("Cleaning up stale chain %s and its rules", chainName)
	
	// Create a temporary manager to clean up
	tempManager := &IptablesManager{
		chainName: chainName,
		rules:     make([]IptablesRule, 0),
	}
	
	// Try to remove all rules associated with this chain
	if err := tempManager.removeAllRules(); err != nil {
		log.Printf("Warning: Failed to clean up stale chain %s: %v", chainName, err)
		return fmt.Errorf("failed to clean up stale iptables chain %s: %v", chainName, err)
	}
	
	log.Printf("Successfully cleaned up stale chain %s", chainName)
	return nil
}

// CheckAndCleanActiveRulesForDestination checks for any active iptables rules that might interfere with traffic to a specific destination
func CheckAndCleanActiveRulesForDestination(destIP string, destPort int) error {
	// Check if iptables is available
	cmd := exec.Command("iptables", "--version")
	if err := cmd.Run(); err != nil {
		return nil
	}
	
	// Check INPUT chain for any rules affecting this destination
	inputCmd := exec.Command("iptables", "-L", "INPUT", "-n", "--line-numbers")
	inputOutput, inputErr := inputCmd.CombinedOutput()
	
	if inputErr != nil {
		return nil
	}
	
	inputStr := string(inputOutput)
	
	// Look for any rules that mention our destination IP and port
	if strings.Contains(inputStr, destIP) && strings.Contains(inputStr, fmt.Sprintf("%d", destPort)) {
		log.Printf("Found active rules affecting destination %s:%d", destIP, destPort)
		
		// Look for TRAFFIQ_REC chains in the rules
		lines := strings.Split(inputStr, "\n")
		var traffiqChains []string
		for _, line := range lines {
			if strings.Contains(line, destIP) && strings.Contains(line, fmt.Sprintf("%d", destPort)) && strings.Contains(line, "TRAFFIQ_REC_") {
				// Extract chain name from the line
				parts := strings.Fields(line)
				for _, part := range parts {
					if strings.HasPrefix(part, "TRAFFIQ_REC_") {
						traffiqChains = append(traffiqChains, part)
						log.Printf("Found active TRAFFIQ chain affecting destination: %s", part)
					}
				}
			}
		}
		
		// Clean up any found chains
		for _, chainName := range traffiqChains {
			log.Printf("Cleaning up active chain %s that affects destination %s:%d", chainName, destIP, destPort)
			
			// Create a temporary manager to clean up
			tempManager := &IptablesManager{
				chainName: chainName,
				rules:     make([]IptablesRule, 0),
			}
			
			if err := tempManager.removeAllRules(); err != nil {
				log.Printf("Warning: Failed to clean up active chain %s: %v", chainName, err)
			} else {
				log.Printf("Successfully cleaned up active chain %s", chainName)
			}
		}
		
		if len(traffiqChains) > 0 {
			log.Printf("Cleaned up %d active chains affecting destination %s:%d", len(traffiqChains), destIP, destPort)
			return nil
		}
	}
	
	return nil
}
