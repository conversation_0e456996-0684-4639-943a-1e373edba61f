package recorder

import (
	"os"
	"path/filepath"
	"showfer-web/models"
	"showfer-web/service/converter"
	"showfer-web/service/interfaces"
	"showfer-web/service/logger"

	//"showfer-web/service/queue"
	"strings"
	"time"
)

// FilesRepository interface defines the methods needed for file manager integration
type FilesRepository interface {
	CreateConvertItem(item models.ConvertItem) (int64, error)
	FindConvertItemByFilenameAndLocation(filename string, location string) (models.ConvertItem, error)
}

// RecorderRepository interface for getting recorder information
type RecorderRepository interface {
	GetRecorderByID(id int) (models.Recorder, error)
}

// RecorderStreamInfo holds information about detected stream characteristics for file manager integration
type RecorderStreamInfo struct {
	TsSync    bool
	Services  []models.ServiceInfo
	VideoInfo models.VideoInfo
	AudioInfo models.AudioInfo
	ServiceID int
}

// AddRecordingToFileManager adds a recorded file to the convert_item table
// so it can be viewed in the File Manager
func AddRecordingToFileManager(filePath string, filesRepo FilesRepository, recorderId int, queueManager interfaces.QueueManagerInterface, recorderRepo RecorderRepository) (int64, error) {
	// Check if file exists
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		logger.Error("Failed to get file info for recording: %v", err)
		return 0, err
	}

	// Get file duration
	duration, err := converter.GetDuration(filePath)
	if err != nil {
		logger.Error("Failed to get duration for recording: %v", err)
		// Continue with duration 0
		duration = 0
	}

	// Get filename
	filename := filepath.Base(filePath)

	// For recordings, we want to store them in the recordings directory
	location := "/recordings/"

	// Check if file already exists in database
	existingItem, err := filesRepo.FindConvertItemByFilenameAndLocation(filename, location)
	if err == nil && existingItem.ID != 0 {
		// File already exists in database, skip
		logger.Log("Recording already exists in file manager: %s", filePath)
		return existingItem.ID, nil
	}

	// Get recorder information to determine codec settings
	var videoCodec, audioCodec *string
	recorder, err := recorderRepo.GetRecorderByID(recorderId)
	if err != nil {
		logger.Error("Failed to get recorder info for codec settings: %v", err)
		// Continue without codec settings - codec fields will be nil
	} else {
		// Try to get more accurate codec information from stream detection first
		streamInfo := getLastStreamInfo(recorderId)
		
		if streamInfo != nil && streamInfo.ServiceID > 0 {
			// If we have service-specific information (from SelectServiceModal),
			// use the detected stream codec information
			logger.Log("Using detected stream codec information for recorder %d, service %d", recorderId, streamInfo.ServiceID)
			
			// Find the specific service that was recorded
			var selectedService *models.ServiceInfo
			for i := range streamInfo.Services {
				if streamInfo.Services[i].ServiceID == streamInfo.ServiceID {
					selectedService = &streamInfo.Services[i]
					break
				}
			}
			
			if selectedService != nil {
				// Use detected video codec
				if selectedService.VideoCodec != "" {
					vCodec := selectedService.VideoCodec
					videoCodec = &vCodec
					logger.Log("Set video codec from stream detection: %s", vCodec)
				}
				
				// Use detected audio codec based on dual audio preference
				if len(selectedService.AudioTracks) > 0 {
					// If we have multiple audio tracks, prefer the first one (usually surround)
					aCodec := selectedService.AudioTracks[0].Codec
					audioCodec = &aCodec
					logger.Log("Set audio codec from stream detection (track 1): %s", aCodec)
				} else if selectedService.AudioCodec != "" {
					// Fallback to legacy single audio codec
					aCodec := selectedService.AudioCodec
					audioCodec = &aCodec
					logger.Log("Set audio codec from stream detection (legacy): %s", aCodec)
				}
			}
		}
		
		// If we don't have stream detection info, fall back to recorder settings
		if videoCodec == nil || audioCodec == nil {
			logger.Log("Using recorder codec settings as fallback for recorder %d", recorderId)
			
			if videoCodec == nil {
				vCodec := normalizeVideoCodecName(recorder.VCodec)
				videoCodec = &vCodec
			}
			
			if audioCodec == nil {
				aCodec := normalizeAudioCodecName(recorder.ACodec)
				audioCodec = &aCodec
			}
		}
	}

	// Create convert item
	now := time.Now().UTC().Format(time.RFC3339)

	// Create a path in the format "recordings/filename.mp4"
	// This ensures a consistent path format regardless of the original path
	cLocation := "recordings/" + filename

	logger.Log("DEBUG: Original file path: %s", filePath)
	logger.Log("DEBUG: Setting CLocation to: %s", cLocation)

	convertItem := models.ConvertItem{
		Filename:    filename,
		Name:        strings.TrimSuffix(filename, filepath.Ext(filename)),
		Location:    location,
		Duration:    duration,
		Status:      1, // Queue
		Size:        fileInfo.Size(),
		StorageType: 0, // Local
		CreatedAt:   now,
		UpdatedAt:   now,
		CLocation:   cLocation, // Store the path without the "data/" prefix
		RecorderID:  &recorderId,
		VideoCodec:  videoCodec,
		AudioCodec:  audioCodec,
	}

	logger.Log("Adding recording to file manager with path: %s", cLocation)

	// Save to database
	id, err := filesRepo.CreateConvertItem(convertItem)
	if err != nil {
		logger.Error("Failed to add recording to file manager: %v", err)
		return 0, err
	}

	logger.Log("File processed successfully: %s (ID: %d) with codecs - Video: %v, Audio: %v", 
		filename, id, logSafeString(videoCodec), logSafeString(audioCodec))

	// Add the file to the conversion queue
	queueManager.AddToQueue(id)

	logger.Log("Added recording to file manager: %s (ID: %d)", filePath, id)
	return id, nil
}

// getLastStreamInfo gets the last stream information for a recorder
// This function would need to be implemented to access the stream info
// stored during recording (from the RtpToMp4Converter)
func getLastStreamInfo(recorderId int) *RecorderStreamInfo {
	// For now, we can't easily access the stream info after recording completion
	// This would require storing the stream info in a way that persists beyond the recorder lifecycle
	// Future enhancement: Store stream detection results in database or cache
	
	// Check if there's an active or joined recorder with this ID that has stream info
	recorderMutex.Lock()
	defer recorderMutex.Unlock()
	
	// Try to get from active recorders first
	if activeRecorder, exists := activeRecorders[recorderId]; exists {
		activeRecorder.streamInfoMutex.Lock()
		streamInfo := &RecorderStreamInfo{
			TsSync:    activeRecorder.TsSync,
			Services:  activeRecorder.Services,
			VideoInfo: activeRecorder.VideoInfo,
			AudioInfo: activeRecorder.AudioInfo,
			ServiceID: activeRecorder.ServiceID,
		}
		activeRecorder.streamInfoMutex.Unlock()
		return streamInfo
	}
	
	// Try to get from joined recorders
	if joinedRecorder, exists := joinedRecorders[recorderId]; exists {
		joinedRecorder.streamInfoMutex.Lock()
		streamInfo := &RecorderStreamInfo{
			TsSync:    joinedRecorder.TsSync,
			Services:  joinedRecorder.Services,
			VideoInfo: joinedRecorder.VideoInfo,
			AudioInfo: joinedRecorder.AudioInfo,
			ServiceID: joinedRecorder.ServiceID,
		}
		joinedRecorder.streamInfoMutex.Unlock()
		return streamInfo
	}
	
	return nil
}

// logSafeString safely logs a pointer to string, handling nil values
func logSafeString(s *string) string {
	if s == nil {
		return "nil"
	}
	return *s
}

// normalizeVideoCodecName converts codec names to user-friendly format
func normalizeVideoCodecName(codecName string) string {
	switch codecName {
	case "h264", "x264enc":
		return "H.264"
	case "h265", "hevc":
		return "H.265"
	case "mpeg2", "mpeg2video":
		return "MPEG-2"
	default:
		return "H.264" // Default fallback
	}
}

// normalizeAudioCodecName converts audio codec names to user-friendly format
func normalizeAudioCodecName(codecName string) string {
	switch codecName {
	case "aac", "aac_downmix":
		return "AAC"
	case "ac3", "ac3_downmix":
		return "AC-3"
	case "ac3_passthrough":
		return "AC-3"
	case "mpeg1l2_downmix", "mp2":
		return "MP2"
	default:
		return "AAC" // Default fallback
	}
}
