package transcoder

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"showfer-web/models"
	"showfer-web/repository"
	"showfer-web/service/logger"
	"showfer-web/service/recorder"
	"showfer-web/service/retranscoder"
	"strings"
)

// Transcoder handles video transcoding operations
type Transcoder struct {
	filesRepo         *repository.FilesRepository
	codecSettingsRepo *repository.CodecSettingsRepository
	statusManager     *recorder.RecorderStatusManager
	baseDir           string
	isGPUAvailable    bool // Flag indicating if NVIDIA GPU is available for hardware acceleration
	db                *sql.DB // Database connection for querying service_id
}

// AudioTrackInfo represents audio track information from FFprobe
type AudioTrackInfo struct {
	Index    int    `json:"index"`
	CodecName string `json:"codec_name"`
	Channels int    `json:"channels"`
	Bitrate  string `json:"bit_rate"`
}

// FFprobeOutput represents the structure of FFprobe JSON output
type FFprobeOutput struct {
	Streams []struct {
		Index     int    `json:"index"`
		CodecType string `json:"codec_type"`
		CodecName string `json:"codec_name"`
		Channels  int    `json:"channels"`
		BitRate   string `json:"bit_rate"`
	} `json:"streams"`
}

// ServiceStreamInfo represents stream information for a specific service
type ServiceStreamInfo struct {
	VideoStreamIndex int
	AudioStreamIndices []int
}

// isNvidiaGPUAvailable checks if an NVIDIA GPU is available on the system
// and if FFmpeg supports NVENC encoding
func isNvidiaGPUAvailable() bool {
	// First check if nvidia-smi command exists and runs successfully
	cmd := exec.Command("nvidia-smi")
	err := cmd.Run()
	if err != nil {
		logger.Log("NVIDIA GPU not detected: %v", err)
		return false
	}

	// GPU is detected, now check if FFmpeg supports NVENC
	testCmd := exec.Command("ffmpeg", "-hide_banner", "-encoders")
	output, err := testCmd.Output()
	if err != nil {
		logger.Error("Failed to check FFmpeg encoders: %v", err)
		return false
	}

	// Check if h264_nvenc encoder is available
	if strings.Contains(string(output), "h264_nvenc") {
		logger.Log("NVIDIA GPU detected and FFmpeg has NVENC support")
		return true
	}

	logger.Log("NVIDIA GPU detected but FFmpeg does not have NVENC support")
	return false
}

// NewTranscoder creates a new Transcoder
func NewTranscoder(filesRepo *repository.FilesRepository, codecSettingsRepo *repository.CodecSettingsRepository, statusManager *recorder.RecorderStatusManager, baseDir string, db *sql.DB) *Transcoder {
	// Check if GPU is available for hardware acceleration
	gpuAvailable := isNvidiaGPUAvailable()

	if gpuAvailable {
		logger.Log("Transcoder will use NVIDIA GPU hardware acceleration")
	} else {
		logger.Log("Transcoder will use CPU-based encoding")
	}

	return &Transcoder{
		filesRepo:         filesRepo,
		codecSettingsRepo: codecSettingsRepo,
		statusManager:     statusManager,
		baseDir:           baseDir,
		isGPUAvailable:    gpuAvailable,
		db:                db,
	}
}

// getRecorderServiceID retrieves the service_id for a file if it was recorded from an RTP stream
func (t *Transcoder) getRecorderServiceID(fileID int64) (*int, error) {
	// Query to get service_id from recorders table if the file has a recorder_id
	var serviceID sql.NullInt64
	query := `
		SELECT r.service_id 
		FROM convert_items ci
		LEFT JOIN recorders r ON ci.recorder_id = r.id
		WHERE ci.id = $1 AND ci.recorder_id IS NOT NULL
	`
	
	err := t.db.QueryRow(query, fileID).Scan(&serviceID)
	if err != nil {
		if err == sql.ErrNoRows {
			// File was not recorded from RTP stream (manually uploaded)
			return nil, nil
		}
		return nil, fmt.Errorf("failed to query service_id: %v", err)
	}
	
	if serviceID.Valid && serviceID.Int64 > 0 {
		id := int(serviceID.Int64)
		return &id, nil
	}
	
	// File was recorded but no specific service was selected
	return nil, nil
}

// TranscodeFile transcodes a file using the codec settings from the database
func (t *Transcoder) TranscodeFile(fileID int64) error {

	logger.Log("-------------- Debug: TranscodeFile called for file %d -----------------", fileID)
	
	statusManager := recorder.GetStatusManager()

	// Get the file from the database
	file, err := t.filesRepo.GetConvertItemById(fileID)
	if err != nil {
		logger.Error("Failed to get file from database: %v", err)
		_ = sendRecorderNotify(file, statusManager, "failed")
		return err
	}

	// Get codec settings
	codecSettings, err := t.codecSettingsRepo.GetCodecSettings()
	if err != nil {
		logger.Error("Failed to get codec settings: %v", err)
		_ = sendRecorderNotify(file, statusManager, "failed")
		return err
	}

	// Check if this file was recorded from an RTP stream and get service ID
	serviceID, err := t.getRecorderServiceID(fileID)
	if err != nil {
		logger.Error("Failed to get service ID for file: %v", err)
		// Continue without service filtering
	}

	// Notify retranscoder service that processing is starting
	retranscoderService := retranscoder.GetRetranscoderService()
	logger.Log("-------------- Debug: About to call OnTranscodingStarted for file %d -----------------", fileID)
	retranscoderService.OnTranscodingStarted(fileID)

	// Update file status to transcoding
	err = t.filesRepo.ChangeStatus(fileID, int(models.FileStatusProcessing))
	if err != nil {
		logger.Error("Failed to update file status: %v", err)
		_ = sendRecorderNotify(file, statusManager, "failed")
		
		// Notify retranscoder service of failure
		retranscoderService.OnTranscodingCompleted(fileID, false)
		
		return err
	}

	// Get input file path
	inputFilePath := filepath.Join(t.baseDir, file.Location, file.Filename)

	// Detect service streams for TS files after we have the input file path
	var serviceStreams *ServiceStreamInfo
	if serviceID != nil && *serviceID > 0 {
		// Check file extension to determine if it's a TS file
		fileExt := strings.ToLower(filepath.Ext(file.Filename))
		if fileExt == ".ts" || fileExt == ".mts" {
			// Detect which streams belong to this service
			serviceStreams, err = t.detectServiceStreams(inputFilePath, *serviceID)
			if err != nil {
				logger.Error("Failed to detect service streams: %v", err)
				logger.Log("Will proceed without service filtering")
			} else {
				logger.Log("File %d: Service %d streams - Video: %d, Audio: %v", 
					fileID, *serviceID, serviceStreams.VideoStreamIndex, serviceStreams.AudioStreamIndices)
			}
		}
	}

	// Detect audio tracks and determine if we should use dual audio mode
	useDualAudio, err := t.shouldUseDualAudio(inputFilePath, codecSettings)
	if err != nil {
		logger.Error("Audio detection failed, proceeding with default settings: %v", err)
		useDualAudio = codecSettings.DualAudioMode
	}

	// Create output file path (same location but with _transcoded suffix)
	fileExt := filepath.Ext(file.Filename)
	fileNameWithoutExt := strings.TrimSuffix(file.Filename, fileExt)
	outputFileName := fmt.Sprintf("%s_transcoded%s", fileNameWithoutExt, fileExt)
	outputFilePath := filepath.Join(t.baseDir, file.Location, outputFileName)

	// Ensure the output directory exists
	outputDir := filepath.Dir(outputFilePath)
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		logger.Error("Failed to create output directory: %v", err)
		t.filesRepo.ChangeStatus(fileID, int(models.FileStatusFailed))
		_ = sendRecorderNotify(file, statusManager, "failed")
		return err
	}

	// Parse resolution to handle p/i suffix
	resolution, isInterlaced := parseResolution(codecSettings.Resolution)
	logger.Log("Parsed resolution: %s (original: %s, interlaced: %v)",
		resolution, codecSettings.Resolution, isInterlaced)

	// Get video codec based on GPU availability
	videoCodec := getVideoCodec(codecSettings.VCodec, t.isGPUAvailable)

	// Log which encoding method we're using
	if t.isGPUAvailable {
		logger.Log("Using NVIDIA GPU hardware acceleration (NVENC) for transcoding")
	} else {
		logger.Log("Using CPU-based encoding for transcoding")
	}

	// Get appropriate profile based on codec
	profile := getVideoProfile(codecSettings.VCodec)
	// Prepare FFmpeg arguments
	ffmpegArgs := []string{}

	// Add hardware acceleration for NVIDIA GPU
	if t.isGPUAvailable {
		ffmpegArgs = append(ffmpegArgs, "-hwaccel", "cuda")
	}

	// Add input-specific options for TS files
	if serviceStreams != nil {
		// For TS files with service filtering, add TS-specific input options
		ffmpegArgs = append(ffmpegArgs, "-fflags", "+genpts")
	}

	// Input file
	ffmpegArgs = append(ffmpegArgs, "-i", inputFilePath)

	// Video codec settings
	ffmpegArgs = append(ffmpegArgs, "-c:v", videoCodec)

	// Add preset based on encoder type
	if t.isGPUAvailable {
		// NVENC presets for GPU
		ffmpegArgs = append(ffmpegArgs, "-preset", "fast")

		// Add specific NVENC parameters for better quality
		ffmpegArgs = append(ffmpegArgs,
			"-rc", "vbr", // Use variable bitrate mode for better quality
			"-rc-lookahead", "20", // Lookahead frames for better quality
			"-spatial-aq", "1", // Enable spatial adaptive quantization
			"-temporal-aq", "1", // Enable temporal adaptive quantization
		)
	} else {
		// CPU encoding preset
		ffmpegArgs = append(ffmpegArgs, "-preset", "medium")
	}

	// Only add profile for codecs that support it
	if profile != "" {
		ffmpegArgs = append(ffmpegArgs, "-profile:v", profile)
	}

	// Add the rest of the arguments
	ffmpegArgs = append(ffmpegArgs,
		"-b:v", fmt.Sprintf("%dk", codecSettings.VBitrate),
		"-maxrate", fmt.Sprintf("%dk", codecSettings.MaxVBitrate),
		"-bufsize", fmt.Sprintf("%dk", codecSettings.MaxVBitrate*2),
		"-s", resolution, // Use the parsed resolution without p/i suffix
		"-r", fmt.Sprintf("%.2f", codecSettings.FPS),
		// "-pix_fmt", "yuv420p", // Ensure compatible pixel format
		"-vsync", "cfr", // Constant frame rate
	)

	// Audio settings - check for dual audio mode
	if useDualAudio {
		logger.Log("Using dual audio mode for transcoding")
		
		// Check if we have service-specific streams (for TS files)
		if serviceStreams != nil {
			// For TS files with service filtering, use specific stream indices
			logger.Log("Using service-specific stream mapping for TS file")
			
			// Map the video stream for this service
			ffmpegArgs = append(ffmpegArgs, "-map", fmt.Sprintf("0:%d", serviceStreams.VideoStreamIndex))
			
			// Map audio streams for this service
			if len(serviceStreams.AudioStreamIndices) >= 2 {
				// Map the first two audio streams from this service
				ffmpegArgs = append(ffmpegArgs, 
					"-map", fmt.Sprintf("0:%d", serviceStreams.AudioStreamIndices[0]),
					"-map", fmt.Sprintf("0:%d", serviceStreams.AudioStreamIndices[1]))
			} else if len(serviceStreams.AudioStreamIndices) == 1 {
				// Only one audio stream, duplicate it for dual audio
				ffmpegArgs = append(ffmpegArgs, 
					"-map", fmt.Sprintf("0:%d", serviceStreams.AudioStreamIndices[0]),
					"-map", fmt.Sprintf("0:%d", serviceStreams.AudioStreamIndices[0]))
			} else {
				logger.Error("No audio streams found for service %d", *serviceID)
				// Fallback to default mapping
				ffmpegArgs = append(ffmpegArgs, "-map", "0:v:0", "-map", "0:a:0", "-map", "0:a:0")
			}
			
			// Use UI codec settings for service-filtered streams
			audio1Codec := getDualAudioCodec(codecSettings.Audio1Codec)
			audio2Codec := getDualAudioCodec(codecSettings.Audio2Codec)
			
			ffmpegArgs = append(ffmpegArgs,
				"-c:a:0", audio1Codec,
				"-b:a:0", fmt.Sprintf("%dk", codecSettings.Audio1Bitrate),
				"-ac:a:0", fmt.Sprintf("%d", codecSettings.Audio1Channels),
				"-c:a:1", audio2Codec,
				"-b:a:1", fmt.Sprintf("%dk", codecSettings.Audio2Bitrate),
				"-ac:a:1", fmt.Sprintf("%d", codecSettings.Audio2Channels),
			)
		} else {
			// Original UI-based dual audio settings (when input doesn't have dual audio)
			logger.Log("Audio1: %s, %dk, %d channels", codecSettings.Audio1Codec, codecSettings.Audio1Bitrate, codecSettings.Audio1Channels)
			logger.Log("Audio2: %s, %dk, %d channels", codecSettings.Audio2Codec, codecSettings.Audio2Bitrate, codecSettings.Audio2Channels)

			audio1Codec := getDualAudioCodec(codecSettings.Audio1Codec)
			audio2Codec := getDualAudioCodec(codecSettings.Audio2Codec)

			// Add dual audio tracks
			ffmpegArgs = append(ffmpegArgs,
				"-map", "0:v:0",                                        // Map first video stream
				"-map", "0:a:0",                                        // Map first audio stream (will be duplicated)
				"-map", "0:a:0",                                        // Map first audio stream again for second track
				"-c:a:0", audio1Codec,                                  // First audio codec
				"-b:a:0", fmt.Sprintf("%dk", codecSettings.Audio1Bitrate), // First audio bitrate
				"-ac:a:0", fmt.Sprintf("%d", codecSettings.Audio1Channels), // First audio channels
				"-c:a:1", audio2Codec,                                  // Second audio codec
				"-b:a:1", fmt.Sprintf("%dk", codecSettings.Audio2Bitrate), // Second audio bitrate
				"-ac:a:1", fmt.Sprintf("%d", codecSettings.Audio2Channels), // Second audio channels
			)
		}
	} else {
		// Single audio track logic
		if serviceStreams != nil {
			// For TS files with service filtering, use specific stream indices
			logger.Log("Using service-specific stream mapping for single audio")
			
			// Map the video stream for this service
			ffmpegArgs = append(ffmpegArgs, "-map", fmt.Sprintf("0:%d", serviceStreams.VideoStreamIndex))
			
			// Map first audio stream for this service
			if len(serviceStreams.AudioStreamIndices) > 0 {
				ffmpegArgs = append(ffmpegArgs, "-map", fmt.Sprintf("0:%d", serviceStreams.AudioStreamIndices[0]))
			} else {
				logger.Error("No audio streams found for service %d", *serviceID)
				// Fallback to default mapping
				ffmpegArgs = append(ffmpegArgs, "-map", "0:a:0")
			}
		} else {
			// Regular single audio mapping for non-service files
			ffmpegArgs = append(ffmpegArgs, "-map", "0:v:0", "-map", "0:a:0")
		}
		
		// Single audio codec settings
		ffmpegArgs = append(ffmpegArgs,
			"-c:a", getAudioCodec(codecSettings.ACodec),
			"-b:a", fmt.Sprintf("%dk", codecSettings.ABitrate),
			"-ar", fmt.Sprintf("%d", codecSettings.SampleRate),
			"-ac", "2", // 2 audio channels
		)
	}

	// Add common audio settings
	ffmpegArgs = append(ffmpegArgs,
		"-async", "1", // Audio synchronization
		"-y", outputFilePath,
	)

	// Add interlace flags if needed
	if isInterlaced {
		ffmpegArgs = append(ffmpegArgs, "-flags", "+ilme+cgop")
		ffmpegArgs = append(ffmpegArgs, "-field_order", "tt")
		ffmpegArgs = append(ffmpegArgs, "-g", "60")
	}

	// Run FFmpeg command
	logger.Log("Starting transcoding of file %s (ID: %d) with FFmpeg", file.Filename, fileID)
	logger.Log("FFmpeg command: ffmpeg %s", strings.Join(ffmpegArgs, " "))

	cmd := exec.Command("ffmpeg", ffmpegArgs...)
	output, err := cmd.CombinedOutput()

	// Check for errors
	if err != nil {
		// Log the full output for debugging
		logger.Error("FFmpeg transcoding failed: %v\nOutput: %s", err, string(output))

		// Extract a more user-friendly error message if possible
		errorMsg := extractFFmpegError(string(output))
		if errorMsg != "" {
			logger.Error("FFmpeg error: %s", errorMsg)
		}

		// Update file status
		t.filesRepo.ChangeStatus(fileID, int(models.FileStatusFailed))
		_ = sendRecorderNotify(file, statusManager, "failed")

		// Notify retranscoder service of failure
		retranscoderService.OnTranscodingCompleted(fileID, false)

		// Return error with more details
		if errorMsg != "" {
			return fmt.Errorf("FFmpeg transcoding failed: %v - %s", err, errorMsg)
		}
		return fmt.Errorf("FFmpeg transcoding failed: %v", err)
	}

	// Replace the original file with the transcoded file
	err = os.Rename(outputFilePath, inputFilePath)
	if err != nil {
		logger.Error("Failed to replace original file with transcoded file: %v", err)
		t.filesRepo.ChangeStatus(fileID, int(models.FileStatusFailed))
		_ = sendRecorderNotify(file, statusManager, "failed")

		// Notify retranscoder service of failure
		retranscoderService.OnTranscodingCompleted(fileID, false)

		return err
	}

	// Update file status to success
	err = t.filesRepo.ChangeStatus(fileID, int(models.FileStatusSuccess))
	if err != nil {
		logger.Error("Failed to update file status: %v", err)
		_ = sendRecorderNotify(file, statusManager, "failed")
		
		// Notify retranscoder service of failure
		retranscoderService.OnTranscodingCompleted(fileID, false)
		
		return err
	} else {
		err = sendRecorderNotify(file, statusManager, "completed")
		if err != nil {
			// Notify retranscoder service of failure
			retranscoderService.OnTranscodingCompleted(fileID, false)
			
			return err
		}
	}

	// Notify retranscoder service of success
	retranscoderService.OnTranscodingCompleted(fileID, true)

	logger.Log("Successfully transcoded file %s (ID: %d)", file.Filename, fileID)
	return nil
}

func sendRecorderNotify(file models.ConvertItem, statusManager *recorder.RecorderStatusManager, status string) error {
	if file.RecorderID != nil {
		if statusManager == nil {
			logger.Error("DEBUG: Status manager is nil, cannot notify about recorder %d completion\"", *file.RecorderID)
			return nil
		}
		err := statusManager.UpdateRecorderStatus(*file.RecorderID, status)
		if err != nil {
			logger.Error("Failed to update recorder status: %v", err)
			return err
		} else {
			logger.Log("DEBUG: Notifying status manager that recorder %d completed with status=%v", *file.RecorderID, true)
		}
	}

	return nil
}

// getVideoCodec maps the video codec from the database to the FFmpeg codec name
// If useGPU is true, it will return the GPU-accelerated codec name when available
func getVideoCodec(codec string, useGPU bool) string {
	if useGPU {
		// Use NVIDIA hardware-accelerated encoders when GPU is available
		switch codec {
		case "h264":
			return "h264_nvenc" // NVIDIA hardware-accelerated H.264
		case "h265":
			return "hevc_nvenc" // NVIDIA hardware-accelerated H.265
		case "mpeg2":
			return "mpeg2video" // No NVIDIA encoder for MPEG-2, use CPU
		default:
			return "h264_nvenc" // Default to NVIDIA H.264
		}
	} else {
		// Use CPU-based encoders when no GPU is available
		switch codec {
		case "h264":
			return "libx264" // CPU-based H.264
		case "h265":
			return "libx265" // CPU-based H.265
		case "mpeg2":
			return "mpeg2video" // CPU-based MPEG-2
		default:
			return "libx264" // Default to CPU-based H.264
		}
	}
}

// getVideoProfile returns the appropriate profile for the given codec
// This works for both CPU and GPU encoders as they use the same profile names
func getVideoProfile(codec string) string {
	switch codec {
	case "h264":
		return "high" // H.264 supports high profile
	case "h265":
		return "main" // H.265 uses main profile
	case "mpeg2":
		return "" // MPEG-2 doesn't use profile in the same way
	default:
		return "" // No profile by default
	}
}

// getAudioCodec maps the audio codec from the database to the FFmpeg codec name
func getAudioCodec(codec string) string {
	switch codec {
	case "ac3_passthrough":
		return "copy"
	case "ac3_downmix":
		return "ac3"
	case "aac_downmix":
		return "aac"
	case "mpeg1l2_downmix":
		return "mp2"
	default:
		return "aac" // Default to AAC
	}
}

// getDualAudioCodec maps the audio codec from the database to the FFmpeg codec name for dual audio mode
func getDualAudioCodec(codec string) string {
	switch codec {
	case "ac3_passthrough":
		return "copy"
	case "ac3_downmix":
		return "ac3"
	case "aac_downmix":
		return "aac"
	case "mpeg1l2_downmix":
		return "mp2"
	default:
		return "aac" // Default to AAC
	}
}

// parseResolution parses a resolution string and returns the clean resolution and whether it's interlaced
// Examples:
// - "1920x1080p" -> "1920x1080", false
// - "1920x1080i" -> "1920x1080", true
// - "1280x720" -> "1280x720", false
// - "1920x1080p60" -> "1920x1080", false (ignores framerate info)
func parseResolution(resolution string) (string, bool) {
	// First, extract just the resolution part (in case there's additional info)
	// Look for the pattern of digits followed by 'x' followed by digits
	re := regexp.MustCompile(`(\d+)x(\d+)`)
	matches := re.FindStringSubmatch(resolution)

	if len(matches) >= 3 {
		// We found a resolution pattern
		width := matches[1]
		height := matches[2]
		cleanResolution := width + "x" + height

		// Check if the original resolution ends with 'i' for interlaced
		if strings.Contains(resolution, "i") {
			return cleanResolution, true
		}

		// Otherwise, it's progressive
		return cleanResolution, false
	}

	// If we couldn't parse it with regex, try the simple suffix check
	if strings.HasSuffix(resolution, "p") {
		// Progressive scan
		return resolution[:len(resolution)-1], false
	} else if strings.HasSuffix(resolution, "i") {
		// Interlaced scan
		return resolution[:len(resolution)-1], true
	}

	// No suffix or pattern match, return as is and assume progressive
	return resolution, false
}

// extractFFmpegError extracts a more user-friendly error message from FFmpeg output
func extractFFmpegError(output string) string {
	// Common error patterns to look for
	errorPatterns := []struct {
		regex      string
		formatFunc func([]string) string
	}{
		// Profile errors
		{
			regex: `unknown profile <([^>]+)>`,
			formatFunc: func(matches []string) string {
				if len(matches) > 1 {
					return fmt.Sprintf("Unknown profile: %s", matches[1])
				}
				return "Unknown profile specified"
			},
		},
		// Invalid codec
		{
			regex: `Unknown encoder '([^']+)'`,
			formatFunc: func(matches []string) string {
				if len(matches) > 1 {
					return fmt.Sprintf("Unknown encoder: %s", matches[1])
				}
				return "Unknown encoder specified"
			},
		},
		// Invalid parameters
		{
			regex: `Error while opening encoder.*such as bit_rate, rate, width or height`,
			formatFunc: func(matches []string) string {
				return "Invalid encoding parameters (bit rate, width, height, etc.)"
			},
		},
		// File not found
		{
			regex: `No such file or directory`,
			formatFunc: func(matches []string) string {
				return "Input file not found or inaccessible"
			},
		},
		// Permission denied
		{
			regex: `Permission denied`,
			formatFunc: func(matches []string) string {
				return "Permission denied when accessing files"
			},
		},
		// NVENC errors
		{
			regex: `CUDA error: no CUDA-capable device is detected`,
			formatFunc: func(matches []string) string {
				return "NVIDIA GPU detection failed - no CUDA-capable device detected"
			},
		},
		{
			regex: `NVENC error: could not open the encoder`,
			formatFunc: func(matches []string) string {
				return "NVIDIA encoder initialization failed - check GPU drivers"
			},
		},
		{
			regex: `NVENC error: out of memory`,
			formatFunc: func(matches []string) string {
				return "NVIDIA encoder out of memory - reduce encoding parameters or free GPU memory"
			},
		},
		{
			regex: `NVENC error: unsupported device`,
			formatFunc: func(matches []string) string {
				return "Unsupported NVIDIA GPU device - check compatibility"
			},
		},
	}

	// Check each error pattern
	for _, pattern := range errorPatterns {
		re := regexp.MustCompile(pattern.regex)
		matches := re.FindStringSubmatch(output)
		if len(matches) > 0 {
			return pattern.formatFunc(matches)
		}
	}

	// If no specific error pattern is found, look for any error message
	re := regexp.MustCompile(`(?i)error:?\s+(.+)`)
	matches := re.FindStringSubmatch(output)
	if len(matches) > 1 {
		return matches[1]
	}

	return ""
}

// detectAudioTracks detects audio tracks in an MP4 file using FFprobe
func (t *Transcoder) detectAudioTracks(filePath string) ([]AudioTrackInfo, error) {
	// Use FFprobe to get audio stream information
	cmd := exec.Command("ffprobe", 
		"-v", "quiet",
		"-print_format", "json",
		"-show_streams",
		"-select_streams", "a", // Only audio streams
		filePath)
	
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("failed to run ffprobe: %v", err)
	}

	var probe FFprobeOutput
	if err := json.Unmarshal(output, &probe); err != nil {
		return nil, fmt.Errorf("failed to parse ffprobe output: %v", err)
	}

	var audioTracks []AudioTrackInfo
	for _, stream := range probe.Streams {
		if stream.CodecType == "audio" {
			audioTracks = append(audioTracks, AudioTrackInfo{
				Index:     stream.Index,
				CodecName: stream.CodecName,
				Channels:  stream.Channels,
				Bitrate:   stream.BitRate,
			})
		}
	}

	logger.Log("Detected %d audio tracks in file: %s", len(audioTracks), filePath)
	for i, track := range audioTracks {
		logger.Log("  Track %d: %s, %d channels, bitrate: %s", 
			i, track.CodecName, track.Channels, track.Bitrate)
	}

	return audioTracks, nil
}

// shouldUseDualAudio determines if dual audio mode should be used based on input file and settings
func (t *Transcoder) shouldUseDualAudio(inputFilePath string, codecSettings models.CodecSettings) (bool, error) {
	// First detect audio tracks in the input file
	audioTracks, err := t.detectAudioTracks(inputFilePath)
	if err != nil {
		logger.Error("Failed to detect audio tracks, using default settings: %v", err)
		// If detection fails, use the codec settings from UI
		return codecSettings.DualAudioMode, nil
	}

	// If input file has multiple audio tracks, preserve dual audio
	if len(audioTracks) >= 2 {
		logger.Log("Input file has %d audio tracks, enabling dual audio mode", len(audioTracks))
		return true, nil
	}

	// If input file has only one audio track, use codec settings from UI
	logger.Log("Input file has %d audio track(s), using UI codec settings (dual audio: %v)", 
		len(audioTracks), codecSettings.DualAudioMode)
	return codecSettings.DualAudioMode, nil
}

// detectServiceStreams detects which streams belong to a specific service in a TS file
func (t *Transcoder) detectServiceStreams(filePath string, serviceID int) (*ServiceStreamInfo, error) {
	// Use FFprobe to get detailed stream information including program mappings
	cmd := exec.Command("ffprobe", 
		"-v", "quiet",
		"-print_format", "json",
		"-show_programs", 
		"-show_streams",
		filePath)
	
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("failed to run ffprobe: %v", err)
	}

	// Parse the ffprobe output to find streams for the specified service
	var probe struct {
		Programs []struct {
			ProgramID int `json:"program_id"`
			Streams   []struct {
				Index int `json:"index"`
			} `json:"streams"`
		} `json:"programs"`
		Streams []struct {
			Index     int    `json:"index"`
			CodecType string `json:"codec_type"`
		} `json:"streams"`
	}

	if err := json.Unmarshal(output, &probe); err != nil {
		return nil, fmt.Errorf("failed to parse ffprobe output: %v", err)
	}

	// Find the program that matches our service ID
	var targetProgram *struct {
		ProgramID int `json:"program_id"`
		Streams   []struct {
			Index int `json:"index"`
		} `json:"streams"`
	}

	for _, program := range probe.Programs {
		if program.ProgramID == serviceID {
			targetProgram = &program
			break
		}
	}

	if targetProgram == nil {
		return nil, fmt.Errorf("service ID %d not found in TS file", serviceID)
	}

	// Extract video and audio stream indices for this service
	serviceInfo := &ServiceStreamInfo{
		VideoStreamIndex: -1,
		AudioStreamIndices: []int{},
	}

	for _, programStream := range targetProgram.Streams {
		// Find the corresponding stream info
		for _, stream := range probe.Streams {
			if stream.Index == programStream.Index {
				if stream.CodecType == "video" && serviceInfo.VideoStreamIndex == -1 {
					serviceInfo.VideoStreamIndex = stream.Index
				} else if stream.CodecType == "audio" {
					serviceInfo.AudioStreamIndices = append(serviceInfo.AudioStreamIndices, stream.Index)
				}
				break
			}
		}
	}

	if serviceInfo.VideoStreamIndex == -1 {
		return nil, fmt.Errorf("no video stream found for service ID %d", serviceID)
	}

	logger.Log("Service %d streams - Video: %d, Audio: %v", 
		serviceID, serviceInfo.VideoStreamIndex, serviceInfo.AudioStreamIndices)

	return serviceInfo, nil
}
