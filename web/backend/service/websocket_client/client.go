package websocket_client

import (
	"encoding/json"
	"fmt"
	"log"
	"net/url"
	"showfer-web/utils"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

// RoleChangeMessage represents a role change message
type RoleChangeMessage struct {
	Type string `json:"type"`
	Data struct {
		NewRole string `json:"new_role"`
	} `json:"data"`
}

// RoleChangeResponse represents the response to a role change message
type RoleChangeResponse struct {
	Type string `json:"type"`
	Data struct {
		Success bool   `json:"success"`
		Message string `json:"message"`
		Role    string `json:"role"`
	} `json:"data"`
}

// WebSocketClient manages WebSocket client connections to backup servers
type WebSocketClient struct {
	conn           *websocket.Conn
	backupIP       string
	connected      bool
	reconnectTimer *time.Timer
	mutex          sync.RWMutex
	stopChan       chan struct{}
}

var (
	clientInstance *WebSocketClient
	clientMutex    sync.Mutex
)

// GetClient returns the singleton WebSocket client instance
func GetClient() *WebSocketClient {
	clientMutex.Lock()
	defer clientMutex.Unlock()
	
	if clientInstance == nil {
		clientInstance = &WebSocketClient{
			stopChan: make(chan struct{}),
		}
	}
	return clientInstance
}

// Connect establishes a WebSocket connection to the backup server
func (c *WebSocketClient) Connect(backupIP string) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// Close existing connection if any
	if c.conn != nil {
		c.conn.Close()
		c.conn = nil
	}

	c.backupIP = backupIP
	
	// Create WebSocket URL
	u := url.URL{
		Scheme: "ws",
		Host:   fmt.Sprintf("%s:8080", backupIP),
		Path:   "/ws",
	}

	log.Printf("Connecting to backup server WebSocket: %s", u.String())

	// Establish WebSocket connection
	conn, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		return fmt.Errorf("failed to connect to backup server: %v", err)
	}

	c.conn = conn
	c.connected = true

	// Start message handling goroutines
	go c.readPump()
	go c.writePump()

	log.Printf("Successfully connected to backup server: %s", backupIP)
	return nil
}

// Disconnect closes the WebSocket connection
func (c *WebSocketClient) Disconnect() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.reconnectTimer != nil {
		c.reconnectTimer.Stop()
		c.reconnectTimer = nil
	}

	if c.conn != nil {
		c.conn.Close()
		c.conn = nil
	}

	c.connected = false
	c.backupIP = ""

	// Signal stop to goroutines
	select {
	case c.stopChan <- struct{}{}:
	default:
	}

	log.Println("Disconnected from backup server")
}

// SendRoleChange sends a role change message to the backup server
func (c *WebSocketClient) SendRoleChange(newRole string) error {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if !c.connected || c.conn == nil {
		return fmt.Errorf("not connected to backup server")
	}

	message := RoleChangeMessage{
		Type: "role_change",
	}
	message.Data.NewRole = newRole

	log.Printf("Sending role change message: %s", newRole)
	return c.conn.WriteJSON(message)
}

// IsConnected returns whether the client is connected
func (c *WebSocketClient) IsConnected() bool {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.connected
}

// readPump handles incoming messages from the backup server
func (c *WebSocketClient) readPump() {
	defer func() {
		c.mutex.Lock()
		if c.conn != nil {
			c.conn.Close()
			c.conn = nil
		}
		c.connected = false
		c.mutex.Unlock()
		
		// Attempt to reconnect after 5 seconds
		c.scheduleReconnect()
	}()

	for {
		select {
		case <-c.stopChan:
			return
		default:
		}

		c.mutex.RLock()
		conn := c.conn
		c.mutex.RUnlock()

		if conn == nil {
			return
		}

		_, message, err := conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket client read error: %v", err)
			}
			return
		}

		c.handleMessage(message)
	}
}

// writePump handles outgoing ping messages
func (c *WebSocketClient) writePump() {
	ticker := time.NewTicker(54 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-c.stopChan:
			return
		case <-ticker.C:
			c.mutex.RLock()
			conn := c.conn
			connected := c.connected
			c.mutex.RUnlock()

			if !connected || conn == nil {
				return
			}

			if err := conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				log.Printf("WebSocket client ping error: %v", err)
				return
			}
		}
	}
}

// handleMessage processes incoming messages from the backup server
func (c *WebSocketClient) handleMessage(data []byte) {
	var response RoleChangeResponse
	if err := json.Unmarshal(data, &response); err != nil {
		log.Printf("Failed to parse message from backup server: %v", err)
		return
	}

	if response.Type == "role_change_response" {
		log.Printf("Received role change response: success=%v, message=%s, role=%s", 
			response.Data.Success, response.Data.Message, response.Data.Role)
	}
}

// scheduleReconnect schedules a reconnection attempt
func (c *WebSocketClient) scheduleReconnect() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.backupIP == "" {
		return
	}

	if c.reconnectTimer != nil {
		c.reconnectTimer.Stop()
	}

	c.reconnectTimer = time.AfterFunc(5*time.Second, func() {
		log.Printf("Attempting to reconnect to backup server: %s", c.backupIP)
		if err := c.Connect(c.backupIP); err != nil {
			log.Printf("Failed to reconnect to backup server: %v", err)
		}
	})
}

// StartBackupConnection starts the WebSocket client connection if backup IP is configured
func StartBackupConnection() {
	// Only start if this is a primary server
	if utils.GetServerType() != "primary" {
		return
	}

	backupIP := utils.GetBackupIP()
	if backupIP == "" {
		log.Println("No backup IP configured, skipping backup connection")
		return
	}

	client := GetClient()
	if err := client.Connect(backupIP); err != nil {
		log.Printf("Failed to connect to backup server %s: %v", backupIP, err)
		return
	}

	// Send role change message to set backup server as backup
	if err := client.SendRoleChange("backup"); err != nil {
		log.Printf("Failed to send role change message: %v", err)
	}
}

// UpdateBackupConnection updates the backup connection when backup IP changes
func UpdateBackupConnection(newBackupIP string) {
	client := GetClient()
	
	// Disconnect existing connection
	client.Disconnect()

	// Save new backup IP
	if err := utils.SetBackupIP(newBackupIP); err != nil {
		log.Printf("Failed to save backup IP: %v", err)
		return
	}

	// Connect to new backup server if IP is provided
	if newBackupIP != "" && utils.GetServerType() == "primary" {
		if err := client.Connect(newBackupIP); err != nil {
			log.Printf("Failed to connect to new backup server %s: %v", newBackupIP, err)
			return
		}

		// Send role change message
		if err := client.SendRoleChange("backup"); err != nil {
			log.Printf("Failed to send role change message to new backup server: %v", err)
		}
	}
}
