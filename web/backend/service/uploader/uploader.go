package uploader

import (
	"os"
	"path/filepath"
	"showfer-web/models"
	"showfer-web/repository"
	"showfer-web/service/converter"
	"showfer-web/service/logger"
	"showfer-web/service/queue"
)

// ProcessFile processes a file after upload
func ProcessFile(filePath, location, baseDir string, repo *repository.FilesRepository, codecRepo *repository.CodecSettingsRepository) error {
	filename := filepath.Base(filePath)

	// Check if file exists
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		logger.Error("Failed to get file info: %v", err)
		return err
	}

	// Get file duration
	duration, err := converter.GetDuration(filePath)
	if err != nil {
		logger.Error("Failed to get file duration: %v", err)
		// Continue with duration 0
		duration = 0
	}

	// Get default codec settings for uploaded files
	codecSettings, err := codecRepo.GetCodecSettings()
	var videoCodec, audioCodec *string
	if err != nil {
		logger.Error("Failed to get codec settings for uploaded file: %v", err)
		// Continue without codec settings - codec fields will be nil
	} else {
		// Set video codec based on system settings
		vCodec := normalizeVideoCodecName(codecSettings.VCodec)
		videoCodec = &vCodec

		// Set audio codec based on dual audio mode
		if codecSettings.DualAudioMode {
			// For dual audio mode, prioritize Audio1 codec (usually surround sound)
			aCodec := normalizeAudioCodecName(codecSettings.Audio1Codec)
			audioCodec = &aCodec
		} else {
			// For single audio mode, use the regular audio codec
			aCodec := normalizeAudioCodecName(codecSettings.ACodec)
			audioCodec = &aCodec
		}
	}

	// Create convert item with initial status as Queue
	now := models.NewConvertItem("", "", 0, 0).CreatedAt // Use the NewConvertItem function to get the current time format
	convertItem := models.ConvertItem{
		Filename:    filename,
		Name:        filename,
		Location:    location,
		Duration:    duration,
		Status:      int(models.FileStatusQueue), // Set status to Queue initially
		Size:        fileInfo.Size(),
		StorageType: int(models.StorageTypeLocal),
		CreatedAt:   now,
		UpdatedAt:   now,
		CLocation:   location, // Set CLocation to the same value as Location
		VideoCodec:  videoCodec,
		AudioCodec:  audioCodec,
	}

	// Save to database
	id, err := repo.CreateConvertItem(convertItem)
	if err != nil {
		logger.Error("Failed to create convert item: %v", err)
		return err
	}

	logger.Log("File processed successfully: %s (ID: %d)", filename, id)

	// Add the file to the conversion queue
	queueManager := queue.GetInstance()
	queueManager.AddToQueue(id)

	return nil
}

// normalizeVideoCodecName converts codec names to user-friendly format
func normalizeVideoCodecName(codecName string) string {
	switch codecName {
	case "h264":
		return "H.264"
	case "h265":
		return "H.265"
	case "mpeg2":
		return "MPEG-2"
	default:
		return "H.264" // Default fallback
	}
}

// normalizeAudioCodecName converts audio codec names to user-friendly format
func normalizeAudioCodecName(codecName string) string {
	switch codecName {
	case "aac_downmix":
		return "AAC"
	case "ac3_downmix":
		return "AC-3"
	case "ac3_passthrough":
		return "AC-3"
	case "mpeg1l2_downmix":
		return "MP2"
	default:
		return "AAC" // Default fallback
	}
}
