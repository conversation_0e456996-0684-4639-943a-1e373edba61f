package database_sync

import (
	"fmt"
	"os"
	"os/exec"
	"showfer-web/config"
	"showfer-web/service/logger"
	"showfer-web/utils"
	"time"
)

// DumpService handles periodic database dumping for primary servers
type DumpService struct {
	dumpInterval time.Duration
	dumpPath     string // Single file path for the dump
}

// NewDumpService creates a new database dump service
func NewDumpService() *DumpService {
	return &DumpService{
		dumpInterval: 30 * time.Second, // 30 seconds as requested
		dumpPath:     "/tmp/showfer_db_dump.sql", // Single file name
	}
}

// Start begins the periodic database dumping process
func (ds *DumpService) Start() {
	logger.Log("Database dump service: Starting periodic database dumps")
	
	ticker := time.NewTicker(ds.dumpInterval)
	
	go func() {
		defer ticker.Stop()
		
		for {
			select {
			case <-ticker.C:
				ds.performDump()
			}
		}
	}()
}

// performDump creates a database dump if this server is primary
func (ds *DumpService) performDump() {
	// Only dump if this is a primary server
	if utils.GetServerType() != "primary" {
		return
	}

	logger.Log("Database dump service: Creating database dump to %s", ds.dumpPath)

	// Get database configuration
	dbConfig := config.GetDatabaseConfig()

	// Create pg_dump command - overwrites the same file each time
	cmd := exec.Command("pg_dump",
		"-h", dbConfig.Host,
		"-p", dbConfig.Port,
		"-U", dbConfig.User,
		"-d", dbConfig.DBName,
		"-f", ds.dumpPath,
		"--no-password", // Use PGPASSWORD environment variable
		"--verbose",
	)

	// Set PGPASSWORD environment variable for authentication
	cmd.Env = append(os.Environ(), fmt.Sprintf("PGPASSWORD=%s", dbConfig.Password))

	// Execute the dump command
	output, err := cmd.CombinedOutput()
	if err != nil {
		logger.Error("Database dump service: Failed to create dump: %v, output: %s", err, string(output))
		return
	}

	logger.Log("Database dump service: Successfully updated dump file: %s", ds.dumpPath)

	// Sync to backup server if configured
	ds.syncToBackupServer()
}

// GetDumpPath returns the path to the dump file
func (ds *DumpService) GetDumpPath() string {
	return ds.dumpPath
}

// DumpExists checks if the dump file exists
func (ds *DumpService) DumpExists() bool {
	_, err := os.Stat(ds.dumpPath)
	return err == nil
}

// GetDumpInfo returns information about the dump file
func (ds *DumpService) GetDumpInfo() (map[string]interface{}, error) {
	info, err := os.Stat(ds.dumpPath)
	if err != nil {
		return nil, fmt.Errorf("dump file not found: %v", err)
	}

	return map[string]interface{}{
		"path":         ds.dumpPath,
		"size":         info.Size(),
		"modified":     info.ModTime(),
		"exists":       true,
	}, nil
}

// syncToBackupServer syncs the dump file and data directory to backup server using rsync
func (ds *DumpService) syncToBackupServer() {
	// Check if backup server is configured
	backupIP := utils.GetBackupIP()
	if backupIP == "" {
		return // No backup server configured
	}

	// Check if SSH credentials are configured
	sshUser := utils.GetSSHUser()
	sshPassword := utils.GetSSHPassword()
	if sshUser == "" || sshPassword == "" {
		logger.Error("Database dump service: SSH credentials not configured for rsync")
		return
	}

	logger.Log("Database dump service: Syncing dump file and data directory to backup server %s", backupIP)

	// First, sync the database dump file
	ds.syncDumpFile(backupIP, sshUser, sshPassword)

	// Then, sync the data directory
	ds.syncDataDirectory(backupIP, sshUser, sshPassword)

	// Finally, sync primary server SSH credentials for recovery purposes
	ds.syncPrimarySSHCredentials(backupIP, sshUser, sshPassword)
}

// syncDumpFile syncs the database dump file to backup server
func (ds *DumpService) syncDumpFile(backupIP, sshUser, sshPassword string) {
	logger.Log("Database dump service: Syncing dump file to backup server")

	// Build rsync command for dump file
	destination := fmt.Sprintf("%s@%s:/tmp/", sshUser, backupIP)

	cmd := exec.Command("sshpass",
		"-p", sshPassword,                          // specify password
		"rsync",
		"-avz",                                     // archive, verbose, compress
		"-e", "ssh -o StrictHostKeyChecking=no",   // SSH options
		ds.dumpPath,                               // source file
		destination,                               // destination
	)

	// Execute rsync command with sshpass
	output, err := cmd.CombinedOutput()
	if err != nil {
		logger.Error("Database dump service: Failed to sync dump file to backup server: %v, output: %s", err, string(output))
		return
	}

	logger.Log("Database dump service: Successfully synced dump file to backup server")
}

// syncDataDirectory syncs the entire data directory to backup server
func (ds *DumpService) syncDataDirectory(backupIP, sshUser, sshPassword string) {
	logger.Log("Database dump service: Syncing data directory to backup server")

	// Get the data directory path (relative to backend)
	dataDir := "./data/"

	// Check if data directory exists
	if _, err := os.Stat(dataDir); os.IsNotExist(err) {
		logger.Log("Database dump service: Data directory %s does not exist, skipping sync", dataDir)
		return
	}

	// Build rsync command for data directory
	// Sync to backup server's equivalent data directory
	destination := fmt.Sprintf("%s@%s:~/traffiq/web/backend/data/", sshUser, backupIP)

	cmd := exec.Command("sshpass",
		"-p", sshPassword,                          // specify password
		"rsync",
		"-avz",                                     // archive, verbose, compress
		"--delete",                                 // delete files in destination that don't exist in source
		"-e", "ssh -o StrictHostKeyChecking=no",   // SSH options
		dataDir,                                   // source directory (with trailing slash)
		destination,                               // destination directory
	)

	// Execute rsync command with sshpass
	output, err := cmd.CombinedOutput()
	if err != nil {
		logger.Error("Database dump service: Failed to sync data directory to backup server: %v, output: %s", err, string(output))
		return
	}

	logger.Log("Database dump service: Successfully synced data directory to backup server")
}

// syncPrimarySSHCredentials syncs primary server SSH credentials to backup server for recovery
func (ds *DumpService) syncPrimarySSHCredentials(backupIP, sshUser, sshPassword string) {
	// Get primary server SSH credentials
	primarySSHUser := utils.GetPrimarySSHUser()
	primarySSHPassword := utils.GetPrimarySSHPassword()

	// Only sync if primary SSH credentials are configured
	if primarySSHUser == "" || primarySSHPassword == "" {
		logger.Log("Database dump service: Primary SSH credentials not configured, skipping sync")
		return
	}

	logger.Log("Database dump service: Syncing primary SSH credentials to backup server")

	// Create temporary files with primary SSH credentials
	primaryUserFile := "/tmp/primary_ssh_user_temp"
	primaryPasswordFile := "/tmp/primary_ssh_password_temp"

	// Write credentials to temporary files
	if err := os.WriteFile(primaryUserFile, []byte(primarySSHUser), 0644); err != nil {
		logger.Error("Database dump service: Failed to create temp primary SSH user file: %v", err)
		return
	}
	defer os.Remove(primaryUserFile)

	if err := os.WriteFile(primaryPasswordFile, []byte(primarySSHPassword), 0600); err != nil {
		logger.Error("Database dump service: Failed to create temp primary SSH password file: %v", err)
		return
	}
	defer os.Remove(primaryPasswordFile)

	// Sync primary SSH user
	cmd := exec.Command("sshpass",
		"-p", sshPassword,
		"rsync",
		"-avz",
		"-e", "ssh -o StrictHostKeyChecking=no",
		primaryUserFile,
		fmt.Sprintf("%s@%s:/tmp/primary_ssh_user", sshUser, backupIP),
	)

	if output, err := cmd.CombinedOutput(); err != nil {
		logger.Error("Database dump service: Failed to sync primary SSH user: %v, output: %s", err, string(output))
		return
	}

	// Sync primary SSH password
	cmd = exec.Command("sshpass",
		"-p", sshPassword,
		"rsync",
		"-avz",
		"-e", "ssh -o StrictHostKeyChecking=no",
		primaryPasswordFile,
		fmt.Sprintf("%s@%s:/tmp/primary_ssh_password", sshUser, backupIP),
	)

	if output, err := cmd.CombinedOutput(); err != nil {
		logger.Error("Database dump service: Failed to sync primary SSH password: %v, output: %s", err, string(output))
		return
	}

	logger.Log("Database dump service: Successfully synced primary SSH credentials to backup server")
}
