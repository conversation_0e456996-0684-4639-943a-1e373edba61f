package system

import (
	"sync"
	"time"

	"showfer-web/models"
	"showfer-web/service/logger"

	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/disk"
	"github.com/shirou/gopsutil/v3/mem"
)

// SystemMonitor handles system resource monitoring
type SystemMonitor struct {
	stats          models.SystemStats
	thresholds     models.AlarmThresholds
	mutex          sync.RWMutex
	stopChan       chan struct{}
	broadcastChan  chan models.SystemStats
	monitoringPath string
}

var monitor *SystemMonitor

// InitSystemMonitor initializes the system monitor
func InitSystemMonitor(monitoringPath string) {
	monitor = &SystemMonitor{
		thresholds:     models.DefaultAlarmThresholds(),
		stop<PERSON>han:       make(chan struct{}),
		broadcastChan:  make(chan models.SystemStats, 10),
		monitoringPath: monitoringPath,
	}

	// Start monitoring in a goroutine
	go monitor.startMonitoring()
}

// GetSystemMonitor returns the system monitor instance
func GetSystemMonitor() *SystemMonitor {
	return monitor
}

// GetBroadcastChannel returns the channel for broadcasting system stats
func (m *SystemMonitor) GetBroadcastChannel() <-chan models.SystemStats {
	return m.broadcastChan
}

// GetCurrentStats returns the current system stats
func (m *SystemMonitor) GetCurrentStats() models.SystemStats {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.stats
}

// SetThresholds sets the alarm thresholds
func (m *SystemMonitor) SetThresholds(thresholds models.AlarmThresholds) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.thresholds = thresholds
}

// GetThresholds returns the current alarm thresholds
func (m *SystemMonitor) GetThresholds() models.AlarmThresholds {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.thresholds
}

// Stop stops the monitoring
func (m *SystemMonitor) Stop() {
	close(m.stopChan)
}

// startMonitoring starts the monitoring loop
func (m *SystemMonitor) startMonitoring() {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			stats, err := m.collectStats()
			if err != nil {
				logger.Error("Failed to collect system stats: %v", err)
				continue
			}

			// Update stats
			m.mutex.Lock()
			m.stats = stats
			m.mutex.Unlock()

			// Broadcast stats
			select {
			case m.broadcastChan <- stats:
				// Successfully sent
			default:
				// Channel buffer is full, discard this update
				logger.Warn("System stats broadcast channel is full, discarding update")
			}

		case <-m.stopChan:
			return
		}
	}
}

// collectStats collects system resource usage statistics
func (m *SystemMonitor) collectStats() (models.SystemStats, error) {
	stats := models.SystemStats{
		Timestamp: time.Now(),
	}

	// Get disk usage
	diskStat, err := disk.Usage(m.monitoringPath)
	if err != nil {
		return stats, err
	}
	stats.DiskTotal = diskStat.Total
	stats.DiskUsed = diskStat.Used
	stats.DiskFree = diskStat.Free
	stats.DiskUsage = diskStat.UsedPercent

	// Get CPU usage
	cpuPercent, err := cpu.Percent(time.Second, false)
	if err != nil {
		return stats, err
	}
	if len(cpuPercent) > 0 {
		stats.CPUUsage = cpuPercent[0]
	}

	// Get memory usage
	memStat, err := mem.VirtualMemory()
	if err != nil {
		return stats, err
	}
	stats.MemoryTotal = memStat.Total
	stats.MemoryUsed = memStat.Used
	stats.MemoryFree = memStat.Free
	stats.MemoryUsage = memStat.UsedPercent

	// Get GPU information if available
	gpuInfo := GetGPUInfo()
	stats.GPUAvailable = gpuInfo.Available

	if gpuInfo.Available {
		stats.GPUModel = gpuInfo.Model
		stats.GPUMemTotal = gpuInfo.MemTotal
		stats.GPUMemUsed = gpuInfo.MemUsed
		stats.GPUMemFree = gpuInfo.MemFree
		stats.GPUUsage = gpuInfo.Usage
		stats.GPUTemp = gpuInfo.Temp
	}

	// Check alarms
	m.mutex.RLock()
	thresholds := m.thresholds
	m.mutex.RUnlock()

	stats.DiskAlarm = stats.DiskUsage > thresholds.DiskThreshold
	stats.CPUAlarm = stats.CPUUsage > thresholds.CPUThreshold
	stats.MemoryAlarm = stats.MemoryUsage > thresholds.MemoryThreshold

	// Only check GPU alarm if GPU is available
	if stats.GPUAvailable {
		stats.GPUAlarm = stats.GPUUsage > thresholds.GPUThreshold
	}

	return stats, nil
}
