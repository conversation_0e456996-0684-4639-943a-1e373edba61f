package system

import (
	"bytes"
	"os/exec"
	"strconv"
	"strings"

	"showfer-web/service/logger"
)

// GPUInfo contains information about a GPU
type GPUInfo struct {
	Available bool    // Whether a GPU is available
	Model     string  // GPU model name
	MemTotal  uint64  // Total memory in bytes
	MemUsed   uint64  // Used memory in bytes
	MemFree   uint64  // Free memory in bytes
	Usage     float64 // GPU utilization percentage (0-100)
	Temp      float64 // GPU temperature in Celsius
}

// IsGPUAvailable checks if an NVIDIA GPU is available on the system
// by attempting to run the nvidia-smi command
func IsGPUAvailable() bool {
	// Check if nvidia-smi command exists and runs successfully
	cmd := exec.Command("nvidia-smi", "--query-gpu=name", "--format=csv,noheader")
	err := cmd.Run()
	if err != nil {
		logger.Log("GPU not detected: %v", err)
		return false
	}

	logger.Log("NVIDIA GPU detected")
	return true
}

// GetGPUInfo retrieves information about the GPU
func GetGPUInfo() GPUInfo {
	info := GPUInfo{
		Available: false,
	}

	// Check if GPU is available
	if !IsGPUAvailable() {
		return info
	}

	// GPU is available
	info.Available = true

	// Get GPU model
	modelCmd := exec.Command("nvidia-smi", "--query-gpu=name", "--format=csv,noheader")
	var modelOut bytes.Buffer
	modelCmd.Stdout = &modelOut
	if err := modelCmd.Run(); err == nil {
		info.Model = strings.TrimSpace(modelOut.String())
	} else {
		logger.Error("Failed to get GPU model: %v", err)
	}

	// Get GPU memory information
	memCmd := exec.Command("nvidia-smi", "--query-gpu=memory.total,memory.used,memory.free", "--format=csv,noheader")
	var memOut bytes.Buffer
	memCmd.Stdout = &memOut
	if err := memCmd.Run(); err == nil {
		memInfo := strings.TrimSpace(memOut.String())
		parts := strings.Split(memInfo, ",")
		if len(parts) == 3 {
			// Parse total memory (format: "16384 MiB")
			totalParts := strings.Fields(strings.TrimSpace(parts[0]))
			if len(totalParts) == 2 && totalParts[1] == "MiB" {
				if total, err := strconv.ParseUint(totalParts[0], 10, 64); err == nil {
					// Convert MiB to bytes
					info.MemTotal = total * 1024 * 1024
				}
			}

			// Parse used memory
			usedParts := strings.Fields(strings.TrimSpace(parts[1]))
			if len(usedParts) == 2 && usedParts[1] == "MiB" {
				if used, err := strconv.ParseUint(usedParts[0], 10, 64); err == nil {
					// Convert MiB to bytes
					info.MemUsed = used * 1024 * 1024
				}
			}

			// Parse free memory
			freeParts := strings.Fields(strings.TrimSpace(parts[2]))
			if len(freeParts) == 2 && freeParts[1] == "MiB" {
				if free, err := strconv.ParseUint(freeParts[0], 10, 64); err == nil {
					// Convert MiB to bytes
					info.MemFree = free * 1024 * 1024
				}
			}
		}
	} else {
		logger.Error("Failed to get GPU memory info: %v", err)
	}

	// Get GPU utilization
	utilCmd := exec.Command("nvidia-smi", "--query-gpu=utilization.gpu", "--format=csv,noheader")
	var utilOut bytes.Buffer
	utilCmd.Stdout = &utilOut
	if err := utilCmd.Run(); err == nil {
		utilInfo := strings.TrimSpace(utilOut.String())
		parts := strings.Fields(utilInfo)
		if len(parts) == 2 && parts[1] == "%" {
			if util, err := strconv.ParseFloat(parts[0], 64); err == nil {
				info.Usage = util
			}
		}
	} else {
		logger.Error("Failed to get GPU utilization: %v", err)
	}

	// Get GPU temperature
	tempCmd := exec.Command("nvidia-smi", "--query-gpu=temperature.gpu", "--format=csv,noheader")
	var tempOut bytes.Buffer
	tempCmd.Stdout = &tempOut
	if err := tempCmd.Run(); err == nil {
		tempInfo := strings.TrimSpace(tempOut.String())
		if temp, err := strconv.ParseFloat(tempInfo, 64); err == nil {
			info.Temp = temp
		}
	} else {
		logger.Error("Failed to get GPU temperature: %v", err)
	}

	return info
}
