package traffic_blocker

import (
	"database/sql"
	"fmt"
	"net/url"
	"os/exec"
	"showfer-web/models"
	"showfer-web/repository"
	"showfer-web/service/logger"
	"showfer-web/utils"
	"strconv"
	"strings"
	"sync"
)

// TrafficBlocker manages iptables rules to block/unblock stream traffic
type TrafficBlocker struct {
	db          *sql.DB
	mutex       sync.RWMutex
	isBlocking  bool
	blockedURLs []string
}

var (
	instance *TrafficBlocker
	once     sync.Once
)

// GetTrafficBlocker returns the singleton instance
func GetTrafficBlocker(db *sql.DB) *TrafficBlocker {
	once.Do(func() {
		instance = &TrafficBlocker{
			db:          db,
			isBlocking:  false,
			blockedURLs: make([]string, 0),
		}
		
		// Auto-block if this is a backup server
		instance.checkAndBlockTraffic()
	})
	return instance
}

// checkAndBlockTraffic blocks traffic if server is backup
func (tb *TrafficBlocker) checkAndBlockTraffic() {
	if utils.GetServerType() == "backup" {
		tb.BlockAllStreamTraffic()
	}
}

// BlockAllStreamTraffic blocks all scheduler output traffic
func (tb *TrafficBlocker) BlockAllStreamTraffic() error {
	tb.mutex.Lock()
	defer tb.mutex.Unlock()

	if tb.isBlocking {
		logger.Log("Traffic blocker: Traffic already blocked")
		return nil
	}

	// Get all scheduler output URLs
	outputURLs, err := tb.getAllSchedulerOutputs()
	if err != nil {
		return fmt.Errorf("failed to get scheduler outputs: %v", err)
	}

	logger.Log("Traffic blocker: Blocking %d stream outputs for backup server", len(outputURLs))

	// Block each output URL
	for _, outputURL := range outputURLs {
		if err := tb.blockSingleOutput(outputURL); err != nil {
			logger.Error("Traffic blocker: Failed to block %s: %v", outputURL, err)
			continue
		}
		tb.blockedURLs = append(tb.blockedURLs, outputURL)
	}

	tb.isBlocking = true
	logger.Log("Traffic blocker: Successfully blocked all stream traffic")
	return nil
}

// UnblockAllStreamTraffic removes all traffic blocking rules
func (tb *TrafficBlocker) UnblockAllStreamTraffic() error {
	tb.mutex.Lock()
	defer tb.mutex.Unlock()

	if !tb.isBlocking {
		logger.Log("Traffic blocker: Traffic not currently blocked")
		return nil
	}

	logger.Log("Traffic blocker: Unblocking %d stream outputs", len(tb.blockedURLs))

	// Unblock each output URL
	for _, outputURL := range tb.blockedURLs {
		if err := tb.unblockSingleOutput(outputURL); err != nil {
			logger.Error("Traffic blocker: Failed to unblock %s: %v", outputURL, err)
		}
	}

	tb.blockedURLs = make([]string, 0)
	tb.isBlocking = false
	logger.Log("Traffic blocker: Successfully unblocked all stream traffic")
	return nil
}

// getAllSchedulerOutputs gets all output URLs from schedules
func (tb *TrafficBlocker) getAllSchedulerOutputs() ([]string, error) {
	scheduleRepo := repository.NewScheduleRepository(tb.db)
	
	// Use pagination to get all schedules (set high limit to get all)
	pagination := models.Pagination{
		Page:  1,
		Limit: 1000, // High limit to get all schedules
	}
	
	scheduleList, err := scheduleRepo.ListSchedules(pagination)
	if err != nil {
		return nil, fmt.Errorf("failed to get schedules: %v", err)
	}

	var outputURLs []string
	for _, schedule := range scheduleList.Items {
		if schedule.OutputUrl != "" {
			outputURLs = append(outputURLs, schedule.OutputUrl)
		}
	}

	return outputURLs, nil
}

// blockSingleOutput blocks traffic for a single output URL
func (tb *TrafficBlocker) blockSingleOutput(outputURL string) error {
	u, err := url.Parse(outputURL)
	if err != nil {
		return fmt.Errorf("failed to parse URL %s: %v", outputURL, err)
	}

	host := u.Hostname()
	port, err := strconv.Atoi(u.Port())
	if err != nil {
		return fmt.Errorf("failed to parse port from URL %s: %v", outputURL, err)
	}

	// Check if it's a multicast address
	isMulticast := tb.isMulticastAddress(host)

	var cmd *exec.Cmd
	if isMulticast {
		// Block multicast OUTPUT traffic (preventing transmission)
		cmd = exec.Command("iptables", "-A", "OUTPUT", "-d", host, "-p", "udp", "--dport", strconv.Itoa(port), "-j", "DROP")
	} else {
		// Block unicast OUTPUT traffic
		cmd = exec.Command("iptables", "-A", "OUTPUT", "-d", host, "-p", "udp", "--dport", strconv.Itoa(port), "-j", "DROP")
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("iptables command failed: %v, output: %s", err, string(output))
	}

	logger.Log("Traffic blocker: Blocked %s traffic to %s:%d", 
		map[bool]string{true: "multicast", false: "unicast"}[isMulticast], host, port)
	return nil
}

// unblockSingleOutput removes blocking rule for a single output URL
func (tb *TrafficBlocker) unblockSingleOutput(outputURL string) error {
	u, err := url.Parse(outputURL)
	if err != nil {
		return fmt.Errorf("failed to parse URL %s: %v", outputURL, err)
	}

	host := u.Hostname()
	port, err := strconv.Atoi(u.Port())
	if err != nil {
		return fmt.Errorf("failed to parse port from URL %s: %v", outputURL, err)
	}

	// Check if it's a multicast address
	isMulticast := tb.isMulticastAddress(host)

	var cmd *exec.Cmd
	if isMulticast {
		// Remove multicast OUTPUT blocking rule
		cmd = exec.Command("iptables", "-D", "OUTPUT", "-d", host, "-p", "udp", "--dport", strconv.Itoa(port), "-j", "DROP")
	} else {
		// Remove unicast OUTPUT blocking rule
		cmd = exec.Command("iptables", "-D", "OUTPUT", "-d", host, "-p", "udp", "--dport", strconv.Itoa(port), "-j", "DROP")
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		// Don't error if rule doesn't exist (may have been manually removed)
		if strings.Contains(string(output), "No chain/target/match by that name") ||
		   strings.Contains(string(output), "Bad rule") {
			logger.Log("Traffic blocker: Rule for %s:%d already removed", host, port)
			return nil
		}
		return fmt.Errorf("iptables command failed: %v, output: %s", err, string(output))
	}

	logger.Log("Traffic blocker: Unblocked %s traffic to %s:%d", 
		map[bool]string{true: "multicast", false: "unicast"}[isMulticast], host, port)
	return nil
}

// isMulticastAddress checks if an IP address is multicast
func (tb *TrafficBlocker) isMulticastAddress(ip string) bool {
	// IPv4 multicast: ********* to ***************
	return strings.HasPrefix(ip, "224.") || 
		   strings.HasPrefix(ip, "225.") || 
		   strings.HasPrefix(ip, "226.") || 
		   strings.HasPrefix(ip, "227.") || 
		   strings.HasPrefix(ip, "228.") || 
		   strings.HasPrefix(ip, "229.") || 
		   strings.HasPrefix(ip, "230.") || 
		   strings.HasPrefix(ip, "231.") || 
		   strings.HasPrefix(ip, "232.") || 
		   strings.HasPrefix(ip, "233.") || 
		   strings.HasPrefix(ip, "234.") || 
		   strings.HasPrefix(ip, "235.") || 
		   strings.HasPrefix(ip, "236.") || 
		   strings.HasPrefix(ip, "237.") || 
		   strings.HasPrefix(ip, "238.") || 
		   strings.HasPrefix(ip, "239.")
}

// OnServerTypeChange handles server type changes (backup -> primary or vice versa)
func (tb *TrafficBlocker) OnServerTypeChange(newServerType string) {
	logger.Log("Traffic blocker: Server type changed to %s, updating traffic rules", newServerType)
	
	if newServerType == "backup" {
		if err := tb.BlockAllStreamTraffic(); err != nil {
			logger.Error("Traffic blocker: Failed to block traffic after role change: %v", err)
		}
	} else if newServerType == "primary" {
		if err := tb.UnblockAllStreamTraffic(); err != nil {
			logger.Error("Traffic blocker: Failed to unblock traffic after role change: %v", err)
		}
	}
}

// GetStatus returns the current blocking status
func (tb *TrafficBlocker) GetStatus() map[string]interface{} {
	tb.mutex.RLock()
	defer tb.mutex.RUnlock()

	return map[string]interface{}{
		"is_blocking":   tb.isBlocking,
		"blocked_count": len(tb.blockedURLs),
		"blocked_urls":  tb.blockedURLs,
		"server_type":   utils.GetServerType(),
	}
}

// RefreshBlocking re-applies blocking rules (useful after schedule changes)
func (tb *TrafficBlocker) RefreshBlocking() error {
	if utils.GetServerType() != "backup" {
		return nil
	}

	// First unblock all current rules
	if tb.isBlocking {
		if err := tb.UnblockAllStreamTraffic(); err != nil {
			logger.Error("Traffic blocker: Failed to unblock during refresh: %v", err)
		}
	}

	// Then reapply blocking with current schedules
	return tb.BlockAllStreamTraffic()
} 