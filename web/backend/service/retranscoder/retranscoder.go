package retranscoder

import (
	"fmt"
	"os"
	"path/filepath"
	"showfer-web/models"
	"showfer-web/repository"
	"showfer-web/service/interfaces"
	"showfer-web/service/logger"
	"strings"
	"sync"
	"time"
)

// RetranscoderService handles retranscoding operations (memory-based)
type RetranscoderService struct {
	filesRepo         *repository.FilesRepository
	codecSettingsRepo *repository.CodecSettingsRepository
	queueManager      interfaces.QueueManagerInterface
	baseDir           string
	isProcessing      bool
	retranscodeJobs   map[int64]*models.RetranscodeJob // In-memory storage
	jobIDCounter      int64
	mutex             sync.Mutex
	statusUpdateChan  chan RetranscodeStatusUpdate
	clients           map[*Client]bool
	clientsMux        sync.Mutex
}

// Client represents a connected WebSocket client for retranscoding updates
type Client struct {
	ID   string
	Send chan RetranscodeStatusUpdate
}

// RetranscodeStatusUpdate represents a status update for retranscoding
type RetranscodeStatusUpdate struct {
	Type           string                    `json:"type"` // "job_update" or "status_update"
	Job            *models.RetranscodeJob    `json:"job,omitempty"`
	Status         *models.RetranscodeStatus `json:"status,omitempty"`
	Progress       int                       `json:"progress,omitempty"`
	CurrentFileID  int64                     `json:"current_file_id,omitempty"`
	Message        string                    `json:"message,omitempty"`
}

var retranscoderInstance *RetranscoderService
var retranscoderOnce sync.Once

// GetRetranscoderService returns the singleton instance of RetranscoderService
func GetRetranscoderService() *RetranscoderService {
	retranscoderOnce.Do(func() {
		retranscoderInstance = &RetranscoderService{
			isProcessing:     false,
			retranscodeJobs:  make(map[int64]*models.RetranscodeJob),
			jobIDCounter:     1,
			statusUpdateChan: make(chan RetranscodeStatusUpdate, 100),
			clients:          make(map[*Client]bool),
		}
		// Start the broadcaster
		go retranscoderInstance.broadcaster()
	})
	return retranscoderInstance
}

// Init initializes the retranscoder service
func (r *RetranscoderService) Init(
	filesRepo *repository.FilesRepository,
	codecSettingsRepo *repository.CodecSettingsRepository,
	queueManager interfaces.QueueManagerInterface,
	baseDir string,
) {
	r.filesRepo = filesRepo
	r.codecSettingsRepo = codecSettingsRepo
	r.queueManager = queueManager
	r.baseDir = baseDir
	
	logger.Log("-------------- Debug: Retranscoder service initialized (memory-based) -----------------")
}

// broadcaster handles broadcasting status updates to all connected clients
func (r *RetranscoderService) broadcaster() {
	for {
		update := <-r.statusUpdateChan
		
		r.clientsMux.Lock()
		for client := range r.clients {
			select {
			case client.Send <- update:
				// Message sent successfully
			default:
				// Client is not receiving messages, remove it
				close(client.Send)
				delete(r.clients, client)
			}
		}
		r.clientsMux.Unlock()
	}
}

// RegisterClient registers a new WebSocket client
func (r *RetranscoderService) RegisterClient(client *Client) {
	r.clientsMux.Lock()
	defer r.clientsMux.Unlock()
	r.clients[client] = true
	logger.Log("-------------- Debug: Registered retranscode client %s -----------------", client.ID)
}

// UnregisterClient unregisters a WebSocket client
func (r *RetranscoderService) UnregisterClient(client *Client) {
	r.clientsMux.Lock()
	defer r.clientsMux.Unlock()
	if _, ok := r.clients[client]; ok {
		close(client.Send)
		delete(r.clients, client)
		logger.Log("-------------- Debug: Unregistered retranscode client %s -----------------", client.ID)
	}
}

// createRetranscodeJob creates a new retranscode job in memory
func (r *RetranscoderService) createRetranscodeJob(fileID int64) *models.RetranscodeJob {
	now := time.Now()
	job := &models.RetranscodeJob{
		ID:        r.jobIDCounter,
		FileID:    fileID,
		Status:    models.RetranscodeStatusPending,
		Progress:  0,
		CreatedAt: now,
		UpdatedAt: now,
	}
	r.retranscodeJobs[fileID] = job
	r.jobIDCounter++
	return job
}

// StartRetranscoding starts the retranscoding process for all files
func (r *RetranscoderService) StartRetranscoding() error {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	
	if r.isProcessing {
		return fmt.Errorf("retranscoding is already in progress")
	}
	
	logger.Log("-------------- Debug: Starting retranscoding for all files -----------------")
	
	// Clear any existing completed/failed jobs from previous retranscode attempts
	r.clearCompletedJobs()
	
	// Clear the queue to ensure no stale items are blocking new retranscode jobs
	r.queueManager.ClearQueue()
	
	// Get all files with status Success (0)
	pagination := models.Pagination{Page: 1, Limit: 10000}
	result, err := r.filesRepo.ListConvertItems(pagination)
	if err != nil {
		logger.Error("-------------- Debug: Failed to get files for retranscoding: %v -----------------", err)
		return fmt.Errorf("failed to get files: %v", err)
	}
	
	// Filter files that need retranscoding (only successful files)
	var filesToRetranscode []models.ConvertItem
	for _, file := range result.Items {
		if file.Status == int(models.FileStatusSuccess) {
			// Always allow retranscoding of successful files in a new attempt
			filesToRetranscode = append(filesToRetranscode, file)
		}
	}
	
	if len(filesToRetranscode) == 0 {
		logger.Log("-------------- Debug: No files found for retranscoding -----------------")
		return fmt.Errorf("no files found for retranscoding")
	}
	
	logger.Log("-------------- Debug: Found %d files for retranscoding -----------------", len(filesToRetranscode))
	
	// Create ALL retranscode jobs at once to avoid incremental count updates
	var successfulJobs []models.ConvertItem
	for _, file := range filesToRetranscode {
		// Create retranscode job in memory
		r.createRetranscodeJob(file.ID)
		
		// Create backup of the original file
		err = r.createBackupForRetranscoding(file)
		if err != nil {
			logger.Error("-------------- Debug: Failed to create backup for file %d: %v -----------------", file.ID, err)
			// Mark job as failed
			if job, exists := r.retranscodeJobs[file.ID]; exists {
				job.Status = models.RetranscodeStatusFailed
				job.UpdatedAt = time.Now()
			}
			continue
		}
		
		logger.Log("-------------- Debug: Created retranscode job and backup for file %d (%s) -----------------", file.ID, file.Filename)
		successfulJobs = append(successfulJobs, file)
	}
	
	// Set processing flag and send initial status update BEFORE adding to queue
	r.isProcessing = true
	r.broadcastStatusUpdate()
	
	// Now add all successful jobs to the queue
	for _, file := range successfulJobs {
		// Add file to the existing queue manager for transcoding
		r.queueManager.AddToQueue(file.ID)
	}
	
	logger.Log("-------------- Debug: Added %d files to transcoding queue -----------------", len(successfulJobs))
	
	return nil
}

// clearCompletedJobs removes completed and failed jobs from memory (helper function)
func (r *RetranscoderService) clearCompletedJobs() {
	for fileID, job := range r.retranscodeJobs {
		if job.Status == models.RetranscodeStatusCompleted || job.Status == models.RetranscodeStatusFailed {
			logger.Log("-------------- Debug: Clearing old retranscode job for file %d (status: %s) -----------------", fileID, job.Status)
			delete(r.retranscodeJobs, fileID)
		}
	}
}

// createBackupForRetranscoding creates a backup of the original file for retranscoding
func (r *RetranscoderService) createBackupForRetranscoding(file models.ConvertItem) error {
	// Get the current file path
	originalFilePath := filepath.Join(r.baseDir, file.Location, file.Filename)
	
	// Create backup path with _original suffix
	fileExt := filepath.Ext(file.Filename)
	fileNameWithoutExt := strings.TrimSuffix(file.Filename, fileExt)
	backupFileName := fmt.Sprintf("%s_original%s", fileNameWithoutExt, fileExt)
	backupFilePath := filepath.Join(r.baseDir, file.Location, backupFileName)
	
	// Check if backup already exists
	if _, err := os.Stat(backupFilePath); err == nil {
		logger.Log("-------------- Debug: Backup already exists for file %d, skipping -----------------", file.ID)
		return nil
	}
	
	// Create backup of original file
	err := r.copyFile(originalFilePath, backupFilePath)
	if err != nil {
		return fmt.Errorf("failed to create backup: %v", err)
	}
	
	logger.Log("-------------- Debug: Created backup file %s -----------------", backupFilePath)
	return nil
}

// OnTranscodingStarted should be called when a retranscode job starts processing by the QueueManager
func (r *RetranscoderService) OnTranscodingStarted(fileID int64) {
	logger.Log("-------------- Debug: Retranscoding started for file %d -----------------", fileID)
	
	r.mutex.Lock()
	defer r.mutex.Unlock()
	
	// Get the retranscode job for this file
	job, exists := r.retranscodeJobs[fileID]
	if !exists {
		// This file is not being retranscoded, ignore
		logger.Log("-------------- Debug: No retranscode job found for file %d, ignoring start notification -----------------", fileID)
		return
	}
	
	// Only update if the job is currently pending
	if job.Status == models.RetranscodeStatusPending {
		job.Status = models.RetranscodeStatusProcessing
		job.Progress = 0
		now := time.Now()
		job.StartedAt = &now
		job.UpdatedAt = now
		
		r.broadcastJobUpdate(job, 0, "Retranscoding started")
		logger.Log("-------------- Debug: Updated retranscode job %d to processing status -----------------", fileID)
		
		// Broadcast overall status update
		r.broadcastStatusUpdate()
	} else {
		logger.Log("-------------- Debug: Retranscode job %d is not in pending state (current: %s), skipping start notification -----------------", fileID, job.Status)
	}
}

// OnTranscodingCompleted should be called when a retranscode job is completed by the QueueManager
func (r *RetranscoderService) OnTranscodingCompleted(fileID int64, success bool) {
	logger.Log("-------------- Debug: Retranscoding completion notification for file %d, success: %v -----------------", fileID, success)
	
	r.mutex.Lock()
	defer r.mutex.Unlock()
	
	// Get the retranscode job for this file
	job, exists := r.retranscodeJobs[fileID]
	if !exists {
		// This file is not being retranscoded, ignore
		return
	}
	
	if success {
		// Mark job as completed
		job.Status = models.RetranscodeStatusCompleted
		job.Progress = 100
		now := time.Now()
		job.CompletedAt = &now
		job.UpdatedAt = now
		
		r.broadcastJobUpdate(job, 100, "Retranscoding completed successfully")
		
		// Clean up backup file
		file, err := r.filesRepo.GetConvertItemById(fileID)
		if err == nil {
			fileExt := filepath.Ext(file.Filename)
			fileNameWithoutExt := strings.TrimSuffix(file.Filename, fileExt)
			backupFileName := fmt.Sprintf("%s_original%s", fileNameWithoutExt, fileExt)
			backupFilePath := filepath.Join(r.baseDir, file.Location, backupFileName)
			
			os.Remove(backupFilePath) // Clean up backup, ignore errors
			logger.Log("-------------- Debug: Cleaned up backup file for file %d -----------------", fileID)
		}
		
		logger.Log("-------------- Debug: Retranscoding completed successfully for file %d -----------------", fileID)
	} else {
		// Restore from backup if transcoding failed
		file, err := r.filesRepo.GetConvertItemById(fileID)
		if err == nil {
			originalFilePath := filepath.Join(r.baseDir, file.Location, file.Filename)
			fileExt := filepath.Ext(file.Filename)
			fileNameWithoutExt := strings.TrimSuffix(file.Filename, fileExt)
			backupFileName := fmt.Sprintf("%s_original%s", fileNameWithoutExt, fileExt)
			backupFilePath := filepath.Join(r.baseDir, file.Location, backupFileName)
			
			// Restore original file from backup
			if _, err := os.Stat(backupFilePath); err == nil {
				os.Remove(originalFilePath) // Remove potentially corrupted file
				r.copyFile(backupFilePath, originalFilePath) // Restore from backup
				os.Remove(backupFilePath) // Clean up backup
				
				logger.Log("-------------- Debug: Restored original file from backup for file %d -----------------", fileID)
			} else {
				logger.Error("-------------- Debug: Backup file not found for file %d, cannot restore -----------------", fileID)
			}
		}
		
		// Mark job as failed
		job.Status = models.RetranscodeStatusFailed
		job.Progress = 0
		now := time.Now()
		job.CompletedAt = &now // Set completion time even for failed jobs
		job.UpdatedAt = now
		
		r.broadcastJobUpdate(job, 0, "Retranscoding failed")
		
		logger.Error("-------------- Debug: Retranscoding failed for file %d -----------------", fileID)
	}
	
	// Always check if all retranscode jobs are completed, regardless of success/failure
	r.checkRetranscodingCompletion()
	
	// Broadcast overall status update
	r.broadcastStatusUpdate()
}

// checkRetranscodingCompletion checks if all retranscode jobs are completed and updates the processing status
func (r *RetranscoderService) checkRetranscodingCompletion() {
	pendingCount := 0
	processingCount := 0
	
	for _, job := range r.retranscodeJobs {
		if job.Status == models.RetranscodeStatusPending {
			pendingCount++
		} else if job.Status == models.RetranscodeStatusProcessing {
			processingCount++
		}
	}
	
	logger.Log("-------------- Debug: Checking completion - Pending: %d, Processing: %d -----------------", pendingCount, processingCount)
	
	if pendingCount == 0 && processingCount == 0 {
		r.isProcessing = false
		logger.Log("-------------- Debug: All retranscode jobs completed -----------------")
		r.broadcastStatusUpdate()
	}
}

// copyFile copies a file from src to dst
func (r *RetranscoderService) copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()
	
	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()
	
	// Copy file content
	_, err = destFile.ReadFrom(sourceFile)
	if err != nil {
		return err
	}
	
	// Sync to ensure data is written
	return destFile.Sync()
}

// broadcastJobUpdate broadcasts a job-specific update
func (r *RetranscoderService) broadcastJobUpdate(job *models.RetranscodeJob, progress int, message string) {
	update := RetranscodeStatusUpdate{
		Type:          "job_update",
		Job:           job,
		Progress:      progress,
		CurrentFileID: job.FileID,
		Message:       message,
	}
	
	select {
	case r.statusUpdateChan <- update:
	default:
		logger.Error("-------------- Debug: Status update channel is full -----------------")
	}
}

// broadcastStatusUpdate broadcasts the overall status
func (r *RetranscoderService) broadcastStatusUpdate() {
	status := r.getRetranscodeStatus()
	
	update := RetranscodeStatusUpdate{
		Type:   "status_update",
		Status: status,
	}
	
	select {
	case r.statusUpdateChan <- update:
	default:
		logger.Error("-------------- Debug: Status update channel is full -----------------")
	}
}

// getRetranscodeStatus gets the current retranscoding status from memory
func (r *RetranscoderService) getRetranscodeStatus() *models.RetranscodeStatus {
	var status models.RetranscodeStatus
	
	// Initialize with default values
	status.TotalJobs = 0
	status.CompletedJobs = 0
	status.FailedJobs = 0
	status.ProcessingJobs = 0
	status.PendingJobs = 0
	status.OverallProgress = 0
	status.IsActive = false
	
	var currentJob *models.RetranscodeJob
	for _, job := range r.retranscodeJobs {
		status.TotalJobs++
		switch job.Status {
		case models.RetranscodeStatusCompleted:
			status.CompletedJobs++
		case models.RetranscodeStatusFailed:
			status.FailedJobs++
		case models.RetranscodeStatusProcessing:
			status.ProcessingJobs++
			// Set the first processing job as current job for display
			if currentJob == nil {
				currentJob = job
			}
		case models.RetranscodeStatusPending:
			status.PendingJobs++
		}
	}
	
	// Calculate overall progress - include both completed and failed jobs in progress
	if status.TotalJobs > 0 {
		status.OverallProgress = ((status.CompletedJobs + status.FailedJobs) * 100) / status.TotalJobs
	}
	
	// Check if retranscoding is active
	status.IsActive = status.ProcessingJobs > 0 || status.PendingJobs > 0
	
	// Add current job info if available
	if currentJob != nil {
		// Get file information for the current job
		if file, err := r.filesRepo.GetConvertItemById(currentJob.FileID); err == nil {
			status.CurrentJob = &models.CurrentJobInfo{
				FileID:   currentJob.FileID,
				Filename: file.Filename,
				Progress: currentJob.Progress,
			}
		}
	}
	
	logger.Log("-------------- Debug: Current retranscode status - Total: %d, Completed: %d, Failed: %d, Processing: %d, Pending: %d, Active: %v -----------------", 
		status.TotalJobs, status.CompletedJobs, status.FailedJobs, status.ProcessingJobs, status.PendingJobs, status.IsActive)
	
	return &status
}

// GetRetranscodeStatus gets the current retranscoding status
func (r *RetranscoderService) GetRetranscodeStatus() (*models.RetranscodeStatus, error) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	return r.getRetranscodeStatus(), nil
}

// GetRetranscodeJobs gets all retranscode jobs
func (r *RetranscoderService) GetRetranscodeJobs() ([]models.RetranscodeJob, error) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	
	// Initialize with empty slice to ensure we never return nil
	jobs := make([]models.RetranscodeJob, 0, len(r.retranscodeJobs))
	for _, job := range r.retranscodeJobs {
		jobs = append(jobs, *job)
	}
	
	return jobs, nil
}

// ActivateNewCodecSettings activates the new codec settings by cleaning up retranscode jobs
func (r *RetranscoderService) ActivateNewCodecSettings() error {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	
	logger.Log("-------------- Debug: Activating new codec settings -----------------")
	
	// Clear completed and failed jobs from memory
	r.clearCompletedJobs()
	
	logger.Log("-------------- Debug: Cleaned up completed retranscode jobs -----------------")
	
	// Send status update
	r.broadcastStatusUpdate()
	
	return nil
}

// IsRetranscodingActive checks if retranscoding is currently active
func (r *RetranscoderService) IsRetranscodingActive() bool {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	return r.isProcessing
}

// GetRetranscodeJobByFileID gets a retranscode job by file ID from memory
func (r *RetranscoderService) GetRetranscodeJobByFileID(fileID int64) *models.RetranscodeJob {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	
	if job, exists := r.retranscodeJobs[fileID]; exists {
		return job
	}
	return nil
} 