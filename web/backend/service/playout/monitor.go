package playout

import (
	"fmt"
	"net"
	"showfer-web/models"
	"showfer-web/repository"
	"showfer-web/service/logger"
	"sync"
	"time"
)

type RTPMonitor struct {
	playout          *Playout
	monitoredStreams map[string]*StreamStatus
	mutex            sync.Mutex
	stopChan         chan struct{}
	running          bool
	checkInterval    time.Duration
	errorMutex       sync.Mutex
	errorStreams     map[string]time.Time
}

type StreamStatus struct {
	shortID      string
	host         string
	port         int
	lastActive   time.Time
	failureCount int
	conn         *net.UDPConn
}

func NewRTPMonitor(playout *Playout) *RTPMonitor {
	return &RTPMonitor{
		playout:          playout,
		monitoredStreams: make(map[string]*StreamStatus),
		stopChan:         make(chan struct{}),
		checkInterval:    time.Second * 10, // Check every 10 seconds
		errorStreams:     make(map[string]time.Time),
	}
}

func (m *RTPMonitor) Start() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.running {
		return fmt.Errorf("RTP monitor is already running")
	}

	m.running = true

	go m.monitorLoop()

	// Start debug status printer
	go func() {
		ticker := time.NewTicker(time.Minute)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				m.DebugStatus()
			case <-m.stopChan:
				return
			}
		}
	}()

	logger.Log("RTP output monitor started")
	return nil
}

func (m *RTPMonitor) Stop() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if !m.running {
		return
	}

	close(m.stopChan)
	m.running = false

	// Close all UDP connections
	for _, status := range m.monitoredStreams {
		if status.conn != nil {
			status.conn.Close()
		}
	}

	logger.Log("RTP output monitor stopped")
}

func (m *RTPMonitor) RegisterStream(shortID string, host string, port int) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if _, exists := m.monitoredStreams[shortID]; exists {
		// Close existing connection if any
		if m.monitoredStreams[shortID].conn != nil {
			m.monitoredStreams[shortID].conn.Close()
		}
	}

	// Create UDP connection for monitoring
	conn, err := createUDPListener(host, port)
	if err != nil {
		logger.Error("Failed to create UDP listener for RTP monitoring %s: %v", shortID, err)
		// Still register the stream without a connection, we'll try to reconnect later
	}

	m.monitoredStreams[shortID] = &StreamStatus{
		shortID:      shortID,
		host:         host,
		port:         port,
		lastActive:   time.Now(), // Give it initial time to start up
		failureCount: 0,
		conn:         conn,
	}

	logger.Log("Registered RTP stream for monitoring: %s (%s:%d)", shortID, host, port)
}

func (m *RTPMonitor) UnregisterStream(shortID string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if status, exists := m.monitoredStreams[shortID]; exists {
		if status.conn != nil {
			status.conn.Close()
		}
		delete(m.monitoredStreams, shortID)
		logger.Log("Unregistered RTP stream from monitoring: %s", shortID)
	}
}

func (m *RTPMonitor) monitorLoop() {
	ticker := time.NewTicker(m.checkInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			m.checkAllStreams()
		case <-m.stopChan:
			return
		}
	}
}

func (m *RTPMonitor) checkAllStreams() {
	m.mutex.Lock()
	streamsCopy := make(map[string]*StreamStatus)
	for k, v := range m.monitoredStreams {
		streamsCopy[k] = v
	}
	m.mutex.Unlock()

	for shortID, status := range streamsCopy {
		isActive := m.checkStreamActivity(status)

		m.mutex.Lock()
		// Make sure the stream is still being monitored
		if currentStatus, exists := m.monitoredStreams[shortID]; exists {
			if isActive {
				currentStatus.lastActive = time.Now()
				currentStatus.failureCount = 0
				logger.Log("RTP stream %s is active", shortID)
			} else {
				timeSinceLastActive := time.Since(currentStatus.lastActive)
				currentStatus.failureCount++

				// If stream has been inactive for too long or accumulated too many failures
				if timeSinceLastActive > time.Minute || currentStatus.failureCount >= 3 {
					logger.Error("RTP stream %s is down (inactive for %v, failure count: %d). Attempting recovery...",
						shortID, timeSinceLastActive, currentStatus.failureCount)

					// We'll recover the stream outside the lock
					m.mutex.Unlock()
					m.recoverStream(shortID)
					m.mutex.Lock()
				} else {
					logger.Error("RTP stream %s appears inactive (failure count: %d)",
						shortID, currentStatus.failureCount)
				}
			}
		}
		m.mutex.Unlock()
	}
}

func (m *RTPMonitor) checkStreamActivity(status *StreamStatus) bool {
	// If we don't have a connection, try to establish one
	if status.conn == nil {
		conn, err := createUDPListener(status.host, status.port)
		if err != nil {
			logger.Error("Failed to create UDP listener for %s: %v", status.shortID, err)
			return false
		}

		m.mutex.Lock()
		// Update the connection if the stream is still being monitored
		if current, exists := m.monitoredStreams[status.shortID]; exists {
			current.conn = conn
		}
		m.mutex.Unlock()

		status.conn = conn
	}

	// Check if we're receiving data
	if status.conn != nil {
		status.conn.SetReadDeadline(time.Now().Add(500 * time.Millisecond))

		buffer := make([]byte, 1500) // Standard MTU size
		n, _, err := status.conn.ReadFromUDP(buffer)

		if err != nil {
			if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
				// This is expected if no data is received
				return false
			}

			logger.Error("Error reading from UDP for %s: %v", status.shortID, err)
			return false
		}

		return n > 0 // We received data
	}

	return false
}

func (m *RTPMonitor) recoverStream(shortID string) {
	// Get the schedule from the playout service
	m.playout.mutex.Lock()
	streamer, exists := m.playout.streamers[shortID]
	m.playout.mutex.Unlock()

	if !exists || streamer == nil {
		logger.Error("Cannot recover RTP stream %s: streamer not found", shortID)
		return
	}

	// Get the schedule details for this stream
	repo := repository.NewScheduleRepository(m.playout.db)
	schedules, err := repo.ListSchedules(models.Pagination{Limit: 100, Page: 1})
	if err != nil {
		logger.Error("Cannot recover RTP stream %s: failed to list schedules: %v", shortID, err)
		return
	}

	var schedule models.Schedule
	var alternativeSchedules []models.Schedule

	// First find the primary schedule
	for _, s := range schedules.Items {
		if s.ShortID == shortID {
			schedule = s
			break
		}
	}

	if schedule.ShortID == "" {
		logger.Error("Cannot recover RTP stream %s: schedule not found", shortID)
		return
	}

	// Now find alternative schedules with the same output URL
	for _, s := range schedules.Items {
		if s.ShortID != shortID && s.OutputUrl == schedule.OutputUrl {
			// Store alternative schedules with the same output URL for potential fallback
			alternativeSchedules = append(alternativeSchedules, s)
		}
	}

	// Check if this is the first recovery attempt for this stream
	m.errorMutex.Lock()
	_, alreadyInError := m.errorStreams[shortID]
	if !alreadyInError {
		// First recovery attempt - try with the original scheduler
		m.errorStreams[shortID] = time.Now()
		m.errorMutex.Unlock()

		logger.Log("First recovery attempt for RTP stream %s using original scheduler", shortID)

		// Stop the current stream
		err = m.playout.Stop(schedule)
		if err != nil {
			logger.Error("Failed to stop RTP stream %s for recovery: %v", shortID, err)
			// Still try to run it even if stopping fails
		}

		// Wait a moment for resources to be properly released
		time.Sleep(1 * time.Second)

		// Start the stream again with the original scheduler
		err = m.playout.Run(schedule)
		if err != nil {
			logger.Error("Failed to recover RTP stream %s: %v", shortID, err)
		} else {
			logger.Log("Successfully recovered RTP stream %s using original scheduler", shortID)

			// Reset the status for this stream
			m.mutex.Lock()
			if status, ok := m.monitoredStreams[shortID]; ok {
				status.lastActive = time.Now()
				status.failureCount = 0
			}
			m.mutex.Unlock()
		}
	} else {
		// This is a subsequent failure - try alternative scheduler if available
		recoverySince := time.Since(m.errorStreams[shortID])
		m.errorMutex.Unlock()

		if len(alternativeSchedules) > 0 {
			// Find the first alternative schedule
			alternativeSchedule := alternativeSchedules[0]
			logger.Log("Second recovery attempt for RTP stream %s - switching to alternative scheduler %s after %v of instability",
				shortID, alternativeSchedule.ShortID, recoverySince)

			// Stop the current failed stream
			err = m.playout.Stop(schedule)
			if err != nil {
				logger.Error("Failed to stop unstable RTP stream %s for failover: %v", shortID, err)
				// Continue with fallback regardless
			}

			// Wait a moment for resources to be properly released
			time.Sleep(1 * time.Second)

			// Start the alternative stream
			err = m.playout.Run(alternativeSchedule)
			if err != nil {
				logger.Error("Failed to start alternative RTP stream %s: %v", alternativeSchedule.ShortID, err)
			} else {
				logger.Log("Successfully failed over to alternative RTP stream %s", alternativeSchedule.ShortID)

				// Reset error state since we've switched to an alternative
				m.errorMutex.Lock()
				delete(m.errorStreams, shortID)
				m.errorMutex.Unlock()

				// Reset monitoring for the original stream
				m.mutex.Lock()
				if status, ok := m.monitoredStreams[shortID]; ok {
					// We'll unregister this stream since we've switched to an alternative
					if status.conn != nil {
						status.conn.Close()
					}
					delete(m.monitoredStreams, shortID)
					logger.Log("Unregistered unstable RTP stream %s after failover", shortID)
				}
				m.mutex.Unlock()
			}
		} else {
			// No alternative available, try original again with a warning
			logger.Error("No alternative schedulers available for %s - attempting recovery with original scheduler", shortID)

			// Stop the current stream
			err = m.playout.Stop(schedule)
			if err != nil {
				logger.Error("Failed to stop RTP stream %s for retry recovery: %v", shortID, err)
			}

			// Wait a moment for resources to be properly released
			time.Sleep(1 * time.Second)

			// Start the stream again
			err = m.playout.Run(schedule)
			if err != nil {
				logger.Error("Failed to recover RTP stream %s on retry: %v", shortID, err)
			} else {
				logger.Log("Successfully recovered RTP stream %s on retry", shortID)

				// Reset the status for this stream
				m.mutex.Lock()
				if status, ok := m.monitoredStreams[shortID]; ok {
					status.lastActive = time.Now()
					status.failureCount = 0
				}
				m.mutex.Unlock()

				// Reset error state after successful recovery
				m.errorMutex.Lock()
				delete(m.errorStreams, shortID)
				m.errorMutex.Unlock()
			}
		}
	}
}

// CheckNetworkConnectivity tries to establish a UDP connection to test network connectivity
func (m *RTPMonitor) CheckNetworkConnectivity(host string, port int) bool {
	ip := net.ParseIP(host)
	if ip == nil {
		return false
	}

	addr := &net.UDPAddr{
		IP:   ip,
		Port: port,
	}

	// Try to dial the address
	conn, err := net.DialUDP("udp", nil, addr)
	if err != nil {
		return false
	}
	defer conn.Close()

	return true
}

// IsStreamActive checks if a stream with the given shortID is active
func (m *RTPMonitor) IsStreamActive(shortID string) bool {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	status, exists := m.monitoredStreams[shortID]
	if !exists {
		return false
	}

	// Consider a stream active if it has been active recently and has no failures
	timeSinceLastActive := time.Since(status.lastActive)
	return timeSinceLastActive < time.Minute && status.failureCount == 0
}

// DebugStatus prints the current status of all monitored streams
func (m *RTPMonitor) DebugStatus() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	for id, status := range m.monitoredStreams {
		timeSinceActive := time.Since(status.lastActive)
		logger.Log("Stream %s: host=%s:%d, last active=%v ago, failure count=%d",
			id, status.host, status.port, timeSinceActive, status.failureCount)

		// Check if this stream is in error state
		m.errorMutex.Lock()
		errorTime, isInError := m.errorStreams[id]
		m.errorMutex.Unlock()

		if isInError {
			logger.Log("  - Stream %s is in error state since %v ago",
				id, time.Since(errorTime))
		}

		// Verify if we can receive packets
		conn := status.conn
		if conn != nil {
			conn.SetReadDeadline(time.Now().Add(200 * time.Millisecond))
			buffer := make([]byte, 1500)
			n, _, err := conn.ReadFromUDP(buffer)
			if err != nil {
				if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
					logger.Log("  - UDP read test: No packets received (timeout)")
				} else {
					logger.Log("  - UDP read test: Error: %v", err)
				}
			} else {
				logger.Log("  - UDP read test: Received %d bytes", n)
			}
		} else {
			logger.Log("  - UDP connection not established")
		}

		// Check connectivity
		netOK := m.CheckNetworkConnectivity(status.host, status.port)
		logger.Log("  - Network connectivity test: %t", netOK)
	}

	logger.Log("========================")
}

func createUDPListener(host string, port int) (*net.UDPConn, error) {
	// For multicast addresses, we need to handle joining the multicast group
	ip := net.ParseIP(host)
	if ip == nil {
		return nil, fmt.Errorf("invalid IP address: %s", host)
	}

	addr := &net.UDPAddr{
		IP:   ip,
		Port: port,
	}

	if ip.IsMulticast() {
		// For multicast, we need to use ListenMulticastUDP
		interfaces, err := net.Interfaces()
		if err != nil {
			return nil, fmt.Errorf("failed to get network interfaces: %v", err)
		}

		// Try each interface until one works
		for _, intf := range interfaces {
			// Skip loopback and down interfaces
			if intf.Flags&net.FlagUp == 0 || intf.Flags&net.FlagLoopback != 0 {
				continue
			}

			conn, err := net.ListenMulticastUDP("udp", &intf, addr)
			if err == nil {
				return conn, nil
			}
		}
		return nil, fmt.Errorf("failed to listen on any interface for multicast address %s:%d", host, port)
	} else {
		// For unicast, we can use ListenUDP
		conn, err := net.ListenUDP("udp", addr)
		if err != nil {
			return nil, fmt.Errorf("failed to listen on %s:%d: %v", host, port, err)
		}
		return conn, nil
	}
}

// SetCheckInterval sets a custom check interval for the monitor (used primarily for testing)
func (m *RTPMonitor) SetCheckInterval(interval time.Duration) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	m.checkInterval = interval
	logger.Log("RTP monitor check interval set to %v", interval)
}
