package playout

import (
	"database/sql"
	"fmt"
	"net/url"
	"showfer-web/models"
	"showfer-web/repository"
	"showfer-web/service/logger"
	"slices"
	"strconv"
	"sync"
)

type Playout struct {
	BaseDir    string
	db         *sql.DB
	streamers  map[string]*StreamScheduler
	mutex      sync.Mutex
	rtpMonitor *RTPMonitor
}

func NewPlayout(db *sql.DB, baseDir string) *Playout {
	playout := &Playout{
		BaseDir:   baseDir,
		db:        db,
		streamers: make(map[string]*StreamScheduler),
	}

	// Create and start the RTP monitor
	playout.rtpMonitor = NewRTPMonitor(playout)

	return playout
}

func (p *Playout) StartAll() error {
	// Start the RTP monitor
	err := p.rtpMonitor.Start()
	if err != nil {
		return fmt.Errorf("failed to start RTP monitor: %w", err)
	}
	var ids []string

	repo := repository.NewScheduleRepository(p.db)
	schedules, err := repo.ListSchedules(models.Pagination{Limit: 100, Page: 1})
	if err != nil {
		return fmt.Errorf("failed to list playout schedules: %w", err)
	}

	for _, schedule := range schedules.Items {
		if slices.Contains(ids, schedule.ShortID) {
			schedule.ShortID, err = repo.UpdateScheduleShortId(schedule.ID)
			if err != nil {
				logger.Error("Failed to update schedule short id for duplicate: %v", err)
			}
		}

		ids = append(ids, schedule.ShortID)

		err = p.Run(schedule)
		if err != nil {
			logger.Error("Failed to run playout schedule %s: %v", schedule.Name, err)
		}
	}

	return nil
}

func (p *Playout) Run(schedule models.Schedule) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	// Get codec settings from repository
	codecSettingsRepo := repository.NewCodecSettingsRepository(p.db)
	codecSettings, err := codecSettingsRepo.GetCodecSettings()
	if err != nil {
		logger.Warn("Failed to get codec settings, using defaults: %v", err)
		// Will use defaults in StreamScheduler
		codecSettings = models.DefaultCodecSettings()
	}

	runStreamer := func(streamer *StreamScheduler) {
		go func() {
			streamer.mutex.Lock()
			if streamer.running {
				streamer.mutex.Unlock()
				logger.Log("Scheduler %s is already running", schedule.Name)
				return
			}
			streamer.mutex.Unlock()

			err := streamer.RunSchedule()
			if err != nil {
				streamer.running = false
				logger.Error("Failed to run playout schedule %s: %v", schedule.Name, err)
			} else {
				logger.Log("Successfully run playout schedule %s", schedule.Name)

				// Parse the output URL to get host and port for monitoring
				u, err := url.Parse(schedule.OutputUrl)
				if err != nil {
					logger.Error("Failed to parse output URL %s: %v", schedule.OutputUrl, err)
					return
				}

				port, err := strconv.Atoi(u.Port())
				if err != nil {
					logger.Error("Failed to parse port from URL %s: %v", schedule.OutputUrl, err)
					return
				}

				// Register the stream with the RTP monitor
				p.rtpMonitor.RegisterStream(schedule.ShortID, u.Hostname(), port)
			}
		}()
	}

	if streamer, ok := p.streamers[schedule.ShortID]; ok {
		runStreamer(streamer)
		return nil
	}

	streamer, err := NewStreamScheduler(schedule, p.db, p.BaseDir, codecSettings)
	if err != nil {
		return fmt.Errorf("failed to create playout streamer: %w", err)
	}
	p.streamers[schedule.ShortID] = streamer

	runStreamer(streamer)
	return nil
}

func (p *Playout) Stop(schedule models.Schedule) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if streamer, ok := p.streamers[schedule.ShortID]; ok {
		if streamer.running {
			streamer.Stop()
			logger.Log("Stopped %s", schedule.ShortID)
		}

		// Unregister from RTP monitor
		p.rtpMonitor.UnregisterStream(schedule.ShortID)

		delete(p.streamers, schedule.ShortID)
		return nil
	}

	logger.Log("Not running: %s", schedule.ShortID)
	return nil
}

func (p *Playout) Restart(schedule models.Schedule) error {
	err := p.Stop(schedule)
	if err != nil {
		return fmt.Errorf("failed to stop playout: %s", err)
	}

	return p.Run(schedule)
}

func (p *Playout) StopRTPMonitor() {
	if p.rtpMonitor != nil {
		p.rtpMonitor.Stop()
	}
}

// StartRTPMonitor starts the RTP output monitor without starting any streams
func (p *Playout) StartRTPMonitor() error {
	if p.rtpMonitor == nil {
		p.rtpMonitor = NewRTPMonitor(p)
	}

	return p.rtpMonitor.Start()
}

// GetRTPMonitor returns the RTP monitor for testing purposes
func (p *Playout) GetRTPMonitor() *RTPMonitor {
	return p.rtpMonitor
}

// IsRunning checks if a scheduler with the given shortID is currently running
func (p *Playout) IsRunning(shortID string) bool {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	streamer, exists := p.streamers[shortID]
	if !exists {
		return false
	}

	return streamer.running
}

// IsRtpSynced checks if a scheduler with the given shortID has RTP sync
func (p *Playout) IsRtpSynced(shortID string) bool {
	// Check if the stream is being monitored by the RTP monitor
	return p.rtpMonitor.IsStreamActive(shortID)
}
