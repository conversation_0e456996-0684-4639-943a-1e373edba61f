package test

import (
	"fmt"
	"log"
	"os"
	"showfer-web/models"
	"testing"
	"time"

	_ "github.com/mattn/go-sqlite3"
)

// TestMockLayeredRecovery tests the layered recovery mechanism using mock objects
// to avoid direct GStreamer calls
func TestMockLayeredRecovery(t *testing.T) {
	// Setup logging
	log.SetOutput(os.Stdout)
	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)
	
	// Create test database
	db, err := createTestDB()
	if err != nil {
		t.Fatalf("Failed to create test database: %v", err)
	}
	defer db.Close()
	
	// Set up test data with primary and backup schedules pointing to the same output
	_, err = db.Exec(`
		INSERT INTO schedules (id, name, short_id, output_url, network_interface, timezone, fillers, icon, ads, channels, regular_days, special_days) 
		VALUES 
		(1, 'Primary Stream', 'primary', ?, '', 'UTC', '{}', '', '{}', '[]', '[]', '[]'),
		(2, 'Backup Stream', 'backup', ?, '', 'UTC', '{}', '', '{}', '[]', '[]', '[]')`,
		fmt.Sprintf("rtp://%s:%d", testRTPHost, testRTPPort),
		fmt.Sprintf("rtp://%s:%d", testRTPHost, testRTPPort),
	)
	if err != nil {
		t.Fatalf("Failed to insert test schedules: %v", err)
	}
	
	// Insert guide data for the schedules
	_, err = db.Exec(`
		INSERT INTO guides (id, schedule_id, elements) 
		VALUES 
		(1, 1, '[]'),
		(2, 2, '[]')`)
	if err != nil {
		t.Fatalf("Failed to insert test guides: %v", err)
	}
	
	// Insert test files
	_, err = db.Exec(`
		INSERT INTO files (id, filename, location, duration) 
		VALUES 
		(1, ?, ?, 60.0),
		(2, ?, ?, 60.0)`,
		primaryVideoFile, testBaseDir,
		backupVideoFile, testBaseDir,
	)
	if err != nil {
		t.Fatalf("Failed to insert test files: %v", err)
	}
	
	// Create mock playout manager
	mockPlayout := NewMockPlayout(db, testBaseDir)
	
	// Get the mock RTP monitor
	mockMonitor := mockPlayout.GetRTPMonitor()
	
	// Use shorter check interval for testing
	mockMonitor.SetCheckInterval(100 * time.Millisecond)
	
	// Start the mock RTP monitor
	err = mockPlayout.StartRTPMonitor()
	if err != nil {
		t.Fatalf("Failed to start mock RTP monitor: %v", err)
	}
	
	// Define test schedules
	primarySchedule := models.Schedule{
		ID:               1,
		Name:             "Primary Stream",
		ShortID:          "primary",
		OutputUrl:        fmt.Sprintf("rtp://%s:%d", testRTPHost, testRTPPort),
		NetworkInterface: "",
	}
	
	// We define the backup schedule but don't use it directly - the monitor will find it
	// when it needs to perform the second recovery attempt
	_ = models.Schedule{
		ID:               2,
		Name:             "Backup Stream",
		ShortID:          "backup",
		OutputUrl:        fmt.Sprintf("rtp://%s:%d", testRTPHost, testRTPPort), // Same output URL
		NetworkInterface: "",
	}
	
	// Start primary stream
	t.Log("Starting primary stream")
	err = mockPlayout.Run(primarySchedule)
	if err != nil {
		t.Fatalf("Failed to start primary stream: %v", err)
	}
	
	// Verify stream is registered and active
	time.Sleep(200 * time.Millisecond)
	
	// Display status
	mockMonitor.DebugStatus()
	
	// Verify primary stream is running
	if !mockPlayout.RunningStreamers["primary"] {
		t.Fatalf("Primary stream should be running")
	}
	
	// First failure - simulate stream stopping
	t.Log("Stopping primary stream to simulate first failure")
	mockPlayout.RunningStreamers["primary"] = false
	
	// Wait for first recovery attempt
	time.Sleep(500 * time.Millisecond)
	
	// Debug status to see what happened
	mockMonitor.DebugStatus()
	
	// Check recovery attempts and confirm stream is running again
	if mockPlayout.RecoveryAttempts["primary"] < 1 {
		t.Fatalf("Expected at least one recovery attempt, got %d", mockPlayout.RecoveryAttempts["primary"])
	}
	
	// Check if stream was restarted
	if !mockPlayout.RunningStreamers["primary"] {
		t.Fatalf("Primary stream should be running after first recovery")
	}
	
	// Second failure - simulate stream stopping again
	t.Log("Stopping primary stream again to simulate second failure")
	mockPlayout.RunningStreamers["primary"] = false
	
	// Wait for second recovery attempt (should use backup)
	time.Sleep(500 * time.Millisecond)
	
	// Debug status to see what happened
	mockMonitor.DebugStatus()
	
	// Check if backup stream is now running
	if !mockPlayout.RunningStreamers["backup"] {
		t.Fatalf("Backup stream should be running after second failure")
	}
	
	// Verify recovery attempts 
	if mockPlayout.RecoveryAttempts["primary"] < 2 {
		t.Fatalf("Expected at least two recovery attempts, got %d", mockPlayout.RecoveryAttempts["primary"])
	}
	
	// Clean up
	mockPlayout.StopRTPMonitor()
	
	t.Log("Mock test completed successfully - verified layered recovery behavior")
} 