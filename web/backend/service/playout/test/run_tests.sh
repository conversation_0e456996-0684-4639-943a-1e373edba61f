#!/bin/bash

# Helper script for running the RTP Monitor failover tests

# Create test directory if it doesn't exist
TEST_DIR="/tmp/rtp-failover-test"
mkdir -p $TEST_DIR

# Check if test files exist, otherwise provide instructions
if [ ! -f "$TEST_DIR/input.mp4" ] || [ ! -f "$TEST_DIR/test_primary.mp4" ] || [ ! -f "$TEST_DIR/test_backup.mp4" ]; then
    echo "Test video files not found. Please create or copy sample video files:"
    echo ""
    echo "For basic testing, you need at least:"
    echo "  - $TEST_DIR/input.mp4 (required for TestSchedulerRestartAfterFailure)"
    echo ""
    echo "For full test suite, you also need:"
    echo "  - $TEST_DIR/test_primary.mp4 (for TestRTPFailoverRecovery)"
    echo "  - $TEST_DIR/test_backup.mp4 (for TestAutomaticRecovery)"
    echo ""
    echo "You can use the same file for all three if you don't have multiple files."
    echo "You can copy a sample MP4 file with:"
    echo "  cp /path/to/your/sample.mp4 $TEST_DIR/input.mp4"
    echo "  cp /path/to/your/sample.mp4 $TEST_DIR/test_primary.mp4"
    echo "  cp /path/to/your/sample.mp4 $TEST_DIR/test_backup.mp4"
    echo ""
    exit 1
fi

# Check if FFmpeg is available for creating test files if needed
if command -v ffmpeg &> /dev/null; then
    echo "FFmpeg is available. You can create test video files with:"
    echo "  ffmpeg -f lavfi -i testsrc=duration=60:size=1280x720:rate=30 -c:v libx264 $TEST_DIR/input.mp4"
    echo ""
fi

# Instructions
echo "=== RTP Monitor Failover Tests ==="
echo ""
echo "Available tests:"
echo "1. Test RTP Monitor Registration:"
echo "   go test -v -run TestRTPMonitorRegistration"
echo ""
echo "2. Test basic failover recovery:"
echo "   go test -v -run TestRTPFailoverRecovery"
echo ""
echo "3. Test automatic recovery with backup streams:"
echo "   go test -v -run TestAutomaticRecovery"
echo ""
echo "4. Test scheduler restart after failure (matches your exact scenario):"
echo "   go test -v -run TestSchedulerRestartAfterFailure"
echo ""
echo "5. Run all tests (takes several minutes):"
echo "   go test -v"
echo ""
echo "Note: These tests require a working GStreamer installation"
echo "      and take several minutes to complete."
echo "==================================" 