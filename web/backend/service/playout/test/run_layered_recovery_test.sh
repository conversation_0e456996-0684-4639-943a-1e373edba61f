#!/bin/bash

# Run layered recovery test script
# This script runs the test that verifies the layered recovery mechanism

echo "Running layered recovery test..."

# Set up test environment
export GSTREAMER_DEBUG=4  # Enable detailed GStreamer logging if needed

# Create test files directory if it doesn't exist
mkdir -p /tmp/rtp-failover-test

# The test will use local test files, but we can create dummy files if needed
if [ ! -f "/tmp/rtp-failover-test/test_primary.mp4" ]; then
    echo "Creating dummy test files..."
    # Use gst-launch to create a test video source
    gst-launch-1.0 -e videotestsrc pattern=smpte num-buffers=1500 ! \
        video/x-raw,width=1280,height=720,framerate=30/1 ! \
        timeoverlay ! \
        x264enc ! \
        mp4mux ! \
        filesink location=/tmp/rtp-failover-test/test_primary.mp4 > /dev/null 2>&1
    
    gst-launch-1.0 -e videotestsrc pattern=ball num-buffers=1500 ! \
        video/x-raw,width=1280,height=720,framerate=30/1 ! \
        textoverlay text="BACKUP STREAM" valignment=top halignment=right font-desc="Sans, 40" ! \
        x264enc ! \
        mp4mux ! \
        filesink location=/tmp/rtp-failover-test/test_backup.mp4 > /dev/null 2>&1
    
    echo "Dummy test files created."
fi

# Print current date and time
echo "Starting test at $(date)"

# Navigate to the backend directory where go.mod is located
cd "../../../" # Go back to backend directory

# Get the module name from go.mod
MODULE_NAME=$(grep "^module" go.mod | awk '{print $2}')
if [ -z "$MODULE_NAME" ]; then
    echo "Error: Could not determine module name from go.mod"
    MODULE_NAME="showfer-web" # Fallback based on what we found
fi

echo "Running test with module: $MODULE_NAME"
go test -v $MODULE_NAME/service/playout/test -run TestLayeredRecoveryMechanism

# Print completion time
echo "Test completed at $(date)" 