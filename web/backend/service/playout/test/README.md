# RTP Failover Tests

This directory contains tests for the RTP failover recovery system.

## Overview

The RTP monitor continuously checks active RTP streams for activity and automatically recovers them if they fail. This includes:

1. Monitoring RTP output for data packets
2. Detecting when streams become inactive
3. Automatically recovering failed streams by restarting them
4. Implementing a layered recovery approach that tries with the original scheduler first, then uses an alternative scheduler if the first recovery attempt fails

## Test Files

- `failover_test.go`: Contains tests for the RTP monitor's failover functionality
- `layered_recovery_test.go`: Tests the layered recovery mechanism with primary and backup schedulers
- `mock_streamer.go`: Contains mock implementations for testing without GStreamer dependencies
- `mock_layered_recovery_test.go`: Tests layered recovery using mock objects to avoid GStreamer calls

## Running the Tests

To run the tests, make sure you have:

1. Valid test video files available (replace the paths in the test constants)
2. Navigate to the test directory:
   ```
   cd backend/service/playout/test
   ```
3. Run all tests:
   ```
   go test -v
   ```
4. Run a specific test:
   ```
   go test -v -run TestLayeredRecoveryMechanism
   ```
5. Skip long-running tests:
   ```
   go test -v -short
   ```

### Using the Test Scripts

We've provided helper scripts to simplify testing:

1. **Run the layered recovery test:**

   ```
   ./run_layered_recovery_test.sh
   ```

   This script will create test video files if they don't exist and run the layered recovery test.

2. **Run the mock layered recovery test (recommended):**

   ```
   ./run_mock_test.sh
   ```

   This test avoids GStreamer dependency issues by using mock objects.

3. **Monitor logs with highlighting:**

   ```
   ./monitor_logs.sh TestLayeredRecoveryMechanism
   ```

   This will run the test and highlight important log messages with colors.

   Or monitor logs from another terminal while a test is running:

   ```
   ./monitor_logs.sh
   ```

## Test Configuration

You can customize the test configuration by modifying the constants at the top of `failover_test.go`:

- `testBaseDir`: Directory for test files
- `testRTPHost`: Host IP for RTP testing (default: 127.0.0.1)
- `testRTPPort`: Port for RTP testing (default: 5001)
- `videoDuration`: Duration of test video playback
- `primaryVideoFile`: Primary video test file
- `backupVideoFile`: Backup video test file

## Test Cases

1. **TestRTPMonitorRegistration**: Verifies that streams can be registered and unregistered with the RTP monitor
2. **TestRTPFailoverRecovery**: Tests basic failover from primary to backup stream
3. **TestAutomaticRecovery**: Tests the automatic recovery functionality with a specific focus on backup streams
4. **TestSchedulerRestartAfterFailure**: Tests that a stopped scheduler is detected and restarted by the recovery monitor
5. **TestLayeredRecoveryMechanism**: Tests the new layered recovery approach that first tries the original scheduler and then fallbacks to an alternative scheduler if the first recovery attempt fails
6. **TestMockLayeredRecovery**: Uses mock objects to test the layered recovery approach without relying on GStreamer

## Interpreting Results

The tests output detailed logs about the failover process. Look for:

- "RTP stream X is active" - Indicates the monitor detected an active stream
- "RTP stream X is down (inactive for Y)" - Indicates failure detection
- "First recovery attempt for RTP stream X" - Indicates the first attempt to recover with the original scheduler
- "Second recovery attempt - switching to alternative scheduler" - Indicates the switch to an alternative scheduler
- "Successfully recovered RTP stream X" - Indicates successful recovery
- "Successfully failed over to alternative RTP stream X" - Indicates successful failover to the backup

## Notes

- The tests require GStreamer to be installed and properly configured
- Some tests may take several minutes to complete due to the nature of failover detection
- The test uses a memory SQLite database for testing purposes
