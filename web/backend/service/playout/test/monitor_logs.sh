#!/bin/bash

# Log monitoring script for RTP failover tests
# This script helps to filter and highlight important log messages during the tests

# Create log directory if it doesn't exist
LOG_DIR="/tmp/rtp-failover-logs"
mkdir -p $LOG_DIR

# Log file
LOG_FILE="$LOG_DIR/rtp_recovery_$(date +%Y%m%d_%H%M%S).log"

# Function to highlight important messages
highlight_logs() {
    # Read input from stdin and apply colors based on content
    while IFS= read -r line; do
        # Check for important patterns and highlight them
        if [[ $line == *"First recovery attempt"* ]]; then
            echo -e "\e[33m$line\e[0m"  # Yellow for first recovery attempt
        elif [[ $line == *"Second recovery attempt"* ]] || [[ $line == *"switching to alternative scheduler"* ]]; then
            echo -e "\e[35m$line\e[0m"  # Magenta for second recovery/alternative
        elif [[ $line == *"Successfully recovered"* ]] || [[ $line == *"Successfully failed over"* ]]; then
            echo -e "\e[32m$line\e[0m"  # Green for success
        elif [[ $line == *"Failed to"* ]] || [[ $line == *"Error"* ]] || [[ $line == *"Cannot recover"* ]]; then
            echo -e "\e[31m$line\e[0m"  # Red for errors
        elif [[ $line == *"Stream became inactive"* ]]; then
            echo -e "\e[91m$line\e[0m"  # Light red for inactive
        elif [[ $line == *"Stream recovered"* ]]; then
            echo -e "\e[92m$line\e[0m"  # Light green for recovery
        elif [[ $line == *"RTP stream"* ]] && [[ $line == *"is active"* ]]; then
            echo -e "\e[94m$line\e[0m"  # Light blue for active
        elif [[ $line == *"=== RTP Monitor Status ==="* ]]; then
            echo -e "\e[36m$line\e[0m"  # Cyan for status headers
        else
            echo "$line"
        fi
    done
}

# Get the module name from go.mod
get_module_name() {
    local go_mod_path="$1/go.mod"
    local module_name=$(grep "^module" "$go_mod_path" 2>/dev/null | awk '{print $2}')
    echo "$module_name"
}

# If a test is provided as an argument, run it and process the logs
if [ -n "$1" ]; then
    # Run the specified test and capture output
    echo "Running test $1 and monitoring logs..."
    
    # Go to backend directory where go.mod is located
    cd "../../../"
    
    # Get module name from go.mod
    MODULE_NAME=$(get_module_name "$(pwd)")
    if [ -z "$MODULE_NAME" ]; then
        echo "Error: Could not determine module name from go.mod"
        MODULE_NAME="showfer-web" # Fallback based on what we found
    fi
    
    echo "Running test with module: $MODULE_NAME"
    go test -v $MODULE_NAME/service/playout/test -run "$1" 2>&1 | tee "$LOG_FILE" | highlight_logs
    echo "Test complete. Full logs saved to $LOG_FILE"
else
    # Just monitor the logs from another terminal while tests are running
    echo "Log monitor started. Press Ctrl+C to exit."
    echo "Watching for important recovery events..."
    
    # Use tail to follow the latest log file
    latest_log=$(ls -t $LOG_DIR/*.log 2>/dev/null | head -1)
    
    if [ -n "$latest_log" ]; then
        tail -f "$latest_log" | highlight_logs
    else
        echo "No log file found. Run a test first or specify a test name as an argument."
        exit 1
    fi
fi 