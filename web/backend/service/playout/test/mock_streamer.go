package test

import (
	"database/sql"
	"fmt"
	"net/url"
	"showfer-web/models"
	"showfer-web/repository"
	"showfer-web/service/logger"
	"strconv"
	"sync"
	"time"
)

// MockPlayout is a mock implementation of playout service for testing
type MockPlayout struct {
	DB                 *sql.DB
	BaseDir            string
	Streamers          map[string]*MockStreamer
	Mutex              sync.Mutex
	Monitor            *MockRTPMonitor
	RecoveryAttempts   map[string]int
	RunningStreamers   map[string]bool
	RegisteredStreams  map[string]bool
}

// MockStreamer is a mock implementation of StreamScheduler for testing
type MockStreamer struct {
	ShortID     string
	OutputURL   string
	Running     bool
	Mutex       sync.Mutex
}

// MockRTPMonitor is a mock implementation of RTPMonitor for testing
type MockRTPMonitor struct {
	Playout           *MockPlayout
	MonitoredStreams  map[string]*MockStreamStatus
	Mutex             sync.Mutex
	Running           bool
	CheckInterval     time.Duration
	ErrorStreams      map[string]time.Time
}

// MockStreamStatus is a mock implementation of StreamStatus for testing
type MockStreamStatus struct {
	ShortID      string
	Host         string
	Port         int
	LastActive   time.Time
	FailureCount int
}

// NewMockPlayout creates a new mock playout service
func NewMockPlayout(db *sql.DB, baseDir string) *MockPlayout {
	playout := &MockPlayout{
		DB:                db,
		BaseDir:           baseDir,
		Streamers:         make(map[string]*MockStreamer),
		RecoveryAttempts:  make(map[string]int),
		RunningStreamers:  make(map[string]bool),
		RegisteredStreams: make(map[string]bool),
	}
	
	// Create and attach the mock RTP monitor
	playout.Monitor = NewMockRTPMonitor(playout)
	
	return playout
}

// NewMockRTPMonitor creates a new mock RTP monitor
func NewMockRTPMonitor(playout *MockPlayout) *MockRTPMonitor {
	return &MockRTPMonitor{
		Playout:          playout,
		MonitoredStreams: make(map[string]*MockStreamStatus),
		Running:          false,
		CheckInterval:    10 * time.Second,
		ErrorStreams:     make(map[string]time.Time),
	}
}

// Run starts a mock streamer
func (p *MockPlayout) Run(schedule models.Schedule) error {
	p.Mutex.Lock()
	defer p.Mutex.Unlock()
	
	if streamer, ok := p.Streamers[schedule.ShortID]; ok {
		streamer.Mutex.Lock()
		streamer.Running = true
		streamer.Mutex.Unlock()
		p.RunningStreamers[schedule.ShortID] = true
		logger.Log("Started existing streamer: %s", schedule.ShortID)
		
		// Register with mock RTP monitor
		u, err := url.Parse(schedule.OutputUrl)
		if err != nil {
			return fmt.Errorf("failed to parse output URL: %w", err)
		}
		
		port, err := strconv.Atoi(u.Port())
		if err != nil {
			return fmt.Errorf("failed to parse port: %w", err)
		}
		
		p.Monitor.RegisterStream(schedule.ShortID, u.Hostname(), port)
		
		return nil
	}
	
	// Create a new mock streamer
	streamer := &MockStreamer{
		ShortID:   schedule.ShortID,
		OutputURL: schedule.OutputUrl,
		Running:   true,
	}
	
	p.Streamers[schedule.ShortID] = streamer
	p.RunningStreamers[schedule.ShortID] = true
	
	logger.Log("Started new streamer: %s", schedule.ShortID)
	
	// Register with mock RTP monitor
	u, err := url.Parse(schedule.OutputUrl)
	if err != nil {
		return fmt.Errorf("failed to parse output URL: %w", err)
	}
	
	port, err := strconv.Atoi(u.Port())
	if err != nil {
		return fmt.Errorf("failed to parse port: %w", err)
	}
	
	p.Monitor.RegisterStream(schedule.ShortID, u.Hostname(), port)
	
	return nil
}

// Stop stops a mock streamer
func (p *MockPlayout) Stop(schedule models.Schedule) error {
	p.Mutex.Lock()
	defer p.Mutex.Unlock()
	
	if streamer, ok := p.Streamers[schedule.ShortID]; ok {
		streamer.Mutex.Lock()
		streamer.Running = false
		streamer.Mutex.Unlock()
		
		delete(p.RunningStreamers, schedule.ShortID)
		
		// Unregister from RTP monitor
		p.Monitor.UnregisterStream(schedule.ShortID)
		
		logger.Log("Stopped streamer: %s", schedule.ShortID)
		return nil
	}
	
	logger.Log("Streamer not found: %s", schedule.ShortID)
	return nil
}

// Restart restarts a mock streamer
func (p *MockPlayout) Restart(schedule models.Schedule) error {
	err := p.Stop(schedule)
	if err != nil {
		return err
	}
	
	return p.Run(schedule)
}

// StartRTPMonitor starts the mock RTP monitor
func (p *MockPlayout) StartRTPMonitor() error {
	return p.Monitor.Start()
}

// StopRTPMonitor stops the mock RTP monitor
func (p *MockPlayout) StopRTPMonitor() {
	p.Monitor.Stop()
}

// GetRTPMonitor returns the mock RTP monitor
func (p *MockPlayout) GetRTPMonitor() *MockRTPMonitor {
	return p.Monitor
}

// Start starts the mock RTP monitor
func (m *MockRTPMonitor) Start() error {
	m.Mutex.Lock()
	defer m.Mutex.Unlock()
	
	if m.Running {
		return fmt.Errorf("mock RTP monitor is already running")
	}
	
	m.Running = true
	logger.Log("Mock RTP monitor started")
	
	// Start a goroutine to check streams periodically
	go func() {
		ticker := time.NewTicker(m.CheckInterval)
		defer ticker.Stop()
		
		for m.Running {
			<-ticker.C
			m.checkAllStreams()
		}
	}()
	
	return nil
}

// Stop stops the mock RTP monitor
func (m *MockRTPMonitor) Stop() {
	m.Mutex.Lock()
	defer m.Mutex.Unlock()
	
	m.Running = false
	logger.Log("Mock RTP monitor stopped")
}

// RegisterStream registers a stream with the mock RTP monitor
func (m *MockRTPMonitor) RegisterStream(shortID string, host string, port int) {
	m.Mutex.Lock()
	defer m.Mutex.Unlock()
	
	m.MonitoredStreams[shortID] = &MockStreamStatus{
		ShortID:      shortID,
		Host:         host,
		Port:         port,
		LastActive:   time.Now(),
		FailureCount: 0,
	}
	
	m.Playout.RegisteredStreams[shortID] = true
	logger.Log("Registered stream for monitoring: %s (%s:%d)", shortID, host, port)
}

// UnregisterStream unregisters a stream from the mock RTP monitor
func (m *MockRTPMonitor) UnregisterStream(shortID string) {
	m.Mutex.Lock()
	defer m.Mutex.Unlock()
	
	delete(m.MonitoredStreams, shortID)
	delete(m.Playout.RegisteredStreams, shortID)
	logger.Log("Unregistered stream from monitoring: %s", shortID)
}

// DebugStatus prints the current status of monitored streams
func (m *MockRTPMonitor) DebugStatus() {
	m.Mutex.Lock()
	defer m.Mutex.Unlock()
	
	logger.Log("=== Mock RTP Monitor Status ===")
	logger.Log("Monitor running: %t", m.Running)
	logger.Log("Check interval: %v", m.CheckInterval)
	logger.Log("Number of monitored streams: %d", len(m.MonitoredStreams))
	
	for id, status := range m.MonitoredStreams {
		timeSinceActive := time.Since(status.LastActive)
		logger.Log("Stream %s: host=%s:%d, last active=%v ago, failure count=%d", 
			id, status.Host, status.Port, timeSinceActive, status.FailureCount)
		
		// Check if stream is in error state
		if errorTime, isInError := m.ErrorStreams[id]; isInError {
			logger.Log("  - Stream %s is in error state since %v ago", 
				id, time.Since(errorTime))
		}
	}
	
	logger.Log("========================")
}

// SetCheckInterval sets the check interval for the mock RTP monitor
func (m *MockRTPMonitor) SetCheckInterval(interval time.Duration) {
	m.Mutex.Lock()
	defer m.Mutex.Unlock()
	
	m.CheckInterval = interval
	logger.Log("Mock RTP monitor check interval set to %v", interval)
}

// checkAllStreams checks all streams for activity
func (m *MockRTPMonitor) checkAllStreams() {
	m.Mutex.Lock()
	streams := make(map[string]*MockStreamStatus)
	for id, status := range m.MonitoredStreams {
		streams[id] = status
	}
	m.Mutex.Unlock()
	
	for shortID, status := range streams {
		// Check if the stream is running in the playout
		m.Playout.Mutex.Lock()
		isRunning := m.Playout.RunningStreamers[shortID]
		m.Playout.Mutex.Unlock()
		
		m.Mutex.Lock()
		if isRunning {
			// Stream is active
			status.LastActive = time.Now()
			status.FailureCount = 0
			logger.Log("RTP stream %s is active", shortID)
		} else {
			// Stream is inactive
			status.FailureCount++
			timeSinceLastActive := time.Since(status.LastActive)
			
			if timeSinceLastActive > time.Minute || status.FailureCount >= 3 {
				logger.Error("RTP stream %s is down (inactive for %v, failure count: %d). Attempting recovery...", 
					shortID, timeSinceLastActive, status.FailureCount)
				
				// We'll recover the stream outside the lock
				m.Mutex.Unlock()
				m.recoverStream(shortID)
				m.Mutex.Lock()
			} else {
				logger.Error("RTP stream %s appears inactive (failure count: %d)", 
					shortID, status.FailureCount)
			}
		}
		m.Mutex.Unlock()
	}
}

// recoverStream attempts to recover a stream
func (m *MockRTPMonitor) recoverStream(shortID string) {
	// Get the schedule from the repository
	repo := repository.NewScheduleRepository(m.Playout.DB)
	schedules, err := repo.ListSchedules(models.Pagination{Limit: 100, Page: 1})
	if err != nil {
		logger.Error("Cannot recover RTP stream %s: failed to list schedules: %v", shortID, err)
		return
	}
	
	var schedule models.Schedule
	var alternativeSchedules []models.Schedule

	// First find the primary schedule
	for _, s := range schedules.Items {
		if s.ShortID == shortID {
			schedule = s
			break
		}
	}
	
	if schedule.ShortID == "" {
		logger.Error("Cannot recover RTP stream %s: schedule not found", shortID)
		return
	}
	
	// Now find alternative schedules with the same output URL
	for _, s := range schedules.Items {
		if s.ShortID != shortID && s.OutputUrl == schedule.OutputUrl {
			// Store alternative schedules with the same output URL for potential fallback
			alternativeSchedules = append(alternativeSchedules, s)
		}
	}
	
	// Check if this is the first recovery attempt
	m.Mutex.Lock()
	_, alreadyInError := m.ErrorStreams[shortID]
	
	// Track recovery attempts
	m.Playout.RecoveryAttempts[shortID]++
	
	if !alreadyInError {
		// First recovery attempt - try with the original scheduler
		m.ErrorStreams[shortID] = time.Now()
		m.Mutex.Unlock()
		
		logger.Log("First recovery attempt for RTP stream %s using original scheduler", shortID)
		
		// Stop the current stream
		err = m.Playout.Stop(schedule)
		if err != nil {
			logger.Error("Failed to stop RTP stream %s for recovery: %v", shortID, err)
		}
		
		// Wait a moment
		time.Sleep(100 * time.Millisecond)
		
		// Start the stream again with the original scheduler
		err = m.Playout.Run(schedule)
		if err != nil {
			logger.Error("Failed to recover RTP stream %s: %v", shortID, err)
		} else {
			logger.Log("Successfully recovered RTP stream %s using original scheduler", shortID)
			
			// Reset the status for this stream
			m.Mutex.Lock()
			if status, ok := m.MonitoredStreams[shortID]; ok {
				status.LastActive = time.Now()
				status.FailureCount = 0
			}
			m.Mutex.Unlock()
		}
	} else {
		// This is a subsequent failure - try alternative scheduler if available
		recoverySince := time.Since(m.ErrorStreams[shortID])
		m.Mutex.Unlock()
		
		if len(alternativeSchedules) > 0 {
			// Find the first alternative schedule
			alternativeSchedule := alternativeSchedules[0]
			logger.Log("Second recovery attempt for RTP stream %s - switching to alternative scheduler %s after %v of instability", 
				shortID, alternativeSchedule.ShortID, recoverySince)
			
			// Stop the current failed stream
			err = m.Playout.Stop(schedule)
			if err != nil {
				logger.Error("Failed to stop unstable RTP stream %s for failover: %v", shortID, err)
			}
			
			// Wait a moment
			time.Sleep(100 * time.Millisecond)
			
			// Start the alternative stream
			err = m.Playout.Run(alternativeSchedule)
			if err != nil {
				logger.Error("Failed to start alternative RTP stream %s: %v", alternativeSchedule.ShortID, err)
			} else {
				logger.Log("Successfully failed over to alternative RTP stream %s", alternativeSchedule.ShortID)
				
				// Reset error state since we've switched to an alternative
				m.Mutex.Lock()
				delete(m.ErrorStreams, shortID)
				m.Mutex.Unlock()
				
				// Reset monitoring for the original stream
				m.Mutex.Lock()
				if _, ok := m.MonitoredStreams[shortID]; ok {
					delete(m.MonitoredStreams, shortID)
					logger.Log("Unregistered unstable RTP stream %s after failover", shortID)
				}
				m.Mutex.Unlock()
			}
		} else {
			// No alternative available, try original again with a warning
			logger.Error("No alternative schedulers available for %s - attempting recovery with original scheduler", shortID)
			
			// Stop the current stream
			err = m.Playout.Stop(schedule)
			if err != nil {
				logger.Error("Failed to stop RTP stream %s for retry recovery: %v", shortID, err)
			}
			
			// Wait a moment
			time.Sleep(100 * time.Millisecond)
			
			// Start the stream again
			err = m.Playout.Run(schedule)
			if err != nil {
				logger.Error("Failed to recover RTP stream %s on retry: %v", shortID, err)
			} else {
				logger.Log("Successfully recovered RTP stream %s on retry", shortID)
				
				// Reset the status for this stream
				m.Mutex.Lock()
				if status, ok := m.MonitoredStreams[shortID]; ok {
					status.LastActive = time.Now()
					status.FailureCount = 0
				}
				m.Mutex.Unlock()
				
				// Reset error state after successful recovery
				m.Mutex.Lock()
				delete(m.ErrorStreams, shortID)
				m.Mutex.Unlock()
			}
		}
	}
}
