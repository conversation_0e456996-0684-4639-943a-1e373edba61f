package test

import (
	"database/sql"
	"fmt"
	"log"
	"net"
	"os"
	"showfer-web/models"
	"showfer-web/service/playout"
	"sync"
	"testing"
	"time"

	_ "github.com/mattn/go-sqlite3"
)

const (
	// Test configuration
	testBaseDir  = "/tmp/rtp-failover-test/" // Temporary directory for test files
	testRTPHost  = "127.0.0.1"               // Use localhost for testing
	testRTPPort  = 5001                      // Test RTP port
	videoDuration = 60 * time.Second        // Duration of test video playback
	
	// Test file paths - replace these with actual test files in your environment
	primaryVideoFile = "test_primary.mp4"
	backupVideoFile  = "test_backup.mp4"
)

func createTestDB() (*sql.DB, error) {
	// Create a temporary in-memory database for testing
	db, err := sql.Open("sqlite3", ":memory:")
	if err != nil {
		return nil, fmt.Errorf("failed to open test database: %w", err)
	}

	// Create necessary tables for testing
	_, err = db.Exec(`
		CREATE TABLE schedules (
			id INTEGER PRIMARY KEY,
			name TEXT NOT NULL,
			short_id TEXT NOT NULL,
			output_url TEXT NOT NULL,
			network_interface TEXT,
			timezone TEXT,
			fillers TEXT,
			icon TEXT,
			ads TEXT DEFAULT '{}',
			channels TEXT DEFAULT '[]',
			regular_days TEXT DEFAULT '[]',
			special_days TEXT DEFAULT '[]',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		);
		
		CREATE TABLE files (
			id INTEGER PRIMARY KEY,
			filename TEXT NOT NULL,
			location TEXT NOT NULL,
			duration REAL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		);
		
		CREATE TABLE guides (
			id INTEGER PRIMARY KEY,
			schedule_id INTEGER NOT NULL,
			elements TEXT DEFAULT '[]',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		);
	`)
	if err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to create test tables: %w", err)
	}

	return db, nil
}

func setupTestData(db *sql.DB) error {
	// Create test directory if it doesn't exist
	if err := os.MkdirAll(testBaseDir, 0755); err != nil {
		return fmt.Errorf("failed to create test directory: %w", err)
	}

	// Insert test schedules
	_, err := db.Exec(
		"INSERT INTO schedules (id, name, short_id, output_url, network_interface, timezone, fillers, icon, ads, channels, regular_days, special_days) VALUES "+
			"(1, 'Primary Stream', 'primary', ?, '', 'UTC', '{}', '', '{}', '[]', '[]', '[]'), "+
			"(2, 'Backup Stream', 'backup', ?, '', 'UTC', '{}', '', '{}', '[]', '[]', '[]')",
		fmt.Sprintf("rtp://%s:%d", testRTPHost, testRTPPort),
		fmt.Sprintf("rtp://%s:%d", testRTPHost, testRTPPort),
	)
	if err != nil {
		return fmt.Errorf("failed to insert test schedules: %w", err)
	}

	// Insert guide data for the schedules
	_, err = db.Exec(
		"INSERT INTO guides (id, schedule_id, elements) VALUES "+
			"(1, 1, '[]'), "+
			"(2, 2, '[]')",
	)
	if err != nil {
		return fmt.Errorf("failed to insert test guides: %w", err)
	}

	// Insert test files
	_, err = db.Exec(
		"INSERT INTO files (id, filename, location, duration) VALUES "+
			"(1, ?, ?, 60.0), "+
			"(2, ?, ?, 60.0)",
		primaryVideoFile, testBaseDir,
		backupVideoFile, testBaseDir,
	)
	if err != nil {
		return fmt.Errorf("failed to insert test files: %w", err)
	}

	return nil
}

// monitorRTPOutput listens for RTP packets and reports activity
func monitorRTPOutput(t *testing.T, done chan struct{}, wg *sync.WaitGroup) {
	defer wg.Done()

	// Set up RTP listener
	addr := &net.UDPAddr{
		IP:   net.ParseIP(testRTPHost),
		Port: testRTPPort,
	}

	conn, err := net.ListenUDP("udp", addr)
	if err != nil {
		t.Errorf("Failed to set up RTP monitoring: %v", err)
		return
	}
	defer conn.Close()

	// Set read deadline to avoid blocking forever
	conn.SetReadDeadline(time.Now().Add(500 * time.Millisecond))

	// Variables to track stream activity
	var (
		lastActivity   = time.Now()
		inactiveStream = false
	)

	// Monitor for RTP packets
	buffer := make([]byte, 1500) // Standard MTU size
	for {
		select {
		case <-done:
			return
		default:
			// Reset read deadline
			conn.SetReadDeadline(time.Now().Add(500 * time.Millisecond))

			n, _, err := conn.ReadFromUDP(buffer)
			if err != nil {
				if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
					// No data received - check if we've gone too long without activity
					if !inactiveStream && time.Since(lastActivity) > 2*time.Second {
						t.Logf("Stream became inactive at %v", time.Now())
						inactiveStream = true
					}
				} else {
					t.Logf("Error reading from UDP: %v", err)
				}
				// Continue monitoring
				time.Sleep(100 * time.Millisecond)
				continue
			}

			if n > 0 {
				// We received data - update activity timestamp
				now := time.Now()
				if inactiveStream {
					// Stream has recovered
					inactiveStream = false
					t.Logf("Stream recovered at %v after %v of inactivity", 
						now, now.Sub(lastActivity))
				}
				lastActivity = now
			}
		}
	}
}

func TestRTPFailoverRecovery(t *testing.T) {
	// Skip in short mode
	if testing.Short() {
		t.Skip("Skipping RTP failover test in short mode")
	}

	// Set up logging
	log.SetOutput(os.Stdout)
	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)

	// Create test database
	db, err := createTestDB()
	if err != nil {
		t.Fatalf("Failed to create test database: %v", err)
	}
	defer db.Close()

	// Set up test data
	if err := setupTestData(db); err != nil {
		t.Fatalf("Failed to set up test data: %v", err)
	}

	// Create playout manager
	playoutManager := playout.NewPlayout(db, testBaseDir)

	// Start monitoring for RTP output
	monitorDone := make(chan struct{})
	var monitorWg sync.WaitGroup
	monitorWg.Add(1)
	go monitorRTPOutput(t, monitorDone, &monitorWg)

	// Start just the RTP monitor
	err = playoutManager.StartRTPMonitor()
	if err != nil {
		t.Fatalf("Failed to start RTP monitor: %v", err)
	}

	// Start primary schedule
	primarySchedule := models.Schedule{
		ID:               1,
		Name:             "Primary Stream",
		ShortID:          "primary",
		OutputUrl:        fmt.Sprintf("rtp://%s:%d", testRTPHost, testRTPPort),
		NetworkInterface: "",
	}

	// Run the primary schedule
	err = playoutManager.Run(primarySchedule)
	if err != nil {
		t.Fatalf("Failed to start primary schedule: %v", err)
	}

	t.Log("Started primary stream")

	// Wait for one minute to simulate primary stream running
	time.Sleep(1 * time.Minute)

	// Stop the primary stream to simulate failure
	err = playoutManager.Stop(primarySchedule)
	if err != nil {
		t.Fatalf("Failed to stop primary stream: %v", err)
	}

	t.Log("Stopped primary stream to simulate failure")

	// Wait for backup to be started by the monitor
	// The RTP monitor checks every 10 seconds, so we need to wait a bit longer
	// to allow for: detection + recovery attempt
	time.Sleep(30 * time.Second)

	// Clean up
	close(monitorDone)
	monitorWg.Wait()

	// Stop the RTP monitor
	playoutManager.StopRTPMonitor()

	// Check if the backup stream was started
	// We don't have a direct way to check this in the test, but we can verify
	// that the RTP monitor logged recovery attempts
	t.Log("Test complete - check logs to verify failover recovery")
}

// TestRTPMonitorRegistration tests the stream registration and unregistration functionality
func TestRTPMonitorRegistration(t *testing.T) {
	// Skip in short mode
	if testing.Short() {
		t.Skip("Skipping RTP monitor registration test in short mode")
	}

	// Create test database
	db, err := createTestDB()
	if err != nil {
		t.Fatalf("Failed to create test database: %v", err)
	}
	defer db.Close()

	// Create playout manager
	playoutManager := playout.NewPlayout(db, testBaseDir)

	// Start the RTP monitor
	err = playoutManager.StartRTPMonitor()
	if err != nil {
		t.Fatalf("Failed to start RTP monitor: %v", err)
	}
	defer playoutManager.StopRTPMonitor()

	// Directly test the registration functionality by accessing the RTPMonitor
	// We'll manually register a test stream
	rtpMonitor := playoutManager.GetRTPMonitor()
	if rtpMonitor == nil {
		t.Fatalf("Failed to get RTP monitor from playout manager")
	}

	// Register a test stream
	testStreamID := "test-stream-1"
	rtpMonitor.RegisterStream(testStreamID, testRTPHost, testRTPPort)

	// Wait briefly for registration to complete
	time.Sleep(100 * time.Millisecond)

	// We should see debug output for the registered stream
	// Manually unregister the stream
	rtpMonitor.UnregisterStream(testStreamID)

	// Wait briefly for unregistration to complete
	time.Sleep(100 * time.Millisecond)

	// Trigger a debug status to verify the stream was unregistered
	rtpMonitor.DebugStatus()

	t.Log("Registration test complete - check logs to verify registration/unregistration")
}

// TestAutomaticRecovery tests the automatic recovery functionality
func TestAutomaticRecovery(t *testing.T) {
	// Skip in short mode
	if testing.Short() {
		t.Skip("Skipping automatic recovery test in short mode")
	}

	// Create test database
	db, err := createTestDB()
	if err != nil {
		t.Fatalf("Failed to create test database: %v", err)
	}
	defer db.Close()

	// Set up test data 
	if err := setupTestData(db); err != nil {
		t.Fatalf("Failed to set up test data: %v", err)
	}

	// Create playout manager
	playoutManager := playout.NewPlayout(db, testBaseDir)

	// Start monitoring for RTP output
	monitorDone := make(chan struct{})
	var monitorWg sync.WaitGroup
	monitorWg.Add(1)
	go monitorRTPOutput(t, monitorDone, &monitorWg)

	// Start the RTP monitor
	err = playoutManager.StartRTPMonitor()
	if err != nil {
		t.Fatalf("Failed to start RTP monitor: %v", err)
	}

	// Prepare primary and backup schedules
	primarySchedule := models.Schedule{
		ID:               1,
		Name:             "Primary Stream",
		ShortID:          "primary",
		OutputUrl:        fmt.Sprintf("rtp://%s:%d", testRTPHost, testRTPPort),
		NetworkInterface: "",
	}

	backupSchedule := models.Schedule{
		ID:               2,
		Name:             "Backup Stream",
		ShortID:          "backup",
		OutputUrl:        fmt.Sprintf("rtp://%s:%d", testRTPHost, testRTPPort),
		NetworkInterface: "",
	}

	// First, make sure backup is ready to be used but not running
	// Set it up in the database but don't start it
	t.Log("Setting up backup schedule")

	// Start primary stream
	err = playoutManager.Run(primarySchedule)
	if err != nil {
		t.Fatalf("Failed to start primary stream: %v", err)
	}
	t.Log("Started primary stream")

	// Wait for primary stream to establish itself
	time.Sleep(5 * time.Second)

	// Get the RTP monitor
	rtpMonitor := playoutManager.GetRTPMonitor()
	if rtpMonitor == nil {
		t.Fatalf("Failed to get RTP monitor")
	}
	
	// Force a debug status output before stopping
	rtpMonitor.DebugStatus()

	// Stop the primary stream to simulate failure
	err = playoutManager.Stop(primarySchedule)
	if err != nil {
		t.Fatalf("Failed to stop primary stream: %v", err)
	}
	t.Log("Stopped primary stream to simulate failure")

	// The RTP monitor should detect the stream has stopped,
	// see that it's in the schedules table, and try to restart it
	time.Sleep(30 * time.Second)
	
	// Force another debug status to see what happened
	rtpMonitor.DebugStatus()

	// Clean up
	if err := playoutManager.Stop(primarySchedule); err == nil {
		t.Log("Stopped primary stream during cleanup")
	}
	if err := playoutManager.Stop(backupSchedule); err == nil {
		t.Log("Stopped backup stream during cleanup")
	}
	
	playoutManager.StopRTPMonitor()
	close(monitorDone)
	monitorWg.Wait()

	t.Log("Automatic recovery test complete - check logs to verify recovery")
}

// TestSchedulerRestartAfterFailure tests that the recovery monitor can detect and restart
// a stopped scheduler without switching to a backup stream
func TestSchedulerRestartAfterFailure(t *testing.T) {
	// Skip in short mode
	if testing.Short() {
		t.Skip("Skipping scheduler restart test in short mode")
	}

	// Set up logging
	log.SetOutput(os.Stdout)
	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)

	// Create test database
	db, err := createTestDB()
	if err != nil {
		t.Fatalf("Failed to create test database: %v", err)
	}
	defer db.Close()

	// Set up test data with just the primary stream
	_, err = db.Exec(`
		INSERT INTO schedules (id, name, short_id, output_url, network_interface, timezone, fillers, icon, ads, channels, regular_days, special_days) 
		VALUES (1, 'Primary Stream', 'primary', ?, '', 'UTC', '{}', '', '{}', '[]', '[]', '[]')`,
		fmt.Sprintf("rtp://%s:%d", testRTPHost, testRTPPort),
	)
	if err != nil {
		t.Fatalf("Failed to insert primary schedule: %v", err)
	}
	
	// Insert guide data for the primary stream
	_, err = db.Exec(`
		INSERT INTO guides (id, schedule_id, elements) 
		VALUES (1, 1, '[]')`)
	if err != nil {
		t.Fatalf("Failed to insert primary guide: %v", err)
	}

	// Insert test file for the primary stream
	_, err = db.Exec(`
		INSERT INTO files (id, filename, location, duration) 
		VALUES (1, ?, ?, 60.0)`,
		"input.mp4", testBaseDir,
	)
	if err != nil {
		t.Fatalf("Failed to insert test file: %v", err)
	}

	// Create playout manager
	playoutManager := playout.NewPlayout(db, testBaseDir)

	// Start monitoring for RTP output
	monitorDone := make(chan struct{})
	var monitorWg sync.WaitGroup
	monitorWg.Add(1)
	go monitorRTPOutput(t, monitorDone, &monitorWg)

	// Start the RTP monitor
	err = playoutManager.StartRTPMonitor()
	if err != nil {
		t.Fatalf("Failed to start RTP monitor: %v", err)
	}

	// Define primary schedule
	primarySchedule := models.Schedule{
		ID:               1,
		Name:             "Primary Stream",
		ShortID:          "primary",
		OutputUrl:        fmt.Sprintf("rtp://%s:%d", testRTPHost, testRTPPort),
		NetworkInterface: "",
	}

	// Start primary stream
	t.Log("Starting primary stream")
	err = playoutManager.Run(primarySchedule)
	if err != nil {
		t.Fatalf("Failed to start primary stream: %v", err)
	}

	// Wait briefly to allow the stream to fully initialize
	time.Sleep(5 * time.Second)
	
	// Get the RTP monitor for status output
	rtpMonitor := playoutManager.GetRTPMonitor()
	if rtpMonitor == nil {
		t.Fatalf("Failed to get RTP monitor")
	}
	
	// Log initial monitor status
	t.Log("Initial stream status:")
	rtpMonitor.DebugStatus()

	// Let the stream run for one minute (simulating server uptime)
	t.Log("Waiting one minute for stream to run normally...")
	time.Sleep(1 * time.Minute)

	// Stop the primary stream to simulate failure
	t.Log("Stopping primary stream to simulate failure")
	err = playoutManager.Stop(primarySchedule)
	if err != nil {
		t.Fatalf("Failed to stop primary stream: %v", err)
	}

	// Log status after stopping
	t.Log("Stream status after stopping:")
	rtpMonitor.DebugStatus()

	// Wait for the RTP monitor to detect failure and recover
	// The monitor checks every 10 seconds, so we need to wait for:
	// - Failure detection (up to 3 checks = 30 seconds)
	// - Recovery attempt
	t.Log("Waiting for recovery monitor to detect and restart the stream...")
	time.Sleep(45 * time.Second)

	// Log status after recovery attempt
	t.Log("Stream status after recovery attempt:")
	rtpMonitor.DebugStatus()

	// Check if we're receiving RTP packets again (the monitorRTPOutput function logs this)
	// We'll wait a bit to see if packets are flowing
	time.Sleep(5 * time.Second)

	// Clean up
	t.Log("Test complete, cleaning up")
	playoutManager.Stop(primarySchedule)
	playoutManager.StopRTPMonitor()
	close(monitorDone)
	monitorWg.Wait()

	t.Log("Test complete - check logs to verify the stream was properly recovered")
} 