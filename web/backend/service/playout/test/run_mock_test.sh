#!/bin/bash

# Run mock layered recovery test
# This test avoids GStreamer calls by using mock objects

echo "Running mock layered recovery test..."

# Create log directory if it doesn't exist
LOG_DIR="/tmp/rtp-failover-logs"
mkdir -p $LOG_DIR

# Log file
LOG_FILE="$LOG_DIR/mock_recovery_$(date +%Y%m%d_%H%M%S).log"

# Function to highlight important messages
highlight_logs() {
    # Read input from stdin and apply colors based on content
    while IFS= read -r line; do
        # Check for important patterns and highlight them
        if [[ $line == *"First recovery attempt"* ]]; then
            echo -e "\e[33m$line\e[0m"  # Yellow for first recovery attempt
        elif [[ $line == *"Second recovery attempt"* ]] || [[ $line == *"switching to alternative scheduler"* ]]; then
            echo -e "\e[35m$line\e[0m"  # Magenta for second recovery/alternative
        elif [[ $line == *"Successfully recovered"* ]] || [[ $line == *"Successfully failed over"* ]]; then
            echo -e "\e[32m$line\e[0m"  # Green for success
        elif [[ $line == *"Failed to"* ]] || [[ $line == *"Error"* ]] || [[ $line == *"Cannot recover"* ]]; then
            echo -e "\e[31m$line\e[0m"  # Red for errors
        elif [[ $line == *"Stream became inactive"* ]]; then
            echo -e "\e[91m$line\e[0m"  # Light red for inactive
        elif [[ $line == *"Stream recovered"* ]]; then
            echo -e "\e[92m$line\e[0m"  # Light green for recovery
        elif [[ $line == *"RTP stream"* ]] && [[ $line == *"is active"* ]]; then
            echo -e "\e[94m$line\e[0m"  # Light blue for active
        elif [[ $line == *"=== Mock RTP Monitor Status ==="* ]]; then
            echo -e "\e[36m$line\e[0m"  # Cyan for status headers
        elif [[ $line == *"PASS"* ]]; then
            echo -e "\e[42m$line\e[0m"  # Green background for PASS
        elif [[ $line == *"FAIL"* ]]; then
            echo -e "\e[41m$line\e[0m"  # Red background for FAIL
        else
            echo "$line"
        fi
    done
}

# Print current date and time
echo "Starting test at $(date)"

# Go to backend directory
cd "../../../"

# Get the module name from go.mod
MODULE_NAME=$(grep "^module" go.mod | awk '{print $2}')
if [ -z "$MODULE_NAME" ]; then
    echo "Error: Could not determine module name from go.mod"
    MODULE_NAME="showfer-web" # Fallback
fi

echo "Running test with module: $MODULE_NAME"
go test -v $MODULE_NAME/service/playout/test -run TestMockLayeredRecovery 2>&1 | tee "$LOG_FILE" | highlight_logs

# Print completion time
echo "Test completed at $(date)"
echo "Full logs saved to $LOG_FILE" 