package test

import (
	"fmt"
	"log"
	"os"
	"showfer-web/models"
	"showfer-web/service/playout"
	"sync"
	"testing"
	"time"

	_ "github.com/mattn/go-sqlite3"
)

// This test uses the constants defined in failover_test.go:
// - testBaseDir: "/tmp/rtp-failover-test/"
// - testRTPHost: "127.0.0.1"
// - testRTPPort: 5001
// - primaryVideoFile: "test_primary.mp4"
// - backupVideoFile: "test_backup.mp4"

// TestLayeredRecoveryMechanism tests the layered recovery mechanism
// First try: recover using original scheduler
// Second try: switch to alternative scheduler
func TestLayeredRecoveryMechanism(t *testing.T) {
	// Skip in short mode
	if testing.Short() {
		t.Skip("Skipping layered recovery test in short mode")
	}

	// Setup logging
	log.SetOutput(os.Stdout)
	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)
	
	// Create test database
	db, err := createTestDB()
	if err != nil {
		t.Fatalf("Failed to create test database: %v", err)
	}
	defer db.Close()
	
	// Set up test data with primary and backup schedules pointing to the same output
	_, err = db.Exec(`
		INSERT INTO schedules (id, name, short_id, output_url, network_interface, timezone, fillers, icon, ads, channels, regular_days, special_days) 
		VALUES 
		(1, 'Primary Stream', 'primary', ?, '', 'UTC', '{}', '', '{}', '[]', '[]', '[]'),
		(2, 'Backup Stream', 'backup', ?, '', 'UTC', '{}', '', '{}', '[]', '[]', '[]')`,
		fmt.Sprintf("rtp://%s:%d", testRTPHost, testRTPPort),
		fmt.Sprintf("rtp://%s:%d", testRTPHost, testRTPPort),
	)
	if err != nil {
		t.Fatalf("Failed to insert test schedules: %v", err)
	}
	
	// Insert guide data for the schedules
	_, err = db.Exec(`
		INSERT INTO guides (id, schedule_id, elements) 
		VALUES 
		(1, 1, '[]'),
		(2, 2, '[]')`)
	if err != nil {
		t.Fatalf("Failed to insert test guides: %v", err)
	}
	
	// Insert test files
	_, err = db.Exec(`
		INSERT INTO files (id, filename, location, duration) 
		VALUES 
		(1, ?, ?, 60.0),
		(2, ?, ?, 60.0)`,
		primaryVideoFile, testBaseDir,
		backupVideoFile, testBaseDir,
	)
	if err != nil {
		t.Fatalf("Failed to insert test files: %v", err)
	}
	
	// Create playout manager
	playoutManager := playout.NewPlayout(db, testBaseDir)
	
	// Start monitoring for RTP output
	monitorDone := make(chan struct{})
	var monitorWg sync.WaitGroup
	monitorWg.Add(1)
	go monitorRTPOutput(t, monitorDone, &monitorWg)
	
	// Start the RTP monitor with more frequent checks for testing
	rtpMonitor := playoutManager.GetRTPMonitor()
	if rtpMonitor == nil {
		t.Fatalf("Failed to get RTP monitor from playout manager")
	}
	
	// Use shorter check interval for testing (2 seconds instead of default 10)
	rtpMonitor.SetCheckInterval(2 * time.Second)
	
	err = playoutManager.StartRTPMonitor()
	if err != nil {
		t.Fatalf("Failed to start RTP monitor: %v", err)
	}
	
	// Prepare schedules
	primarySchedule := models.Schedule{
		ID:               1,
		Name:             "Primary Stream",
		ShortID:          "primary",
		OutputUrl:        fmt.Sprintf("rtp://%s:%d", testRTPHost, testRTPPort),
		NetworkInterface: "",
	}
	
	backupSchedule := models.Schedule{
		ID:               2,
		Name:             "Backup Stream",
		ShortID:          "backup",
		OutputUrl:        fmt.Sprintf("rtp://%s:%d", testRTPHost, testRTPPort), // Same output URL
		NetworkInterface: "",
	}
	
	// Start primary stream
	t.Log("Starting primary stream")
	err = playoutManager.Run(primarySchedule)
	if err != nil {
		t.Fatalf("Failed to start primary stream: %v", err)
	}
	
	// Wait for stream to establish (5 seconds)
	time.Sleep(5 * time.Second)
	
	// First failure and recovery attempt
	t.Log("Stopping primary stream to simulate first failure")
	err = playoutManager.Stop(primarySchedule)
	if err != nil {
		t.Fatalf("Failed to stop primary stream: %v", err)
	}
	
	// Wait for first recovery attempt (should use original scheduler)
	// We reduced the check interval to 2 seconds, so we need to wait about:
	// - Detection time: up to 3 checks = 6 seconds
	// - Recovery time: a few seconds
	t.Log("Waiting for first recovery attempt (should use original scheduler)...")
	time.Sleep(10 * time.Second)
	
	// Force debug status to see what happened
	rtpMonitor.DebugStatus()
	
	// Second failure
	t.Log("Stopping primary stream again to simulate second failure")
	err = playoutManager.Stop(primarySchedule)
	if err != nil {
		t.Fatalf("Failed to stop primary stream: %v", err)
	}
	
	// Wait for second recovery attempt (should use alternative scheduler - backup)
	t.Log("Waiting for second recovery attempt (should use alternative scheduler)...")
	time.Sleep(10 * time.Second)
	
	// Force debug status to see what happened
	rtpMonitor.DebugStatus()
	
	// Clean up
	t.Log("Test complete, cleaning up")
	playoutManager.Stop(primarySchedule)
	playoutManager.Stop(backupSchedule)
	playoutManager.StopRTPMonitor()
	close(monitorDone)
	monitorWg.Wait()
	
	t.Log("Layered recovery test complete - check logs to verify failover behavior")
}

// monitorRTPOutput listens for RTP packets and reports activity
// Reused from failover_test.go
