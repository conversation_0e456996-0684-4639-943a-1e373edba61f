package playout

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"math/rand"
	"net/url"
	"os"
	"os/exec"
	"path/filepath"
	"showfer-web/models"
	"showfer-web/service/logger"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/go-gst/go-gst/gst"
)

// AudioTrackInfo represents audio track information from FFprobe
type AudioTrackInfo struct {
	Index     int    `json:"index"`
	CodecName string `json:"codec_name"`
	Channels  int    `json:"channels"`
	Bitrate   string `json:"bit_rate"`
}

// FFprobeOutput represents the structure of FFprobe JSON output  
type FFprobeOutput struct {
	Streams []struct {
		Index     int    `json:"index"`
		CodecType string `json:"codec_type"`
		CodecName string `json:"codec_name"`
		Channels  int    `json:"channels"`
		BitRate   string `json:"bit_rate"`
	} `json:"streams"`
}

type StreamScheduler struct {
	fileHelper       *FileHelper
	host             string
	port             int
	networkInterface string
	items            []StreamItem
	mainPipeline     *gst.Pipeline
	sourcePipeline   *gst.Pipeline
	sourceCancelFunc context.CancelFunc
	compositor       *gst.Element
	mutex            sync.Mutex
	stopChan         chan struct{}
	switchNext       chan struct{}
	running          bool
	currentIndex     int
	currentItem      StreamItem
	nextItem         StreamItem
	schedulerID      string
	outputUrl        string
	codecSettings    models.CodecSettings
	currentDualMode  bool // Track current dual audio mode to detect changes
}

func NewStreamScheduler(schedule models.Schedule, db *sql.DB, baseDir string, codecSettings models.CodecSettings) (*StreamScheduler, error) {
	gst.Init(nil)

	// Parse the output URL
	u, err := url.Parse(schedule.OutputUrl)
	if err != nil {
		return nil, fmt.Errorf("failed to parse url: %v", err)
	}
	port, err := strconv.Atoi(u.Port())
	if err != nil {
		return nil, fmt.Errorf("failed to parse port: %v", err)
	}

	return &StreamScheduler{
		host:             u.Hostname(),
		port:             port,
		networkInterface: schedule.NetworkInterface,
		fileHelper:       NewFileHelper(db, schedule, baseDir),
		items:            make([]StreamItem, 0),
		stopChan:         make(chan struct{}),
		switchNext:       make(chan struct{}, 10),
		schedulerID:      schedule.ShortID,
		outputUrl:        schedule.OutputUrl,
		codecSettings:    codecSettings,
		currentDualMode:  codecSettings.DualAudioMode, // Initialize with current setting
	}, nil
}

func (s *StreamScheduler) RunSchedule() error {
	var err error
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.running {
		return fmt.Errorf("scheduler is already running")
	}
	s.running = true

	s.currentItem, err = s.fileHelper.GetItemByTime(time.Now().UTC(), true)
	if err != nil {
		return fmt.Errorf("failed to find item in the schedule: %v", err)
	}

	// Create and start the main pipeline
	err = s.createMainPipeline()
	if err != nil {
		return fmt.Errorf("failed to create main pipeline: %v", err)
	}

	s.mainPipeline.SetState(gst.StatePlaying)

	// Start playing items
	s.currentIndex = 0
	s.playCurrentItem()

	// Main loop to handle item switching
	go func() {
		for s.running {
			select {
			case <-s.switchNext:
				s.currentIndex++
				if !s.nextItemExist() {
					s.nextItem, err = s.fileHelper.GetItemByTime(time.Now().UTC(), false)
				}
				s.currentItem = s.nextItem
				s.playCurrentItem()

			case <-time.After(s.currentItem.Duration):
				// Make sure we don't block if a channel is full
				select {
				case s.switchNext <- struct{}{}:
					// Successfully sent signal
				default:
					// Channel is full, log and continue
					fmt.Printf("[%s] Warning: switchNext channel is full, skipping signal\n",
						time.Now().Format("15:04:05.000"))
					s.currentIndex++
					if !s.nextItemExist() {
						s.nextItem, err = s.fileHelper.GetItemByTime(time.Now().UTC(), false)
					}
					s.currentItem = s.nextItem
					s.playCurrentItem()
				}

			case <-s.stopChan:
				s.cleanupPipelines()
				return
			}
		}
	}()

	return nil
}

func (s *StreamScheduler) createMainPipeline() error {
	pipeline, err := gst.NewPipeline("main-pipeline" + s.schedulerID)
	if err != nil {
		return fmt.Errorf("failed to create main pipeline: %v", err)
	}

	// Create two intervideosrc elements for the two input channels
	// First, check if the intervideosrc element is available
	logger.Log("Checking if intervideosrc element is available...")
	checkCmd := exec.Command("gst-inspect-1.0", "intervideosrc")
	err = checkCmd.Run()
	if err != nil {
		logger.Error("intervideosrc element is not available: %v", err)
		logger.Log("Trying to reinstall GStreamer inter plugin...")

		// Try to reinstall the inter plugin
		installCmd := exec.Command("sudo", "apt-get", "install", "-y", "gstreamer1.0-plugins-bad")
		installOutput, installErr := installCmd.CombinedOutput()
		if installErr != nil {
			logger.Error("Failed to reinstall GStreamer inter plugin: %v\nOutput: %s", installErr, string(installOutput))
			return fmt.Errorf("intervideosrc element is not available and reinstallation failed: %v", err)
		}

		logger.Log("GStreamer inter plugin reinstalled, checking again...")
		checkCmd = exec.Command("gst-inspect-1.0", "intervideosrc")
		err = checkCmd.Run()
		if err != nil {
			logger.Error("intervideosrc element is still not available after reinstallation: %v", err)
			return fmt.Errorf("intervideosrc element is not available even after reinstallation: %v", err)
		}
	}

	// Try to create the intervideosrc elements with fallback mechanism
	var intervideo1, intervideo2 *gst.Element

	// First try to create intervideosrc1
	intervideo1, err = gst.NewElement("intervideosrc")
	if err != nil {
		logger.Error("Failed to create intervideosrc1: %v", err)
		logger.Log("Trying to create intervideosrc1 with registry update...")

		// Try to update the GStreamer registry
		updateCmd := exec.Command("gst-inspect-1.0", "--gst-update-registry")
		updateOutput, updateErr := updateCmd.CombinedOutput()
		if updateErr != nil {
			logger.Error("Failed to update GStreamer registry: %v\nOutput: %s", updateErr, string(updateOutput))
		} else {
			logger.Log("GStreamer registry updated, trying again...")
		}

		// Try again after updating the registry
		intervideo1, err = gst.NewElement("intervideosrc")
		if err != nil {
			logger.Error("Still failed to create intervideosrc1 after registry update: %v", err)

			// Try one more approach - reinitialize GStreamer
			logger.Log("Trying to reinitialize GStreamer...")
			gst.Deinit()
			gst.Init(nil)

			// Try one more time
			intervideo1, err = gst.NewElement("intervideosrc")
			if err != nil {
				logger.Error("Still failed to create intervideosrc1 after GStreamer reinitialization: %v", err)
				return fmt.Errorf("failed to create intervideosrc1 after multiple attempts: %v", err)
			}
		}
	}

	// Set properties after creation
	intervideo1.SetProperty("channel", "input1"+s.schedulerID)
	intervideo1.SetProperty("do-timestamp", true)
	intervideo1.SetProperty("name", "intervideosrc1"+s.schedulerID)

	logger.Log("Successfully created intervideosrc1 with channel input1%s", s.schedulerID)

	// Now try to create intervideosrc2
	intervideo2, err = gst.NewElement("intervideosrc")
	if err != nil {
		logger.Error("Failed to create intervideosrc2: %v", err)
		return fmt.Errorf("failed to create intervideosrc2: %v", err)
	}

	// Set properties after creation
	intervideo2.SetProperty("channel", "input2"+s.schedulerID)
	intervideo2.SetProperty("do-timestamp", true)
	intervideo2.SetProperty("name", "intervideosrc2"+s.schedulerID)

	logger.Log("Successfully created intervideosrc2 with channel input2%s", s.schedulerID)

	// Create video mixer (compositor)
	s.compositor, err = gst.NewElementWithProperties("compositor", map[string]interface{}{
		"background":            1, // black background
		"zero-size-is-unscaled": true,
		"name":                  "compositor" + s.schedulerID,
	})
	if err != nil {
		return fmt.Errorf("failed to create compositor: %v", err)
	}

	// Parse resolution for configuring the video format
	width, height := parseResolution(s.codecSettings.Resolution)

	// Configure compositor sink pads for proper positioning
	pad1 := s.compositor.GetStaticPad("sink_0")
	if pad1 != nil {
		pad1.SetProperty("xpos", 0)
		pad1.SetProperty("ypos", 0)
		pad1.SetProperty("width", width)
		pad1.SetProperty("height", height)
		pad1.SetProperty("alpha", 1.0) // input1 visible
	}

	pad2 := s.compositor.GetStaticPad("sink_1")
	if pad2 != nil {
		pad2.SetProperty("xpos", 0)
		pad2.SetProperty("ypos", 0)
		pad2.SetProperty("width", width)
		pad2.SetProperty("height", height)
		pad2.SetProperty("alpha", 0.0) // input2 hidden
	}

	// Create appropriate video encoder based on codec settings
	var videoEnc *gst.Element

	// Check if GPU is available for hardware acceleration
	useGPU := isGPUAvailable()

	if useGPU {
		// Use NVIDIA hardware acceleration
		if s.codecSettings.VCodec == "h264" {
			// Use nvh264enc for H.264 encoding with NVIDIA GPU
			videoEnc, err = gst.NewElementWithProperties("nvh264enc", map[string]interface{}{
				"bitrate":     s.codecSettings.VBitrate, // nvenc uses bits/sec
				"rc-mode":     1,                        // 1=CBR mode
				"gop-size":    60,                       // GOP size
				"preset":      2,                        // 2=medium quality preset
				"zerolatency": true,
				"name":        "nvh264enc" + s.schedulerID,
				"max-bitrate": s.codecSettings.MaxVBitrate,
			})
			if err != nil {
				logger.Warn("Failed to create nvh264enc, falling back to CPU encoding: %v", err)
				// Fall back to CPU encoding if nvh264enc fails
				videoEnc, err = gst.NewElementWithProperties("x264enc", map[string]interface{}{
					"tune":             0x00000004,                                    // zerolatency
					"bitrate":          s.codecSettings.VBitrate,                      // Target bitrate
					"vbv-buf-capacity": calculateBufferSize(s.codecSettings.VBitrate), // Buffer size based on bitrate
					"key-int-max":      60,                                            // GOP size
					"speed-preset":     3,                                             // medium
					"pass":             0,                                             // 0=cbr
					"rc-lookahead":     30,                                            // lookahead for better quality
					"sliced-threads":   true,
					"name":             "h264enc" + s.schedulerID,
					"aud":              true, // Add access unit delimiters
					"option-string":    fmt.Sprintf("vbv-maxrate=%d:vbv-init=0.8:nal-hrd=cbr:force-cfr=1", s.codecSettings.MaxVBitrate),
				})
			}
		} else if s.codecSettings.VCodec == "h265" {
			// Use nvh265enc for H.265 encoding with NVIDIA GPU
			videoEnc, err = gst.NewElementWithProperties("nvh265enc", map[string]interface{}{
				"bitrate":     s.codecSettings.VBitrate * 1000, // nvenc uses bits/sec
				"rc-mode":     1,                               // 1=CBR mode
				"gop-size":    60,                              // GOP size
				"preset":      2,                               // 2=medium quality preset
				"zerolatency": true,
				"name":        "nvh265enc" + s.schedulerID,
				"max-bitrate": s.codecSettings.MaxVBitrate * 1000,
			})
			if err != nil {
				logger.Warn("Failed to create nvh265enc, falling back to CPU encoding: %v", err)
				// Fall back to CPU encoding if nvh265enc fails
				videoEnc, err = gst.NewElementWithProperties("x265enc", map[string]interface{}{
					"tune":          0x00000004,                   // zerolatency
					"bitrate":       s.codecSettings.VBitrate,     // Target bitrate
					"vbv-bufsize":   s.codecSettings.VBitrate / 2, // Half of bitrate for strict CBR
					"key-int-max":   60,                           // GOP size
					"speed-preset":  4,                            // medium
					"name":          "h265enc" + s.schedulerID,
					"option-string": "hrd=1:nal-hrd=cbr", // Force CBR mode
				})
			}
		} else {
			// Default to H.264 with NVIDIA GPU for unsupported codecs
			videoEnc, err = gst.NewElementWithProperties("nvh264enc", map[string]interface{}{
				"bitrate":     s.codecSettings.VBitrate * 1000, // nvenc uses bits/sec
				"rc-mode":     1,                               // 1=CBR mode
				"gop-size":    60,                              // GOP size
				"preset":      2,                               // 2=medium quality preset
				"zerolatency": true,
				"name":        "nvh264enc" + s.schedulerID,
				"max-bitrate": s.codecSettings.MaxVBitrate * 1000,
			})
			if err != nil {
				logger.Warn("Failed to create nvh264enc, falling back to CPU encoding: %v", err)
				// Fall back to CPU encoding
				videoEnc, err = gst.NewElementWithProperties("x264enc", map[string]interface{}{
					"tune":             0x00000004,                                    // zerolatency
					"bitrate":          s.codecSettings.VBitrate,                      // Target bitrate
					"vbv-buf-capacity": calculateBufferSize(s.codecSettings.VBitrate), // Buffer size based on bitrate
					"key-int-max":      60,                                            // GOP size
					"speed-preset":     3,                                             // medium
					"pass":             0,                                             // 0=cbr
					"rc-lookahead":     30,                                            // lookahead for better quality
					"sliced-threads":   true,
					"name":             "h264enc" + s.schedulerID,
					"aud":              true, // Add access unit delimiters
					"option-string":    fmt.Sprintf("vbv-maxrate=%d:vbv-init=0.8:nal-hrd=cbr:force-cfr=1", s.codecSettings.MaxVBitrate),
				})
			}
		}
	} else {
		// Use CPU-based encoding
		if s.codecSettings.VCodec == "h264" {
			videoEnc, err = gst.NewElementWithProperties("x264enc", map[string]interface{}{
				"tune":             0x00000004,                                    // zerolatency
				"bitrate":          s.codecSettings.VBitrate,                      // Target bitrate
				"vbv-buf-capacity": calculateBufferSize(s.codecSettings.VBitrate), // Buffer size based on bitrate
				"key-int-max":      25,                                            // GOP size
				"speed-preset":     2,                                             // medium
				"pass":             0,
				"rc-lookahead":     0,
				"threads":          0,
				"b-adapt":          0,
				"bframes":          0,
				"sliced-threads":   true,
				"name":             "h264enc" + s.schedulerID,
				"aud":              true, // Add access unit delimiters
				"option-string":    fmt.Sprintf("vbv-maxrate=%d:vbv-init=0.8:nal-hrd=cbr:force-cfr=1", s.codecSettings.MaxVBitrate),
			})
		} else if s.codecSettings.VCodec == "h265" {
			videoEnc, err = gst.NewElementWithProperties("x265enc", map[string]interface{}{
				"tune":          0x00000004,                   // zerolatency
				"bitrate":       s.codecSettings.VBitrate,     // Target bitrate
				"vbv-bufsize":   s.codecSettings.VBitrate / 2, // Half of bitrate for strict CBR
				"key-int-max":   60,                           // GOP size
				"speed-preset":  4,                            // medium
				"name":          "h265enc" + s.schedulerID,
				"option-string": "hrd=1:nal-hrd=cbr", // Force CBR mode
			})
		} else {
			// Default to h264 if codec is not supported
			videoEnc, err = gst.NewElementWithProperties("x264enc", map[string]interface{}{
				"tune":             0x00000004,                                    // zerolatency
				"bitrate":          s.codecSettings.VBitrate,                      // Target bitrate
				"vbv-buf-capacity": calculateBufferSize(s.codecSettings.VBitrate), // Buffer size based on bitrate
				"key-int-max":      60,                                            // GOP size
				"speed-preset":     3,                                             // medium
				"pass":             0,                                             // 0=cbr
				"rc-lookahead":     30,                                            // lookahead for better quality
				"sliced-threads":   true,
				"name":             "h264enc" + s.schedulerID,
				"aud":              true, // Add access unit delimiters
				"option-string":    fmt.Sprintf("vbv-maxrate=%d:vbv-init=0.8:nal-hrd=cbr:force-cfr=1", s.codecSettings.MaxVBitrate),
			})
		}
	}

	if err != nil {
		return fmt.Errorf("failed to create video encoder: %v", err)
	}

	// Create audio elements (interaudiosrc, audiomixer, etc.) with fallback mechanisms
	var interaudio1, interaudio2 *gst.Element

	// Try to create interaudio1
	interaudio1, err = gst.NewElement("interaudiosrc")
	if err != nil {
		logger.Error("Failed to create interaudiosrc1: %v", err)
		logger.Log("Trying to create interaudiosrc1 with registry update...")

		// Try to update the GStreamer registry (if not already done for intervideosrc)
		updateCmd := exec.Command("gst-inspect-1.0", "--gst-update-registry")
		updateOutput, updateErr := updateCmd.CombinedOutput()
		if updateErr != nil {
			logger.Error("Failed to update GStreamer registry: %v\nOutput: %s", updateErr, string(updateOutput))
		} else {
			logger.Log("GStreamer registry updated, trying again...")
		}

		// Try again after updating the registry
		interaudio1, err = gst.NewElement("interaudiosrc")
		if err != nil {
			logger.Error("Still failed to create interaudiosrc1 after registry update: %v", err)

			// Try one more approach - reinitialize GStreamer
			logger.Log("Trying to reinitialize GStreamer...")
			gst.Deinit()
			gst.Init(nil)

			// Try one more time
			interaudio1, err = gst.NewElement("interaudiosrc")
			if err != nil {
				logger.Error("Still failed to create interaudiosrc1 after GStreamer reinitialization: %v", err)
				return fmt.Errorf("failed to create interaudiosrc1 after multiple attempts: %v", err)
			}
		}
	}

	// Set properties after creation
	interaudio1.SetProperty("channel", "audio1"+s.schedulerID)
	interaudio1.SetProperty("do-timestamp", true)
	interaudio1.SetProperty("name", "interaudiosrc1"+s.schedulerID)

	logger.Log("Successfully created interaudiosrc1 with channel audio1%s", s.schedulerID)

	// Try to create interaudio2
	interaudio2, err = gst.NewElement("interaudiosrc")
	if err != nil {
		logger.Error("Failed to create interaudiosrc2: %v", err)
		return fmt.Errorf("failed to create interaudiosrc2: %v", err)
	}

	// Set properties after creation
	interaudio2.SetProperty("channel", "audio2"+s.schedulerID)
	interaudio2.SetProperty("do-timestamp", true)
	interaudio2.SetProperty("name", "interaudiosrc2"+s.schedulerID)

	logger.Log("Successfully created interaudiosrc2 with channel audio2%s", s.schedulerID)

	audiomixer, err := gst.NewElement("audiomixer")
	audiomixer.SetProperty("name", "audiomixer"+s.schedulerID)
	if err != nil {
		return fmt.Errorf("failed to create audiomixer: %v", err)
	}

	// Check if dual audio mode is enabled
	var dualAudioElements map[string]*gst.Element
	if s.codecSettings.DualAudioMode {
		logger.Log("Creating dual audio pipeline with 5.1 surround + stereo tracks")
		
		// Create dual audio elements
		dualAudioElements, err = s.createDualAudioElements()
		if err != nil {
			return fmt.Errorf("failed to create dual audio elements: %v", err)
		}
		
		// We'll use the audiomixer output as input to the audio tee
		// The old single audio encoder logic will be bypassed
	} else {
		// Original single audio track logic
		logger.Log("Creating single audio track pipeline")
		
		// Create audio encoder based on codec settings
		var audioEnc *gst.Element

		if s.codecSettings.ACodec == "aac_downmix" {
			audioEnc, err = gst.NewElementWithProperties("avenc_aac", map[string]interface{}{
				"bitrate": s.codecSettings.ABitrate * 1000, // Convert from kbps to bps
				"name":    "aacenc" + s.schedulerID,
			})
		} else if s.codecSettings.ACodec == "mpeg1l2_downmix" {
			audioEnc, err = gst.NewElementWithProperties("twolamemp2enc", map[string]interface{}{
				"bitrate":  s.codecSettings.ABitrate, // twolame uses kbps directly
				"name":     "mp2enc" + s.schedulerID,
				"mode":     1, // 0=stereo, 1=joint-stereo, 2=dual-channel, 3=mono
				"psymodel": 3, // Use the best psymodel (3)
			})
		} else if s.codecSettings.ACodec == "ac3_downmix" {
			audioEnc, err = gst.NewElementWithProperties("avenc_ac3", map[string]interface{}{
				"bitrate": s.codecSettings.ABitrate * 1000, // Convert from kbps to bps
				"name":    "ac3enc" + s.schedulerID,
			})
		} else if s.codecSettings.ACodec == "ac3_passthrough" {
			// For AC3 passthrough, we'll just use a parser to handle the AC3 stream
			audioEnc, err = gst.NewElementWithProperties("avenc_ac3", map[string]interface{}{
				"bitrate": s.codecSettings.ABitrate * 1000, // Convert from kbps to bps
				"name":    "ac3enc" + s.schedulerID,
			})
		} else {
			// Default to AAC if codec is not supported
			audioEnc, err = gst.NewElementWithProperties("avenc_aac", map[string]interface{}{
				"bitrate": s.codecSettings.ABitrate * 1000, // Convert from kbps to bps
				"name":    "aacenc" + s.schedulerID,
			})
		}
		if err != nil {
			return fmt.Errorf("failed to create audio encoder: %v", err)
		}

		var audioparse *gst.Element
		if s.codecSettings.ACodec == "aac_downmix" {
			audioparse, err = gst.NewElement("aacparse")
		} else if s.codecSettings.ACodec == "mpeg1l2_downmix" {
			audioparse, err = gst.NewElement("mpegaudioparse")
		} else {
			audioparse, err = gst.NewElement("ac3parse")
		}

		if err != nil {
			return fmt.Errorf("failed to create audioparse: %v", err)
		}
		
		// Store these for later linking (single audio mode)
		dualAudioElements = map[string]*gst.Element{
			"audioEnc":   audioEnc,
			"audioparse": audioparse,
		}
	}

	audiomixercaps, err := gst.NewElementWithProperties("capsfilter", map[string]interface{}{
		"caps": gst.NewCapsFromString("audio/x-raw, format={S16LE, F32LE}, layout=interleaved, rate={ 44100, 48000 }, channels=2"),
	})

	if err != nil {
		return fmt.Errorf("failed to create audio audiomixercaps: %v", err)
	}

	// Add a tee element to split the stream
	streamTee, err := gst.NewElement("tee")
	if err != nil {
		return fmt.Errorf("failed to create tee element: %v", err)
	}

	hlsQueue, err := gst.NewElementWithProperties("queue", map[string]interface{}{
		"max-size-buffers": 100,
		"max-size-time":    uint64(500 * time.Millisecond),
		"leaky":            0, // No leaking
		"name":             "hlsQueue" + s.schedulerID,
	})
	if err != nil {
		return fmt.Errorf("failed to create hlsQueue: %v", err)
	}

	// Create HLS elements
	hlsDir := filepath.Join("data", "preview", s.schedulerID)
	os.MkdirAll(hlsDir, 0755)

	hlssink, err := gst.NewElementWithProperties("hlssink", map[string]interface{}{
		"location":          filepath.Join(hlsDir, "segment_%05d.ts"),
		"playlist-location": filepath.Join(hlsDir, "playlist.m3u8"),
		"target-duration":   6,
		"playlist-length":   5,
		"max-files":         10,
		"name":              "hlssink" + s.schedulerID,
	})
	if err != nil {
		return fmt.Errorf("failed to create hlssink: %v", err)
	}

	if err != nil {
		return fmt.Errorf("failed to create udpsink: %v", err)
	}

	// Add queues to manage latency in the main pipeline
	videoQueue1, err := gst.NewElementWithProperties("queue", map[string]interface{}{
		"max-size-buffers": 5,
		"max-size-time":    uint64(500 * time.Millisecond),
		"leaky":            2, // No leaking
		"name":             "videoQueue1" + s.schedulerID,
	})
	if err != nil {
		return fmt.Errorf("failed to create videoQueue1: %v", err)
	}

	videoQueue2, err := gst.NewElementWithProperties("queue", map[string]interface{}{
		"max-size-buffers": 5,
		"max-size-time":    uint64(500 * time.Millisecond),
		"leaky":            2, // No leaking
		"name":             "videoQueue2" + s.schedulerID,
	})
	if err != nil {
		return fmt.Errorf("failed to create videoQueue2: %v", err)
	}

	audioQueue1, err := gst.NewElementWithProperties("queue", map[string]interface{}{
		"max-size-buffers":   100,
		"max-size-time":      uint64(500 * time.Millisecond),
		"min-threshold-time": uint64(50 * time.Millisecond),
		"leaky":              0, // No leaking
		"name":               "audioQueue1" + s.schedulerID,
	})
	if err != nil {
		return fmt.Errorf("failed to create audioQueue1: %v", err)
	}

	audioQueue2, err := gst.NewElementWithProperties("queue", map[string]interface{}{
		"max-size-buffers":   100,
		"max-size-time":      uint64(500 * time.Millisecond),
		"min-threshold-time": uint64(50 * time.Millisecond),
		"leaky":              0, // No leaking
		"name":               "audioQueue2" + s.schedulerID,
	})
	if err != nil {
		return fmt.Errorf("failed to create audioQueue2: %v", err)
	}

	// Add queues after mixer/compositor
	videoMixerQueue, err := gst.NewElementWithProperties("queue", map[string]interface{}{
		"max-size-buffers":   100,
		"max-size-time":      uint64(500 * time.Millisecond),
		"min-threshold-time": uint64(50 * time.Millisecond),
		"leaky":              0, // No leaking
		"name":               "videoMixerQueue" + s.schedulerID,
	})
	if err != nil {
		return fmt.Errorf("failed to create videoMixerQueue: %v", err)
	}

	// Create videocaps to set frame rate from codec settings
	videocaps, err := gst.NewElementWithProperties("capsfilter", map[string]interface{}{
		"caps": gst.NewCapsFromString(fmt.Sprintf("video/x-raw, framerate=%d/1", int(s.codecSettings.FPS))),
		"name": "videocaps" + s.schedulerID,
	})
	if err != nil {
		return fmt.Errorf("failed to create videocaps: %v", err)
	}

	// Create muxer and RTP elements
	mpegtsmux, err := gst.NewElementWithProperties("mpegtsmux", map[string]interface{}{
		"alignment":    7,
		"pat-interval": int64(27000),
		"pmt-interval": int64(27000),
		"pcr-interval": int64(2700),
		"si-interval":  int64(500),
		"bitrate":      int64(s.codecSettings.MaxVBitrate * 1000), // Combined bitrate for audio and video
		"name":         "mpegtsmux" + s.schedulerID,
		"start-time":   int64(500000000),
		//	"prog-map":     fmt.Sprintf("service_provider=Traffiq,service_name=%s", s.schedulerID),
	})
	if err != nil {
		return fmt.Errorf("failed to create mpegtsmux: %v", err)
	}

	rtpBin, err := gst.NewElementWithProperties("rtpbin", map[string]interface{}{
		"latency":                uint(400), // Increase latency to handle jitter
		"do-retransmission":      true,      // Enable retransmission
		"rtp-profile":            2,         // AVP profile
		"ntp-sync":               true,      // Enable NTP sync
		"ntp-time-source":        3,         // Use running time
		"max-rtcp-rtp-time-diff": 1000,      // Max allowed drift
		"max-dropout-time":       45000,     // Max time to wait for missing packets
		"max-misorder-time":      5000,      // Max reordering time
		"buffer-mode":            1,         // Slave receiver mode
		"do-lost":                true,      // Handle packet loss
		"max-lateness":           50000000,  // 50ms max lateness
		"rtcp-sync-send-time":    true,      // Send RTCP sync packets
		"name":                   "rtpbin" + s.schedulerID,
	})
	if err != nil {
		return fmt.Errorf("failed to create videocaps: %v", err)
	}

	rtpmp2tpay, err := gst.NewElementWithProperties("rtpmp2tpay", map[string]interface{}{
		"pt":               33,
		"mtu":              1316, // Optimal for MPEG-TS (7 × 188)
		"perfect-rtptime":  true,
		"min-ptime":        uint64(1 * time.Millisecond),
		"max-ptime":        uint64(2 * time.Millisecond), // Reduced for more consistent timing
		"ptime-multiple":   uint64(1 * time.Millisecond),
		"ssrc":             uint(rand.Uint32()),
		"name":             "rtpmp2tpay" + s.schedulerID,
		"seqnum-offset":    uint(rand.Uint32() & 0xFFFF),
		"timestamp-offset": uint(rand.Uint32()),
	})
	if err != nil {
		return fmt.Errorf("failed to create rtpmp2tpay: %v", err)
	}

	// 3. Add a queue before mpegtsmux with timing-sensitive settings
	preMuxQueue, err := gst.NewElementWithProperties("queue", map[string]interface{}{
		"max-size-buffers":   30,
		"max-size-time":      uint64(100 * time.Millisecond), // Short buffer for low latency
		"min-threshold-time": uint64(20 * time.Millisecond),
		"leaky":              0,
		"name":               "preMuxQueue" + s.schedulerID,
	})
	// 4. Optimize RTP queue settings
	rtpQueue, err := gst.NewElementWithProperties("queue", map[string]interface{}{
		"max-size-buffers":   100,                            // Reduced from 1000
		"max-size-bytes":     2097152,                        // 2MB (reduced from 10MB)
		"max-size-time":      uint64(500 * time.Millisecond), // Reduced from 2s
		"min-threshold-time": uint64(20 * time.Millisecond),
		"leaky":              0,
		"flush-on-eos":       false,
		"name":               "rtpQueue" + s.schedulerID,
	})

	// 5. Add clocksync element for better timing
	clocksync, err := gst.NewElementWithProperties("clocksync", map[string]interface{}{
		"sync-to-first": true,
		"name":          "clocksync" + s.schedulerID,
		"latency":       int64(100 * time.Millisecond),
	})

	udpsink, err := gst.NewElementWithProperties("udpsink", map[string]interface{}{
		"host":           s.host,
		"port":           s.port,
		"sync":           false,    // Disable sync for RTP
		"async":          false,    // Disable async
		"buffer-size":    16777216, // 16MB buffer
		"max-lateness":   int64(50 * time.Millisecond),
		"qos":            true,
		"auto-multicast": true,
		"ttl":            255, // Standard TTL property
		"ttl-mc":         255, // Alternative TTL property for multicast
		"name":           "udpsink" + s.schedulerID,
	})
	// Set the multicast interface if specified
	if s.networkInterface != "" {
		udpsink.SetProperty("multicast-iface", s.networkInterface)
		fmt.Printf("[%s] Using network interface %s for multicast transmission\n",
			time.Now().Format("15:04:05.000"), s.networkInterface)
	}
	pipeline.Add(rtpBin)
	// Add all elements to the pipeline
	pipeline.AddMany(intervideo1, intervideo2, s.compositor, videoEnc)
	
	// Add audio elements based on mode
	if s.codecSettings.DualAudioMode {
		// Add dual audio elements
		pipeline.AddMany(interaudio1, interaudio2, audiomixercaps, audiomixer)
		
		// Add all dual audio elements to pipeline
		for _, element := range dualAudioElements {
			pipeline.Add(element)
		}
		
		logger.Log("Added dual audio elements to pipeline")
	} else {
		// Add single audio elements (original logic)
		pipeline.AddMany(interaudio1, interaudio2, audiomixercaps, audiomixer)
		pipeline.AddMany(dualAudioElements["audioparse"], dualAudioElements["audioEnc"])
	}
	
	pipeline.AddMany(mpegtsmux, streamTee, rtpmp2tpay, udpsink, hlssink)
	pipeline.AddMany(rtpQueue, clocksync, preMuxQueue, hlsQueue, hlssink)
	pipeline.AddMany(videoQueue1, videoQueue2, audioQueue1, audioQueue2)
	pipeline.Add(videoMixerQueue)
	pipeline.Add(videocaps)

	// Link video elements
	intervideo1.Link(videoQueue1)
	videoQueue1.Link(s.compositor)
	intervideo2.Link(videoQueue2)
	videoQueue2.Link(s.compositor)
	s.compositor.Link(videoMixerQueue)
	videoMixerQueue.Link(videocaps)
	videocaps.Link(videoEnc)
	videoEnc.Link(mpegtsmux)
	//preMuxQueue.Link(mpegtsmux)
	mpegtsmux.Link(clocksync)
	clocksync.Link(streamTee)

	// Link audio elements based on mode
	err = interaudio1.Link(audioQueue1)
	if err != nil {
		return fmt.Errorf("####### failed to link interaudio1 to audioQueue1: %v", err)
	}

	audioQueue1.Link(audiomixer)

	interaudio2.Link(audioQueue2)
	audioQueue2.Link(audiomixer)
	audiomixer.Link(audiomixercaps)
	
	if s.codecSettings.DualAudioMode {
		// Link dual audio pipeline
		logger.Log("Linking dual audio pipeline")
		err = s.linkDualAudioElements(dualAudioElements, audiomixercaps, mpegtsmux)
		if err != nil {
			return fmt.Errorf("failed to link dual audio elements: %v", err)
		}
	} else {
		// Link single audio pipeline (original logic)
		audiomixercaps.Link(dualAudioElements["audioEnc"])
		dualAudioElements["audioEnc"].Link(dualAudioElements["audioparse"])
		dualAudioElements["audioparse"].Link(mpegtsmux)
	}

	// Link muxer to RTP and UDP sink
	mpegtsmux.Link(streamTee)

	// Link the RTP branch
	streamTee.Link(rtpmp2tpay)
	//	rtpQueue.Link(rtpmp2tpay)

	// 2. Get a request sink pad from rtpbin
	sendRtpSinkPad := rtpBin.GetRequestPad("send_rtp_sink_0")
	if sendRtpSinkPad == nil {
		log.Fatal("Could not get 'send_rtp_sink_0' pad from rtpbin")
	}

	// 3. Link rtpmp2tpay src pad to rtpbin's send_rtp_sink_0 pad
	rtpmp2tpaySrcPad := rtpmp2tpay.GetStaticPad("src")
	if rtpmp2tpaySrcPad == nil {
		log.Fatal("Could not get 'src' pad from rtpmp2tpay")
	}
	rtpmp2tpaySrcPad.Link(sendRtpSinkPad)

	rtpSrcPad := rtpBin.GetStaticPad("send_rtp_src_0")
	rtpSrcPad.Link(udpsink.GetStaticPad("sink"))
	// rtpmp2tpay.Link(rtpjitterbuffer)

	// rtpjitterbuffer.Link(udpsink)

	// Link the HLS branch
	streamTee.Link(hlsQueue)
	hlsQueue.Link(hlssink)

	// Set up bus watch
	bus := pipeline.GetBus()
	go func() {
		for {
			msg := bus.TimedPopFiltered(gst.ClockTime(100_000_000), gst.MessageAny) // 100ms
			if msg == nil {
				break
			}

			switch msg.Type() {
			case gst.MessageError:
				gerr := msg.ParseError()
				fmt.Printf("Error from element %s: %s\n", msg.Source(), gerr.Error())
			case gst.MessageWarning:
				gerr := msg.ParseWarning()
				fmt.Printf("Warning from element %s: %s\n", msg.Source(), gerr.Error())
			case gst.MessageEOS:
				fmt.Printf("End of stream received\n")
			}
		}
	}()

	s.mainPipeline = pipeline
	return nil
}

func (s *StreamScheduler) createSourcePipeline(item StreamItem, index int, channel string) (*gst.Pipeline, error) {
	pipelineName := fmt.Sprintf("source-pipeline-%d", index)
	pipeline, err := gst.NewPipeline(pipelineName)
	if err != nil {
		return nil, fmt.Errorf("failed to create source pipeline: %v", err)
	}

	// For test pattern type, create a special pipeline
	if item.Type == "test" {
		return s.createTestPatternPipeline(index, channel)
	}

	// Check if the file exists before trying to play it
	if item.Type == "file" {
		if _, err := os.Stat(item.Source); os.IsNotExist(err) {
			logger.Error("Source file does not exist: %s", item.Source)
			// Return a more specific error that can be handled by the caller
			return nil, fmt.Errorf("source file does not exist: %s", item.Source)
		}
	}

	// Check if GPU is available for hardware acceleration
	useGPU := isGPUAvailable()

	var playbin *gst.Element

	if useGPU {
		// Try to create a custom pipeline with hardware decoding
		logger.Log("Using GPU-accelerated decoding for source pipeline")

		// Create a custom pipeline with hardware decoding
		// We'll still use playbin3 but configure it to use nvdec for decoding
		playbin, err = gst.NewElementWithProperties("playbin3", map[string]interface{}{
			"uri": fmt.Sprintf("file://%s", item.Source),
		})
		if err != nil {
			return nil, fmt.Errorf("failed to create playbin: %v", err)
		}

		// Set flags to prefer hardware decoding
		playbin.SetProperty("flags", 0x00000057) // Enable audio, video, text, native-video
	} else {
		// Create standard playbin for CPU-based decoding
		playbin, err = gst.NewElementWithProperties("playbin3", map[string]interface{}{
			"uri": fmt.Sprintf("file://%s", item.Source),
		})
		if err != nil {
			return nil, fmt.Errorf("failed to create playbin: %v", err)
		}
	}

	// Add buffering properties to playbin
	playbin.SetProperty("buffer-size", 20971520) // 10MB buffer
	playbin.SetProperty("buffer-duration", uint64(10*time.Second))
	playbin.SetProperty("low-percent", 10)
	playbin.SetProperty("high-percent", 99)
	playbin.SetProperty("use-buffering", true)
	playbin.SetProperty("ring-buf-max-size", 104857600)

	// Create video sink with fallback mechanism
	var intervideosink *gst.Element

	// Try to create intervideosink
	intervideosink, err = gst.NewElement("intervideosink")
	if err != nil {
		logger.Error("Failed to create intervideosink: %v", err)
		logger.Log("Trying to create intervideosink with registry update...")

		// Try to update the GStreamer registry
		updateCmd := exec.Command("gst-inspect-1.0", "--gst-update-registry")
		updateOutput, updateErr := updateCmd.CombinedOutput()
		if updateErr != nil {
			logger.Error("Failed to update GStreamer registry: %v\nOutput: %s", updateErr, string(updateOutput))
		} else {
			logger.Log("GStreamer registry updated, trying again...")
		}

		// Try again after updating the registry
		intervideosink, err = gst.NewElement("intervideosink")
		if err != nil {
			logger.Error("Still failed to create intervideosink after registry update: %v", err)

			// Try one more approach - reinitialize GStreamer
			logger.Log("Trying to reinitialize GStreamer...")
			gst.Deinit()
			gst.Init(nil)

			// Try one more time
			intervideosink, err = gst.NewElement("intervideosink")
			if err != nil {
				logger.Error("Still failed to create intervideosink after GStreamer reinitialization: %v", err)
				return nil, fmt.Errorf("failed to create intervideosink after multiple attempts: %v", err)
			}
		}
	}

	// Set properties after creation
	intervideosink.SetProperty("channel", channel+s.schedulerID)
	intervideosink.SetProperty("sync", true)

	// Add latency control to the video sink
	intervideosink.SetProperty("max-lateness", int64(20*time.Millisecond))
	intervideosink.SetProperty("qos", true)

	logger.Log("Successfully created intervideosink with channel %s", channel+s.schedulerID)

	// Set video sink on playbin
	playbin.SetProperty("video-sink", intervideosink)

	// Create audio bin with format conversion
	audiobin := gst.NewBin("audiobin")

	// Standard audio processing for downmix options
	audioconvert, err := gst.NewElement("audioconvert")
	if err != nil {
		return nil, fmt.Errorf("failed to create audioconvert: %v", err)
	}

	audioresample, err := gst.NewElement("audioresample")
	if err != nil {
		return nil, fmt.Errorf("failed to create audioresample: %v", err)
	}

	audiocaps, err := gst.NewElementWithProperties("capsfilter", map[string]interface{}{
		"caps": gst.NewCapsFromString("audio/x-raw, format={S16LE, F32LE}, layout=interleaved, rate=48000, channels=2"),
		"name": "audiocaps" + s.schedulerID,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create audiocaps: %v", err)
	}

	// Create audio sink with proper format and fallback mechanism
	var interaudiosink *gst.Element

	// Try to create interaudiosink
	interaudiosink, err = gst.NewElement("interaudiosink")
	if err != nil {
		logger.Error("Failed to create interaudiosink: %v", err)
		logger.Log("Trying to create interaudiosink with registry update...")

		// Try to update the GStreamer registry (if not already done for intervideosink)
		updateCmd := exec.Command("gst-inspect-1.0", "--gst-update-registry")
		updateOutput, updateErr := updateCmd.CombinedOutput()
		if updateErr != nil {
			logger.Error("Failed to update GStreamer registry: %v\nOutput: %s", updateErr, string(updateOutput))
		} else {
			logger.Log("GStreamer registry updated, trying again...")
		}

		// Try again after updating the registry
		interaudiosink, err = gst.NewElement("interaudiosink")
		if err != nil {
			logger.Error("Still failed to create interaudiosink after registry update: %v", err)

			// Try one more approach - reinitialize GStreamer
			logger.Log("Trying to reinitialize GStreamer...")
			gst.Deinit()
			gst.Init(nil)

			// Try one more time
			interaudiosink, err = gst.NewElement("interaudiosink")
			if err != nil {
				logger.Error("Still failed to create interaudiosink after GStreamer reinitialization: %v", err)
				return nil, fmt.Errorf("failed to create interaudiosink after multiple attempts: %v", err)
			}
		}
	}

	// Set properties after creation
	interaudiosink.SetProperty("channel", fmt.Sprintf("audio%s", channel[len(channel)-1:])+s.schedulerID)
	interaudiosink.SetProperty("sync", true)
	interaudiosink.SetProperty("name", "interaudiosink"+s.schedulerID)

	logger.Log("Successfully created interaudiosink with channel audio%s%s", channel[len(channel)-1:], s.schedulerID)

	// Add elements to bin
	audiobin.AddMany(audioconvert, audioresample, audiocaps, interaudiosink)

	// Link elements in bin
	audioconvert.Link(audioresample)
	audioresample.Link(audiocaps)
	audiocaps.Link(interaudiosink)

	// Create and add ghost pad using the bin's method
	sinkpad := audioconvert.GetStaticPad("sink")
	if sinkpad == nil {
		return nil, fmt.Errorf("failed to get sink pad from audioconvert")
	}

	ghostpad := gst.NewGhostPad("sink", sinkpad)
	if ghostpad == nil {
		return nil, fmt.Errorf("failed to create ghost pad")
	}

	if !audiobin.AddPad(ghostpad.Pad) {
		return nil, fmt.Errorf("failed to add ghost pad to bin")
	}

	// Set audio-sink property on playbin to use our bin
	playbin.SetProperty("audio-sink", audiobin)

	// Add playbin to pipeline
	pipeline.Add(playbin)

	ctx, cancel := context.WithCancel(context.Background())
	s.sourceCancelFunc = cancel

	// Set up bus watch
	bus := pipeline.GetBus()
	go func() {
		for {
			select {
			case <-ctx.Done():
				return
			default:
				msg := bus.TimedPopFiltered(gst.ClockTime(100_000_000), gst.MessageAny) // 100ms
				if msg == nil {
					continue
				}

				switch msg.Type() {
				case gst.MessageError:
					gerr := msg.ParseError()
					fmt.Printf("[%s] Error from source %d: %s\n",
						time.Now().Format("15:04:05.000"), index, gerr.Error())
					// Try to recover by scheduling the next item with non-blocking send
					select {
					case s.switchNext <- struct{}{}:
						// Successfully sent the signal
					default:
						// Channel is full, log warning
						fmt.Printf("[%s] Warning: switchNext channel is full in error recovery\n",
							time.Now().Format("15:04:05.000"))
					}
				case gst.MessageEOS:
					fmt.Printf("[%s] End of stream for source %d\n",
						time.Now().Format("15:04:05.000"), index)
					// Use non-blocking send for EOS too
					select {
					case s.switchNext <- struct{}{}:
						// Successfully sent the signal
					default:
						// Channel is full, use AfterFunc to retry
						fmt.Printf("[%s] Warning: switchNext channel is full on EOS, scheduling retry\n",
							time.Now().Format("15:04:05.000"))
						time.AfterFunc(100*time.Millisecond, func() {
							select {
							case s.switchNext <- struct{}{}:
								// Successfully sent after delay
							default:
								// Still full, log warning
								fmt.Printf("[%s] Warning: switchNext channel still full on EOS\n",
									time.Now().Format("15:04:05.000"))
							}
						})
					}
				case gst.MessageStateChanged:
					if msg.Source() == pipeline.Element.GetName() {
						oldState, newState := msg.ParseStateChanged()
						if oldState == gst.StatePaused && newState == gst.StatePlaying {
							fmt.Printf("[%s] Pipeline %d state changed to PLAYING\n",
								time.Now().Format("15:04:05.000"), index)
						}
					}
				}
			}
		}
	}()

	return pipeline, nil
}

func (s *StreamScheduler) playCurrentItem() {
	fmt.Printf("[%s] Preparing to play item %d: %s\n",
		time.Now().Format("15:04:05.000"), s.currentIndex, s.currentItem.Source)

	// Detect and update codec settings based on the source file's audio tracks
	if s.currentItem.Type == "file" {
		s.updateCodecSettingsForFile(s.currentItem.Source)
	}

	// Check if we need to recreate the main pipeline due to dual audio mode change
	if s.codecSettings.DualAudioMode != s.currentDualMode {
		logger.Log("Dual audio mode changed from %v to %v, recreating main pipeline",
			s.currentDualMode, s.codecSettings.DualAudioMode)
		
		// Stop and cleanup current main pipeline
		if s.mainPipeline != nil {
			s.mainPipeline.SetState(gst.StateNull)
			s.mainPipeline.Unref()
			s.mainPipeline = nil
		}
		
		// Recreate main pipeline with new dual audio settings
		err := s.createMainPipeline()
		if err != nil {
			logger.Error("Failed to recreate main pipeline: %v", err)
			return
		}
		
		// Start the new main pipeline
		s.mainPipeline.SetState(gst.StatePlaying)
		s.currentDualMode = s.codecSettings.DualAudioMode
	}

	if s.sourcePipeline != nil {
		s.sourcePipeline.SetState(gst.StateNull)
		s.sourcePipeline.Unref()
		s.sourceCancelFunc()
	}

	// Determine which channel to use (alternating between 1 and 2)
	channel := fmt.Sprintf("input%d", (s.currentIndex%2)+1)

	// Create a new source pipeline
	pipeline, err := s.createSourcePipeline(s.currentItem, s.currentIndex, channel)
	if err != nil {
		logger.Error("Error creating source pipeline: %v", err)

		// Check if this is a file not found error
		if strings.Contains(err.Error(), "source file does not exist") {
			logger.Error("File not found: %s", s.currentItem.Source)

			// Try to get a filler as a fallback
			end := s.currentItem.Start.Add(5 * time.Minute) // Use 5 minutes as a reasonable window
			fallbackItem, fallbackErr := s.fileHelper.getRandFiller(s.fileHelper.schedule.Fillers, s.currentItem.Start, end, false)

			if fallbackErr == nil {
				// Use the fallback item
				logger.Warn("Using fallback filler due to missing file")
				s.currentItem = fallbackItem

				// Try again with the fallback item
				channel = fmt.Sprintf("input%d", (s.currentIndex%2)+1)
				pipeline, err = s.createSourcePipeline(s.currentItem, s.currentIndex, channel)

				if err != nil {
					logger.Error("Error creating fallback pipeline: %v", err)
					// Still failed, try to recover by scheduling the next item
					s.scheduleNextItemAfterError()
					return
				}
			} else {
				logger.Error("Failed to get fallback filler: %v", fallbackErr)
				// Try to recover by scheduling the next item
				s.scheduleNextItemAfterError()
				return
			}
		} else {
			// Other error, try to recover by scheduling the next item
			s.scheduleNextItemAfterError()
			return
		}
	}

	s.sourcePipeline = pipeline

	// Start playing
	err1 := pipeline.SetState(gst.StatePlaying)
	if err1 != nil {
		fmt.Printf("[%s] Failed to set pipeline to playing state:\n",
			time.Now().Format("15:04:05.000"))
		// Try to recover by scheduling the next item with non-blocking send
		time.AfterFunc(1*time.Second, func() {
			select {
			case s.switchNext <- struct{}{}:
				// Successfully sent the signal
			default:
				// Channel is full, log warning
				fmt.Printf("[%s] Warning: switchNext channel is full in state recovery\n",
					time.Now().Format("15:04:05.000"))
			}
		})
		return
	}

	fmt.Printf("[%s] Started playing item %d: %s on channel %s\n",
		time.Now().Format("15:04:05.000"), s.currentIndex, s.currentItem.Source, channel)

	// Handle seeking to offset if needed
	if s.currentItem.Offset > 0 {
		go func(offset time.Duration) {
			// Wait a moment for pipeline to stabilize
			time.Sleep(200 * time.Millisecond)
			fmt.Printf("[%s] Seeking to offset %v\n",
				time.Now().Format("15:04:05.000"), offset)

			// Perform seek operation
			ret := pipeline.SeekTime(
				offset,
				gst.SeekFlagFlush|gst.SeekFlagKeyUnit,
			)
			if !ret {
				fmt.Printf("[%s] Failed to seek to offset\n",
					time.Now().Format("15:04:05.000"))
			}
		}(s.currentItem.Offset)
	}

	s.nextItem, err = s.fileHelper.GetItemByTime(s.currentItem.Start.Add(s.currentItem.Duration), false)
	if err != nil {
		logger.Error("Failed to get next item by time: %v", err)

		// Instead of stopping the entire scheduler, try to recover
		// First, try to get a filler as a fallback
		end := s.currentItem.Start.Add(s.currentItem.Duration).Add(5 * time.Minute) // Use 5 minutes as a reasonable window
		fallbackItem, fallbackErr := s.fileHelper.getRandFiller(s.fileHelper.schedule.Fillers, s.currentItem.Start.Add(s.currentItem.Duration), end, false)

		if fallbackErr != nil {
			logger.Error("Failed to get fallback filler after schedule error: %v", fallbackErr)
			// Only stop if we absolutely cannot recover
			logger.Error("Critical error in scheduler, stopping playback")
			s.Stop()
			return
		}

		// Use the fallback item as the next item
		logger.Warn("Using fallback filler due to schedule error")
		s.nextItem = fallbackItem
	}

	// Toggle visibility based on the current channel
	if channel == "input1" {

		pad1 := s.compositor.GetStaticPad("sink_0")
		pad2 := s.compositor.GetStaticPad("sink_1")
		if pad1 != nil && pad2 != nil {
			pad1.SetProperty("alpha", 1.0) // Show input1
			pad2.SetProperty("alpha", 0.0) // Hide input2
		}
	} else {

		pad1 := s.compositor.GetStaticPad("sink_0")
		pad2 := s.compositor.GetStaticPad("sink_1")
		if pad1 != nil && pad2 != nil {
			pad1.SetProperty("alpha", 0.0) // Hide input1
			pad2.SetProperty("alpha", 1.0) // Show input2
		}
	}
}

// Helper method to schedule the next item after an error
func (s *StreamScheduler) scheduleNextItemAfterError() {
	// Try to recover by scheduling the next item with non-blocking send
	time.AfterFunc(1*time.Second, func() {
		select {
		case s.switchNext <- struct{}{}:
			// Successfully sent the signal
			logger.Log("Scheduled next item after error")
		default:
			// Channel is full, log warning
			logger.Warn("Warning: switchNext channel is full in error recovery")
		}
	})
}

func (s *StreamScheduler) cleanupPipelines() {
	// Stop all pipelines
	if s.mainPipeline != nil {
		s.mainPipeline.SetState(gst.StateNull)
		s.mainPipeline.Unref()
	}

	if s.sourcePipeline != nil {
		s.sourcePipeline.SetState(gst.StateNull)
		s.sourceCancelFunc()
		s.sourcePipeline.Unref()
	}
}

func (s *StreamScheduler) Stop() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.running {
		return
	}

	close(s.stopChan)
	s.running = false
}

// Helper function to calculate optimal buffer size
// Used for setting buffer sizes in video encoders
func calculateBufferSize(bitrate int) int {
	// Buffer size should be at least 1 second of video at the given bitrate
	// For CBR encoding, a buffer size of 1-2 times the bitrate is recommended
	// Return value is in kilobits (as expected by vbv-buf-capacity)
	bufsize := bitrate * 2 // Convert to kilobits and double
	if bufsize < 300 {
		return 300
	}
	if bufsize > 10000 {
		return 10000
	}
	return bufsize
}

// createTestPatternPipeline creates a pipeline with a test pattern source
func (s *StreamScheduler) createTestPatternPipeline(index int, channel string) (*gst.Pipeline, error) {
	pipelineName := fmt.Sprintf("test-pattern-pipeline-%d", index)
	pipeline, err := gst.NewPipeline(pipelineName)
	if err != nil {
		return nil, fmt.Errorf("failed to create test pattern pipeline: %v", err)
	}

	// Check if GPU is available for hardware acceleration
	// For test patterns, we don't need GPU acceleration for decoding
	// but we'll log it for consistency
	useGPU := isGPUAvailable()
	if useGPU {
		logger.Log("GPU available for test pattern pipeline, but not needed for test sources")
	}

	// Parse resolution for configuring the video format
	width, height := parseResolution(s.codecSettings.Resolution)

	// Create a videotestsrc element for the test pattern
	videotestsrc, err := gst.NewElementWithProperties("videotestsrc", map[string]interface{}{
		"pattern": 0, // SMPTE color bars
		"is-live": true,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create videotestsrc: %v", err)
	}

	// Create a video converter and scale to the correct resolution
	videoconvert, err := gst.NewElement("videoconvert")
	if err != nil {
		return nil, fmt.Errorf("failed to create videoconvert: %v", err)
	}

	videoscale, err := gst.NewElement("videoscale")
	if err != nil {
		return nil, fmt.Errorf("failed to create videoscale: %v", err)
	}

	// Set the correct video format and framerate
	videocaps, err := gst.NewElementWithProperties("capsfilter", map[string]interface{}{
		"caps": gst.NewCapsFromString(fmt.Sprintf("video/x-raw, width=%d, height=%d, framerate=%d/1",
			width, height, int(s.codecSettings.FPS))),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create videocaps: %v", err)
	}

	// Create video sink with fallback mechanism
	var intervideosink *gst.Element

	// Try to create intervideosink
	intervideosink, err = gst.NewElement("intervideosink")
	if err != nil {
		logger.Error("Failed to create intervideosink in test pattern pipeline: %v", err)
		logger.Log("Trying to create intervideosink with registry update...")

		// Try to update the GStreamer registry
		updateCmd := exec.Command("gst-inspect-1.0", "--gst-update-registry")
		updateOutput, updateErr := updateCmd.CombinedOutput()
		if updateErr != nil {
			logger.Error("Failed to update GStreamer registry: %v\nOutput: %s", updateErr, string(updateOutput))
		} else {
			logger.Log("GStreamer registry updated, trying again...")
		}

		// Try again after updating the registry
		intervideosink, err = gst.NewElement("intervideosink")
		if err != nil {
			logger.Error("Still failed to create intervideosink after registry update: %v", err)

			// Try one more approach - reinitialize GStreamer
			logger.Log("Trying to reinitialize GStreamer...")
			gst.Deinit()
			gst.Init(nil)

			// Try one more time
			intervideosink, err = gst.NewElement("intervideosink")
			if err != nil {
				logger.Error("Still failed to create intervideosink after GStreamer reinitialization: %v", err)
				return nil, fmt.Errorf("failed to create intervideosink after multiple attempts: %v", err)
			}
		}
	}

	// Set properties after creation
	intervideosink.SetProperty("channel", channel+s.schedulerID)
	intervideosink.SetProperty("sync", true)

	logger.Log("Successfully created intervideosink in test pattern pipeline with channel %s", channel+s.schedulerID)

	// Create audiotestsrc for audio
	audiotestsrc, err := gst.NewElementWithProperties("audiotestsrc", map[string]interface{}{
		"wave":    4,     // Sine wave
		"freq":    440.0, // A4 note
		"volume":  0.2,   // Low volume
		"is-live": true,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create audiotestsrc: %v", err)
	}

	// Create audio converter and set the correct audio format
	audioconvert, err := gst.NewElement("audioconvert")
	if err != nil {
		return nil, fmt.Errorf("failed to create audioconvert: %v", err)
	}

	audioresample, err := gst.NewElement("audioresample")
	if err != nil {
		return nil, fmt.Errorf("failed to create audioresample: %v", err)
	}

	audiocaps, err := gst.NewElementWithProperties("capsfilter", map[string]interface{}{
		"caps": gst.NewCapsFromString("audio/x-raw, format={S16LE, F32LE}, layout=interleaved, rate=48000, channels=2"),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create audiocaps: %v", err)
	}

	// Create audio sink with fallback mechanism
	var interaudiosink *gst.Element

	// Try to create interaudiosink
	interaudiosink, err = gst.NewElement("interaudiosink")
	if err != nil {
		logger.Error("Failed to create interaudiosink in test pattern pipeline: %v", err)
		logger.Log("Trying to create interaudiosink with registry update...")

		// Try to update the GStreamer registry
		updateCmd := exec.Command("gst-inspect-1.0", "--gst-update-registry")
		updateOutput, updateErr := updateCmd.CombinedOutput()
		if updateErr != nil {
			logger.Error("Failed to update GStreamer registry: %v\nOutput: %s", updateErr, string(updateOutput))
		} else {
			logger.Log("GStreamer registry updated, trying again...")
		}

		// Try again after updating the registry
		interaudiosink, err = gst.NewElement("interaudiosink")
		if err != nil {
			logger.Error("Still failed to create interaudiosink after registry update: %v", err)

			// Try one more approach - reinitialize GStreamer
			logger.Log("Trying to reinitialize GStreamer...")
			gst.Deinit()
			gst.Init(nil)

			// Try one more time
			interaudiosink, err = gst.NewElement("interaudiosink")
			if err != nil {
				logger.Error("Still failed to create interaudiosink after GStreamer reinitialization: %v", err)
				return nil, fmt.Errorf("failed to create interaudiosink after multiple attempts: %v", err)
			}
		}
	}

	// Set properties after creation
	interaudiosink.SetProperty("channel", fmt.Sprintf("audio%s", channel[len(channel)-1:])+s.schedulerID)
	interaudiosink.SetProperty("sync", true)

	logger.Log("Successfully created interaudiosink in test pattern pipeline with channel audio%s%s", channel[len(channel)-1:], s.schedulerID)

	// Add all elements to the pipeline
	pipeline.AddMany(videotestsrc, videoconvert, videoscale, videocaps, intervideosink)
	pipeline.AddMany(audiotestsrc, audioconvert, audioresample, audiocaps, interaudiosink)

	// Link video elements
	videotestsrc.Link(videoconvert)
	videoconvert.Link(videoscale)
	videoscale.Link(videocaps)
	videocaps.Link(intervideosink)

	// Link audio elements
	audiotestsrc.Link(audioconvert)
	audioconvert.Link(audioresample)
	audioresample.Link(audiocaps)
	audiocaps.Link(interaudiosink)

	// Set up bus watch
	ctx, cancel := context.WithCancel(context.Background())
	s.sourceCancelFunc = cancel

	bus := pipeline.GetBus()
	go func() {
		for {
			select {
			case <-ctx.Done():
				return
			default:
				msg := bus.TimedPopFiltered(gst.ClockTime(100_000_000), gst.MessageAny) // 100ms
				if msg == nil {
					continue
				}

				switch msg.Type() {
				case gst.MessageError:
					gerr := msg.ParseError()
					logger.Error("Error from test pattern source: %s", gerr.Error())
				case gst.MessageStateChanged:
					if msg.Source() == pipeline.Element.GetName() {
						oldState, newState := msg.ParseStateChanged()
						if oldState == gst.StatePaused && newState == gst.StatePlaying {
							logger.Log("Test pattern pipeline state changed to PLAYING")
						}
					}
				}
			}
		}
	}()

	logger.Log("Created test pattern pipeline for channel %s", channel)
	return pipeline, nil
}

// Helper function to parse resolution
func parseResolution(res string) (width int, height int) {
	width, height = 1920, 1080 // Default to HD

	// Parse resolution string (e.g., "1920x1080i", "1280x720p")
	if len(res) > 0 {
		parts := strings.Split(res, "x")
		if len(parts) >= 2 {
			// Parse width
			w, err := strconv.Atoi(parts[0])
			if err == nil && w > 0 {
				width = w
			}

			// Parse height, removing trailing 'i' or 'p' if present
			hStr := parts[1]
			if strings.HasSuffix(hStr, "i") || strings.HasSuffix(hStr, "p") {
				hStr = hStr[:len(hStr)-1]
			}

			h, err := strconv.Atoi(hStr)
			if err == nil && h > 0 {
				height = h
			}
		}
	}

	return width, height
}

func (s *StreamScheduler) nextItemExist() bool {
	if _, err := os.Stat(s.nextItem.Source); errors.Is(err, os.ErrNotExist) {
		return false
	}

	return true
}

// isGPUAvailable checks if an NVIDIA GPU is available on the system
// by attempting to run the nvidia-smi command
func isGPUAvailable() bool {
	// Check if nvidia-smi command exists and runs successfully
	cmd := exec.Command("nvidia-smi")
	err := cmd.Run()
	if err != nil {
		logger.Log("GPU not detected: %v", err)
		return false
	}

	logger.Log("NVIDIA GPU detected, will use hardware acceleration")
	return false
}

// createDualAudioElements creates the elements needed for dual audio passthrough
func (s *StreamScheduler) createDualAudioElements() (map[string]*gst.Element, error) {
	elements := make(map[string]*gst.Element)
	
	// Create audio tee to split the source audio
	audioTee, err := gst.NewElementWithProperties("tee", map[string]interface{}{
		"name": "audioTee" + s.schedulerID,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create audio tee: %v", err)
	}
	elements["audioTee"] = audioTee

	// Create queues for each audio path
	audio51Queue, err := gst.NewElementWithProperties("queue", map[string]interface{}{
		"max-size-buffers":   100,
		"max-size-time":      uint64(500 * time.Millisecond),
		"min-threshold-time": uint64(50 * time.Millisecond),
		"leaky":              0,
		"name":               "audio51Queue" + s.schedulerID,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create audio 5.1 queue: %v", err)
	}
	elements["audio51Queue"] = audio51Queue

	stereoQueue, err := gst.NewElementWithProperties("queue", map[string]interface{}{
		"max-size-buffers":   100,
		"max-size-time":      uint64(500 * time.Millisecond),
		"min-threshold-time": uint64(50 * time.Millisecond),
		"leaky":              0,
		"name":               "stereoQueue" + s.schedulerID,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create stereo queue: %v", err)
	}
	elements["stereoQueue"] = stereoQueue

	// Create 5.1 surround audio encoder (path 1)
	var audio51Enc *gst.Element
	if s.codecSettings.Audio1Codec == "ac3_passthrough" {
		// For AC3 passthrough, try to keep the original AC3 stream
		audio51Enc, err = gst.NewElementWithProperties("avenc_ac3", map[string]interface{}{
			"bitrate": s.codecSettings.Audio1Bitrate * 1000, // Convert from kbps to bps
			"name":    "ac3enc51" + s.schedulerID,
		})
	} else if s.codecSettings.Audio1Codec == "ac3_downmix" {
		audio51Enc, err = gst.NewElementWithProperties("avenc_ac3", map[string]interface{}{
			"bitrate": s.codecSettings.Audio1Bitrate * 1000,
			"name":    "ac3enc51" + s.schedulerID,
		})
	} else {
		// Default to AAC if not supported
		audio51Enc, err = gst.NewElementWithProperties("avenc_aac", map[string]interface{}{
			"bitrate": s.codecSettings.Audio1Bitrate * 1000,
			"name":    "aacenc51" + s.schedulerID,
		})
	}
	if err != nil {
		return nil, fmt.Errorf("failed to create 5.1 audio encoder: %v", err)
	}
	elements["audio51Enc"] = audio51Enc

	// Create stereo audio encoder (path 2)
	var stereoEnc *gst.Element
	if s.codecSettings.Audio2Codec == "aac_downmix" {
		stereoEnc, err = gst.NewElementWithProperties("avenc_aac", map[string]interface{}{
			"bitrate": s.codecSettings.Audio2Bitrate * 1000,
			"name":    "aacencStereo" + s.schedulerID,
		})
	} else if s.codecSettings.Audio2Codec == "mpeg1l2_downmix" {
		stereoEnc, err = gst.NewElementWithProperties("twolamemp2enc", map[string]interface{}{
			"bitrate":  s.codecSettings.Audio2Bitrate,
			"name":     "mp2encStereo" + s.schedulerID,
			"mode":     1, // joint-stereo
			"psymodel": 3,
		})
	} else {
		// Default to AAC
		stereoEnc, err = gst.NewElementWithProperties("avenc_aac", map[string]interface{}{
			"bitrate": s.codecSettings.Audio2Bitrate * 1000,
			"name":    "aacencStereo" + s.schedulerID,
		})
	}
	if err != nil {
		return nil, fmt.Errorf("failed to create stereo audio encoder: %v", err)
	}
	elements["stereoEnc"] = stereoEnc

	// Create audio converters and resamplers for stereo path
	stereoConvert, err := gst.NewElement("audioconvert")
	if err != nil {
		return nil, fmt.Errorf("failed to create stereo audioconvert: %v", err)
	}
	elements["stereoConvert"] = stereoConvert

	stereoResample, err := gst.NewElement("audioresample")
	if err != nil {
		return nil, fmt.Errorf("failed to create stereo audioresample: %v", err)
	}
	elements["stereoResample"] = stereoResample

	// Create caps filter for stereo downmix
	stereoCaps, err := gst.NewElementWithProperties("capsfilter", map[string]interface{}{
		"caps": gst.NewCapsFromString("audio/x-raw, format={S16LE, F32LE}, layout=interleaved, rate=48000, channels=2"),
		"name": "stereoCaps" + s.schedulerID,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create stereo caps filter: %v", err)
	}
	elements["stereoCaps"] = stereoCaps

	// Create 5.1 caps filter (preserve original format for passthrough)
	surround51Caps, err := gst.NewElementWithProperties("capsfilter", map[string]interface{}{
		"caps": gst.NewCapsFromString("audio/x-raw, format={S16LE, F32LE}, layout=interleaved, rate=48000, channels=6"),
		"name": "surround51Caps" + s.schedulerID,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create 5.1 caps filter: %v", err)
	}
	elements["surround51Caps"] = surround51Caps

	// Create audio parsers
	var audio51Parse *gst.Element
	if s.codecSettings.Audio1Codec == "ac3_passthrough" || s.codecSettings.Audio1Codec == "ac3_downmix" {
		audio51Parse, err = gst.NewElement("ac3parse")
	} else {
		audio51Parse, err = gst.NewElement("aacparse")
	}
	if err != nil {
		return nil, fmt.Errorf("failed to create 5.1 audio parser: %v", err)
	}
	elements["audio51Parse"] = audio51Parse

	var stereoParse *gst.Element
	if s.codecSettings.Audio2Codec == "aac_downmix" {
		stereoParse, err = gst.NewElement("aacparse")
	} else if s.codecSettings.Audio2Codec == "mpeg1l2_downmix" {
		stereoParse, err = gst.NewElement("mpegaudioparse")
	} else {
		stereoParse, err = gst.NewElement("aacparse")
	}
	if err != nil {
		return nil, fmt.Errorf("failed to create stereo audio parser: %v", err)
	}
	elements["stereoParse"] = stereoParse

	// Create audio converters and resamplers for 5.1 surround path only 
	// (stereo converters already created above)
	surround51Convert, err := gst.NewElement("audioconvert")
	if err != nil {
		return nil, fmt.Errorf("failed to create 5.1 audioconvert: %v", err)
	}
	elements["surround51Convert"] = surround51Convert

	surround51Resample, err := gst.NewElement("audioresample")
	if err != nil {
		return nil, fmt.Errorf("failed to create 5.1 audioresample: %v", err)
	}
	elements["surround51Resample"] = surround51Resample

	return elements, nil
}

// linkDualAudioElements links the dual audio pipeline elements
func (s *StreamScheduler) linkDualAudioElements(elements map[string]*gst.Element, audiomixercaps *gst.Element, mpegtsmux *gst.Element) error {
	// Link the main audio source to the tee
	err := audiomixercaps.Link(elements["audioTee"])
	if err != nil {
		return fmt.Errorf("failed to link audiomixercaps to audioTee: %v", err)
	}

	// Link the 5.1 surround path (first audio track)
	err = elements["audioTee"].Link(elements["audio51Queue"])
	if err != nil {
		return fmt.Errorf("failed to link audioTee to audio51Queue: %v", err)
	}
	
	err = elements["audio51Queue"].Link(elements["surround51Convert"])
	if err != nil {
		return fmt.Errorf("failed to link audio51Queue to surround51Convert: %v", err)
	}
	
	err = elements["surround51Convert"].Link(elements["surround51Resample"])
	if err != nil {
		return fmt.Errorf("failed to link surround51Convert to surround51Resample: %v", err)
	}
	
	err = elements["surround51Resample"].Link(elements["surround51Caps"])
	if err != nil {
		return fmt.Errorf("failed to link surround51Resample to surround51Caps: %v", err)
	}
	
	err = elements["surround51Caps"].Link(elements["audio51Enc"])
	if err != nil {
		return fmt.Errorf("failed to link surround51Caps to audio51Enc: %v", err)
	}
	
	err = elements["audio51Enc"].Link(elements["audio51Parse"])
	if err != nil {
		return fmt.Errorf("failed to link audio51Enc to audio51Parse: %v", err)
	}

	// Get a request pad from mpegtsmux for the first audio track
	audio1SinkPad := mpegtsmux.GetRequestPad("sink_%d")
	if audio1SinkPad == nil {
		return fmt.Errorf("failed to get request pad from mpegtsmux for audio track 1")
	}
	
	audio51ParseSrcPad := elements["audio51Parse"].GetStaticPad("src")
	if audio51ParseSrcPad == nil {
		return fmt.Errorf("failed to get src pad from audio51Parse")
	}
	
	linkResult := audio51ParseSrcPad.Link(audio1SinkPad)
	if linkResult != gst.PadLinkOK {
		return fmt.Errorf("failed to link audio51Parse to mpegtsmux: link result %d", linkResult)
	}

	// Link the stereo path (second audio track)
	err = elements["audioTee"].Link(elements["stereoQueue"])
	if err != nil {
		return fmt.Errorf("failed to link audioTee to stereoQueue: %v", err)
	}
	
	err = elements["stereoQueue"].Link(elements["stereoConvert"])
	if err != nil {
		return fmt.Errorf("failed to link stereoQueue to stereoConvert: %v", err)
	}
	
	err = elements["stereoConvert"].Link(elements["stereoResample"])
	if err != nil {
		return fmt.Errorf("failed to link stereoConvert to stereoResample: %v", err)
	}
	
	err = elements["stereoResample"].Link(elements["stereoCaps"])
	if err != nil {
		return fmt.Errorf("failed to link stereoResample to stereoCaps: %v", err)
	}
	
	err = elements["stereoCaps"].Link(elements["stereoEnc"])
	if err != nil {
		return fmt.Errorf("failed to link stereoCaps to stereoEnc: %v", err)
	}
	
	err = elements["stereoEnc"].Link(elements["stereoParse"])
	if err != nil {
		return fmt.Errorf("failed to link stereoEnc to stereoParse: %v", err)
	}

	// Get another request pad from mpegtsmux for the second audio track
	audio2SinkPad := mpegtsmux.GetRequestPad("sink_%d")
	if audio2SinkPad == nil {
		return fmt.Errorf("failed to get request pad from mpegtsmux for audio track 2")
	}
	
	stereoParseSrcPad := elements["stereoParse"].GetStaticPad("src")
	if stereoParseSrcPad == nil {
		return fmt.Errorf("failed to get src pad from stereoParse")
	}
	
	linkResult = stereoParseSrcPad.Link(audio2SinkPad)
	if linkResult != gst.PadLinkOK {
		return fmt.Errorf("failed to link stereoParse to mpegtsmux: link result %d", linkResult)
	}

	logger.Log("Successfully linked dual audio pipeline with 5.1 and stereo tracks")
	return nil
}

// detectAudioTracks detects audio tracks in an MP4 file using FFprobe
func (s *StreamScheduler) detectAudioTracks(filePath string) ([]AudioTrackInfo, error) {
	// Use FFprobe to get audio stream information
	cmd := exec.Command("ffprobe", 
		"-v", "quiet",
		"-print_format", "json",
		"-show_streams",
		"-select_streams", "a", // Only audio streams
		filePath)
	
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("failed to run ffprobe: %v", err)
	}

	var probe FFprobeOutput
	if err := json.Unmarshal(output, &probe); err != nil {
		return nil, fmt.Errorf("failed to parse ffprobe output: %v", err)
	}

	var audioTracks []AudioTrackInfo
	for _, stream := range probe.Streams {
		if stream.CodecType == "audio" {
			audioTracks = append(audioTracks, AudioTrackInfo{
				Index:     stream.Index,
				CodecName: stream.CodecName,
				Channels:  stream.Channels,
				Bitrate:   stream.BitRate,
			})
		}
	}

	logger.Log("Detected %d audio tracks in file: %s", len(audioTracks), filePath)
	for i, track := range audioTracks {
		logger.Log("  Track %d: %s, %d channels, bitrate: %s", 
			i, track.CodecName, track.Channels, track.Bitrate)
	}

	return audioTracks, nil
}

// shouldUseDualAudioForFile determines if dual audio mode should be used based on input file
func (s *StreamScheduler) shouldUseDualAudioForFile(filePath string) bool {
	// Check if the file exists first
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		logger.Log("File does not exist, using codec settings: %s", filePath)
		return s.codecSettings.DualAudioMode
	}

	// Detect audio tracks in the input file
	audioTracks, err := s.detectAudioTracks(filePath)
	if err != nil {
		logger.Error("Failed to detect audio tracks, using codec settings: %v", err)
		return s.codecSettings.DualAudioMode
	}

	// If input file has multiple audio tracks, enable dual audio mode
	if len(audioTracks) >= 2 {
		logger.Log("Input file has %d audio tracks, enabling dual audio mode", len(audioTracks))
		return true
	}

	// If input file has only one audio track, use codec settings from UI
	logger.Log("Input file has %d audio track(s), using UI codec settings (dual audio: %v)", 
		len(audioTracks), s.codecSettings.DualAudioMode)
	return s.codecSettings.DualAudioMode
}

// updateCodecSettingsForFile updates the codec settings based on the file's audio tracks
func (s *StreamScheduler) updateCodecSettingsForFile(filePath string) {
	// Only update if this is a file (not test pattern)
	if !strings.HasPrefix(filePath, "/") && !strings.Contains(filePath, "/") {
		return // This is probably a test pattern, skip detection
	}

	// Detect if this file should use dual audio
	shouldUseDual := s.shouldUseDualAudioForFile(filePath)
	
	// Update the codec settings for this item
	originalDualMode := s.codecSettings.DualAudioMode
	s.codecSettings.DualAudioMode = shouldUseDual
	
	if originalDualMode != shouldUseDual {
		logger.Log("Updated dual audio mode from %v to %v for file: %s", 
			originalDualMode, shouldUseDual, filepath.Base(filePath))
	}
}
