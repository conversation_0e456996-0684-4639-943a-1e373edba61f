package playout

import (
	"crypto/sha256"
	"database/sql"
	"encoding/binary"
	"errors"
	"fmt"
	"math/rand"
	"os"
	"regexp"
	"showfer-web/models"
	"showfer-web/repository"
	"showfer-web/service/logger"
	"strconv"
	"strings"
	"time"
)

type StreamItem struct {
	Type        string        // "file" or "test" (test pattern)
	Source      string        // File path for "file" type
	Start       time.Time     // When to start playing this item
	Duration    time.Duration // How long to play this item
	Offset      time.Duration // Offset from block start
	ContentName string        // Name of the content being played
	ContentPath string        // Path of the content in the system
	PlayType    string        // Type of content: "scheduled", "filler", "pre-filler", etc.
	ItemID      int64         // ID of the content item if available
}

type FileHelper struct {
	schedule      models.Schedule
	filesRepo     *repository.FilesRepository
	guideRepo     *repository.GuideRepository
	scheduleRepo  *repository.ScheduleRepository
	historyRepo   *repository.HistoryRepository
	analyticsRepo *repository.AnalyticsRepository
	baseDir       string
	offset        int
	rtpOutput     string
	hasPreFiller  bool
	preFiller     models.FileInfo
}

func NewFileHelper(db *sql.DB, schedule models.Schedule, baseDir string) *FileHelper {
	helper := &FileHelper{
		schedule:      schedule,
		filesRepo:     repository.NewFilesRepository(db),
		scheduleRepo:  repository.NewScheduleRepository(db),
		guideRepo:     repository.NewGuideRepository(db),
		historyRepo:   repository.NewHistoryRepository(db),
		analyticsRepo: repository.NewAnalyticsRepository(db),
		baseDir:       baseDir,
		rtpOutput:     schedule.OutputUrl,
		hasPreFiller:  schedule.Fillers.PreFiller.ID > 0,
	}

	helper.preFiller = helper.getPreFiller()

	return helper
}

func (f *FileHelper) GetItemByTime(start time.Time, first bool) (StreamItem, error) {
	err := f.reloadSchedule()
	if err != nil {
		return StreamItem{}, fmt.Errorf("failed to reload schedule: %w", err)
	}

	loc, _ := time.LoadLocation(f.schedule.Timezone)
	_, tzOffset := time.Now().In(loc).Zone()

	guide, err := f.guideRepo.FindGuideByScheduleID(f.schedule.ID)
	if err != nil {
		return StreamItem{}, fmt.Errorf("failed to load the guide: %v", err)
	}

	var item StreamItem

	if first {
		start = start.UTC().Add(time.Duration(tzOffset) * time.Second)
	}

	for _, element := range guide.Elements {
		startTime, _ := time.Parse(time.RFC3339, element.Start)
		endTime, _ := time.Parse(time.RFC3339, element.End)

		// Skip past elements
		if start.After(endTime) {
			continue
		}

		if start.Before(startTime) {
			return StreamItem{}, fmt.Errorf("the first event hasn't happened yet, there's nothing to play")
		}

		if element.Type == "filler" {
			return f.getRandFiller(f.schedule.Fillers, start, endTime, false)
		}

		offset := start.Sub(startTime) // Amount of time passed since the start of the element
		duration := f.getFileDuration(element.File.FileId)

		if f.hasPreFiller {
			preFillerDuration := f.getFileDuration(f.preFiller.ID)
			if preFillerDuration > offset {
				contentName := "Pre-Filler"
				// Record this filler play in analytics
				analytics := models.NewContentAnalytics(
					f.schedule.ID,
					contentName,
					f.preFiller.Path,
					f.rtpOutput,
					"pre-filler",
				)
				analytics.ItemID = f.preFiller.ID
				analytics.Duration = preFillerDuration.Seconds()

				_, err = f.analyticsRepo.RecordContentPlay(analytics)
				if err != nil {
					logger.Error("Failed to record pre-filler analytics: %v", err)
				}

				filePath := fmt.Sprintf("%s%s", f.baseDir, f.preFiller.Path)
				logger.Log("Playing pre-filler: %s, duration: %v", filePath, preFillerDuration)
				return StreamItem{
					Type:        "file",
					Source:      filePath,
					Start:       start,
					Offset:      0,
					Duration:    preFillerDuration,
					ContentName: contentName,
					ContentPath: f.preFiller.Path,
					PlayType:    "pre-filler",
					ItemID:      f.preFiller.ID,
				}, nil
			}

			startTime = startTime.Add(preFillerDuration)
			offset = start.Sub(startTime)
		}

		// Run fillers if the element has already ended
		if offset >= duration {
			if len(element.Fillers.Files) > 0 || len(element.Fillers.Folders) > 0 {
				return f.getRandFiller(element.Fillers, start, endTime, true)
			}

			return f.getRandFiller(f.schedule.Fillers, start, endTime, false)
		}

		convertItem, err := f.filesRepo.GetConvertItemById(element.File.FileId)
		if err != nil {
			fmt.Printf("Failed to get convert item by id %d: %v", element.File.FileId, err)
			return f.getRandFiller(f.schedule.Fillers, start, endTime, false)
		}

		filePath := fmt.Sprintf("%s%s%s", f.baseDir, convertItem.Location, convertItem.Filename)
		if _, err = os.Stat(filePath); errors.Is(err, os.ErrNotExist) {
			fmt.Printf("file %s not exist\n", filePath)

			if len(element.Fillers.Files) > 0 || len(element.Fillers.Folders) > 0 {
				return f.getRandFiller(element.Fillers, start, endTime, true)
			}

			return f.getRandFiller(f.schedule.Fillers, start, endTime, false)
		}

		if duration < 1*time.Second {
			fmt.Printf("Duration less than 1s, skipping file: %s\n", filePath)

			if len(element.Fillers.Files) > 0 || len(element.Fillers.Folders) > 0 {
				return f.getRandFiller(element.Fillers, start, endTime, true)
			}

			return f.getRandFiller(f.schedule.Fillers, start, endTime, false)
		}

		history := models.NewHistory(
			f.schedule.ID,
			element.File.Folder,
			element.File.FileId,
			f.episodeToInt(element.File.Episode),
		)
		_, err = f.historyRepo.CreateHistory(history)
		if err != nil {
			logger.Error("failed to create the history: %v", err)
		}

		// Record this content play in analytics
		contentName := element.Title
		contentPath := convertItem.Location + convertItem.Filename

		// Create analytics entry
		analytics := models.NewContentAnalytics(
			f.schedule.ID,
			contentName,
			contentPath,
			f.rtpOutput,
			"scheduled",
		)
		analytics.ItemID = element.File.FileId
		analytics.Duration = duration.Seconds()

		_, err = f.analyticsRepo.RecordContentPlay(analytics)
		if err != nil {
			logger.Error("failed to record content analytics: %v", err)
		}

		return StreamItem{
			Type:        "file",
			Source:      filePath,
			Start:       startTime,
			Duration:    duration,
			Offset:      offset,
			ContentName: contentName,
			ContentPath: contentPath,
			PlayType:    "scheduled",
			ItemID:      element.File.FileId,
		}, nil
	}

	return item, nil
}

func (f *FileHelper) getRandFiller(activeFillers models.Fillers, start time.Time, end time.Time, isElementFiller bool) (StreamItem, error) {
	var fillers []models.FileInfo
	var validFillers []models.FileInfo

	// Collect all potential fillers from files and folders
	if len(activeFillers.Files) > 0 {
		for _, filler := range activeFillers.Files {
			convertItem, err := f.filesRepo.GetConvertItemById(filler.ID)
			if err != nil {
				logger.Warn("Filler file not found in database: %s, error: %v", filler.Path, err)
				continue
			}
			fillers = append(fillers, models.FileInfo{
				Path: convertItem.Location + convertItem.Filename,
				ID:   convertItem.ID,
			})
		}
	}

	if len(activeFillers.Folders) > 0 {
		for _, folder := range activeFillers.Folders {
			convertItems := f.filesRepo.FindByFolder(folder)
			if len(convertItems) == 0 {
				logger.Warn("No files found in filler folder: %s", folder)
			}
			for _, convertItem := range convertItems {
				fillers = append(fillers, models.FileInfo{
					Path: convertItem.Location + convertItem.Filename,
					ID:   convertItem.ID,
				})
			}
		}
	}

	if len(fillers) == 0 {
		if isElementFiller {
			return f.getRandFiller(f.schedule.Fillers, start, end, false)
		}

		logger.Error("No fillers found in schedule %d", f.schedule.ID)
		// Return a test pattern as a last resort
		return createTestPatternItem(start, end.Sub(start)), nil
	}

	// Shuffle the fillers to avoid trying them in the same order every time
	rand.Shuffle(len(fillers), func(i, j int) { fillers[i], fillers[j] = fillers[j], fillers[i] })

	// First pass: check which files actually exist and have valid duration
	for _, filler := range fillers {
		filePath := fmt.Sprintf("%s%s", f.baseDir, filler.Path)
		if _, err := os.Stat(filePath); err == nil {
			// File exists, check duration
			fillerDuration := f.getFileDuration(filler.ID)
			if fillerDuration >= 1*time.Second {
				validFillers = append(validFillers, filler)
			} else {
				logger.Warn("Filler file has invalid duration (< 1s): %s", filePath)
			}
		} else {
			logger.Warn("Filler file does not exist: %s", filePath)
		}
	}

	// If we have valid fillers, use one of them
	if len(validFillers) > 0 {
		// Pick a random valid filler
		filler := f.chooseDeterministicFiller(validFillers, start)
		fillerDuration := f.getFileDuration(filler.ID)
		fillerEndTime := start.Add(fillerDuration)

		// Adjust duration if it would go past the end time
		if fillerEndTime.After(end) {
			fillerDuration = fillerDuration - fillerEndTime.Sub(end)
			if fillerDuration < 1*time.Second {
				fillerDuration = 1 * time.Second
			}
		}

		filePath := fmt.Sprintf("%s%s", f.baseDir, filler.Path)

		// Get content name for the filler
		convertItem, err := f.filesRepo.GetConvertItemById(filler.ID)
		contentName := "Filler"
		if err == nil {
			if convertItem.Name != "" {
				contentName = convertItem.Name
			} else {
				contentName = convertItem.Filename
			}
		}

		// Record this filler play in analytics
		analytics := models.NewContentAnalytics(
			f.schedule.ID,
			contentName,
			filler.Path,
			f.rtpOutput,
			"filler",
		)
		analytics.ItemID = filler.ID
		analytics.Duration = fillerDuration.Seconds()

		_, err = f.analyticsRepo.RecordContentPlay(analytics)
		if err != nil {
			logger.Error("Failed to record filler analytics: %v", err)
		}

		logger.Log("Playing filler: %s, duration: %v", filePath, fillerDuration)
		return StreamItem{
			Type:        "file",
			Source:      filePath,
			Start:       start,
			Offset:      0,
			Duration:    fillerDuration,
			ContentName: contentName,
			ContentPath: filler.Path,
			PlayType:    "filler",
			ItemID:      filler.ID,
		}, nil
	}

	if isElementFiller {
		return f.getRandFiller(f.schedule.Fillers, start, end, false)
	}

	// If we get here, we couldn't find any valid fillers
	logger.Error("No valid fillers found for schedule %d, using test pattern", f.schedule.ID)
	return createTestPatternItem(start, end.Sub(start)), nil
}

// Helper function to create a test pattern item as a last resort
func createTestPatternItem(start time.Time, duration time.Duration) StreamItem {
	// Ensure minimum duration
	if duration < 5*time.Second {
		duration = 5 * time.Second
	}

	logger.Warn("Using test pattern as fallback for %v", duration)

	return StreamItem{
		Type:        "test", // Special type for test pattern
		Source:      "",     // No file source needed
		Start:       start,
		Offset:      0,
		Duration:    duration,
		ContentName: "Test Pattern",
		ContentPath: "",
		PlayType:    "fallback",
		ItemID:      0,
	}
}

func (f *FileHelper) reloadSchedule() error {
	schedule, err := f.scheduleRepo.FindById(int(f.schedule.ID))
	if err != nil {
		return err
	}

	f.schedule = schedule
	f.hasPreFiller = schedule.Fillers.PreFiller.ID > 0
	f.preFiller = f.getPreFiller()

	return nil
}

func (f *FileHelper) getPreFiller() models.FileInfo {
	if !f.hasPreFiller {
		return models.FileInfo{}
	}

	preFiller, err := f.filesRepo.GetConvertItemById(f.schedule.Fillers.PreFiller.ID)
	if err != nil {
		f.hasPreFiller = false
		logger.Warn("Pre-filler file not found in database: %s, error: %v", f.schedule.Fillers.PreFiller.Path, err)
		return models.FileInfo{}
	}

	duration := f.getFileDuration(preFiller.ID)
	if duration == 0 {
		f.hasPreFiller = false
		logger.Warn("Pre-filler duration less than 0: %d", f.schedule.Fillers.PreFiller.ID)
		return models.FileInfo{}
	}

	filePath := fmt.Sprintf("%s%s%s", f.baseDir, preFiller.Location, preFiller.Filename)
	if _, err = os.Stat(filePath); errors.Is(err, os.ErrNotExist) {
		f.hasPreFiller = false
		logger.Warn("Pre-filler file not found: %s", filePath)
		return models.FileInfo{}
	}

	return models.FileInfo{
		Path: preFiller.Location + preFiller.Filename,
		ID:   preFiller.ID,
	}
}

func (f *FileHelper) getFileDuration(id int64) time.Duration {
	if id > 0 {
		duration, err := f.filesRepo.GetDurationById(id)
		if err != nil {
			return 0
		}
		return time.Duration(duration * float64(time.Second))
	}
	return 0
}

func (f *FileHelper) episodeToInt(episodeNum string) int {
	re := regexp.MustCompile("\\d+")
	str := strings.Join(re.FindAllString(episodeNum, -1), "")
	episode, _ := strconv.Atoi(str)

	return episode
}

func (f *FileHelper) chooseDeterministicFiller(validFillers []models.FileInfo, startTime time.Time) models.FileInfo {
	baseString := startTime.UTC().Format(time.RFC3339)
	baseString += f.schedule.ShortID
	hash := sha256.Sum256([]byte(baseString))
	seed := int64(binary.BigEndian.Uint64(hash[:8]))

	r := rand.New(rand.NewSource(seed))

	return validFillers[r.Intn(len(validFillers))]
}
