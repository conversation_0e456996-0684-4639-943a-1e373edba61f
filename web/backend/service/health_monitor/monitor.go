package health_monitor

import (
	"encoding/json"
	"fmt"
	"net/http"
	"showfer-web/config"
	"showfer-web/service/logger"
	"showfer-web/service/traffic_blocker"
	"showfer-web/utils"
	"sync"
	"time"
	"os/exec"
	"os"
)

// HealthMonitor monitors the primary server health from backup servers
type HealthMonitor struct {
	primaryIP          string
	isMonitoring       bool
	stopChan           chan struct{}
	mutex              sync.RWMutex
	checkInterval      time.Duration
	failureCount       int
	maxFailures        int
	lastHealthCheck    time.Time
	originalPrimaryIP  string  // Store the original primary IP for recovery
	isPromoted         bool    // Track if this server was promoted to primary
}

var (
	monitorInstance *HealthMonitor
	monitorMutex    sync.Mutex
)

// GetHealthMonitor returns the singleton health monitor instance
func GetHealthMonitor() *HealthMonitor {
	monitorMutex.Lock()
	defer monitorMutex.Unlock()
	
	if monitorInstance == nil {
		monitorInstance = &HealthMonitor{
			stopChan:      make(chan struct{}),
			checkInterval: 5 * time.Second, // Check every 5 seconds
			maxFailures:   3,                // Promote to primary after 3 consecutive failures
		}
	}
	return monitorInstance
}

// StartMonitoring starts monitoring the primary server (only for backup servers)
func (h *HealthMonitor) StartMonitoring() {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	// Only backup servers should monitor primary
	if utils.GetServerType() != "backup" {
		logger.Log("Health monitor: Not a backup server, skipping primary monitoring")
		return
	}

	// Get primary IP
	primaryIP := utils.GetPrimaryIP()
	if primaryIP == "" {
		logger.Log("Health monitor: No primary IP configured, skipping monitoring")
		return
	}

	// Don't start if already monitoring
	if h.isMonitoring {
		logger.Log("Health monitor: Already monitoring primary server")
		return
	}

	h.primaryIP = primaryIP
	h.originalPrimaryIP = primaryIP  // Store original primary for recovery
	h.isMonitoring = true
	h.isPromoted = false
	h.failureCount = 0
	h.lastHealthCheck = time.Now()

	logger.Log("Health monitor: Starting to monitor primary server at %s", primaryIP)

	// Start monitoring in a goroutine
	go h.monitorLoop()
}

// StopMonitoring stops the health monitoring
func (h *HealthMonitor) StopMonitoring() {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	if !h.isMonitoring {
		return
	}

	h.isMonitoring = false
	
	// Signal stop to monitoring goroutine
	select {
	case h.stopChan <- struct{}{}:
	default:
	}

	logger.Log("Health monitor: Stopped monitoring primary server")
}

// UpdatePrimaryIP updates the primary IP being monitored
func (h *HealthMonitor) UpdatePrimaryIP(newPrimaryIP string) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	// Save the new primary IP
	if err := utils.SetPrimaryIP(newPrimaryIP); err != nil {
		logger.Error("Health monitor: Failed to save primary IP: %v", err)
		return
	}

	// If we're currently monitoring, restart with new IP
	if h.isMonitoring {
		h.primaryIP = newPrimaryIP
		h.failureCount = 0
		logger.Log("Health monitor: Updated primary IP to %s", newPrimaryIP)
	}
}

// monitorLoop is the main monitoring loop
func (h *HealthMonitor) monitorLoop() {
	ticker := time.NewTicker(h.checkInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			h.performHealthCheck()
		case <-h.stopChan:
			return
		}
	}
}

// performHealthCheck checks if the primary server is healthy
func (h *HealthMonitor) performHealthCheck() {
	h.mutex.Lock()
	isMonitoring := h.isMonitoring
	primaryIP := h.primaryIP
	originalPrimaryIP := h.originalPrimaryIP
	currentServerType := utils.GetServerType()
	h.mutex.Unlock()

	// If we're not monitoring anymore or not a backup server, stop
	if !isMonitoring || currentServerType != "backup" {
		return
	}

	if primaryIP == "" {
		return
	}

	// Test connectivity to primary server
	success := h.testPrimaryConnectivity(primaryIP)
	
	h.mutex.Lock()
	defer h.mutex.Unlock()

	h.lastHealthCheck = time.Now()

	if success {
		// If we're promoted and the original primary is back online, handle recovery
		if h.isPromoted && primaryIP == originalPrimaryIP {
			logger.Log("Health monitor: Original primary server %s is back online. Starting recovery process...", originalPrimaryIP)
			go h.handlePrimaryRecovery(originalPrimaryIP)
			return
		}
		
		// Reset failure count on successful check
		if h.failureCount > 0 {
			logger.Log("Health monitor: Primary server %s is healthy", primaryIP)
			h.failureCount = 0
		}
	} else {
		h.failureCount++
		logger.Warn("Health monitor: Primary server %s health check failed (attempt %d/%d)", 
			primaryIP, h.failureCount, h.maxFailures)

		// If we've reached max failures and haven't been promoted yet, promote this backup to primary
		if h.failureCount >= h.maxFailures && !h.isPromoted {
			logger.Error("Health monitor: Primary server %s is down after %d attempts. Promoting to primary...", 
				primaryIP, h.maxFailures)
			h.promoteToPrimary()
		}
	}
}

// testPrimaryConnectivity tests if the primary server is reachable
func (h *HealthMonitor) testPrimaryConnectivity(primaryIP string) bool {
	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	// Test the server-type endpoint
	url := fmt.Sprintf("http://%s:8080/api/server-type", primaryIP)
	
	resp, err := client.Get(url)
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return false
	}

	// Verify it's still a primary server
	var serverTypeResp struct {
		ServerType string `json:"server_type"`
	}
	if err := json.NewDecoder(resp.Body).Decode(&serverTypeResp); err != nil {
		return false
	}

	// If the server is no longer primary, we should also promote
	if serverTypeResp.ServerType != "primary" {
		logger.Warn("Health monitor: Primary server %s is no longer primary (type: %s)", 
			primaryIP, serverTypeResp.ServerType)
		return false
	}

	return true
}

// promoteToPrimary promotes this backup server to primary
func (h *HealthMonitor) promoteToPrimary() {
	logger.Log("Health monitor: Promoting backup server to primary role")

	// Change server type to primary
	if err := utils.SetServerType("primary"); err != nil {
		logger.Error("Health monitor: Failed to promote to primary: %v", err)
		return
	}

	// Clear primary IP since we're now the primary
	utils.SetPrimaryIP("")

	// Unblock traffic since we're now primary
	db := config.GetDB()
	if db != nil {
		trafficBlocker := traffic_blocker.GetTrafficBlocker(db)
		trafficBlocker.OnServerTypeChange("primary")
		logger.Log("Health monitor: Unblocked stream traffic for primary role")
	}

	// Mark as promoted but keep monitoring for recovery
	h.isPromoted = true
	h.failureCount = 0  // Reset failure count

	logger.Log("Health monitor: Successfully promoted to primary server")
}

// handlePrimaryRecovery handles the recovery process when original primary comes back online
func (h *HealthMonitor) handlePrimaryRecovery(originalPrimaryIP string) {
	logger.Log("Health monitor: Starting recovery process for original primary %s", originalPrimaryIP)

	// Step 1: Sync database dump to recovered primary
	if err := h.syncDatabaseToRecoveredPrimary(originalPrimaryIP); err != nil {
		logger.Error("Health monitor: Failed to sync database to recovered primary: %v", err)
		return
	}

	// Step 2: Sync data files to recovered primary
	if err := h.syncDataFilesToRecoveredPrimary(originalPrimaryIP); err != nil {
		logger.Error("Health monitor: Failed to sync data files to recovered primary: %v", err)
		return
	}

	// Step 3: Signal recovered primary to load database and restart
	if err := h.signalPrimaryToRestart(originalPrimaryIP); err != nil {
		logger.Error("Health monitor: Failed to signal primary to restart: %v", err)
		return
	}

	// Step 4: Wait a bit for primary to restart, then demote ourselves back to backup
	time.Sleep(30 * time.Second)
	
	h.mutex.Lock()
	defer h.mutex.Unlock()

	// Change back to backup role
	if err := utils.SetServerType("backup"); err != nil {
		logger.Error("Health monitor: Failed to demote back to backup: %v", err)
		return
	}

	// Block traffic since we're now backup
	db := config.GetDB()
	if db != nil {
		trafficBlocker := traffic_blocker.GetTrafficBlocker(db)
		trafficBlocker.OnServerTypeChange("backup")
		logger.Log("Health monitor: Blocked stream traffic for backup role")
	}

	// Set primary IP back to original
	if err := utils.SetPrimaryIP(originalPrimaryIP); err != nil {
		logger.Error("Health monitor: Failed to set primary IP: %v", err)
		return
	}

	h.primaryIP = originalPrimaryIP
	h.isPromoted = false
	h.failureCount = 0

	logger.Log("Health monitor: Successfully completed recovery process. Demoted back to backup role.")
}

// syncDatabaseToRecoveredPrimary syncs the current database dump to recovered primary
func (h *HealthMonitor) syncDatabaseToRecoveredPrimary(primaryIP string) error {
	logger.Log("Health monitor: Syncing database dump to recovered primary %s", primaryIP)

	// Get primary server SSH credentials (for connecting TO the primary server)
	username := utils.GetPrimarySSHUser()
	password := utils.GetPrimarySSHPassword()
	
	if username == "" || password == "" {
		return fmt.Errorf("SSH credentials not configured")
	}

	// Sync database dump file
	dumpFile := "/tmp/showfer_db_dump.sql"
	if _, err := os.Stat(dumpFile); err != nil {
		return fmt.Errorf("database dump file not found: %v", err)
	}

	// Use sshpass and rsync to copy database dump
	cmd := exec.Command("sshpass", "-p", password, "rsync", "-avz", 
		"--timeout=30", 
		dumpFile,
		fmt.Sprintf("%s@%s:/tmp/", username, primaryIP))
	
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to sync database dump: %v, output: %s", err, string(output))
	}

	logger.Log("Health monitor: Successfully synced database dump to recovered primary")
	return nil
}

// syncDataFilesToRecoveredPrimary syncs web/backend/data/ files to recovered primary
func (h *HealthMonitor) syncDataFilesToRecoveredPrimary(primaryIP string) error {
	logger.Log("Health monitor: Syncing data files to recovered primary %s", primaryIP)

	// Get primary server SSH credentials (for connecting TO the primary server)
	username := utils.GetPrimarySSHUser()
	password := utils.GetPrimarySSHPassword()
	
	if username == "" || password == "" {
		return fmt.Errorf("SSH credentials not configured")
	}

	// Sync data directory
	dataDir := "./data/"
	
	// Use sshpass and rsync to copy data files
	cmd := exec.Command("sshpass", "-p", password, "rsync", "-avz", 
		"--timeout=30", 
		"--delete",  // Delete files on destination that don't exist on source
		dataDir,
		fmt.Sprintf("%s@%s:~/traffiq/web/backend/data/", username, primaryIP))
	
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to sync data files: %v, output: %s", err, string(output))
	}

	logger.Log("Health monitor: Successfully synced data files to recovered primary")
	return nil
}

// signalPrimaryToRestart signals the recovered primary to load database and restart
func (h *HealthMonitor) signalPrimaryToRestart(primaryIP string) error {
	logger.Log("Health monitor: Signaling recovered primary %s to restart with synced data", primaryIP)

	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// Call the recovery endpoint on the primary server
	url := fmt.Sprintf("http://%s:8080/api/recovery/load-and-restart", primaryIP)
	
	resp, err := client.Post(url, "application/json", nil)
	if err != nil {
		return fmt.Errorf("failed to signal primary restart: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("primary restart signal failed with status: %d", resp.StatusCode)
	}

	logger.Log("Health monitor: Successfully signaled primary to restart")
	return nil
}

// GetStatus returns the current monitoring status
func (h *HealthMonitor) GetStatus() map[string]interface{} {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	return map[string]interface{}{
		"is_monitoring":       h.isMonitoring,
		"primary_ip":          h.primaryIP,
		"original_primary_ip": h.originalPrimaryIP,
		"is_promoted":         h.isPromoted,
		"failure_count":       h.failureCount,
		"max_failures":        h.maxFailures,
		"last_health_check":   h.lastHealthCheck,
		"check_interval":      h.checkInterval.String(),
	}
}

// StartHealthMonitoring initializes and starts health monitoring for backup servers
func StartHealthMonitoring() {
	monitor := GetHealthMonitor()
	monitor.StartMonitoring()
}

// StopHealthMonitoring stops the health monitoring
func StopHealthMonitoring() {
	monitor := GetHealthMonitor()
	monitor.StopMonitoring()
}

// UpdateMonitoredPrimaryIP updates the primary IP being monitored
func UpdateMonitoredPrimaryIP(primaryIP string) {
	monitor := GetHealthMonitor()
	monitor.UpdatePrimaryIP(primaryIP)
}
