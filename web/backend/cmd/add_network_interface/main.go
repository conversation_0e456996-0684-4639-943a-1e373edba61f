package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"path/filepath"

	_ "github.com/mattn/go-sqlite3"
)

func main() {
	// Get the database path
	dbPath := filepath.Join("data", "showfer.db")
	if _, err := os.Stat(dbPath); os.IsNotExist(err) {
		log.Fatalf("Database file not found at %s", dbPath)
	}

	// Open the database
	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		log.Fatalf("Failed to open database: %v", err)
	}
	defer db.Close()

	// Check if the network_interface column already exists
	var columnExists bool
	err = db.QueryRow(`
		SELECT COUNT(*) > 0
		FROM pragma_table_info('schedules')
		WHERE name = 'network_interface'
	`).Scan(&columnExists)
	if err != nil {
		log.Fatalf("Failed to check if column exists: %v", err)
	}

	// Add the network_interface column if it doesn't exist
	if !columnExists {
		_, err = db.Exec(`
			ALTER TABLE schedules
			ADD COLUMN network_interface TEXT DEFAULT NULL
		`)
		if err != nil {
			log.Fatalf("Failed to add network_interface column: %v", err)
		}
		fmt.Println("Successfully added network_interface column to schedules table")
	} else {
		fmt.Println("network_interface column already exists in schedules table")
	}
}
