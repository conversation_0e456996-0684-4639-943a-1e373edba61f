package main

import (
	"fmt"
	"log"
	"net"
	"os"
	"showfer-web/service/recorder"
	"strings"
)

// Helper function to get the IP address for a given network interface
func getInterfaceIPAddress(interfaceName string) (string, error) {
	// Get the network interface by name
	iface, err := net.InterfaceByName(interfaceName)
	if err != nil {
		return "", fmt.Errorf("failed to get interface %s: %v", interfaceName, err)
	}

	// Get the addresses for the interface
	addrs, err := iface.Addrs()
	if err != nil {
		return "", fmt.Errorf("failed to get addresses for interface %s: %v", interfaceName, err)
	}

	// Look for an IPv4 address
	for _, addr := range addrs {
		// Parse the address
		var ip net.IP
		switch v := addr.(type) {
		case *net.IPNet:
			ip = v.IP
		case *net.IPAddr:
			ip = v.IP
		}

		// Skip loopback addresses
		if ip.IsLoopback() {
			continue
		}

		// Skip IPv6 addresses
		if ip.To4() == nil {
			continue
		}

		// Return the first IPv4 address
		return ip.String(), nil
	}

	return "", fmt.<PERSON><PERSON><PERSON>("no IPv4 address found for interface %s", interfaceName)
}

// Mock function to simulate the checkRtpUrlSingleAttempt function
// This allows us to see how the URL is constructed without actually running ffmpeg
func mockCheckRtpUrl(rtpURL string, ipInfo *recorder.IPInfo) {
	// Use ffmpeg to check if an RTP URL is accessible by capturing a single frame
	args := []string{
		"-i", rtpURL,
		"-frames:v", "1",
		"-f", "null",
		"-",
	}

	// Add multicast options if needed
	if ipInfo.IsMulticast && ipInfo.Interface != "" {
		// Insert multicast options before the input
		args = append([]string{"-live_start_index", "0"}, args...)
	}

	// Add network interface and/or localaddr options if specified
	// Find the index of the input URL in the args slice
	inputIndex := -1
	for i, arg := range args {
		if arg == "-i" {
			inputIndex = i
			break
		}
	}

	if inputIndex >= 0 && inputIndex+1 < len(args) {
		// Get the original URL
		originalUrl := args[inputIndex+1]

		// First, determine if we need to add a localaddr parameter
		needLocalAddr := false
		localAddrValue := ""

		// Prefer network interface over local address for multicast streams
		if ipInfo.IsMulticast && ipInfo.Interface != "" {
			// Get the IP address for the specified interface
			ifaceIP, err := getInterfaceIPAddress(ipInfo.Interface)
			if err == nil && ifaceIP != "" {
				needLocalAddr = true
				localAddrValue = ifaceIP
				log.Printf("Using IP %s from network interface %s as localaddr for multicast RTP reception", ifaceIP, ipInfo.Interface)
			} else {
				log.Printf("Failed to get IP address for interface %s: %v", ipInfo.Interface, err)
			}
		} else if ipInfo.LocalAddr != "" {
			needLocalAddr = true
			localAddrValue = ipInfo.LocalAddr
			log.Printf("Using local address %s for RTP reception", ipInfo.LocalAddr)
		}

		// Only modify the URL if we need to add a localaddr parameter
		if needLocalAddr {
			// Create a new URL with only the localaddr parameter
			// First, extract the base URL without any query parameters
			baseUrl := originalUrl
			if strings.Contains(originalUrl, "?") {
				baseUrl = originalUrl[:strings.Index(originalUrl, "?")]
			}

			// Now add the localaddr parameter
			args[inputIndex+1] = baseUrl + "?localaddr=" + localAddrValue
			log.Printf("Modified URL to: %s", args[inputIndex+1])
		}
	}

	// Log the full command for debugging
	log.Printf("Would run ffmpeg command: ffmpeg %s", strings.Join(args, " "))
}

func main() {
	// Set up logging
	log.SetOutput(os.Stdout)
	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)

	// Test URLs
	urls := []string{
		"rtp://***********:5001",
		"rtp://***********:5001?iface=ens33",
		"rtp://***********:5001?localaddr=************",
		"rtp://***********:5001?iface=ens33&localaddr=************",
	}

	for _, url := range urls {
		fmt.Printf("\n--- Testing URL: %s ---\n", url)

		// Parse the URL
		ipInfo, err := recorder.ParseRtpURL(url)
		if err != nil {
			fmt.Printf("Error parsing URL: %v\n", err)
			continue
		}

		fmt.Printf("Parsed URL: %+v\n", ipInfo)

		// Test the URL construction for ffmpeg
		mockCheckRtpUrl(url, ipInfo)
	}
}
