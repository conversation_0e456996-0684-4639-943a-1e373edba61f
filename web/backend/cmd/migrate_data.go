package main

import (
	"database/sql"
	"log"
	"os"
	"showfer-web/config"

	_ "github.com/lib/pq"
	_ "modernc.org/sqlite"
)

func main() {
	// Check if SQLite database exists
	sqliteDBPath := "/home/<USER>/Dev/traffiq/web/backend/data/showfer.db"
	if _, err := os.Stat(sqliteDBPath); os.IsNotExist(err) {
		log.Fatal("SQLite database not found at /home/<USER>/Dev/traffiq/web/backend/data/showfer.db")
	}

	// Open SQLite connection
	sqliteDB, err := sql.Open("sqlite", sqliteDBPath)
	if err != nil {
		log.Fatalf("Failed to open SQLite database: %v", err)
	}
	defer sqliteDB.Close()

	// Open PostgreSQL connection
	pgDB, err := config.InitDB()
	if err != nil {
		log.Fatalf("Failed to connect to PostgreSQL: %v", err)
	}
	defer pgDB.Close()

	log.Println("Starting data migration from SQLite to PostgreSQL...")

	// Migrate each table
	if err := migrateSchedules(sqliteDB, pgDB); err != nil {
		log.Fatalf("Failed to migrate schedules: %v", err)
	}

	if err := migrateConvertItems(sqliteDB, pgDB); err != nil {
		log.Fatalf("Failed to migrate convert_items: %v", err)
	}

	if err := migrateUsers(sqliteDB, pgDB); err != nil {
		log.Fatalf("Failed to migrate users: %v", err)
	}

	if err := migrateGuides(sqliteDB, pgDB); err != nil {
		log.Fatalf("Failed to migrate guides: %v", err)
	}

	if err := migrateHistory(sqliteDB, pgDB); err != nil {
		log.Fatalf("Failed to migrate history: %v", err)
	}

	if err := migrateRtpUrls(sqliteDB, pgDB); err != nil {
		log.Fatalf("Failed to migrate rtp_urls: %v", err)
	}

	if err := migrateRecorders(sqliteDB, pgDB); err != nil {
		log.Fatalf("Failed to migrate recorders: %v", err)
	}

	if err := migrateCodecSettings(sqliteDB, pgDB); err != nil {
		log.Fatalf("Failed to migrate codec_settings: %v", err)
	}

	if err := migrateContentAnalytics(sqliteDB, pgDB); err != nil {
		log.Fatalf("Failed to migrate content_analytics: %v", err)
	}

	log.Println("Data migration completed successfully!")
}

func migrateSchedules(sqliteDB, pgDB *sql.DB) error {
	log.Println("Migrating schedules...")
	
	rows, err := sqliteDB.Query(`SELECT id, name, icon, timezone, short_id, autosave, 
		output_url, network_interface, ads, channels, regular_days, special_days, 
		fillers, created_at, updated_at FROM schedules`)
	if err != nil {
		return err
	}
	defer rows.Close()

	for rows.Next() {
		var id int64
		var name, icon, timezone, shortID, outputURL, networkInterface sql.NullString
		var autosave bool
		var ads, channels, regularDays, specialDays, fillers, createdAt, updatedAt sql.NullString

		err := rows.Scan(&id, &name, &icon, &timezone, &shortID, &autosave,
			&outputURL, &networkInterface, &ads, &channels, &regularDays,
			&specialDays, &fillers, &createdAt, &updatedAt)
		if err != nil {
			return err
		}

		_, err = pgDB.Exec(`
			INSERT INTO schedules (id, name, icon, timezone, short_id, autosave,
				output_url, network_interface, ads, channels, regular_days, 
				special_days, fillers, created_at, updated_at)
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
			ON CONFLICT (id) DO NOTHING`,
			id, name, icon, timezone, shortID, autosave, outputURL,
			networkInterface, ads, channels, regularDays, specialDays,
			fillers, createdAt, updatedAt)
		if err != nil {
			return err
		}
	}

	// Update sequence
	_, err = pgDB.Exec(`SELECT setval('schedules_id_seq', (SELECT COALESCE(MAX(id), 1) FROM schedules))`)
	return err
}

func migrateConvertItems(sqliteDB, pgDB *sql.DB) error {
	log.Println("Migrating convert_items...")
	
	rows, err := sqliteDB.Query(`SELECT id, filename, name, location, duration, 
		status, size, storage_type, created_at, updated_at, c_location, 
		description, episode, width, height, fps, video_codec, audio_codec, bitrate 
		FROM convert_items`)
	if err != nil {
		return err
	}
	defer rows.Close()

	for rows.Next() {
		var id int64
		var filename, name, location, cLocation, description, episode, videoCodec, audioCodec sql.NullString
		var duration, fps sql.NullFloat64
		var status, storageType, width, height, bitrate sql.NullInt64
		var size int64
		var createdAt, updatedAt sql.NullString

		err := rows.Scan(&id, &filename, &name, &location, &duration, &status,
			&size, &storageType, &createdAt, &updatedAt, &cLocation, &description,
			&episode, &width, &height, &fps, &videoCodec, &audioCodec, &bitrate)
		if err != nil {
			return err
		}

		_, err = pgDB.Exec(`
			INSERT INTO convert_items (id, filename, name, location, duration, 
				status, size, storage_type, created_at, updated_at, c_location, 
				description, episode, width, height, fps, video_codec, audio_codec, bitrate)
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19)
			ON CONFLICT (id) DO NOTHING`,
			id, filename, name, location, duration, status, size, storageType,
			createdAt, updatedAt, cLocation, description, episode, width, height,
			fps, videoCodec, audioCodec, bitrate)
		if err != nil {
			return err
		}
	}

	// Update sequence
	_, err = pgDB.Exec(`SELECT setval('convert_items_id_seq', (SELECT COALESCE(MAX(id), 1) FROM convert_items))`)
	return err
}

func migrateUsers(sqliteDB, pgDB *sql.DB) error {
	log.Println("Migrating users...")
	
	rows, err := sqliteDB.Query(`SELECT id, username, email, password, role, 
		status, created_at, updated_at FROM users`)
	if err != nil {
		return err
	}
	defer rows.Close()

	for rows.Next() {
		var id int64
		var username, email, password, role, status, createdAt, updatedAt sql.NullString

		err := rows.Scan(&id, &username, &email, &password, &role, &status,
			&createdAt, &updatedAt)
		if err != nil {
			return err
		}

		_, err = pgDB.Exec(`
			INSERT INTO users (id, username, email, password, role, status, created_at, updated_at)
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
			ON CONFLICT (id) DO NOTHING`,
			id, username, email, password, role, status, createdAt, updatedAt)
		if err != nil {
			return err
		}
	}

	// Update sequence
	_, err = pgDB.Exec(`SELECT setval('users_id_seq', (SELECT COALESCE(MAX(id), 1) FROM users))`)
	return err
}

func migrateGuides(sqliteDB, pgDB *sql.DB) error {
	log.Println("Migrating guides...")
	
	rows, err := sqliteDB.Query(`SELECT id, schedule_id, elements, created_at, updated_at FROM guides`)
	if err != nil {
		return err
	}
	defer rows.Close()

	var migrated, skipped int
	for rows.Next() {
		var id, scheduleID int64
		var elements, createdAt, updatedAt sql.NullString

		err := rows.Scan(&id, &scheduleID, &elements, &createdAt, &updatedAt)
		if err != nil {
			return err
		}

		// Check if the referenced schedule exists
		var exists bool
		err = pgDB.QueryRow(`SELECT EXISTS(SELECT 1 FROM schedules WHERE id = $1)`, scheduleID).Scan(&exists)
		if err != nil {
			return err
		}
		
		if !exists {
			log.Printf("Skipping guides record with id %d: referenced schedule_id %d does not exist", 
				id, scheduleID)
			skipped++
			continue
		}

		_, err = pgDB.Exec(`
			INSERT INTO guides (id, schedule_id, elements, created_at, updated_at)
			VALUES ($1, $2, $3, $4, $5)
			ON CONFLICT (id) DO NOTHING`,
			id, scheduleID, elements, createdAt, updatedAt)
		if err != nil {
			return err
		}
		migrated++
	}

	log.Printf("Guides migration completed: %d migrated, %d skipped (orphaned records)", migrated, skipped)

	// Update sequence
	_, err = pgDB.Exec(`SELECT setval('guides_id_seq', (SELECT COALESCE(MAX(id), 1) FROM guides))`)
	return err
}

func migrateHistory(sqliteDB, pgDB *sql.DB) error {
	log.Println("Migrating history...")
	
	// Check if the old column name exists
	var columnExists bool
	err := sqliteDB.QueryRow(`
		SELECT COUNT(*) > 0
		FROM pragma_table_info('history')
		WHERE name = 'filename'
	`).Scan(&columnExists)
	
	var query string
	if columnExists {
		// Old structure with filename
		query = `SELECT id, schedule_id, folder, filename, episode, created_at FROM history`
	} else {
		// New structure with file_id
		query = `SELECT id, schedule_id, folder, file_id, episode, created_at FROM history`
	}
	
	rows, err := sqliteDB.Query(query)
	if err != nil {
		return err
	}
	defer rows.Close()

	for rows.Next() {
		var id, scheduleID, episode sql.NullInt64
		var folder, filename, createdAt sql.NullString

		err := rows.Scan(&id, &scheduleID, &folder, &filename, &episode, &createdAt)
		if err != nil {
			return err
		}

		// Map filename to file_id if needed (this is a simplified approach)
		// In a real migration, you might need to look up the file ID based on filename
		_, err = pgDB.Exec(`
			INSERT INTO history (id, schedule_id, folder, file_id, episode, created_at)
			VALUES ($1, $2, $3, $4, $5, $6)
			ON CONFLICT (id) DO NOTHING`,
			id, scheduleID, folder, filename, episode, createdAt)
		if err != nil {
			return err
		}
	}

	// Update sequence
	_, err = pgDB.Exec(`SELECT setval('history_id_seq', (SELECT COALESCE(MAX(id), 1) FROM history))`)
	return err
}

func migrateRtpUrls(sqliteDB, pgDB *sql.DB) error {
	log.Println("Migrating rtp_urls...")
	
	rows, err := sqliteDB.Query(`SELECT id, url, recorder_id, created_at, updated_at FROM rtp_urls`)
	if err != nil {
		return err
	}
	defer rows.Close()

	for rows.Next() {
		var id, recorderID int64
		var url, createdAt, updatedAt sql.NullString

		err := rows.Scan(&id, &url, &recorderID, &createdAt, &updatedAt)
		if err != nil {
			return err
		}

		_, err = pgDB.Exec(`
			INSERT INTO rtp_urls (id, url, recorder_id, created_at, updated_at)
			VALUES ($1, $2, $3, $4, $5)
			ON CONFLICT (id) DO NOTHING`,
			id, url, recorderID, createdAt, updatedAt)
		if err != nil {
			return err
		}
	}

	// Update sequence
	_, err = pgDB.Exec(`SELECT setval('rtp_urls_id_seq', (SELECT COALESCE(MAX(id), 1) FROM rtp_urls))`)
	return err
}

func migrateRecorders(sqliteDB, pgDB *sql.DB) error {
	log.Println("Migrating recorders...")
	
	rows, err := sqliteDB.Query(`SELECT id, name, input, rtp_url_id, duration, status, 
		vcodec, acodec, resolution, fps, sample_rate, vbitrate, abitrate, 
		max_vbitrate, network_interface FROM recorders`)
	if err != nil {
		return err
	}
	defer rows.Close()

	var migrated, skipped int
	for rows.Next() {
		var id, rtpUrlID, sampleRate, vbitrate, abitrate, maxVbitrate sql.NullInt64
		var name, input, duration, status, vcodec, acodec, resolution, networkInterface sql.NullString
		var fps sql.NullFloat64

		err := rows.Scan(&id, &name, &input, &rtpUrlID, &duration, &status,
			&vcodec, &acodec, &resolution, &fps, &sampleRate, &vbitrate,
			&abitrate, &maxVbitrate, &networkInterface)
		if err != nil {
			return err
		}

		// Check if the referenced rtp_url exists (if rtp_url_id is not null)
		if rtpUrlID.Valid {
			var exists bool
			err = pgDB.QueryRow(`SELECT EXISTS(SELECT 1 FROM rtp_urls WHERE id = $1)`, rtpUrlID.Int64).Scan(&exists)
			if err != nil {
				return err
			}
			
			if !exists {
				log.Printf("Skipping recorder record with id %d: referenced rtp_url_id %d does not exist", 
					id.Int64, rtpUrlID.Int64)
				skipped++
				continue
			}
		}

		_, err = pgDB.Exec(`
			INSERT INTO recorders (id, name, input, rtp_url_id, duration, status,
				vcodec, acodec, resolution, fps, sample_rate, vbitrate, abitrate,
				max_vbitrate, network_interface)
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
			ON CONFLICT (id) DO NOTHING`,
			id, name, input, rtpUrlID, duration, status, vcodec, acodec,
			resolution, fps, sampleRate, vbitrate, abitrate, maxVbitrate, networkInterface)
		if err != nil {
			return err
		}
		migrated++
	}

	log.Printf("Recorders migration completed: %d migrated, %d skipped (orphaned records)", migrated, skipped)

	// Update sequence
	_, err = pgDB.Exec(`SELECT setval('recorders_id_seq', (SELECT COALESCE(MAX(id), 1) FROM recorders))`)
	return err
}

func migrateCodecSettings(sqliteDB, pgDB *sql.DB) error {
	log.Println("Migrating codec_settings...")
	
	rows, err := sqliteDB.Query(`SELECT id, vcodec, acodec, resolution, fps, 
		sample_rate, vbitrate, abitrate, max_vbitrate FROM codec_settings`)
	if err != nil {
		return err
	}
	defer rows.Close()

	for rows.Next() {
		var id, sampleRate, vbitrate, abitrate, maxVbitrate int64
		var vcodec, acodec, resolution string
		var fps float64

		err := rows.Scan(&id, &vcodec, &acodec, &resolution, &fps,
			&sampleRate, &vbitrate, &abitrate, &maxVbitrate)
		if err != nil {
			return err
		}

		_, err = pgDB.Exec(`
			INSERT INTO codec_settings (id, vcodec, acodec, resolution, fps,
				sample_rate, vbitrate, abitrate, max_vbitrate)
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
			ON CONFLICT (id) DO NOTHING`,
			id, vcodec, acodec, resolution, fps, sampleRate, vbitrate, abitrate, maxVbitrate)
		if err != nil {
			return err
		}
	}

	// Update sequence
	_, err = pgDB.Exec(`SELECT setval('codec_settings_id_seq', (SELECT COALESCE(MAX(id), 1) FROM codec_settings))`)
	return err
}

func migrateContentAnalytics(sqliteDB, pgDB *sql.DB) error {
	log.Println("Migrating content_analytics...")
	
	rows, err := sqliteDB.Query(`SELECT id, schedule_id, item_id, content_name, 
		content_path, rtp_output, played_at, duration, play_type FROM content_analytics`)
	if err != nil {
		return err
	}
	defer rows.Close()

	var migrated, skipped int
	for rows.Next() {
		var id, scheduleID, itemID sql.NullInt64
		var contentName, contentPath, rtpOutput, playedAt, playType sql.NullString
		var duration sql.NullFloat64

		err := rows.Scan(&id, &scheduleID, &itemID, &contentName, &contentPath,
			&rtpOutput, &playedAt, &duration, &playType)
		if err != nil {
			return err
		}

		// Check if the referenced schedule exists
		if scheduleID.Valid {
			var exists bool
			err = pgDB.QueryRow(`SELECT EXISTS(SELECT 1 FROM schedules WHERE id = $1)`, scheduleID.Int64).Scan(&exists)
			if err != nil {
				return err
			}
			
			if !exists {
				log.Printf("Skipping content_analytics record with id %d: referenced schedule_id %d does not exist", 
					id.Int64, scheduleID.Int64)
				skipped++
				continue
			}
		}

		_, err = pgDB.Exec(`
			INSERT INTO content_analytics (id, schedule_id, item_id, content_name,
				content_path, rtp_output, played_at, duration, play_type)
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
			ON CONFLICT (id) DO NOTHING`,
			id, scheduleID, itemID, contentName, contentPath, rtpOutput,
			playedAt, duration, playType)
		if err != nil {
			return err
		}
		migrated++
	}

	log.Printf("Content analytics migration completed: %d migrated, %d skipped (orphaned records)", migrated, skipped)

	// Update sequence
	_, err = pgDB.Exec(`SELECT setval('content_analytics_id_seq', (SELECT COALESCE(MAX(id), 1) FROM content_analytics))`)
	return err
} 