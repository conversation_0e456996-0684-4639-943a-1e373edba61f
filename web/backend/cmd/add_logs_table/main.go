package main

import (
	"database/sql"
	"flag"
	"fmt"
	"log"
	"showfer-web/config"
	"showfer-web/service/logger"

	_ "github.com/lib/pq"
)

func main() {
	// Command line flags
	var dryRun = flag.Bool("dry-run", false, "Show what would be executed without making changes")
	var force = flag.Bool("force", false, "Force migration even if table might already exist")
	flag.Parse()

	// Initialize logger
	logger.Init()

	// Initialize database connection
	db, err := config.InitDB()
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer db.Close()

	logger.Log("Starting migration: Add logs table for logging system")

	if *dryRun {
		logger.Log("DRY RUN MODE - No changes will be made")
		showMigrationPlan()
		return
	}

	// Check if migration is needed
	if !*force {
		if migrationAlreadyApplied(db) {
			logger.Log("Migration appears to already be applied. Use -force to run anyway.")
			return
		}
	}

	// Apply migration
	err = applyMigration(db)
	if err != nil {
		log.Fatalf("Migration failed: %v", err)
	}

	// Verify migration was successful
	err = verifyMigration(db)
	if err != nil {
		log.Fatalf("Migration verification failed: %v", err)
	}

	logger.Log("Migration completed successfully!")
}

func showMigrationPlan() {
	fmt.Println("\nMigration Plan:")
	fmt.Println("===============")
	fmt.Println("1. Create 'logs' table with the following structure:")
	fmt.Println("   - id (SERIAL PRIMARY KEY)")
	fmt.Println("   - timestamp (TIMESTAMP NOT NULL DEFAULT NOW())")
	fmt.Println("   - level (TEXT NOT NULL) - Log level (debug, info, warn, error, fatal)")
	fmt.Println("   - message (TEXT NOT NULL) - Log message content")
	fmt.Println("   - source (TEXT NOT NULL) - Module/service that generated the log")
	fmt.Println("   - user_id (INTEGER) - User ID if applicable (FK to users table)")
	fmt.Println("   - request_id (TEXT) - Request ID for tracing")
	fmt.Println("   - metadata (JSONB) - JSON metadata for additional context")
	fmt.Println("   - created_at (TIMESTAMP DEFAULT NOW())")
	fmt.Println("2. Create optimized indexes for efficient querying:")
	fmt.Println("   - idx_logs_timestamp (timestamp)")
	fmt.Println("   - idx_logs_level (level)")
	fmt.Println("   - idx_logs_source (source)")
	fmt.Println("   - idx_logs_user_id (user_id)")
	fmt.Println("   - idx_logs_request_id (request_id)")
	fmt.Println("   - idx_logs_level_timestamp (level, timestamp)")
	fmt.Println("   - idx_logs_source_timestamp (source, timestamp)")
	fmt.Println("   - idx_logs_message_gin (full-text search on message)")
	fmt.Println("\nThis migration will:")
	fmt.Println("- Enable comprehensive application logging")
	fmt.Println("- Support structured logging with metadata")
	fmt.Println("- Provide efficient querying and filtering capabilities")
	fmt.Println("- Enable full-text search on log messages")
	fmt.Println("- Support user and request tracing")
	fmt.Println("- Maintain referential integrity with users table")
}

func migrationAlreadyApplied(db *sql.DB) bool {
	// Check if the logs table already exists
	query := `
		SELECT COUNT(*) 
		FROM information_schema.tables 
		WHERE table_name = 'logs'
	`
	
	var count int
	err := db.QueryRow(query).Scan(&count)
	if err != nil {
		logger.Error("Failed to check if migration is needed: %v", err)
		return false
	}

	return count > 0
}

func applyMigration(db *sql.DB) error {
	// Start a transaction
	tx, err := db.Begin()
	if err != nil {
		return fmt.Errorf("failed to start transaction: %v", err)
	}
	defer tx.Rollback()

	logger.Log("Creating logs table...")

	// Create logs table
	_, err = tx.Exec(`
		CREATE TABLE IF NOT EXISTS logs (
			id SERIAL PRIMARY KEY,
			timestamp TIMESTAMP NOT NULL DEFAULT NOW(),
			level TEXT NOT NULL,
			message TEXT NOT NULL,
			source TEXT NOT NULL,
			user_id INTEGER,
			request_id TEXT,
			metadata JSONB,
			created_at TIMESTAMP DEFAULT NOW(),
			FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
		)
	`)
	if err != nil {
		return fmt.Errorf("failed to create logs table: %v", err)
	}

	logger.Log("Creating indexes for efficient querying...")

	// Create indexes for efficient querying
	_, err = tx.Exec(`
		CREATE INDEX IF NOT EXISTS idx_logs_timestamp ON logs(timestamp);
		CREATE INDEX IF NOT EXISTS idx_logs_level ON logs(level);
		CREATE INDEX IF NOT EXISTS idx_logs_source ON logs(source);
		CREATE INDEX IF NOT EXISTS idx_logs_user_id ON logs(user_id);
		CREATE INDEX IF NOT EXISTS idx_logs_request_id ON logs(request_id);
		CREATE INDEX IF NOT EXISTS idx_logs_level_timestamp ON logs(level, timestamp);
		CREATE INDEX IF NOT EXISTS idx_logs_source_timestamp ON logs(source, timestamp);
		CREATE INDEX IF NOT EXISTS idx_logs_message_gin ON logs USING gin(to_tsvector('english', message));
	`)
	if err != nil {
		return fmt.Errorf("failed to create logs indexes: %v", err)
	}

	// Commit the transaction
	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	logger.Log("Successfully created logs table and indexes")
	return nil
}

func verifyMigration(db *sql.DB) error {
	logger.Log("Verifying migration...")

	// Check that logs table exists
	var tableExists bool
	err := db.QueryRow(`
		SELECT EXISTS (
			SELECT 1 FROM information_schema.tables 
			WHERE table_name = 'logs'
		)
	`).Scan(&tableExists)
	if err != nil {
		return fmt.Errorf("failed to verify logs table existence: %v", err)
	}

	if !tableExists {
		return fmt.Errorf("logs table was not created")
	}
	logger.Log("✓ Logs table exists")

	// Check that all required columns exist
	columns := []string{
		"id", "timestamp", "level", "message", "source", 
		"user_id", "request_id", "metadata", "created_at",
	}
	
	for _, column := range columns {
		var columnExists bool
		err := db.QueryRow(`
			SELECT EXISTS (
				SELECT 1 FROM information_schema.columns 
				WHERE table_name = 'logs' 
				AND column_name = $1
			)
		`, column).Scan(&columnExists)
		if err != nil {
			return fmt.Errorf("failed to verify column %s: %v", column, err)
		}
		
		if !columnExists {
			return fmt.Errorf("column %s was not created", column)
		}
		
		logger.Log("✓ Column %s exists", column)
	}

	// Check that indexes exist
	indexes := []string{
		"idx_logs_timestamp", "idx_logs_level", "idx_logs_source",
		"idx_logs_user_id", "idx_logs_request_id", "idx_logs_level_timestamp",
		"idx_logs_source_timestamp", "idx_logs_message_gin",
	}
	
	for _, index := range indexes {
		var indexExists bool
		err := db.QueryRow(`
			SELECT EXISTS (
				SELECT 1 FROM pg_indexes 
				WHERE indexname = $1
			)
		`, index).Scan(&indexExists)
		if err != nil {
			return fmt.Errorf("failed to verify index %s: %v", index, err)
		}
		
		if !indexExists {
			return fmt.Errorf("index %s was not created", index)
		}
		
		logger.Log("✓ Index %s exists", index)
	}

	// Check foreign key constraint
	var fkExists bool
	err = db.QueryRow(`
		SELECT EXISTS (
			SELECT 1 FROM information_schema.table_constraints 
			WHERE table_name = 'logs' 
			AND constraint_type = 'FOREIGN KEY'
		)
	`).Scan(&fkExists)
	if err != nil {
		return fmt.Errorf("failed to verify foreign key constraint: %v", err)
	}
	
	if !fkExists {
		return fmt.Errorf("foreign key constraint was not created")
	}
	logger.Log("✓ Foreign key constraint exists")

	logger.Log("Migration verification completed successfully")
	return nil
}
