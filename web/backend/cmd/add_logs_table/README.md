# Add Logs Table Migration

This migration command creates the `logs` table for the application's logging system.

## Purpose

This migration adds comprehensive logging capabilities to the application by creating a structured logs table that supports:

- **Structured Logging**: Store logs with different severity levels (debug, info, warn, error, fatal)
- **Source Tracking**: Track which module/service generated each log entry
- **User Context**: Associate logs with specific users when applicable
- **Request Tracing**: Link logs to specific requests for debugging
- **Metadata Support**: Store additional context as JSON metadata
- **Full-text Search**: Enable efficient searching through log messages
- **Performance Optimization**: Multiple indexes for fast querying and filtering

## Table Structure

The `logs` table includes the following columns:

| Column | Type | Description |
|--------|------|-------------|
| `id` | SERIAL PRIMARY KEY | Unique identifier for each log entry |
| `timestamp` | TIMESTAMP NOT NULL | When the log event occurred |
| `level` | TEXT NOT NULL | Log level (debug, info, warn, error, fatal) |
| `message` | TEXT NOT NULL | The log message content |
| `source` | TEXT NOT NULL | Module/service that generated the log |
| `user_id` | INTEGER | User ID if applicable (FK to users table) |
| `request_id` | TEXT | Request ID for tracing related logs |
| `metadata` | JSONB | Additional context as JSON |
| `created_at` | TIMESTAMP | When the record was inserted |

## Indexes Created

The migration creates the following indexes for optimal performance:

- `idx_logs_timestamp` - For time-based queries
- `idx_logs_level` - For filtering by log level
- `idx_logs_source` - For filtering by source module
- `idx_logs_user_id` - For user-specific logs
- `idx_logs_request_id` - For request tracing
- `idx_logs_level_timestamp` - Composite index for level + time queries
- `idx_logs_source_timestamp` - Composite index for source + time queries
- `idx_logs_message_gin` - Full-text search on log messages

## Usage

### Run the migration
```bash
cd web/backend/cmd/add_logs_table
go run main.go
```

### Preview what will be executed (dry run)
```bash
go run main.go -dry-run
```

### Force migration even if table exists
```bash
go run main.go -force
```

## Command Line Options

- `-dry-run`: Show what would be executed without making changes
- `-force`: Force migration even if the logs table might already exist

## Prerequisites

- PostgreSQL database connection configured
- Users table must exist (for foreign key constraint)
- Proper database permissions to create tables and indexes

## Safety Features

- Uses transactions to ensure atomicity
- Checks if migration is already applied
- Verifies successful creation of table, columns, indexes, and constraints
- Comprehensive error handling and logging

## Integration

This migration is designed to work with:

- **Log Repository**: `web/backend/repository/logs_repository.go`
- **Log Models**: `web/backend/models/log.go`
- **Log API**: `web/backend/api/logs/`
- **Logger Service**: `web/backend/service/logger/`

## Deployment Notes

- This migration should be run during deployment to ensure the logs table exists
- The table is designed to handle high-volume logging with optimized indexes
- JSONB metadata column allows for flexible log context without schema changes
- Foreign key constraint ensures data integrity with user references
