package main

import (
	"database/sql"
	"flag"
	"fmt"
	"log"
	"showfer-web/config"
	"showfer-web/service/logger"

	_ "github.com/lib/pq"
)

func main() {
	// Command line flags
	var dryRun = flag.Bool("dry-run", false, "Show what would be executed without making changes")
	var force = flag.Bool("force", false, "Force migration even if columns might already exist")
	flag.Parse()

	// Initialize logger
	logger.Init()

	// Initialize database connection
	db, err := config.InitDB()
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer db.Close()

	logger.Log("Starting migration: Add dual audio fields to codec_settings table")

	if *dryRun {
		logger.Log("DRY RUN MODE - No changes will be made")
		showMigrationPlan()
		return
	}

	// Check if migration is needed
	if !*force {
		if migrationAlreadyApplied(db) {
			logger.Log("Migration appears to already be applied. Use -force to run anyway.")
			return
		}
	}

	// Apply migration
	err = applyMigration(db)
	if err != nil {
		log.Fatalf("Migration failed: %v", err)
	}

	// Verify migration was successful
	err = verifyMigration(db)
	if err != nil {
		log.Fatalf("Migration verification failed: %v", err)
	}

	logger.Log("Migration completed successfully!")
}

func showMigrationPlan() {
	fmt.Println("\nMigration Plan:")
	fmt.Println("===============")
	fmt.Println("1. Add 'dual_audio_mode' column to codec_settings table (BOOLEAN DEFAULT FALSE)")
	fmt.Println("2. Add 'audio1_codec' column to codec_settings table (TEXT DEFAULT 'ac3_passthrough')")
	fmt.Println("3. Add 'audio1_bitrate' column to codec_settings table (INTEGER DEFAULT 448)")
	fmt.Println("4. Add 'audio1_channels' column to codec_settings table (INTEGER DEFAULT 6)")
	fmt.Println("5. Add 'audio2_codec' column to codec_settings table (TEXT DEFAULT 'aac_downmix')")
	fmt.Println("6. Add 'audio2_bitrate' column to codec_settings table (INTEGER DEFAULT 192)")
	fmt.Println("7. Add 'audio2_channels' column to codec_settings table (INTEGER DEFAULT 2)")
	fmt.Println("\nThis migration will:")
	fmt.Println("- Enable dual audio passthrough functionality (5.1 surround + stereo)")
	fmt.Println("- Add support for configurable audio codecs and bitrates per track")
	fmt.Println("- Set safe defaults for existing codec settings")
	fmt.Println("- Maintain backward compatibility with single audio mode")
	fmt.Println("- Not affect existing streaming functionality")
}

func migrationAlreadyApplied(db *sql.DB) bool {
	// Check if the dual_audio_mode column already exists
	query := `
		SELECT COUNT(*) 
		FROM information_schema.columns 
		WHERE table_name = 'codec_settings' 
		AND column_name = 'dual_audio_mode'
	`
	
	var count int
	err := db.QueryRow(query).Scan(&count)
	if err != nil {
		logger.Error("Failed to check if migration is needed: %v", err)
		return false
	}

	return count > 0
}

func applyMigration(db *sql.DB) error {
	// Start a transaction
	tx, err := db.Begin()
	if err != nil {
		return fmt.Errorf("failed to start transaction: %v", err)
	}
	defer tx.Rollback()

	logger.Log("Adding dual audio fields to codec_settings table...")

	// Add dual_audio_mode column
	logger.Log("Adding dual_audio_mode column...")
	_, err = tx.Exec(`
		ALTER TABLE codec_settings 
		ADD COLUMN IF NOT EXISTS dual_audio_mode BOOLEAN DEFAULT FALSE
	`)
	if err != nil {
		return fmt.Errorf("failed to add dual_audio_mode column: %v", err)
	}

	// Add audio1_codec column (5.1 surround track)
	logger.Log("Adding audio1_codec column...")
	_, err = tx.Exec(`
		ALTER TABLE codec_settings 
		ADD COLUMN IF NOT EXISTS audio1_codec TEXT DEFAULT 'ac3_passthrough'
	`)
	if err != nil {
		return fmt.Errorf("failed to add audio1_codec column: %v", err)
	}

	// Add audio1_bitrate column
	logger.Log("Adding audio1_bitrate column...")
	_, err = tx.Exec(`
		ALTER TABLE codec_settings 
		ADD COLUMN IF NOT EXISTS audio1_bitrate INTEGER DEFAULT 448
	`)
	if err != nil {
		return fmt.Errorf("failed to add audio1_bitrate column: %v", err)
	}

	// Add audio1_channels column
	logger.Log("Adding audio1_channels column...")
	_, err = tx.Exec(`
		ALTER TABLE codec_settings 
		ADD COLUMN IF NOT EXISTS audio1_channels INTEGER DEFAULT 6
	`)
	if err != nil {
		return fmt.Errorf("failed to add audio1_channels column: %v", err)
	}

	// Add audio2_codec column (stereo track)
	logger.Log("Adding audio2_codec column...")
	_, err = tx.Exec(`
		ALTER TABLE codec_settings 
		ADD COLUMN IF NOT EXISTS audio2_codec TEXT DEFAULT 'aac_downmix'
	`)
	if err != nil {
		return fmt.Errorf("failed to add audio2_codec column: %v", err)
	}

	// Add audio2_bitrate column
	logger.Log("Adding audio2_bitrate column...")
	_, err = tx.Exec(`
		ALTER TABLE codec_settings 
		ADD COLUMN IF NOT EXISTS audio2_bitrate INTEGER DEFAULT 192
	`)
	if err != nil {
		return fmt.Errorf("failed to add audio2_bitrate column: %v", err)
	}

	// Add audio2_channels column
	logger.Log("Adding audio2_channels column...")
	_, err = tx.Exec(`
		ALTER TABLE codec_settings 
		ADD COLUMN IF NOT EXISTS audio2_channels INTEGER DEFAULT 2
	`)
	if err != nil {
		return fmt.Errorf("failed to add audio2_channels column: %v", err)
	}

	// Update existing records to have the new dual audio defaults
	logger.Log("Setting default dual audio values for existing records...")
	_, err = tx.Exec(`
		UPDATE codec_settings 
		SET 
			dual_audio_mode = FALSE,
			audio1_codec = 'ac3_passthrough',
			audio1_bitrate = 448,
			audio1_channels = 6,
			audio2_codec = 'aac_downmix',
			audio2_bitrate = 192,
			audio2_channels = 2
		WHERE 
			dual_audio_mode IS NULL OR
			audio1_codec IS NULL OR 
			audio1_bitrate IS NULL OR 
			audio1_channels IS NULL OR
			audio2_codec IS NULL OR 
			audio2_bitrate IS NULL OR 
			audio2_channels IS NULL
	`)
	if err != nil {
		return fmt.Errorf("failed to update existing records: %v", err)
	}

	// Commit the transaction
	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	logger.Log("Successfully added all dual audio fields")
	return nil
}

func verifyMigration(db *sql.DB) error {
	logger.Log("Verifying migration...")

	// Check that all columns exist
	columns := []string{
		"dual_audio_mode", 
		"audio1_codec", 
		"audio1_bitrate", 
		"audio1_channels",
		"audio2_codec", 
		"audio2_bitrate", 
		"audio2_channels",
	}
	
	for _, column := range columns {
		query := `
			SELECT COUNT(*) 
			FROM information_schema.columns 
			WHERE table_name = 'codec_settings' 
			AND column_name = $1
		`
		
		var count int
		err := db.QueryRow(query, column).Scan(&count)
		if err != nil {
			return fmt.Errorf("failed to verify column %s: %v", column, err)
		}
		
		if count == 0 {
			return fmt.Errorf("column %s was not created", column)
		}
		
		logger.Log("✓ Column %s exists", column)
	}

	// Check that existing records have proper values
	var recordCount int
	err := db.QueryRow("SELECT COUNT(*) FROM codec_settings").Scan(&recordCount)
	if err != nil {
		return fmt.Errorf("failed to count records: %v", err)
	}

	if recordCount > 0 {
		var nullFields int
		err = db.QueryRow(`
			SELECT COUNT(*) FROM codec_settings 
			WHERE 
				dual_audio_mode IS NULL OR
				audio1_codec IS NULL OR 
				audio1_bitrate IS NULL OR 
				audio1_channels IS NULL OR
				audio2_codec IS NULL OR 
				audio2_bitrate IS NULL OR 
				audio2_channels IS NULL
		`).Scan(&nullFields)
		if err != nil {
			return fmt.Errorf("failed to check for null dual audio fields: %v", err)
		}

		if nullFields > 0 {
			return fmt.Errorf("found %d records with null dual audio fields", nullFields)
		}

		logger.Log("✓ All %d existing records have proper dual audio defaults", recordCount)
		
		// Check the specific values are correct
		var settingsRecord struct {
			dualAudioMode  bool
			audio1Codec    string
			audio1Bitrate  int
			audio1Channels int
			audio2Codec    string
			audio2Bitrate  int
			audio2Channels int
		}
		
		err = db.QueryRow(`
			SELECT dual_audio_mode, audio1_codec, audio1_bitrate, audio1_channels,
				   audio2_codec, audio2_bitrate, audio2_channels
			FROM codec_settings 
			LIMIT 1
		`).Scan(
			&settingsRecord.dualAudioMode,
			&settingsRecord.audio1Codec,
			&settingsRecord.audio1Bitrate,
			&settingsRecord.audio1Channels,
			&settingsRecord.audio2Codec,
			&settingsRecord.audio2Bitrate,
			&settingsRecord.audio2Channels,
		)
		if err != nil {
			return fmt.Errorf("failed to verify dual audio values: %v", err)
		}

		logger.Log("✓ Dual audio mode: %v", settingsRecord.dualAudioMode)
		logger.Log("✓ Audio1 (5.1): %s @ %d kbps (%d channels)", 
			settingsRecord.audio1Codec, settingsRecord.audio1Bitrate, settingsRecord.audio1Channels)
		logger.Log("✓ Audio2 (stereo): %s @ %d kbps (%d channels)", 
			settingsRecord.audio2Codec, settingsRecord.audio2Bitrate, settingsRecord.audio2Channels)
	}

	logger.Log("Migration verification completed successfully")
	return nil
} 