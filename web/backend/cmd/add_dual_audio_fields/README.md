# Add Dual Audio Fields Migration

This migration adds dual audio passthrough functionality to the existing `codec_settings` table, enabling 5.1 surround + stereo audio tracks in streaming output.

## What this migration does

1. **Adds new columns to the `codec_settings` table:**

   - `dual_audio_mode` (BOOLEAN DEFAULT FALSE) - Whether dual audio mode is enabled
   - `audio1_codec` (TEXT DEFAULT 'ac3_passthrough') - Codec for the 5.1 surround track
   - `audio1_bitrate` (INTEGER DEFAULT 448) - Bitrate for the 5.1 track (kbps)
   - `audio1_channels` (INTEGER DEFAULT 6) - Channel count for 5.1 surround (6 channels)
   - `audio2_codec` (TEXT DEFAULT 'aac_downmix') - Codec for the stereo track
   - `audio2_bitrate` (INTEGER DEFAULT 192) - Bitrate for the stereo track (kbps)
   - `audio2_channels` (INTEGER DEFAULT 2) - Channel count for stereo (2 channels)

2. **Sets safe defaults for existing records:**

   - All existing codec settings will have `dual_audio_mode = FALSE` (backward compatibility)
   - Audio1 defaults: AC3 passthrough @ 448 kbps, 6 channels (5.1 surround)
   - Audio2 defaults: AAC downmix @ 192 kbps, 2 channels (stereo)

3. **Maintains backward compatibility:**
   - Single audio mode continues to work as before
   - Existing streaming functionality is unchanged

## Dual Audio Pipeline Architecture

### When `dual_audio_mode = FALSE` (Default):

```
interaudio1 ──┐
              ├─► audiomixer ─► audioenc ─► audioparse ─► mpegtsmux
interaudio2 ──┘
```

### When `dual_audio_mode = TRUE`:

```
interaudio1 ──┐
              ├─► audiomixer ─► audioTee ─┬─► audio51Queue ─► 5.1 encoder ─► mpegtsmux
interaudio2 ──┘                          │
                                         └─► stereoQueue ─► stereo encoder ─► mpegtsmux
```

## Supported Audio Codecs

### Audio Track 1 (5.1 Surround):

- `ac3_passthrough` - AC3 passthrough (recommended for 5.1)
- `ac3_downmix` - AC3 encoding with downmix
- `aac_downmix` - AAC encoding (fallback)

### Audio Track 2 (Stereo):

- `aac_downmix` - AAC encoding (recommended for web compatibility)
- `mpeg1l2_downmix` - MPEG1 Layer 2 (for DVB compatibility)
- `ac3_downmix` - AC3 encoding

## Usage

### 1. Dry run (recommended first)

```bash
cd backend
go run cmd/add_dual_audio_fields/main.go -dry-run
```

This will show you what the migration will do without making any changes.

### 2. Run the migration

```bash
cd backend
go run cmd/add_dual_audio_fields/main.go
```

### 3. Force migration (if needed)

```bash
cd backend
go run cmd/add_dual_audio_fields/main.go -force
```

Use `-force` only if you need to run the migration again for some reason.

## Build and run as executable

```bash
cd backend
go build -o add_dual_audio_fields cmd/add_dual_audio_fields/main.go
./add_dual_audio_fields -dry-run
./add_dual_audio_fields
```

## After Migration - Enabling Dual Audio

Once the migration is complete, you can enable dual audio mode via:

### 1. API (recommended)

```json
{
  "dual_audio_mode": true,
  "audio1_codec": "ac3_passthrough",
  "audio1_bitrate": 448,
  "audio1_channels": 6,
  "audio2_codec": "aac_downmix",
  "audio2_bitrate": 192,
  "audio2_channels": 2
}
```

### 2. Direct database update

```sql
UPDATE codec_settings SET
  dual_audio_mode = TRUE,
  audio1_codec = 'ac3_passthrough',
  audio1_bitrate = 448,
  audio1_channels = 6,
  audio2_codec = 'aac_downmix',
  audio2_bitrate = 192,
  audio2_channels = 2
WHERE id = 1;
```

## Configuration Examples

### Professional Broadcast (AC3 5.1 + AAC Stereo)

```json
{
  "dual_audio_mode": true,
  "audio1_codec": "ac3_passthrough",
  "audio1_bitrate": 448,
  "audio1_channels": 6,
  "audio2_codec": "aac_downmix",
  "audio2_bitrate": 192,
  "audio2_channels": 2
}
```

### DVB-Compatible (AC3 5.1 + MP2 Stereo)

```json
{
  "dual_audio_mode": true,
  "audio1_codec": "ac3_passthrough",
  "audio1_bitrate": 448,
  "audio1_channels": 6,
  "audio2_codec": "mpeg1l2_downmix",
  "audio2_bitrate": 256,
  "audio2_channels": 2
}
```

### Web-Optimized (AAC 5.1 + AAC Stereo)

```json
{
  "dual_audio_mode": true,
  "audio1_codec": "aac_downmix",
  "audio1_bitrate": 384,
  "audio1_channels": 6,
  "audio2_codec": "aac_downmix",
  "audio2_bitrate": 128,
  "audio2_channels": 2
}
```

## Requirements

- **GStreamer Plugins:** Ensure `gst-plugins-bad` and `gst-plugins-ugly` are installed
- **PostgreSQL database**
- **Go environment** set up
- **Database connection** configured in `config/` package

## Safety

- **Safe to run on production:** This migration only adds new columns with safe defaults
- **No data loss:** Existing data is preserved
- **Backwards compatible:** Single audio mode continues to work
- **Transactional:** All changes are made in a single transaction
- **Verification:** The migration verifies that all changes were applied correctly

## Troubleshooting

### GStreamer Issues

```bash
# Install required plugins
sudo apt-get install gstreamer1.0-plugins-bad gstreamer1.0-plugins-ugly

# Check if elements are available
gst-inspect-1.0 avenc_ac3
gst-inspect-1.0 avenc_aac
gst-inspect-1.0 twolamemp2enc
```

### Audio Linking Errors

- Check that both audio sources are connected
- Verify codec compatibility
- Monitor GStreamer bus for detailed error messages

### Performance Issues

- Reduce bitrates if CPU usage is high
- Consider using hardware acceleration if available
- Monitor total bandwidth (video + audio1 + audio2)

## Rollback

If you need to rollback (not recommended after dual audio has been used):

```sql
-- Remove the dual audio columns
ALTER TABLE codec_settings DROP COLUMN IF EXISTS dual_audio_mode;
ALTER TABLE codec_settings DROP COLUMN IF EXISTS audio1_codec;
ALTER TABLE codec_settings DROP COLUMN IF EXISTS audio1_bitrate;
ALTER TABLE codec_settings DROP COLUMN IF EXISTS audio1_channels;
ALTER TABLE codec_settings DROP COLUMN IF EXISTS audio2_codec;
ALTER TABLE codec_settings DROP COLUMN IF EXISTS audio2_bitrate;
ALTER TABLE codec_settings DROP COLUMN IF EXISTS audio2_channels;
```

**Note:** Only rollback if dual audio mode has not been enabled and used, as this will cause streaming issues.

## Benefits

✅ **Professional audio support** - 5.1 surround + stereo in same stream  
✅ **Flexible codec configuration** - AC3, AAC, MP2 options per track  
✅ **Backward compatibility** - Single audio mode preserved  
✅ **Broadcast standard compliance** - Supports DVB and web standards  
✅ **Future extensible** - Easy to add more audio tracks  
✅ **Production ready** - Safe defaults and comprehensive verification
