package main

import (
	"database/sql"
	"fmt"
	"log"
	"showfer-web/config"
	"showfer-web/service/logger"

	_ "github.com/lib/pq"
)

func main() {
	// Initialize logger
	logger.Init()
	
	log.Println("Starting database cleanup for unused retranscode components...")

	// Initialize database connection
	db, err := config.InitDB()
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer db.Close()

	// Perform cleanup
	if err := performCleanup(db); err != nil {
		log.Fatalf("Database cleanup failed: %v", err)
	}

	log.Println("Database cleanup completed successfully!")
}

func performCleanup(db *sql.DB) error {
	log.Println("Starting database cleanup operations...")

	// 1. Drop retranscode_jobs table if it exists
	if err := dropRetranscodeJobsTable(db); err != nil {
		return fmt.Errorf("failed to drop retranscode_jobs table: %w", err)
	}

	// 2. Remove unused columns from convert_items table
	if err := removeUnusedColumns(db); err != nil {
		return fmt.Errorf("failed to remove unused columns: %w", err)
	}

	// 3. Verify the cleanup
	if err := verifyCleanup(db); err != nil {
		return fmt.Errorf("failed to verify cleanup: %w", err)
	}

	return nil
}

func dropRetranscodeJobsTable(db *sql.DB) error {
	log.Println("Checking if retranscode_jobs table exists...")

	// Check if table exists
	var exists bool
	checkQuery := `
		SELECT EXISTS (
			SELECT 1 
			FROM information_schema.tables 
			WHERE table_schema = 'public' 
			AND table_name = 'retranscode_jobs'
		);
	`
	
	err := db.QueryRow(checkQuery).Scan(&exists)
	if err != nil {
		return fmt.Errorf("failed to check if retranscode_jobs table exists: %w", err)
	}

	if exists {
		log.Println("Dropping retranscode_jobs table...")
		dropQuery := `DROP TABLE IF EXISTS retranscode_jobs;`
		
		_, err = db.Exec(dropQuery)
		if err != nil {
			return fmt.Errorf("failed to drop retranscode_jobs table: %w", err)
		}
		
		log.Println("✅ Successfully dropped retranscode_jobs table")
	} else {
		log.Println("ℹ️  retranscode_jobs table does not exist, skipping")
	}

	return nil
}

func removeUnusedColumns(db *sql.DB) error {
	log.Println("Removing unused columns from convert_items table...")

	// List of columns to remove
	columnsToRemove := []string{
		"retranscoding_status",
		"pending_codec_version", 
		"original_filename",
		"transcoded_filename",
	}

	for _, column := range columnsToRemove {
		if err := removeColumnIfExists(db, "convert_items", column); err != nil {
			return fmt.Errorf("failed to remove column %s: %w", column, err)
		}
	}

	return nil
}

func removeColumnIfExists(db *sql.DB, tableName, columnName string) error {
	// Check if column exists
	var exists bool
	checkQuery := `
		SELECT EXISTS (
			SELECT 1 
			FROM information_schema.columns 
			WHERE table_schema = 'public' 
			AND table_name = $1 
			AND column_name = $2
		);
	`
	
	err := db.QueryRow(checkQuery, tableName, columnName).Scan(&exists)
	if err != nil {
		return fmt.Errorf("failed to check if column %s exists: %w", columnName, err)
	}

	if exists {
		log.Printf("Removing column %s from %s table...", columnName, tableName)
		
		dropQuery := fmt.Sprintf("ALTER TABLE %s DROP COLUMN IF EXISTS %s;", tableName, columnName)
		_, err = db.Exec(dropQuery)
		if err != nil {
			return fmt.Errorf("failed to drop column %s: %w", columnName, err)
		}
		
		log.Printf("✅ Successfully removed column %s", columnName)
	} else {
		log.Printf("ℹ️  Column %s does not exist, skipping", columnName)
	}

	return nil
}

func verifyCleanup(db *sql.DB) error {
	log.Println("Verifying cleanup results...")

	// Check convert_items table structure
	query := `
		SELECT column_name, data_type 
		FROM information_schema.columns 
		WHERE table_schema = 'public' 
		AND table_name = 'convert_items'
		ORDER BY ordinal_position;
	`

	rows, err := db.Query(query)
	if err != nil {
		return fmt.Errorf("failed to query convert_items structure: %w", err)
	}
	defer rows.Close()

	log.Println("\n📋 Current convert_items table structure:")
	log.Println("Column Name                | Data Type")
	log.Println("---------------------------|------------------")

	for rows.Next() {
		var columnName, dataType string
		if err := rows.Scan(&columnName, &dataType); err != nil {
			return fmt.Errorf("failed to scan column info: %w", err)
		}
		log.Printf("%-26s | %s", columnName, dataType)
	}

	if err = rows.Err(); err != nil {
		return fmt.Errorf("error reading column information: %w", err)
	}

	// Check if retranscode_jobs table still exists
	var tableExists bool
	checkTableQuery := `
		SELECT EXISTS (
			SELECT 1 
			FROM information_schema.tables 
			WHERE table_schema = 'public' 
			AND table_name = 'retranscode_jobs'
		);
	`
	
	err = db.QueryRow(checkTableQuery).Scan(&tableExists)
	if err != nil {
		return fmt.Errorf("failed to check retranscode_jobs table: %w", err)
	}

	if tableExists {
		log.Println("⚠️  WARNING: retranscode_jobs table still exists!")
	} else {
		log.Println("✅ Confirmed: retranscode_jobs table has been removed")
	}

	return nil
} 