package main

import (
	"database/sql"
	"log"
	"os"
	"path/filepath"

	_ "github.com/mattn/go-sqlite3"
)

func main() {
	// Get the database path
	dbPath := filepath.Join("data", "showfer.db")
	if _, err := os.Stat(dbPath); os.IsNotExist(err) {
		log.Fatalf("Database file not found at %s", dbPath)
	}

	// Open the database
	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		log.Fatalf("Failed to open database: %v", err)
	}
	defer db.Close()

	_, err = db.Exec("ALTER TABLE history DROP COLUMN filename")
	if err != nil {
		log.Fatalf("Failed to drop column: %v", err)
	}

	_, err = db.Exec("ALTER TABLE history ADD COLUMN file_id INTEGER NOT NULL DEFAULT 0")
	if err != nil {
		log.Fatalf("Failed to add column: %v", err)
	}
	_, err = db.Exec("DELETE FROM history WHERE file_id = 0")
	if err != nil {
		log.Fatalf("Failed to delete old data: %v", err)
	}
}
