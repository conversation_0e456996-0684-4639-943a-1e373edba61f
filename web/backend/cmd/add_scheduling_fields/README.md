# Add Scheduling Fields Migration

This migration adds scheduling functionality to the existing `recorders` table without affecting existing data.

## What this migration does

1. **Adds new columns to the `recorders` table:**

   - `is_scheduled` (BOOLEAN DEFAULT FALSE) - Whether the recorder is scheduled for later
   - `scheduled_start_time` (TIMESTAMP DEFAULT NULL) - When the recording should start
   - `created_at` (TIMESTAMP DEFAULT NOW()) - When the recorder was created
   - `updated_at` (TIMESTAMP DEFAULT NOW()) - When the recorder was last modified

2. **Sets safe defaults for existing records:**

   - All existing recorders will have `is_scheduled = FALSE` (immediate recording)
   - All existing recorders will get current timestamp for `created_at` and `updated_at`

3. **Creates automatic timestamp updates:**
   - Adds a PostgreSQL trigger to automatically update `updated_at` when records are modified

## Usage

### 1. Dry run (recommended first)

```bash
cd backend
go run cmd/add_scheduling_fields/main.go -dry-run
```

This will show you what the migration will do without making any changes.

### 2. Run the migration

```bash
cd backend
go run cmd/add_scheduling_fields/main.go
```

### 3. Force migration (if needed)

```bash
cd backend
go run cmd/add_scheduling_fields/main.go -force
```

Use `-force` only if you need to run the migration again for some reason.

## Build and run as executable

```bash
cd backend
go build -o add_scheduling_fields cmd/add_scheduling_fields/main.go
./add_scheduling_fields -dry-run
./add_scheduling_fields
```

## Safety

- **Safe to run on production:** This migration only adds new columns with safe defaults
- **No data loss:** Existing data is preserved
- **Backwards compatible:** Existing functionality continues to work
- **Transactional:** All changes are made in a single transaction
- **Verification:** The migration verifies that all changes were applied correctly

## Requirements

- PostgreSQL database
- Go environment set up
- Database connection configured in `config/` package

## After migration

Once the migration is complete, you can:

1. Create immediate recordings (existing functionality, unchanged)
2. Create scheduled recordings with future start times
3. The scheduler service will automatically start recordings at their scheduled time

## Rollback

If you need to rollback (not recommended after data has been added):

```sql
-- Remove the trigger first
DROP TRIGGER IF EXISTS update_recorders_updated_at ON recorders;
DROP FUNCTION IF EXISTS update_recorders_updated_at();

-- Remove the columns
ALTER TABLE recorders DROP COLUMN IF EXISTS is_scheduled;
ALTER TABLE recorders DROP COLUMN IF EXISTS scheduled_start_time;
ALTER TABLE recorders DROP COLUMN IF EXISTS created_at;
ALTER TABLE recorders DROP COLUMN IF EXISTS updated_at;
```

**Note:** Only rollback if no scheduled recordings have been created, as this will cause data loss.
