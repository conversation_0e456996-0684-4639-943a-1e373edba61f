package main

import (
	"database/sql"
	"flag"
	"fmt"
	"log"
	"showfer-web/config"
	"showfer-web/service/logger"

	_ "github.com/lib/pq"
)

func main() {
	// Command line flags
	var dryRun = flag.Bool("dry-run", false, "Show what would be executed without making changes")
	var force = flag.Bool("force", false, "Force migration even if columns might already exist")
	flag.Parse()

	// Initialize logger
	logger.Init()

	// Initialize database connection
	db, err := config.InitDB()
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer db.Close()

	logger.Log("Starting migration: Add scheduling fields to recorders table")

	if *dryRun {
		logger.Log("DRY RUN MODE - No changes will be made")
		showMigrationPlan()
		return
	}

	// Check if migration is needed
	if !*force {
		if migrationAlreadyApplied(db) {
			logger.Log("Migration appears to already be applied. Use -force to run anyway.")
			return
		}
	}

	// Apply migration
	err = applyMigration(db)
	if err != nil {
		log.Fatalf("Migration failed: %v", err)
	}

	// Verify migration was successful
	err = verifyMigration(db)
	if err != nil {
		log.Fatalf("Migration verification failed: %v", err)
	}

	logger.Log("Migration completed successfully!")
}

func showMigrationPlan() {
	fmt.Println("\nMigration Plan:")
	fmt.Println("===============")
	fmt.Println("1. Add 'is_scheduled' column to recorders table (BOOLEAN DEFAULT FALSE)")
	fmt.Println("2. Add 'scheduled_start_time' column to recorders table (TIMESTAMP DEFAULT NULL)")
	fmt.Println("3. Add 'created_at' column to recorders table (TIMESTAMP DEFAULT NOW())")
	fmt.Println("4. Add 'updated_at' column to recorders table (TIMESTAMP DEFAULT NOW())")
	fmt.Println("\nThis migration will:")
	fmt.Println("- Enable scheduling functionality for recordings")
	fmt.Println("- Add audit trail with created_at and updated_at timestamps")
	fmt.Println("- Set safe defaults for existing records")
	fmt.Println("- Not affect existing data or functionality")
}

func migrationAlreadyApplied(db *sql.DB) bool {
	// Check if the is_scheduled column already exists
	query := `
		SELECT COUNT(*) 
		FROM information_schema.columns 
		WHERE table_name = 'recorders' 
		AND column_name = 'is_scheduled'
	`
	
	var count int
	err := db.QueryRow(query).Scan(&count)
	if err != nil {
		logger.Error("Failed to check if migration is needed: %v", err)
		return false
	}

	return count > 0
}

func applyMigration(db *sql.DB) error {
	// Start a transaction
	tx, err := db.Begin()
	if err != nil {
		return fmt.Errorf("failed to start transaction: %v", err)
	}
	defer tx.Rollback()

	logger.Log("Adding scheduling fields to recorders table...")

	// Add is_scheduled column
	logger.Log("Adding is_scheduled column...")
	_, err = tx.Exec(`
		ALTER TABLE recorders 
		ADD COLUMN IF NOT EXISTS is_scheduled BOOLEAN DEFAULT FALSE
	`)
	if err != nil {
		return fmt.Errorf("failed to add is_scheduled column: %v", err)
	}

	// Add scheduled_start_time column
	logger.Log("Adding scheduled_start_time column...")
	_, err = tx.Exec(`
		ALTER TABLE recorders 
		ADD COLUMN IF NOT EXISTS scheduled_start_time TIMESTAMP DEFAULT NULL
	`)
	if err != nil {
		return fmt.Errorf("failed to add scheduled_start_time column: %v", err)
	}

	// Add created_at column
	logger.Log("Adding created_at column...")
	_, err = tx.Exec(`
		ALTER TABLE recorders 
		ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT NOW()
	`)
	if err != nil {
		return fmt.Errorf("failed to add created_at column: %v", err)
	}

	// Add updated_at column
	logger.Log("Adding updated_at column...")
	_, err = tx.Exec(`
		ALTER TABLE recorders 
		ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT NOW()
	`)
	if err != nil {
		return fmt.Errorf("failed to add updated_at column: %v", err)
	}

	// Update existing records to have created_at and updated_at timestamps
	logger.Log("Setting timestamps for existing records...")
	_, err = tx.Exec(`
		UPDATE recorders 
		SET created_at = NOW(), updated_at = NOW() 
		WHERE created_at IS NULL OR updated_at IS NULL
	`)
	if err != nil {
		return fmt.Errorf("failed to update existing records: %v", err)
	}

	// Create a function to automatically update updated_at on record changes
	logger.Log("Creating trigger function for updated_at...")
	_, err = tx.Exec(`
		CREATE OR REPLACE FUNCTION update_recorders_updated_at()
		RETURNS TRIGGER AS $$
		BEGIN
			NEW.updated_at = NOW();
			RETURN NEW;
		END;
		$$ LANGUAGE plpgsql;
	`)
	if err != nil {
		return fmt.Errorf("failed to create trigger function: %v", err)
	}

	// Create trigger to automatically update updated_at
	logger.Log("Creating trigger for updated_at...")
	_, err = tx.Exec(`
		DROP TRIGGER IF EXISTS update_recorders_updated_at ON recorders;
		CREATE TRIGGER update_recorders_updated_at
			BEFORE UPDATE ON recorders
			FOR EACH ROW
			EXECUTE FUNCTION update_recorders_updated_at();
	`)
	if err != nil {
		return fmt.Errorf("failed to create trigger: %v", err)
	}

	// Commit the transaction
	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	logger.Log("Successfully added all scheduling fields")
	return nil
}

func verifyMigration(db *sql.DB) error {
	logger.Log("Verifying migration...")

	// Check that all columns exist
	columns := []string{"is_scheduled", "scheduled_start_time", "created_at", "updated_at"}
	
	for _, column := range columns {
		query := `
			SELECT COUNT(*) 
			FROM information_schema.columns 
			WHERE table_name = 'recorders' 
			AND column_name = $1
		`
		
		var count int
		err := db.QueryRow(query, column).Scan(&count)
		if err != nil {
			return fmt.Errorf("failed to verify column %s: %v", column, err)
		}
		
		if count == 0 {
			return fmt.Errorf("column %s was not created", column)
		}
		
		logger.Log("✓ Column %s exists", column)
	}

	// Check that existing records have proper values
	var recordCount int
	err := db.QueryRow("SELECT COUNT(*) FROM recorders").Scan(&recordCount)
	if err != nil {
		return fmt.Errorf("failed to count records: %v", err)
	}

	if recordCount > 0 {
		var nullTimestamps int
		err = db.QueryRow(`
			SELECT COUNT(*) FROM recorders 
			WHERE created_at IS NULL OR updated_at IS NULL
		`).Scan(&nullTimestamps)
		if err != nil {
			return fmt.Errorf("failed to check for null timestamps: %v", err)
		}

		if nullTimestamps > 0 {
			return fmt.Errorf("found %d records with null timestamps", nullTimestamps)
		}

		logger.Log("✓ All %d existing records have proper timestamps", recordCount)
	}

	logger.Log("Migration verification completed successfully")
	return nil
} 