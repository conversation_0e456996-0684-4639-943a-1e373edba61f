package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"

	_ "modernc.org/sqlite"
)

func main() {
	// Check if SQLite database exists
	sqliteDBPath := "/home/<USER>/Dev/traffiq/web/backend/data/showfer.db"
	if _, err := os.Stat(sqliteDBPath); os.IsNotExist(err) {
		log.Fatal("SQLite database not found at /home/<USER>/Dev/traffiq/web/backend/data/showfer.db")
	}

	// Open SQLite connection
	db, err := sql.Open("sqlite", sqliteDBPath)
	if err != nil {
		log.Fatalf("Failed to open SQLite database: %v", err)
	}
	defer db.Close()

	fmt.Println("=== Orphaned Records Check ===")
	
	// Check for orphaned content_analytics records
	fmt.Println("\n1. Checking content_analytics for orphaned schedule_id references:")
	rows, err := db.Query(`
		SELECT DISTINCT ca.schedule_id 
		FROM content_analytics ca 
		LEFT JOIN schedules s ON ca.schedule_id = s.id 
		WHERE s.id IS NULL
	`)
	if err != nil {
		log.Printf("Error checking content_analytics: %v", err)
	} else {
		defer rows.Close()
		orphanedScheduleIDs := []int64{}
		for rows.Next() {
			var scheduleID int64
			rows.Scan(&scheduleID)
			orphanedScheduleIDs = append(orphanedScheduleIDs, scheduleID)
		}
		
		if len(orphanedScheduleIDs) > 0 {
			fmt.Printf("Found %d orphaned schedule_id references in content_analytics: %v\n", 
				len(orphanedScheduleIDs), orphanedScheduleIDs)
			
			// Count total orphaned records
			var count int
			db.QueryRow(`
				SELECT COUNT(*) 
				FROM content_analytics ca 
				LEFT JOIN schedules s ON ca.schedule_id = s.id 
				WHERE s.id IS NULL
			`).Scan(&count)
			fmt.Printf("Total orphaned content_analytics records: %d\n", count)
		} else {
			fmt.Println("No orphaned content_analytics records found.")
		}
	}

	// Check for orphaned guides records
	fmt.Println("\n2. Checking guides for orphaned schedule_id references:")
	rows, err = db.Query(`
		SELECT DISTINCT g.schedule_id 
		FROM guides g 
		LEFT JOIN schedules s ON g.schedule_id = s.id 
		WHERE s.id IS NULL
	`)
	if err != nil {
		log.Printf("Error checking guides: %v", err)
	} else {
		defer rows.Close()
		orphanedScheduleIDs := []int64{}
		for rows.Next() {
			var scheduleID int64
			rows.Scan(&scheduleID)
			orphanedScheduleIDs = append(orphanedScheduleIDs, scheduleID)
		}
		
		if len(orphanedScheduleIDs) > 0 {
			fmt.Printf("Found %d orphaned schedule_id references in guides: %v\n", 
				len(orphanedScheduleIDs), orphanedScheduleIDs)
			
			var count int
			db.QueryRow(`
				SELECT COUNT(*) 
				FROM guides g 
				LEFT JOIN schedules s ON g.schedule_id = s.id 
				WHERE s.id IS NULL
			`).Scan(&count)
			fmt.Printf("Total orphaned guides records: %d\n", count)
		} else {
			fmt.Println("No orphaned guides records found.")
		}
	}

	// Check for orphaned recorders records
	fmt.Println("\n3. Checking recorders for orphaned rtp_url_id references:")
	rows, err = db.Query(`
		SELECT DISTINCT r.rtp_url_id 
		FROM recorders r 
		LEFT JOIN rtp_urls ru ON r.rtp_url_id = ru.id 
		WHERE r.rtp_url_id IS NOT NULL AND ru.id IS NULL
	`)
	if err != nil {
		log.Printf("Error checking recorders: %v", err)
	} else {
		defer rows.Close()
		orphanedRtpUrlIDs := []int64{}
		for rows.Next() {
			var rtpUrlID int64
			rows.Scan(&rtpUrlID)
			orphanedRtpUrlIDs = append(orphanedRtpUrlIDs, rtpUrlID)
		}
		
		if len(orphanedRtpUrlIDs) > 0 {
			fmt.Printf("Found %d orphaned rtp_url_id references in recorders: %v\n", 
				len(orphanedRtpUrlIDs), orphanedRtpUrlIDs)
			
			var count int
			db.QueryRow(`
				SELECT COUNT(*) 
				FROM recorders r 
				LEFT JOIN rtp_urls ru ON r.rtp_url_id = ru.id 
				WHERE r.rtp_url_id IS NOT NULL AND ru.id IS NULL
			`).Scan(&count)
			fmt.Printf("Total orphaned recorders records: %d\n", count)
		} else {
			fmt.Println("No orphaned recorders records found.")
		}
	}

	// Show table counts
	fmt.Println("\n=== Table Record Counts ===")
	tables := []string{"schedules", "convert_items", "users", "guides", "history", 
		"rtp_urls", "recorders", "codec_settings", "content_analytics"}
	
	for _, table := range tables {
		var count int
		err := db.QueryRow(fmt.Sprintf("SELECT COUNT(*) FROM %s", table)).Scan(&count)
		if err != nil {
			fmt.Printf("%s: Error - %v\n", table, err)
		} else {
			fmt.Printf("%s: %d records\n", table, count)
		}
	}

	fmt.Println("\n=== Check Complete ===")
	fmt.Println("If orphaned records were found, the migration script will now skip them automatically.")
} 