package main

import (
	"fmt"
	"log"
	"showfer-web/config"

	_ "github.com/lib/pq"
)

func main() {
	db, err := config.InitDB()
	if err != nil {
		log.Fatalf("Failed to connect to PostgreSQL: %v", err)
	}
	defer db.Close()

	// Check if the recorder_id column already exists
	var columnExists bool
	err = db.QueryRow(`
		SELECT EXISTS (
			SELECT 1
			FROM information_schema.columns
			WHERE table_name = 'convert_items'
			AND column_name = 'recorder_id'
		)
	`).Scan(&columnExists)

	// Add the recorder_id column if it doesn't exist
	if !columnExists {
		_, err = db.Exec(`
			ALTER TABLE convert_items
			ADD COLUMN recorder_id INTEGER DEFAULT NULL
		`)
		if err != nil {
			log.Fatalf("Failed to add recorder_id column: %v", err)
		} else {
			_, err = db.Exec(`
				ALTER TABLE convert_items
				ADD CONSTRAINT fk_recorder
				FOREIGN KEY (recorder_id)
				REFERENCES recorders(id) ON DELETE SET NULL;
			`)
			if err != nil {
				log.Fatalf("Failed to add recorder_id column: %v", err)
			}
		}
		fmt.Println("Successfully added recorder_id column to convert_items table")
	} else {
		fmt.Println("recorder_id column already exists in convert_items table")

		// Check if a foreign key constraint exists
		var constraintName string
		err = db.QueryRow(`
			SELECT tc.constraint_name
			FROM information_schema.table_constraints tc
			JOIN information_schema.key_column_usage kcu
			ON tc.constraint_name = kcu.constraint_name
			WHERE tc.table_name = 'convert_items'
			AND tc.constraint_type = 'FOREIGN KEY'
			AND kcu.column_name = 'recorder_id'
		`).Scan(&constraintName)

		if err == nil && constraintName != "" {
			// Drop the existing constraint
			_, err = db.Exec(fmt.Sprintf(`
				ALTER TABLE convert_items
				DROP CONSTRAINT %s
			`, constraintName))
			if err != nil {
				log.Fatalf("Failed to drop existing foreign key constraint: %v", err)
			}

			// Recreate with ON DELETE SET NULL
			_, err = db.Exec(`
				ALTER TABLE convert_items
				ADD CONSTRAINT fk_recorder
				FOREIGN KEY (recorder_id)
				REFERENCES recorders(id)
				ON DELETE SET NULL
			`)
			if err != nil {
				log.Fatalf("Failed to recreate foreign key with ON DELETE SET NULL: %v", err)
			}

			fmt.Println("Updated foreign key constraint with ON DELETE SET NULL")

		}
	}
}
