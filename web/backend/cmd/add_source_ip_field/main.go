package main

import (
	"database/sql"
	"fmt"
	"log"
	"showfer-web/config"
	"showfer-web/service/logger"

	_ "github.com/lib/pq"
)

func main() {
	logger.Log("Starting migration to add source_ip field to recorders table...")

	db, err := config.InitDB()
	if err != nil {
		log.Fatalf("Failed to connect to PostgreSQL: %v", err)
	}
	defer db.Close()

	err = addSourceIPField(db)
	if err != nil {
		log.Fatalf("Migration failed: %v", err)
	}

	logger.Log("Migration completed successfully!")
}

func addSourceIPField(db *sql.DB) error {
	// Start a transaction
	tx, err := db.Begin()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %v", err)
	}
	defer tx.Rollback()

	// Check if source_ip column exists
	var sourceIPExists bool
	err = tx.QueryRow(`
		SELECT EXISTS (
			SELECT 1 FROM information_schema.columns 
			WHERE table_name = 'recorders' AND column_name = 'source_ip'
		)
	`).Scan(&sourceIPExists)
	if err != nil {
		return fmt.Errorf("failed to check if source_ip column exists: %v", err)
	}

	// Add source_ip column if it doesn't exist
	if !sourceIPExists {
		logger.Log("Adding source_ip column to recorders table...")
		_, err = tx.Exec(`ALTER TABLE recorders ADD COLUMN source_ip TEXT DEFAULT ''`)
		if err != nil {
			return fmt.Errorf("failed to add source_ip column: %v", err)
		}
		logger.Log("Successfully added source_ip column to recorders table")
	} else {
		logger.Log("source_ip column already exists in recorders table - no migration needed")
	}

	// Commit the transaction
	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	return nil
} 