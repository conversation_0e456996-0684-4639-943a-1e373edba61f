package main

import (
	"fmt"
	"log"
	"showfer-web/config"

	_ "github.com/lib/pq"
)

func main() {
	// Connect to PostgreSQL
	db, err := config.InitDB()
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	fmt.Println("Checking history table schema...")

	// Check if history table exists and what columns it has
	rows, err := db.Query(`
		SELECT column_name, data_type 
		FROM information_schema.columns 
		WHERE table_name = 'history'
		ORDER BY ordinal_position
	`)
	if err != nil {
		log.Fatalf("Failed to check table schema: %v", err)
	}
	defer rows.Close()

	fmt.Println("Current history table columns:")
	hasFileId := false
	hasFilename := false
	
	for rows.Next() {
		var columnName, dataType string
		err := rows.Scan(&columnName, &dataType)
		if err != nil {
			log.Fatalf("Failed to scan column info: %v", err)
		}
		fmt.Printf("  - %s (%s)\n", columnName, dataType)
		
		if columnName == "file_id" {
			hasFileId = true
		}
		if columnName == "filename" {
			hasFilename = true
		}
	}

	if hasFileId {
		fmt.Println("✓ History table already has the correct file_id column!")
		return
	}

	fmt.Println("⚠ History table needs to be fixed...")

	if hasFilename {
		fmt.Println("Found 'filename' column, attempting to rename it to 'file_id'...")
		
		// Try to rename filename to file_id and change type
		_, err = db.Exec(`ALTER TABLE history RENAME COLUMN filename TO file_id`)
		if err != nil {
			log.Printf("Failed to rename column: %v", err)
		} else {
			fmt.Println("✓ Successfully renamed filename to file_id")
		}
		
		// Try to change the type to INTEGER
		_, err = db.Exec(`ALTER TABLE history ALTER COLUMN file_id TYPE INTEGER USING file_id::INTEGER`)
		if err != nil {
			log.Printf("Failed to change column type: %v", err)
		} else {
			fmt.Println("✓ Successfully changed file_id type to INTEGER")
		}
	} else {
		fmt.Println("Recreating history table with correct schema...")
		
		// Drop and recreate the table
		_, err = db.Exec(`DROP TABLE IF EXISTS history`)
		if err != nil {
			log.Fatalf("Failed to drop history table: %v", err)
		}

		_, err = db.Exec(`
			CREATE TABLE history (
				id SERIAL PRIMARY KEY,
				schedule_id INTEGER NOT NULL,
				folder TEXT NOT NULL,
				file_id INTEGER NOT NULL,
				episode INTEGER DEFAULT NULL,
				created_at TIMESTAMP DEFAULT NOW()
			)
		`)
		if err != nil {
			log.Fatalf("Failed to create history table: %v", err)
		}

		fmt.Println("✓ Successfully recreated history table with correct schema")
	}

	fmt.Println("History table schema fix completed!")
} 