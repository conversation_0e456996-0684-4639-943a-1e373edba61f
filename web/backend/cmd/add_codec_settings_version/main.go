package main

import (
	"database/sql"
	"flag"
	"fmt"
	"log"
	"showfer-web/config"
	"showfer-web/service/logger"

	_ "github.com/lib/pq"
)

func main() {
	// Command line flags
	var dryRun = flag.Bool("dry-run", false, "Show what would be executed without making changes")
	var force = flag.Bool("force", false, "Force migration even if column might already exist")
	flag.Parse()

	// Initialize logger
	logger.Init()

	// Initialize database connection
	db, err := config.InitDB()
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer db.Close()

	logger.Log("Starting migration: Add codec_settings_version column to convert_items table")

	if *dryRun {
		logger.Log("DRY RUN MODE - No changes will be made")
		showMigrationPlan()
		return
	}

	// Check if migration is needed
	if !*force {
		if migrationAlreadyApplied(db) {
			logger.Log("Migration appears to already be applied. Use -force to run anyway.")
			return
		}
	}

	// Apply migration
	err = applyMigration(db)
	if err != nil {
		log.Fatalf("Migration failed: %v", err)
	}

	// Verify migration was successful
	err = verifyMigration(db)
	if err != nil {
		log.Fatalf("Migration verification failed: %v", err)
	}

	logger.Log("Migration completed successfully!")
}

func showMigrationPlan() {
	fmt.Println("\nMigration Plan:")
	fmt.Println("===============")
	fmt.Println("1. Add 'codec_settings_version' column to convert_items table (INTEGER DEFAULT NULL)")
	fmt.Println("\nThis migration will:")
	fmt.Println("- Add codec_settings_version column to track which codec settings version was used")
	fmt.Println("- Allow NULL values for existing records")
	fmt.Println("- Enable version tracking for future codec setting changes")
	fmt.Println("- Fix the error: 'column \"codec_settings_version\" does not exist'")
}

func migrationAlreadyApplied(db *sql.DB) bool {
	// Check if the codec_settings_version column already exists
	query := `
		SELECT COUNT(*) 
		FROM information_schema.columns 
		WHERE table_name = 'convert_items' 
		AND column_name = 'codec_settings_version'
	`
	
	var count int
	err := db.QueryRow(query).Scan(&count)
	if err != nil {
		logger.Error("Failed to check if migration is needed: %v", err)
		return false
	}

	return count > 0
}

func applyMigration(db *sql.DB) error {
	// Start a transaction
	tx, err := db.Begin()
	if err != nil {
		return fmt.Errorf("failed to start transaction: %v", err)
	}
	defer tx.Rollback()

	logger.Log("Adding codec_settings_version column to convert_items table...")

	// Add codec_settings_version column
	logger.Log("Adding codec_settings_version column...")
	_, err = tx.Exec(`
		ALTER TABLE convert_items 
		ADD COLUMN IF NOT EXISTS codec_settings_version INTEGER DEFAULT NULL
	`)
	if err != nil {
		return fmt.Errorf("failed to add codec_settings_version column: %v", err)
	}

	// Commit the transaction
	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	logger.Log("Successfully added codec_settings_version column")
	return nil
}

func verifyMigration(db *sql.DB) error {
	// Verify the column was added
	query := `
		SELECT COUNT(*) 
		FROM information_schema.columns 
		WHERE table_name = 'convert_items' 
		AND column_name = 'codec_settings_version'
	`
	
	var count int
	err := db.QueryRow(query).Scan(&count)
	if err != nil {
		return fmt.Errorf("failed to verify migration: %v", err)
	}

	if count == 0 {
		return fmt.Errorf("verification failed: codec_settings_version column was not added")
	}

	logger.Log("Verification successful: codec_settings_version column exists")

	// Test that we can query the column
	testQuery := `SELECT codec_settings_version FROM convert_items LIMIT 1`
	_, err = db.Query(testQuery)
	if err != nil {
		return fmt.Errorf("verification failed: cannot query codec_settings_version column: %v", err)
	}

	logger.Log("Verification successful: codec_settings_version column is queryable")
	return nil
} 