# Add Codec Settings Version Migration

This migration adds the missing `codec_settings_version` column to the `convert_items` table.

## Problem

The application code references a `codec_settings_version` column in the `convert_items` table, but this column doesn't exist in the database schema. This causes the error:

```
Failed to get convert items: pq: column "codec_settings_version" does not exist
```

## Solution

This migration adds the `codec_settings_version` column as an INTEGER with DEFAULT NULL to the `convert_items` table.

## Usage

### Dry Run (recommended first)

```bash
cd backend/cmd/add_codec_settings_version
go run main.go -dry-run
```

### Apply Migration

```bash
cd backend/cmd/add_codec_settings_version
go run main.go
```

### Force Migration (if column exists but you want to run anyway)

```bash
cd backend/cmd/add_codec_settings_version
go run main.go -force
```

## What this migration does

1. Adds `codec_settings_version INTEGER DEFAULT NULL` column to `convert_items` table
2. Allows tracking which codec settings version was used for each file
3. Fixes the database query errors related to the missing column
4. Maintains backward compatibility by allowing NULL values for existing records

## Verification

The migration will verify that:

1. The column was successfully added
2. The column can be queried without errors

## Safety

- Uses transactions to ensure atomicity
- Includes rollback mechanism on failure
- Uses `IF NOT EXISTS` to prevent errors if column already exists
- Provides dry-run mode to preview changes
