CREATE TABLE IF NOT EXISTS recorders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    rtp_url TEXT NOT NULL,
    output_path TEXT NOT NULL,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL
);

CREATE TABLE IF NOT EXISTS rtp_urls (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    url TEXT NOT NULL UNIQUE,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL
);

CREATE TABLE IF NOT EXISTS convert_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    filename TEXT NOT NULL,
    name TEXT NOT NULL,
    location TEXT NOT NULL,
    duration REAL DEFAULT 0,
    status INTEGER DEFAULT 0,
    size INTEGER DEFAULT 0,
    storage_type INTEGER DEFAULT 0,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    c_location TEXT,
    description TEXT,
    episode TEXT
);

CREATE TABLE IF NOT EXISTS content_analytics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    schedule_id INTEGER NOT NULL,
    item_id INTEGER,
    content_name TEXT NOT NULL,
    content_path TEXT NOT NULL,
    rtp_output TEXT NOT NULL,
    played_at TEXT NOT NULL,
    duration REAL DEFAULT 0,
    play_type TEXT NOT NULL,
    FOREIGN KEY (schedule_id) REFERENCES schedules(id)
);

CREATE INDEX IF NOT EXISTS idx_content_analytics_schedule_id ON content_analytics(schedule_id);
CREATE INDEX IF NOT EXISTS idx_content_analytics_content_path ON content_analytics(content_path);
CREATE INDEX IF NOT EXISTS idx_content_analytics_played_at ON content_analytics(played_at);

CREATE INDEX IF NOT EXISTS idx_convert_items_location ON convert_items(location);
CREATE INDEX IF NOT EXISTS idx_convert_items_filename_location ON convert_items(filename, location);
