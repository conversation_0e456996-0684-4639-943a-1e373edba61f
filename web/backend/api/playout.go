package api

import (
	"fmt"
	"net/http"
	"showfer-web/service/playout"

	"github.com/gin-gonic/gin"
)

// PlayoutAPI handles playout-related API endpoints
type PlayoutAPI struct {
	playout *playout.Playout
}

// NewPlayoutAPI creates a new PlayoutAPI
func NewPlayoutAPI(playout *playout.Playout) *PlayoutAPI {
	return &PlayoutAPI{
		playout: playout,
	}
}

// GetStreamPreviewURL handles GET /api/v1/playout/preview/:shortID
func (api *PlayoutAPI) GetStreamPreviewURL(c *gin.Context) {
	shortID := c.Param("shortID")
	if shortID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "shortID is required"})
		return
	}

	// Construct the HLS URL
	// This assumes your web server is serving files from the data directory
	previewURL := fmt.Sprintf("/data/preview/%s/playlist.m3u8", shortID)

	c.JSON(http.StatusOK, gin.H{
		"previewUrl": previewURL,
	})
}
