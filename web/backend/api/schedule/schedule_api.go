package schedule

import (
	"database/sql"
	"errors"
	"net/http"
	"showfer-web/config"
	"showfer-web/models"
	"showfer-web/repository"
	"showfer-web/service/guide"
	"showfer-web/service/logger"
	"showfer-web/service/playout"
	"showfer-web/service/traffic_blocker"
	"showfer-web/utils"
	"strconv"

	"github.com/gin-gonic/gin"
)

type ScheduleApi struct {
	db             *sql.DB
	scheduleRepo   *repository.ScheduleRepository
	guideRepo      *repository.GuideRepository
	fileRepo       *repository.FilesRepository
	guideGenerator *guide.GuideGenerator
	playout        *playout.Playout
}

func NewScheduleApi(db *sql.DB, playout *playout.Playout) *ScheduleApi {
	return &ScheduleApi{
		db:             db,
		scheduleRepo:   repository.NewScheduleRepository(db),
		guideRepo:      repository.NewGuideRepository(db),
		fileRepo:       repository.NewFilesRepository(db),
		playout:        playout,
		guideGenerator: guide.NewGuideGenerator(db, playout),
	}
}

// GetSchedules handles GET /api/v1/schedule
func (api *ScheduleApi) GetSchedules(c *gin.Context) {
	// Parse pagination parameters
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(c.DefaultQuery("limit", "10"))
	if err != nil || limit < 1 || limit > 100 {
		limit = 10
	}

	pagination := models.Pagination{
		Page:  page,
		Limit: limit,
	}

	result, err := api.scheduleRepo.ListSchedules(pagination)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get schedules", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

// CreateSchedule handles POST /api/v1/schedule
func (api *ScheduleApi) CreateSchedule(c *gin.Context) {
	var input models.Schedule
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	id, err := api.scheduleRepo.CreateSchedule(input)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create schedule", "details": err.Error()})
		return
	}

	createdSchedule, err := api.scheduleRepo.FindById(int(id))
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			c.JSON(http.StatusNotFound, gin.H{"error": "Failed to find schedule", "details": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find schedule", "details": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, createdSchedule)
}

// GetSchedule handles GET /api/v1/schedule/:id
func (api *ScheduleApi) GetSchedule(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to parse ID", "details": err.Error()})
		return
	}

	result, err := api.scheduleRepo.FindById(id)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			c.JSON(http.StatusNotFound, gin.H{"error": "Failed to find schedule", "details": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get schedule", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

// UpdateSchedule handles PUT /api/v1/schedule/:id
func (api *ScheduleApi) UpdateSchedule(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to parse ID", "details": err.Error()})
		return
	}

	var input models.Schedule
	if err = c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	schedule, err := api.scheduleRepo.FindById(id)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			c.JSON(http.StatusNotFound, gin.H{"error": "Failed to find schedule", "details": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get schedule", "details": err.Error()})
		return
	}

	err = api.scheduleRepo.UpdateSchedule(input)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update schedule", "details": err.Error()})
		return
	}

	updatedSchedule, err := api.scheduleRepo.FindById(id)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			c.JSON(http.StatusNotFound, gin.H{"error": "Failed to find schedule", "details": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get schedule", "details": err.Error()})
		return
	}

	if schedule.OutputUrl != input.OutputUrl {
		err = api.playout.Restart(updatedSchedule)
		if err != nil {
			logger.Error("Failed to restart playout: %v", err)
		}
	} else {
		err = api.playout.Run(updatedSchedule)
		if err != nil {
			logger.Error("Failed to run playout: %v", err)
		}
	}

	// Refresh traffic blocking if this is a backup server (new schedules should be blocked)
	if utils.GetServerType() == "backup" {
		db := config.GetDB()
		if db != nil {
			trafficBlocker := traffic_blocker.GetTrafficBlocker(db)
			if err := trafficBlocker.RefreshBlocking(); err != nil {
				logger.Error("Failed to refresh traffic blocking after schedule update: %v", err)
			}
		}
	}

	c.JSON(http.StatusOK, updatedSchedule)
}

// DeleteSchedule handles DELETE /api/v1/schedule/:id
func (api *ScheduleApi) DeleteSchedule(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to parse ID", "details": err.Error()})
		return
	}

	schedule, err := api.scheduleRepo.FindById(id)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			c.JSON(http.StatusNotFound, gin.H{"error": "Failed to find schedule", "details": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get schedule", "details": err.Error()})
		return
	}

	err = api.playout.Stop(schedule)
	if err != nil {
		logger.Error("Failed to stop playout: %v", err)
	}

	err = api.scheduleRepo.DeleteSchedule(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete schedule", "details": err.Error()})
		return
	}

	err = api.guideRepo.DeleteGuidee(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete guide", "details": err.Error()})
	}

	c.JSON(http.StatusOK, gin.H{"message": "Schedule deleted successfully"})
}

// FindGuideByScheduleId handles GET /api/v1/schedule/:id/guide
func (api *ScheduleApi) FindGuideByScheduleId(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to parse ID", "details": err.Error()})
		return
	}

	_, err = api.scheduleRepo.FindById(id)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			c.JSON(http.StatusNotFound, gin.H{"error": "Schedule not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find schedule", "details": err.Error()})
		}
		return
	}

	guide, err := api.guideRepo.FindGuideByScheduleID(int64(id))
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			c.JSON(http.StatusOK, models.Guide{})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find guide", "details": err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, guide)
}

// UpdateGuide handles POST /api/v1/schedule/:id/guide
func (api *ScheduleApi) UpdateGuide(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to parse ID", "details": err.Error()})
		return
	}

	schedule, err := api.scheduleRepo.FindById(id)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			c.JSON(http.StatusNotFound, gin.H{"error": "Schedule not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find schedule", "details": err.Error()})
		}
		return
	}

	err = api.guideGenerator.Generate(schedule, true)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate guide", "details": err.Error()})
		return
	}

	updatedGuide, err := api.guideRepo.FindGuideByScheduleID(int64(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find guide", "details": err.Error()})
		return
	}
	c.JSON(http.StatusOK, updatedGuide)
}

// ChangeProgramInGuide handles POST /api/v1/schedule/:id/guide/change-program
func (api *ScheduleApi) ChangeProgramInGuide(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))

	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to parse ID", "details": err.Error()})
		return
	}

	var input models.ChangeProgram
	if err = c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	schedule, err := api.scheduleRepo.FindById(id)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			c.JSON(http.StatusNotFound, gin.H{"error": "Schedule not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find schedule", "details": err.Error()})
		}
		return
	}

	guide, err := api.guideRepo.FindGuideByScheduleID(int64(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find guide", "details": err.Error()})
		return
	}

	guide, err = api.guideGenerator.ReplaceProgramWithNewFile(guide, input.OriginalProgram, input.NewProgram, schedule)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find new file", "details": err.Error()})
		return
	}

	err = api.guideRepo.UpdateGuide(guide)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to change program", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, guide)
}

// FilesToNestedStructure handles GET /api/v1/schedule/files
func (api *ScheduleApi) FilesToNestedStructure(c *gin.Context) {
	durationStr := c.Query("duration")
	var duration *int = nil
	var minDuration int
	var maxDuration int

	if durationStr != "" {
		if d, err := strconv.Atoi(durationStr); err == nil {
			duration = &d
		} else {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid duration"})
			return
		}

	}
	if duration != nil {
		minDuration, maxDuration = api.guideGenerator.GetMaxAndMinFileDuration(*duration)
		result := api.fileRepo.FilesToNestedStructureFilterByDuration(minDuration, maxDuration)
		c.JSON(http.StatusOK, result)
		return
	} else {
		result := api.fileRepo.FilesToNestedStructure()
		c.JSON(http.StatusOK, result)
		return
	}

}

// BatchUpdateFiles handles POST /api/v1/schedule/files
func (api *ScheduleApi) BatchUpdateFiles(c *gin.Context) {
	var input models.UpdatedConvertItems
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	for _, item := range input.Items {
		file, err := api.fileRepo.GetConvertItemById(item.ID)
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				c.JSON(http.StatusNotFound, gin.H{"error": "File not found"})
			} else {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get file", "details": err.Error()})
			}
			return
		}

		file.Name = item.Name
		file.Description = item.Description
		file.Episode = item.Episode

		err = api.fileRepo.UpdateConvertItem(file)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update file", "details": err.Error()})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{"message": "Files updated successfully"})
}

// GetSchedulersStatus handles GET /api/v1/schedule/status
// Returns the RTP sync status for all schedulers
func (api *ScheduleApi) GetSchedulersStatus(c *gin.Context) {
	// Get all schedules
	pagination := models.Pagination{
		Page:  1,
		Limit: 1000, // High limit to get all schedules
	}

	schedules, err := api.scheduleRepo.ListSchedules(pagination)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			c.JSON(http.StatusNotFound, gin.H{"error": "Failed to find schedule", "details": err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get schedules", "details": err.Error()})
		return
	}

	// Create a map to store the status of each scheduler
	schedulerStatuses := make(map[string]models.SchedulerStatus)

	// Get the status for each scheduler from the RTP monitor
	for _, schedule := range schedules.Items {
		// Check if the scheduler is running in the playout service
		isActive := api.playout.IsRunning(schedule.ShortID)

		// Get RTP sync status from the RTP monitor
		rtpSynced := false
		if isActive {
			rtpSynced = api.playout.IsRtpSynced(schedule.ShortID)
		}

		// Create a status object for this scheduler
		schedulerStatuses[schedule.ShortID] = models.SchedulerStatus{
			ID:        schedule.ID,
			ShortID:   schedule.ShortID,
			IsActive:  isActive,
			RtpSynced: rtpSynced,
		}
	}

	c.JSON(http.StatusOK, schedulerStatuses)
}

// GetSchedulesForEPG handles GET /api/v1/schedule/epg
// Returns a list of schedules with their short_ids for EPG generation
func (api *ScheduleApi) GetSchedulesForEPG(c *gin.Context) {
	// Get all schedules with valid short_ids
	pagination := models.Pagination{
		Page:  1,
		Limit: 1000, // High limit to get all schedules
	}

	schedules, err := api.scheduleRepo.ListSchedules(pagination)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get schedules", "details": err.Error()})
		return
	}

	// Filter to include only schedules with non-empty short_ids
	validSchedules := []gin.H{}
	for _, schedule := range schedules.Items {
		if schedule.ShortID != "" {
			validSchedules = append(validSchedules, gin.H{
				"id":       schedule.ID,
				"name":     schedule.Name,
				"short_id": schedule.ShortID,
			})
		}
	}

	c.JSON(http.StatusOK, gin.H{"schedules": validSchedules})
}
