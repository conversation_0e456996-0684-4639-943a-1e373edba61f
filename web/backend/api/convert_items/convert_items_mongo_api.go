package convert_items

import (
	"context"
	"net/http"
	"showfer-web/models"
	"showfer-web/repository"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// ConvertItemsMongoAPI handles convert items API endpoints for MongoDB
type ConvertItemsMongoAPI struct {
	convertItemsRepo *repository.ConvertItemsMongoRepository
	bucketRepo       *repository.BucketRepository
}

// NewConvertItemsMongoAPI creates a new ConvertItemsMongoAPI
func NewConvertItemsMongoAPI(mongoDB *mongo.Database) *ConvertItemsMongoAPI {
	return &ConvertItemsMongoAPI{
		convertItemsRepo: repository.NewConvertItemsMongoRepository(mongoDB),
		bucketRepo:       repository.NewBucketRepository(mongoDB),
	}
}

// GetItemsByBucket handles GET /api/v1/convert-items/bucket/:bucketId
func (api *ConvertItemsMongoAPI) GetItemsByBucket(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	bucketIDParam := c.Param("bucketId")
	bucketID, err := primitive.ObjectIDFromHex(bucketIDParam)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid bucket ID",
		})
		return
	}

	items, err := api.convertItemsRepo.GetItemsByBucket(ctx, bucketID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get convert items",
			"details": err.Error(),
		})
		return
	}

	// Convert to response format
	var responses []models.ConvertItemResponse
	for _, item := range items {
		responses = append(responses, item.ToResponse())
	}

	c.JSON(http.StatusOK, responses)
}

// GetItemsByBucketAndLocation handles GET /api/v1/convert-items/bucket/:bucketId/location
func (api *ConvertItemsMongoAPI) GetItemsByBucketAndLocation(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	bucketIDParam := c.Param("bucketId")
	bucketID, err := primitive.ObjectIDFromHex(bucketIDParam)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid bucket ID",
		})
		return
	}

	location := c.Query("location")
	if location == "" {
		location = "/"
	}

	items, err := api.convertItemsRepo.GetItemsByBucketAndLocation(ctx, bucketID, location)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get convert items",
			"details": err.Error(),
		})
		return
	}

	// Convert to response format
	var responses []models.ConvertItemResponse
	for _, item := range items {
		responses = append(responses, item.ToResponse())
	}

	c.JSON(http.StatusOK, responses)
}

// GetFoldersByBucket handles GET /api/v1/convert-items/bucket/:bucketId/folders
func (api *ConvertItemsMongoAPI) GetFoldersByBucket(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	bucketIDParam := c.Param("bucketId")
	bucketID, err := primitive.ObjectIDFromHex(bucketIDParam)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid bucket ID",
		})
		return
	}

	folders, err := api.convertItemsRepo.GetFoldersByBucket(ctx, bucketID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get folders",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, folders)
}

// GetSubfoldersByBucket handles GET /api/v1/convert-items/bucket/:bucketId/subfolders
func (api *ConvertItemsMongoAPI) GetSubfoldersByBucket(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	bucketIDParam := c.Param("bucketId")
	bucketID, err := primitive.ObjectIDFromHex(bucketIDParam)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid bucket ID",
		})
		return
	}

	location := c.Query("location")
	if location == "" {
		location = "/"
	}

	subfolders, err := api.convertItemsRepo.GetSubfoldersByBucket(ctx, bucketID, location)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get subfolders",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, subfolders)
}

// GetItemByID handles GET /api/v1/convert-items/:id
func (api *ConvertItemsMongoAPI) GetItemByID(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	idParam := c.Param("id")
	id, err := primitive.ObjectIDFromHex(idParam)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid item ID",
		})
		return
	}

	item, err := api.convertItemsRepo.GetItemByID(ctx, id)
	if err != nil {
		if err.Error() == "convert item not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Convert item not found",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to get convert item",
				"details": err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, item.ToResponse())
}

// TestConnection handles GET /api/v1/convert-items/test
func (api *ConvertItemsMongoAPI) TestConnection(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	count, err := api.convertItemsRepo.TestConnection(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to connect to MongoDB convert_items collection",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":        "MongoDB convert_items connection successful",
		"collection":     "convert_items",
		"document_count": count,
		"timestamp":      time.Now(),
	})
}

// GetItemsByBucketName handles GET /api/v1/convert-items/bucket-name/:bucketName
func (api *ConvertItemsMongoAPI) GetItemsByBucketName(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	bucketName := c.Param("bucketName")
	if bucketName == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Bucket name is required",
		})
		return
	}

	// First, get the bucket by name to get its ID
	bucket, err := api.bucketRepo.GetBucketByName(ctx, bucketName)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "Bucket not found",
			"details": err.Error(),
		})
		return
	}

	// Then get items by bucket ID
	items, err := api.convertItemsRepo.GetItemsByBucket(ctx, bucket.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get convert items",
			"details": err.Error(),
		})
		return
	}

	// Convert to response format
	var responses []models.ConvertItemResponse
	for _, item := range items {
		responses = append(responses, item.ToResponse())
	}

	c.JSON(http.StatusOK, responses)
}
