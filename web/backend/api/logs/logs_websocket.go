package logs

import (
	"database/sql"
	"net/http"
	"showfer-web/models"
	"showfer-web/repository"
	"showfer-web/service/logger"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true // Allow all origins for now
	},
}

// LogsWebSocketManager manages WebSocket connections for real-time log streaming
type LogsWebSocketManager struct {
	clients    map[*websocket.Conn]bool
	broadcast  chan models.LogEntry
	register   chan *websocket.Conn
	unregister chan *websocket.Conn
	mutex      sync.RWMutex
}

var wsManager *LogsWebSocketManager

// InitLogsWebSocket initializes the WebSocket manager
func InitLogsWebSocket() {
	wsManager = &LogsWebSocketManager{
		clients:    make(map[*websocket.Conn]bool),
		broadcast:  make(chan models.LogEntry, 256),
		register:   make(chan *websocket.Conn),
		unregister: make(chan *websocket.Conn),
	}

	go wsManager.run()
}

// GetLogsWebSocketManager returns the WebSocket manager instance
func GetLogsWebSocketManager() *LogsWebSocketManager {
	return wsManager
}

// run handles the WebSocket manager's main loop
func (manager *LogsWebSocketManager) run() {
	for {
		select {
		case client := <-manager.register:
			manager.mutex.Lock()
			manager.clients[client] = true
			manager.mutex.Unlock()
			logger.LogDebug("WebSocket client connected for logs streaming")

		case client := <-manager.unregister:
			manager.mutex.Lock()
			if _, ok := manager.clients[client]; ok {
				delete(manager.clients, client)
				client.Close()
			}
			manager.mutex.Unlock()
			logger.LogDebug("WebSocket client disconnected from logs streaming")

		case logEntry := <-manager.broadcast:
			manager.mutex.RLock()
			// Create the message in the expected format
			message := LogStreamMessage{
				Type:      "log_entry",
				Timestamp: time.Now().Unix(),
				LogEntry:  &logEntry,
			}

			for client := range manager.clients {
				select {
				case <-time.After(1 * time.Second):
					// Client is not responding, remove it
					delete(manager.clients, client)
					client.Close()
				default:
					if err := client.WriteJSON(message); err != nil {
						delete(manager.clients, client)
						client.Close()
					}
				}
			}
			manager.mutex.RUnlock()
		}
	}
}

// BroadcastLogEntry broadcasts a log entry to all connected clients
func (manager *LogsWebSocketManager) BroadcastLogEntry(entry models.LogEntry) {
	if manager != nil {
		select {
		case manager.broadcast <- entry:
		default:
			// Channel is full, skip this broadcast
		}
	}
}

// HandleLogsWebSocket handles WebSocket connections for real-time log streaming
func (api *LogsAPI) HandleLogsWebSocket(c *gin.Context) {
	// Get user information from context (set by auth middleware)
	_, _ = c.Get("userID")
	username, _ := c.Get("username")

	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		logger.LogError("Failed to upgrade WebSocket connection: %v", err)
		return
	}

	logger.LogInfo("WebSocket connection established for user: %v", username)

	// Register the new client
	if wsManager != nil {
		wsManager.register <- conn
	}

	// Handle client messages and cleanup
	go func() {
		defer func() {
			if wsManager != nil {
				wsManager.unregister <- conn
			}
			logger.LogInfo("WebSocket connection closed for user: %v", username)
		}()

		// Set read deadline and pong handler for keepalive
		conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		conn.SetPongHandler(func(string) error {
			conn.SetReadDeadline(time.Now().Add(60 * time.Second))
			return nil
		})

		for {
			// Read message from client
			var message map[string]interface{}
			if err := conn.ReadJSON(&message); err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					logger.LogWarn("WebSocket error for user %v: %v", username, err)
				}
				break
			}

			// Handle different message types
			if msgType, ok := message["type"].(string); ok {
				switch msgType {
				case "ping":
					// Respond with pong
					response := LogStreamMessage{
						Type:      "pong",
						Timestamp: time.Now().Unix(),
						Message:   "pong",
					}
					if err := conn.WriteJSON(response); err != nil {
						break
					}

				case "subscribe":
					// Handle subscription with filters
					response := LogStreamMessage{
						Type:      "subscribed",
						Timestamp: time.Now().Unix(),
						Message:   "Successfully subscribed to log stream",
					}
					if err := conn.WriteJSON(response); err != nil {
						break
					}
					logger.LogInfo("User %v subscribed to log stream", username)

				case "unsubscribe":
					// Handle unsubscription
					response := LogStreamMessage{
						Type:      "unsubscribed",
						Timestamp: time.Now().Unix(),
						Message:   "Successfully unsubscribed from log stream",
					}
					if err := conn.WriteJSON(response); err != nil {
						break
					}
					logger.LogInfo("User %v unsubscribed from log stream", username)
				}
			}
		}
	}()

	// Send keepalive pings
	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				if err := conn.WriteMessage(websocket.PingMessage, nil); err != nil {
					return
				}
			}
		}
	}()
}

// Enhanced logger hook to broadcast new log entries
type WebSocketLogHook struct {
	manager *LogsWebSocketManager
}

// NewWebSocketLogHook creates a new WebSocket log hook
func NewWebSocketLogHook(manager *LogsWebSocketManager) *WebSocketLogHook {
	return &WebSocketLogHook{manager: manager}
}

// Fire is called when a log entry is created
func (hook *WebSocketLogHook) Fire(entry models.LogEntry) {
	if hook.manager != nil {
		hook.manager.BroadcastLogEntry(entry)
	}
}

// Enhanced repository wrapper that broadcasts log entries
type BroadcastingLogsRepository struct {
	*repository.LogsRepository
	hook *WebSocketLogHook
}

// NewBroadcastingLogsRepository creates a new broadcasting logs repository
func NewBroadcastingLogsRepository(db *sql.DB, manager *LogsWebSocketManager) *BroadcastingLogsRepository {
	return &BroadcastingLogsRepository{
		LogsRepository: repository.NewLogsRepository(db),
		hook:           NewWebSocketLogHook(manager),
	}
}

// CreateLogEntry creates a log entry and broadcasts it
func (r *BroadcastingLogsRepository) CreateLogEntry(entry *models.LogEntry) error {
	// Create the log entry in the database
	err := r.LogsRepository.CreateLogEntry(entry)
	if err != nil {
		return err
	}

	// Broadcast the log entry to WebSocket clients
	if r.hook != nil {
		r.hook.Fire(*entry)
	}

	return nil
}

// LogStreamMessage represents a message sent over the WebSocket for log streaming
type LogStreamMessage struct {
	Type      string           `json:"type"`
	Timestamp int64            `json:"timestamp"`
	LogEntry  *models.LogEntry `json:"log_entry,omitempty"`
	Message   string           `json:"message,omitempty"`
	Error     string           `json:"error,omitempty"`
}

// SendLogStreamMessage sends a formatted message to a WebSocket connection
func SendLogStreamMessage(conn *websocket.Conn, msgType string, logEntry *models.LogEntry, message, errorMsg string) error {
	msg := LogStreamMessage{
		Type:      msgType,
		Timestamp: time.Now().Unix(),
		LogEntry:  logEntry,
		Message:   message,
		Error:     errorMsg,
	}
	return conn.WriteJSON(msg)
}

// GetConnectedClientsCount returns the number of connected WebSocket clients
func (manager *LogsWebSocketManager) GetConnectedClientsCount() int {
	manager.mutex.RLock()
	defer manager.mutex.RUnlock()
	return len(manager.clients)
}

// CloseAllConnections closes all WebSocket connections
func (manager *LogsWebSocketManager) CloseAllConnections() {
	manager.mutex.Lock()
	defer manager.mutex.Unlock()

	for client := range manager.clients {
		client.Close()
		delete(manager.clients, client)
	}
}
