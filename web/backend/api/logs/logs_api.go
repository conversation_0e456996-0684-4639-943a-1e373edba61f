package logs

import (
	"database/sql"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"showfer-web/models"
	"showfer-web/repository"
	"showfer-web/service/logger"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// LogsAPI handles logs-related API endpoints
type LogsAPI struct {
	logsRepo *repository.LogsRepository
}

// NewLogsAPI creates a new LogsAPI
func NewLogsAPI(db *sql.DB) *LogsAPI {
	return &LogsAPI{
		logsRepo: repository.NewLogsRepository(db),
	}
}

// GetLogs handles GET /api/v1/logs
func (api *LogsAPI) GetLogs(c *gin.Context) {
	var filter models.LogFilter
	
	// Parse query parameters
	if err := c.ShouldBindQuery(&filter); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.<PERSON>r()})
		return
	}

	// Set default pagination if not provided
	if filter.Limit <= 0 {
		filter.Limit = 50
	}
	if filter.Limit > 1000 {
		filter.Limit = 1000 // Max limit
	}

	// Get logs from repository
	logs, err := api.logsRepo.GetLogs(filter)
	if err != nil {
		logger.ErrorWithContext(&logger.LogContext{
			Source: "logs_api",
			Metadata: map[string]interface{}{
				"error": err.Error(),
				"filter": filter,
			},
		}, "Failed to get logs: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve logs"})
		return
	}

	// Get total count for pagination
	totalCount, err := api.logsRepo.CountLogs(filter)
	if err != nil {
		logger.LogWarn("Failed to get logs count: %v", err)
		totalCount = 0
	}

	c.JSON(http.StatusOK, gin.H{
		"logs":        logs,
		"total_count": totalCount,
		"limit":       filter.Limit,
		"offset":      filter.Offset,
	})
}

// GetLogStats handles GET /api/v1/logs/stats
func (api *LogsAPI) GetLogStats(c *gin.Context) {
	var filter models.LogFilter
	
	// Parse query parameters for filtering stats
	if err := c.ShouldBindQuery(&filter); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	stats, err := api.logsRepo.GetLogStats(filter)
	if err != nil {
		logger.ErrorWithContext(&logger.LogContext{
			Source: "logs_api",
			Metadata: map[string]interface{}{
				"error": err.Error(),
			},
		}, "Failed to get log stats: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve log statistics"})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// GetLogSources handles GET /api/v1/logs/sources
func (api *LogsAPI) GetLogSources(c *gin.Context) {
	sources, err := api.logsRepo.GetLogSources()
	if err != nil {
		logger.ErrorWithContext(&logger.LogContext{
			Source: "logs_api",
			Metadata: map[string]interface{}{
				"error": err.Error(),
			},
		}, "Failed to get log sources: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve log sources"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"sources": sources})
}

// ExportLogs handles POST /api/v1/logs/export
func (api *LogsAPI) ExportLogs(c *gin.Context) {
	var request models.LogExportRequest
	
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// Validate format
	if request.Format != "csv" && request.Format != "json" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid format. Supported formats: csv, json"})
		return
	}

	// Set reasonable limits for export
	if request.Filter.Limit <= 0 || request.Filter.Limit > 10000 {
		request.Filter.Limit = 10000
	}

	// Get logs
	logs, err := api.logsRepo.GetLogs(request.Filter)
	if err != nil {
		logger.ErrorWithContext(&logger.LogContext{
			Source: "logs_api",
			Metadata: map[string]interface{}{
				"error": err.Error(),
				"format": request.Format,
			},
		}, "Failed to get logs for export: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve logs for export"})
		return
	}

	// Generate unique filename
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("logs_export_%s.%s", timestamp, request.Format)
	
	// Create exports directory if it doesn't exist
	exportsDir := "./data/exports"
	if err := os.MkdirAll(exportsDir, 0755); err != nil {
		logger.LogError("Failed to create exports directory: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create export file"})
		return
	}

	filePath := filepath.Join(exportsDir, filename)

	// Export based on format
	switch request.Format {
	case "csv":
		err = api.exportToCSV(logs, filePath)
	case "json":
		err = api.exportToJSON(logs, filePath)
	}

	if err != nil {
		logger.ErrorWithContext(&logger.LogContext{
			Source: "logs_api",
			Metadata: map[string]interface{}{
				"error": err.Error(),
				"format": request.Format,
				"file_path": filePath,
			},
		}, "Failed to export logs: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to export logs"})
		return
	}

	// Return download URL
	downloadURL := fmt.Sprintf("/data/exports/%s", filename)
	expiresAt := time.Now().Add(24 * time.Hour).Format(time.RFC3339)

	logger.LogUserAction(getUserIDFromContext(c), "export_logs", map[string]interface{}{
		"format": request.Format,
		"count": len(logs),
		"filename": filename,
	})

	c.JSON(http.StatusOK, models.LogExportResponse{
		DownloadURL: downloadURL,
		ExpiresAt:   expiresAt,
	})
}

// exportToCSV exports logs to CSV format
func (api *LogsAPI) exportToCSV(logs []models.LogEntry, filePath string) error {
	file, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	// Write header
	header := []string{"ID", "Timestamp", "Level", "Message", "Source", "User ID", "Request ID", "Metadata"}
	if err := writer.Write(header); err != nil {
		return err
	}

	// Write data
	for _, log := range logs {
		record := []string{
			strconv.FormatInt(log.ID, 10),
			log.Timestamp.Format(time.RFC3339),
			string(log.Level),
			log.Message,
			log.Source,
			formatInt64Pointer(log.UserID),
			formatStringPointer(log.RequestID),
			formatStringPointer(log.Metadata),
		}
		if err := writer.Write(record); err != nil {
			return err
		}
	}

	return nil
}

// exportToJSON exports logs to JSON format
func (api *LogsAPI) exportToJSON(logs []models.LogEntry, filePath string) error {
	file, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")
	
	exportData := map[string]interface{}{
		"exported_at": time.Now().Format(time.RFC3339),
		"count":       len(logs),
		"logs":        logs,
	}

	return encoder.Encode(exportData)
}

// Helper functions

func formatInt64Pointer(ptr *int64) string {
	if ptr == nil {
		return ""
	}
	return strconv.FormatInt(*ptr, 10)
}

func formatStringPointer(ptr *string) string {
	if ptr == nil {
		return ""
	}
	return *ptr
}

func getUserIDFromContext(c *gin.Context) int64 {
	// Try to get user ID from JWT token or session
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(int64); ok {
			return id
		}
	}
	return 0 // Default to 0 if no user context
}

// CleanupOldLogs handles DELETE /api/v1/logs/cleanup
func (api *LogsAPI) CleanupOldLogs(c *gin.Context) {
	// Parse retention period from query parameter (default: 30 days)
	retentionDaysStr := c.DefaultQuery("retention_days", "30")
	retentionDays, err := strconv.Atoi(retentionDaysStr)
	if err != nil || retentionDays < 1 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid retention_days parameter"})
		return
	}

	retentionDuration := time.Duration(retentionDays) * 24 * time.Hour
	deletedCount, err := api.logsRepo.DeleteOldLogs(retentionDuration)
	if err != nil {
		logger.ErrorWithContext(&logger.LogContext{
			Source: "logs_api",
			Metadata: map[string]interface{}{
				"error": err.Error(),
				"retention_days": retentionDays,
			},
		}, "Failed to cleanup old logs: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to cleanup old logs"})
		return
	}

	logger.LogSystemEvent("logs_cleanup", map[string]interface{}{
		"deleted_count": deletedCount,
		"retention_days": retentionDays,
	})

	c.JSON(http.StatusOK, gin.H{
		"message":       "Old logs cleaned up successfully",
		"deleted_count": deletedCount,
		"retention_days": retentionDays,
	})
}
