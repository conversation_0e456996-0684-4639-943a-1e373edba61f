package recorder

import (
	"database/sql"
	"fmt"
	"net/http"
	"path/filepath"
	"showfer-web/models"
	"showfer-web/repository"
	"showfer-web/service/detector"
	"showfer-web/service/logger"
	"showfer-web/service/recorder"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// RecorderAPI handles recorder-related API endpoints
type RecorderAPI struct {
	db           *sql.DB
	recorderRepo *repository.RecorderRepository
	networkRepo  *repository.NetworkRepository
}

// NewRecorderAPI creates a new RecorderAPI
func NewRecorderAPI(db *sql.DB) *RecorderAPI {
	return &RecorderAPI{
		db:           db,
		recorderRepo: repository.NewRecorderRepository(db),
		networkRepo:  repository.NewNetworkRepository(db),
	}
}

// GetRecorders handles GET /api/v1/recorders
func (api *RecorderAPI) GetRecorders(c *gin.Context) {
	// Parse pagination parameters
	page, err := strconv.Atoi(c.<PERSON>("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(c.<PERSON>("limit", "10"))
	if err != nil || limit < 1 || limit > 100 {
		limit = 10
	}

	pagination := models.Pagination{
		Page:  page,
		Limit: limit,
	}

	// Get recorders with pagination
	result, err := api.recorderRepo.GetRecorders(pagination)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get recorders", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetRecorder handles GET /api/v1/recorders/:id
func (api *RecorderAPI) GetRecorder(c *gin.Context) {
	// Parse recorder ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid recorder ID"})
		return
	}

	// Get recorder by ID
	recorder, err := api.recorderRepo.GetRecorderByID(id)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": "Recorder not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get recorder", "details": err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, recorder)
}

// CreateRecorder handles POST /api/v1/recorders
func (api *RecorderAPI) CreateRecorder(c *gin.Context) {
	// Parse request body
	var input models.RecorderInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// Create recorder
	recorder := models.Recorder{
		Name:               input.Name,
		Input:              input.Input,
		Duration:           input.Duration,
		Status:             "stopped",
		VCodec:             input.VCodec,
		ACodec:             input.ACodec,
		Resolution:         input.Resolution,
		FPS:                input.FPS,
		SampleRate:         input.SampleRate,
		VBitrate:           input.VBitrate,
		ABitrate:           input.ABitrate,
		MaxVBitrate:        input.MaxVBitrate,
		NetworkInterface:   input.NetworkInterface,
		SourceIP:           input.SourceIP,
		IsScheduled:        input.IsScheduled,
		ScheduledStartTime: input.ScheduledStartTime,
	}

	id, err := api.recorderRepo.CreateRecorder(recorder)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create recorder", "details": err.Error()})
		return
	}

	// Get the created recorder
	createdRecorder, err := api.recorderRepo.GetRecorderByID(int(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get created recorder", "details": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, createdRecorder)
}

// UpdateRecorder handles PUT /api/v1/recorders/:id
func (api *RecorderAPI) UpdateRecorder(c *gin.Context) {
	// Parse recorder ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid recorder ID"})
		return
	}

	// Parse request body
	var input models.RecorderInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// Get existing recorder
	existingRecorder, err := api.recorderRepo.GetRecorderByID(id)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": "Recorder not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get recorder", "details": err.Error()})
		}
		return
	}

	// Check if recorder is running, recording, or transcoding
	if existingRecorder.Status == "running" || existingRecorder.Status == "recording" || existingRecorder.Status == "transcoding" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot update a recorder that is currently active"})
		return
	}

	// Update recorder
	updatedRecorder := models.Recorder{
		ID:                 id,
		Name:               input.Name,
		Input:              input.Input,
		RtpUrlID:           existingRecorder.RtpUrlID,
		Duration:           input.Duration,
		Status:             "stopped",
		VCodec:             input.VCodec,
		ACodec:             input.ACodec,
		Resolution:         input.Resolution,
		FPS:                input.FPS,
		SampleRate:         input.SampleRate,
		VBitrate:           input.VBitrate,
		ABitrate:           input.ABitrate,
		MaxVBitrate:        input.MaxVBitrate,
		NetworkInterface:   input.NetworkInterface,
		SourceIP:           input.SourceIP,
		IsScheduled:        input.IsScheduled,
		ScheduledStartTime: input.ScheduledStartTime,
	}

	err = api.recorderRepo.UpdateRecorder(updatedRecorder)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update recorder", "details": err.Error()})
		return
	}

	// Get the updated recorder
	updatedRecorder, err = api.recorderRepo.GetRecorderByID(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get updated recorder", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, updatedRecorder)
}

// DeleteRecorder handles DELETE /api/v1/recorders/:id
func (api *RecorderAPI) DeleteRecorder(c *gin.Context) {
	// Parse recorder ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid recorder ID"})
		return
	}

	// Get existing recorder
	existingRecorder, err := api.recorderRepo.GetRecorderByID(id)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": "Recorder not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get recorder", "details": err.Error()})
		}
		return
	}

	// Check if recorder is running, recording, or transcoding
	if existingRecorder.Status == "running" || existingRecorder.Status == "recording" || existingRecorder.Status == "transcoding" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot delete a recorder that is currently active"})
		return
	}

	// Delete recorder
	err = api.recorderRepo.DeleteRecorder(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete recorder", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Recorder deleted successfully"})
}

// StartRecorder handles POST /api/v1/recorders/:id/start
func (api *RecorderAPI) StartRecorder(c *gin.Context) {
	// Parse recorder ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid recorder ID"})
		return
	}

	// Parse request body for service ID and source IP
	var input struct {
		ServiceID int    `json:"service_id"`
		SourceIP  string `json:"source_ip"`
	}

	if err := c.ShouldBindJSON(&input); err != nil {
		// If there's no body, that's fine - we'll use service ID 0 (all services) and no source IP
		input.ServiceID = 0
		input.SourceIP = ""
	}

	// Get existing recorder
	existingRecorder, err := api.recorderRepo.GetRecorderByID(id)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": "Recorder not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get recorder", "details": err.Error()})
		}
		return
	}

	// Check if recorder is already running, recording, or transcoding
	if existingRecorder.Status == "running" || existingRecorder.Status == "recording" || existingRecorder.Status == "transcoding" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Recorder is already active"})
		return
	}

	// Get the recordings directory
	recordingsDir := filepath.Join("./data", "recordings")

	// Parse duration string to seconds
	var durationSeconds int

	// Check if the duration is in HH:MM:SS format
	if strings.Contains(existingRecorder.Duration, ":") {
		durationParts := strings.Split(existingRecorder.Duration, ":")
		if len(durationParts) != 3 {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid duration format, expected HH:MM:SS"})
			return
		}

		hours, err := strconv.Atoi(durationParts[0])
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid hours in duration"})
			return
		}

		minutes, err := strconv.Atoi(durationParts[1])
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid minutes in duration"})
			return
		}

		seconds, err := strconv.Atoi(durationParts[2])
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid seconds in duration"})
			return
		}

		durationSeconds = hours*3600 + minutes*60 + seconds
	} else {
		// Try to parse as a direct number of seconds
		seconds, err := strconv.Atoi(existingRecorder.Duration)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid duration format, expected either HH:MM:SS or number of seconds"})
			return
		}
		durationSeconds = seconds
	}

	// Update the status in the database first
	err = api.recorderRepo.StartRecorder(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update recorder status in database", "details": err.Error()})
		return
	}

	// Get the status manager to update the status to "recording" (instead of "running")
	statusManager := recorder.GetStatusManager()
	if statusManager != nil {
		err = statusManager.UpdateRecorderStatus(id, "recording")
		if err != nil {
			logger.Log("Failed to update recorder status to 'recording': %v", err)
			// Continue anyway, not a critical error
		}
	}

	// Prepare the RTP URL with network interface if specified
	rtpURL := existingRecorder.Input
	if existingRecorder.NetworkInterface != "" {
		// Check if the URL already has query parameters
		if strings.Contains(rtpURL, "?") {
			rtpURL += "&iface=" + existingRecorder.NetworkInterface
		} else {
			rtpURL += "?iface=" + existingRecorder.NetworkInterface
		}
		logger.Log("Using network interface %s for recorder %d", existingRecorder.NetworkInterface, id)
	}

	// Start the recording process
	err = recorder.StartRecording(
		existingRecorder.ID,
		rtpURL,
		recordingsDir,
		durationSeconds,
		existingRecorder.VCodec,
		existingRecorder.ACodec,
		existingRecorder.Resolution,
		fmt.Sprintf("%.2f", existingRecorder.FPS),
		existingRecorder.SampleRate,
		existingRecorder.VBitrate,
		existingRecorder.ABitrate,
		existingRecorder.MaxVBitrate,
		input.ServiceID, // Pass the service ID
		input.SourceIP,  // Pass the source IP from request
	)

	if err != nil {
		// If starting the recording fails, revert the database status
		_ = api.recorderRepo.StopRecorder(id)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to start recording", "details": err.Error()})
		return
	}

	// Update the recorder with the selected service_id and source_ip
	err = api.recorderRepo.UpdateRecorderRecordingParams(id, input.ServiceID, input.SourceIP)
	if err != nil {
		logger.Error("Failed to update recorder recording params: %v", err)
		// Continue anyway as this is not critical for recording functionality
	}

	c.JSON(http.StatusOK, gin.H{"message": "Recorder started successfully"})
}

// StopRecorder handles POST /api/v1/recorders/:id/stop
func (api *RecorderAPI) StopRecorder(c *gin.Context) {
	// Parse recorder ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid recorder ID"})
		return
	}

	// Get existing recorder
	existingRecorder, err := api.recorderRepo.GetRecorderByID(id)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": "Recorder not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get recorder", "details": err.Error()})
		}
		return
	}

	// Check if recorder is running, recording, or transcoding
	if existingRecorder.Status != "running" && existingRecorder.Status != "recording" && existingRecorder.Status != "transcoding" {
		c.JSON(http.StatusOK, gin.H{"message": "Recorder is not active"})
		return
	}

	// Update the database status first
	err = api.recorderRepo.StopRecorder(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update recorder status in database", "details": err.Error()})
		return
	}

	// Try to stop the recording process, but don't fail if it's already stopped
	err = recorder.StopRecording(id)
	if err != nil {
		// Check if the error is because the recorder is not active
		if strings.Contains(err.Error(), "no active recorder") {
			// This is fine - the recorder process has already stopped naturally
			logger.Log("Recorder %d is already stopped", id)
		} else {
			// Log other errors but don't fail the operation since we've already updated the database
			logger.Log("Failed to stop recording process: %v", err)
		}
	}

	c.JSON(http.StatusOK, gin.H{"message": "Recorder stopped successfully"})
}

// JoinRecorder handles POST /api/v1/recorders/:id/join
func (api *RecorderAPI) JoinRecorder(c *gin.Context) {
	// Parse recorder ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid recorder ID"})
		return
	}

	// Get existing recorder
	existingRecorder, err := api.recorderRepo.GetRecorderByID(id)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": "Recorder not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get recorder", "details": err.Error()})
		}
		return
	}

	// Check if recorder is already running, recording, or transcoding
	if existingRecorder.Status == "running" || existingRecorder.Status == "recording" || existingRecorder.Status == "transcoding" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot join a recorder that is currently active"})
		return
	}

	// Get the recordings directory
	recordingsDir := filepath.Join("./data", "recordings")

	// Prepare the RTP URL with network interface if specified
	rtpURL := existingRecorder.Input
	if existingRecorder.NetworkInterface != "" {
		// Check if the URL already has query parameters
		if strings.Contains(rtpURL, "?") {
			rtpURL += "&iface=" + existingRecorder.NetworkInterface
		} else {
			rtpURL += "?iface=" + existingRecorder.NetworkInterface
		}
		logger.Log("Using network interface %s for joining recorder %d", existingRecorder.NetworkInterface, id)
	}

	// Join the stream
	err = recorder.JoinRecorder(
		existingRecorder.ID,
		rtpURL,
		recordingsDir,
		existingRecorder.VCodec,
		existingRecorder.ACodec,
		existingRecorder.Resolution,
		fmt.Sprintf("%.2f", existingRecorder.FPS),
		existingRecorder.SampleRate,
		existingRecorder.VBitrate,
		existingRecorder.ABitrate,
		existingRecorder.MaxVBitrate,
	)

	if err != nil {
		// If the error is because the recorder is already running, return a specific error
		if strings.Contains(err.Error(), "already running") {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot join a recorder that is currently active"})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to join stream", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Stream joined successfully"})
}

// UnjoinRecorder handles POST /api/v1/recorders/:id/unjoin
func (api *RecorderAPI) UnjoinRecorder(c *gin.Context) {
	// Parse recorder ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid recorder ID"})
		return
	}

	// Get existing recorder
	existingRecorder, err := api.recorderRepo.GetRecorderByID(id)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": "Recorder not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get recorder", "details": err.Error()})
		}
		return
	}

	// Check if recorder is running, recording, or transcoding
	if existingRecorder.Status == "running" || existingRecorder.Status == "recording" || existingRecorder.Status == "transcoding" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot unjoin a recorder that is currently active"})
		return
	}

	// Unjoin the stream
	err = recorder.UnjoinRecorder(id)
	if err != nil {
		// If the error is because the recorder is not joined, return success
		if strings.Contains(err.Error(), "no joined recorder") {
			c.JSON(http.StatusOK, gin.H{"message": "Recorder is not joined"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to unjoin stream", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Stream unjoined successfully"})
}

// GetRecorderStatus handles GET /api/v1/recorders/status
func (api *RecorderAPI) GetRecorderStatus(c *gin.Context) {
	// Get recorder status
	status := recorder.GetRecorderStatus()
	c.JSON(http.StatusOK, status)
}

// FailRecorder handles POST /api/v1/recorders/:id/fail
func (api *RecorderAPI) FailRecorder(c *gin.Context) {
	// Parse recorder ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid recorder ID"})
		return
	}

	// Get existing recorder
	existingRecorder, err := api.recorderRepo.GetRecorderByID(id)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": "Recorder not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get recorder", "details": err.Error()})
		}
		return
	}

	// First, try to stop the recording process if it's running
	if existingRecorder.Status == "running" {
		// Try to stop the recording process, but don't fail if it's already stopped
		err = recorder.StopRecording(id)
		if err != nil {
			// Check if the error is because the recorder is not active
			if strings.Contains(err.Error(), "no active recorder") {
				// This is fine - the recorder process has already stopped naturally
				logger.Log("Recorder %d is already stopped", id)
			} else {
				// Log other errors but don't fail the operation
				logger.Log("Failed to stop recording process: %v", err)
			}
		}
	}

	// Now update the status to failed in the database
	err = api.recorderRepo.FailRecorder(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update recorder status in database", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Recorder marked as failed successfully"})
}

// GetRtpUrls handles GET /api/v1/rtp-urls
func (api *RecorderAPI) GetRtpUrls(c *gin.Context) {
	// Get all RTP URLs
	rtpUrls, err := api.recorderRepo.GetAllRtpUrls()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get RTP URLs", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, rtpUrls)
}

// GetNetworkInterfaces handles GET /api/v1/network/interfaces
func (api *RecorderAPI) GetNetworkInterfaces(c *gin.Context) {
	// Get all network interfaces
	interfaces, err := api.networkRepo.GetNetworkInterfaces()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get network interfaces", "details": err.Error()})
		return
	}

	// Filter to only include active interfaces with valid IP addresses (for operational use)
	activeInterfaces := []models.NetworkInterface{}
	for _, iface := range interfaces {
		if iface.IsActive && iface.IPAddress != "" {
			activeInterfaces = append(activeInterfaces, iface)
		}
	}

	c.JSON(http.StatusOK, activeInterfaces)
}

// GetRecorderServices handles GET /api/v1/recorders/:id/services
func (api *RecorderAPI) GetRecorderServices(c *gin.Context) {
	// Parse recorder ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid recorder ID"})
		return
	}

	// Check if recorder exists
	_, err = api.recorderRepo.GetRecorderByID(id)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": "Recorder not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get recorder", "details": err.Error()})
		}
		return
	}

	// Get recorder status to check if it's joined and get services
	statuses := recorder.GetRecorderStatus()
	var recorderStatus *models.RecorderStatus
	for i, status := range statuses {
		if status.ID == id {
			recorderStatus = &statuses[i]
			break
		}
	}

	if recorderStatus == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Recorder status not found"})
		return
	}

	if !recorderStatus.IsJoined {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Recorder is not joined. Join the recorder first to detect services."})
		return
	}

	// Return the services and stream information
	c.JSON(http.StatusOK, gin.H{
		"services":   recorderStatus.Services,
		"ts_sync":    recorderStatus.TsSync,
		"video_info": recorderStatus.VideoInfo,
		"audio_info": recorderStatus.AudioInfo,
	})
}

// ValidateRtpURL handles POST /api/v1/rtp-urls/validate
func (api *RecorderAPI) ValidateRtpURL(c *gin.Context) {
	// Parse request body
	var input struct {
		URL string `json:"url" binding:"required"`
	}

	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// Validate the RTP URL format
	if !strings.HasPrefix(input.URL, "rtp://") {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid RTP URL format. URL must start with rtp://"})
		return
	}

	// Try to parse the RTP URL to validate its format
	_, err := recorder.ParseRtpURL(input.URL)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid RTP URL format", "details": err.Error()})
		return
	}

	// Check if the RTP URL is working
	isWorking := recorder.CheckRtpUrlIsWorking(input.URL)

	// If the URL is working, try to detect services
	var services []models.ServiceInfo
	var videoInfo models.VideoInfo
	var audioInfo models.AudioInfo

	if isWorking {
		// Import detector package and try to detect services
		options := detector.DefaultDetectionOptions()
		options.Timeout = 5 // Shorter timeout for validation

		// Extract network interface from URL if present
		if strings.Contains(input.URL, "iface=") {
			parts := strings.Split(input.URL, "iface=")
			if len(parts) > 1 {
				ifacePart := strings.Split(parts[1], "&")[0]
				options.Interface = ifacePart
			}
		}

		streamInfo, err := detector.DetectServices(input.URL, options)
		if err == nil && streamInfo.TsSync && len(streamInfo.Services) > 0 {
			services = streamInfo.Services
			if len(streamInfo.VideoInfo) > 0 {
				videoInfo = streamInfo.VideoInfo[0]
			}
			if len(streamInfo.AudioInfo) > 0 {
				audioInfo = streamInfo.AudioInfo[0]
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"url":        input.URL,
		"is_working": isWorking,
		"services":   services,
		"video_info": videoInfo,
		"audio_info": audioInfo,
	})
}

// GetRtpSenders handles POST /api/v1/rtp-senders
func (api *RecorderAPI) GetRtpSenders(c *gin.Context) {
	// Parse request body
	var input struct {
		MulticastAddr    string `json:"multicast_addr" binding:"required"`
		Port             int    `json:"port" binding:"required"`
		NetworkInterface string `json:"network_interface"` // Optional network interface
	}

	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// Get RTP senders for the multicast address with network interface
	var senders []*detector.RtpSender
	var err error
	
	if input.NetworkInterface != "" {
		senders, err = detector.GetRtpSendersWithInterface(input.MulticastAddr, input.Port, input.NetworkInterface)
	} else {
		senders, err = detector.GetRtpSenders(input.MulticastAddr, input.Port)
	}
	
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get RTP senders", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"multicast_addr":    input.MulticastAddr,
		"port":              input.Port,
		"network_interface": input.NetworkInterface,
		"senders":           senders,
	})
}
