package api

import (
	"database/sql"
	"log"
	"showfer-web/api/admin"
	"showfer-web/api/auth"
	"showfer-web/api/files"
	"showfer-web/api/logs"
	"showfer-web/api/recorder"
	"showfer-web/api/schedule"
	"showfer-web/api/system"
	"showfer-web/api/websocket"
	"showfer-web/middleware"
	"showfer-web/repository"
	"showfer-web/service/playout"
	"showfer-web/service/retranscoder"
	"showfer-web/service/storage"

	"github.com/gin-gonic/gin"
)

// SetupRoutes initializes all API routes
func SetupRoutes(
	router *gin.Engine,
	db *sql.DB,
	streamerPlayout *playout.Playout,
	retranscoderService *retranscoder.RetranscoderService,
	storageService *storage.BackblazeService,
) {
	// Get base directory for file storage
	baseDir := "./data"

	// Initialize APIs
	filesAPI := files.NewFilesAPI(db, baseDir, storageService)
	fileStatusAPI := files.NewFileStatusAPI(repository.NewFilesRepository(db))

	// API v1 group
	v1 := router.Group("/api/v1")
	{
		// Add Backblaze specific routes first
		if storageService != nil {
			log.Printf("Registering Backblaze routes, storage service is initialized")
			v1.GET("/buckets", filesAPI.ListBuckets)
		} else {
			log.Printf("Warning: Backblaze storage service is not initialized, skipping bucket routes")
		}

		// Files routes - combine both authenticated and unauthenticated routes
		filesRoutes := v1.Group("/files")
		{
			// Public file routes
			filesRoutes.GET("", filesAPI.GetFiles)
			filesRoutes.GET("/location/:location", filesAPI.GetFilesByLocation)
			filesRoutes.GET("/folders/:location", filesAPI.GetFoldersByLocation)
			filesRoutes.POST("/upload", filesAPI.UploadFiles)
			filesRoutes.DELETE("/:id", filesAPI.DeleteFile)
			filesRoutes.PUT("/:id", filesAPI.UpdateFile)
			filesRoutes.PUT("/:id/rename", filesAPI.RenameFile)
			filesRoutes.GET("/:id/download", filesAPI.DownloadFile)
			filesRoutes.POST("/move", filesAPI.MoveFiles)
			filesRoutes.GET("/:id", filesAPI.GetFileInfoById)

			// Protected file routes
			authFiles := filesRoutes.Group("")
			authFiles.Use(middleware.AuthMiddleware())
			{
				authFiles.GET("/recorder/:id", fileStatusAPI.GetFilesByRecorderID)
			}
		}

		// Auth routes
		authAPI := auth.NewAuthAPI(db)
		authRoutes := v1.Group("/auth")
		{
			authRoutes.POST("/login", authAPI.Login)
			authRoutes.POST("/register", authAPI.Register)
		}

		// User routes (protected by auth middleware)
		userRoutes := v1.Group("/users")
		userRoutes.Use(middleware.AuthMiddleware())
		{
			userRoutes.GET("", middleware.RoleMiddleware("admin"), authAPI.GetUsers)
			userRoutes.GET("/me", authAPI.GetCurrentUser)
			userRoutes.GET("/:id", middleware.RoleMiddleware("admin"), authAPI.GetUser)
			userRoutes.POST("", middleware.RoleMiddleware("admin"), authAPI.CreateUser)
			userRoutes.PUT("/:id", middleware.RoleMiddleware("admin"), authAPI.UpdateUser)
			userRoutes.DELETE("/:id", middleware.RoleMiddleware("admin"), authAPI.DeleteUser)
		}

		// Recorder routes (protected by auth middleware)
		recorderAPI := recorder.NewRecorderAPI(db)
		recorderRoutes := v1.Group("/recorders")
		recorderRoutes.Use(middleware.AuthMiddleware())
		{
			recorderRoutes.GET("", recorderAPI.GetRecorders)
			recorderRoutes.GET("/:id", recorderAPI.GetRecorder)
			recorderRoutes.POST("", recorderAPI.CreateRecorder)
			recorderRoutes.PUT("/:id", recorderAPI.UpdateRecorder)
			recorderRoutes.DELETE("/:id", recorderAPI.DeleteRecorder)
			recorderRoutes.POST("/:id/start", recorderAPI.StartRecorder)
			recorderRoutes.POST("/:id/stop", recorderAPI.StopRecorder)
			recorderRoutes.POST("/:id/fail", recorderAPI.FailRecorder)
			recorderRoutes.POST("/:id/join", recorderAPI.JoinRecorder)
			recorderRoutes.POST("/:id/unjoin", recorderAPI.UnjoinRecorder)
			recorderRoutes.GET("/status", recorderAPI.GetRecorderStatus)
			recorderRoutes.GET("/:id/services", recorderAPI.GetRecorderServices) // New endpoint for getting services
		}

		// RTP URL routes (protected by auth middleware)
		rtpRoutes := v1.Group("/rtp-urls")
		rtpRoutes.Use(middleware.AuthMiddleware())
		{
			rtpRoutes.GET("", recorderAPI.GetRtpUrls)
			rtpRoutes.POST("/validate", recorderAPI.ValidateRtpURL)
		}

		// RTP Senders routes (protected by auth middleware)
		rtpSendersRoutes := v1.Group("/rtp-senders")
		rtpSendersRoutes.Use(middleware.AuthMiddleware())
		{
			rtpSendersRoutes.POST("", recorderAPI.GetRtpSenders)
		}

		// Network interface routes for recorder (protected by auth middleware)
		networkRoutes := v1.Group("/network")
		networkRoutes.Use(middleware.AuthMiddleware())
		{
			networkRoutes.GET("/interfaces", recorderAPI.GetNetworkInterfaces)
		}

		// System monitoring routes (protected by auth middleware)
		systemAPI := system.NewSystemAPI()
		systemRoutes := v1.Group("/system")
		systemRoutes.Use(middleware.AuthMiddleware())
		{
			systemRoutes.GET("/stats", systemAPI.GetSystemStats)
			systemRoutes.GET("/thresholds", systemAPI.GetAlarmThresholds)
			systemRoutes.PUT("/thresholds", middleware.RoleMiddleware("admin"), systemAPI.UpdateAlarmThresholds)
		}

		// Admin routes (protected by auth middleware and admin role)
		adminAPI := admin.NewAdminAPI(db)
		// Initialize retranscode API (memory-based)
		retranscodeAPI := admin.NewRetranscodeAPI(retranscoderService)
		adminRoutes := v1.Group("/admin")
		adminRoutes.Use(middleware.AuthMiddleware(), middleware.RoleMiddleware("admin"))
		{
			// User management
			adminRoutes.GET("/users/pending", adminAPI.GetPendingUsers)
			adminRoutes.POST("/users/:id/approve", adminAPI.ApproveUser)
			adminRoutes.POST("/users/:id/reject", adminAPI.RejectUser)

			// Network settings
			adminRoutes.GET("/network/interfaces", adminAPI.GetNetworkInterfaces)
			adminRoutes.PUT("/network/interfaces/:name", adminAPI.UpdateNetworkInterface)

			// Codec settings
			adminRoutes.GET("/codec-settings", adminAPI.GetCodecSettings)
			adminRoutes.PUT("/codec-settings", adminAPI.UpdateCodecSettings)

			adminRoutes.GET("/general-settings", adminAPI.GetGeneralSettings)
			adminRoutes.PUT("/general-settings", adminAPI.UpdateGeneralSettings)

			// Retranscoding routes
			adminRoutes.POST("/retranscode/start", retranscodeAPI.StartRetranscoding)
			adminRoutes.GET("/retranscode/status", retranscodeAPI.GetRetranscodeStatus)
			adminRoutes.GET("/retranscode/jobs", retranscodeAPI.GetRetranscodeJobs)
			adminRoutes.POST("/retranscode/activate", retranscodeAPI.ActivateNewCodecSettings)
		}

		// WebSocket routes (no auth for now, could be added with a custom middleware)
		wsHandler := websocket.NewWebSocketHandler()
		router.GET("/ws", wsHandler.HandleWebSocket)

		// Schedule routes
		scheduleAPI := schedule.NewScheduleApi(db, streamerPlayout)
		scheduleRoutes := v1.Group("/schedule")
		{
			scheduleRoutes.GET("", scheduleAPI.GetSchedules)
			scheduleRoutes.POST("", scheduleAPI.CreateSchedule)
			scheduleRoutes.GET("/:id", scheduleAPI.GetSchedule)
			scheduleRoutes.PUT("/:id", scheduleAPI.UpdateSchedule)
			scheduleRoutes.DELETE("/:id", scheduleAPI.DeleteSchedule)
			scheduleRoutes.GET("/:id/guide", scheduleAPI.FindGuideByScheduleId)
			scheduleRoutes.POST("/:id/guide", scheduleAPI.UpdateGuide)
			scheduleRoutes.POST("/:id/guide/change-program", scheduleAPI.ChangeProgramInGuide)
			scheduleRoutes.GET("/files", scheduleAPI.FilesToNestedStructure)
			scheduleRoutes.POST("/files", scheduleAPI.BatchUpdateFiles)
			scheduleRoutes.GET("/status", scheduleAPI.GetSchedulersStatus)
			scheduleRoutes.GET("/epg", scheduleAPI.GetSchedulesForEPG)
		}

		// Playout routes
		playoutAPI := NewPlayoutAPI(streamerPlayout)
		playoutRoutes := v1.Group("/playout")
		{
			playoutRoutes.GET("/preview/:shortID", playoutAPI.GetStreamPreviewURL)
		}

		// Analytics routes
		analyticsRepo := repository.NewAnalyticsRepository(db)
		analyticsAPI := NewAnalyticsAPI(analyticsRepo)
		analyticsAPI.RegisterRoutes(v1)

		// Logs routes (protected by auth middleware)
		logsAPI := logs.NewLogsAPI(db)
		logsRoutes := v1.Group("/logs")
		logsRoutes.Use(middleware.AuthMiddleware())
		{
			logsRoutes.GET("", logsAPI.GetLogs)
			logsRoutes.GET("/stats", logsAPI.GetLogStats)
			logsRoutes.GET("/sources", logsAPI.GetLogSources)
			logsRoutes.POST("/export", logsAPI.ExportLogs)
			logsRoutes.DELETE("/cleanup", middleware.RoleMiddleware("admin"), logsAPI.CleanupOldLogs)
		}
	}

	// WebSocket route for logs (protected by WebSocket auth middleware)
	wsLogsRoute := router.Group("/ws/logs")
	wsLogsRoute.Use(middleware.WebSocketAuthMiddleware())
	{
		logsAPI := logs.NewLogsAPI(db)
		wsLogsRoute.GET("", logsAPI.HandleLogsWebSocket)
	}
}
