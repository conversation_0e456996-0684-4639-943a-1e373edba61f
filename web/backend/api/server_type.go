package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"showfer-web/config"
	"showfer-web/service/health_monitor"
	"showfer-web/service/database_sync"
	"showfer-web/service/websocket_client"
	"showfer-web/service/traffic_blocker"
	"showfer-web/utils"
	"strings"
	"time"
	"os"
	"os/exec"
	"showfer-web/service/logger"
)

type ServerTypeResponse struct {
	ServerType string `json:"server_type"`
}

type BackupIPRequest struct {
	BackupIP string `json:"backup_ip"`
}

type BackupIPResponse struct {
	BackupIP string `json:"backup_ip"`
}

type PrimaryIPRequest struct {
	PrimaryIP string `json:"primary_ip"`
}

type PrimaryIPResponse struct {
	PrimaryIP string `json:"primary_ip"`
}

type BackupServerTestRequest struct {
	BackupIP string `json:"backup_ip"`
}

type BackupServerTestResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type SSHConfigRequest struct {
	SSHUser     string `json:"ssh_user"`
	SSHPassword string `json:"ssh_password"`
}

type SSHConfigResponse struct {
	SSHUser     string `json:"ssh_user"`
	SSHPassword string `json:"ssh_password"`
}

type PrimarySSHConfigRequest struct {
	PrimarySSHUser     string `json:"primary_ssh_user"`
	PrimarySSHPassword string `json:"primary_ssh_password"`
}

type PrimarySSHConfigResponse struct {
	PrimarySSHUser     string `json:"primary_ssh_user"`
	PrimarySSHPassword string `json:"primary_ssh_password"`
}

// GetServerTypeHandler returns the current server type
func GetServerTypeHandler(w http.ResponseWriter, r *http.Request) {
	serverType := utils.GetServerType()
	response := ServerTypeResponse{ServerType: serverType}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// SetServerTypeHandler sets the server type
func SetServerTypeHandler(w http.ResponseWriter, r *http.Request) {
	var request ServerTypeResponse
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	if request.ServerType != "primary" && request.ServerType != "backup" {
		http.Error(w, "Invalid server type. Must be 'primary' or 'backup'", http.StatusBadRequest)
		return
	}

	// Get database connection from context or use a shared connection
	db := config.GetDB()
	if db == nil {
		http.Error(w, "Database connection not available", http.StatusInternalServerError)
		return
	}

	if err := utils.SetServerType(request.ServerType); err != nil {
		http.Error(w, "Failed to set server type", http.StatusInternalServerError)
		return
	}

	// Handle traffic blocking based on server type change
	trafficBlocker := traffic_blocker.GetTrafficBlocker(db)
	trafficBlocker.OnServerTypeChange(request.ServerType)

	w.WriteHeader(http.StatusOK)
}

// GetBackupIPHandler returns the current backup IP
func GetBackupIPHandler(w http.ResponseWriter, r *http.Request) {
	backupIP := utils.GetBackupIP()
	response := BackupIPResponse{BackupIP: backupIP}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// SetBackupIPHandler sets the backup IP and manages WebSocket connection
func SetBackupIPHandler(w http.ResponseWriter, r *http.Request) {
	var request BackupIPRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Update backup connection
	websocket_client.UpdateBackupConnection(request.BackupIP)

	w.WriteHeader(http.StatusOK)
}

// GetPrimaryIPHandler returns the current primary IP
func GetPrimaryIPHandler(w http.ResponseWriter, r *http.Request) {
	primaryIP := utils.GetPrimaryIP()
	response := PrimaryIPResponse{PrimaryIP: primaryIP}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// SetPrimaryIPHandler sets the primary IP
func SetPrimaryIPHandler(w http.ResponseWriter, r *http.Request) {
	var request PrimaryIPRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	if err := utils.SetPrimaryIP(request.PrimaryIP); err != nil {
		http.Error(w, "Failed to set primary IP", http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
}

// TestBackupServerHandler tests connectivity to a backup server
func TestBackupServerHandler(w http.ResponseWriter, r *http.Request) {
	var request BackupServerTestRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	if request.BackupIP == "" {
		response := BackupServerTestResponse{
			Success: false,
			Message: "Backup IP address is required",
		}
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
		return
	}

	// Test connectivity to backup server
	success, message := testBackupServerConnectivity(request.BackupIP)

	response := BackupServerTestResponse{
		Success: success,
		Message: message,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// testBackupServerConnectivity tests if the backup server is reachable
func testBackupServerConnectivity(backupIP string) (bool, string) {
	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	// Test the server-type endpoint to verify it's a valid traffiq server
	url := fmt.Sprintf("http://%s:8080/api/server-type", backupIP)

	resp, err := client.Get(url)
	if err != nil {
		// Clean up error message for better user experience
		errorMsg := err.Error()
		if strings.Contains(errorMsg, "context deadline exceeded") {
			return false, "Connection timeout - backup server is not responding"
		}
		if strings.Contains(errorMsg, "connection refused") {
			return false, "Connection refused - backup server is not running"
		}
		if strings.Contains(errorMsg, "no such host") {
			return false, "Invalid IP address or hostname"
		}
		if strings.Contains(errorMsg, "network is unreachable") {
			return false, "Network unreachable - check IP address and network connectivity"
		}
		// Generic fallback for other errors
		return false, "Failed to connect to backup server"
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return false, fmt.Sprintf("Backup server responded with status: %d", resp.StatusCode)
	}

	// Try to decode the response to verify it's a valid traffiq server
	var serverTypeResp ServerTypeResponse
	if err := json.NewDecoder(resp.Body).Decode(&serverTypeResp); err != nil {
		return false, "Backup server is not a valid traffiq server"
	}

	return true, fmt.Sprintf("Successfully connected to backup server (Type: %s)", serverTypeResp.ServerType)
}

// GetHealthStatusHandler returns the health monitoring status
func GetHealthStatusHandler(w http.ResponseWriter, r *http.Request) {
	monitor := health_monitor.GetHealthMonitor()
	status := monitor.GetStatus()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(status)
}

// GetRestoreStatusHandler returns the database restore service status
func GetRestoreStatusHandler(w http.ResponseWriter, r *http.Request) {
	restoreService := database_sync.GetRestoreServiceInstance()
	if restoreService == nil {
		http.Error(w, "Restore service not initialized", http.StatusInternalServerError)
		return
	}

	status := restoreService.GetStatus()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(status)
}

// GetSSHConfigHandler returns the current SSH configuration
func GetSSHConfigHandler(w http.ResponseWriter, r *http.Request) {
	response := SSHConfigResponse{
		SSHUser:     utils.GetSSHUser(),
		SSHPassword: utils.GetSSHPassword(),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// SetSSHConfigHandler sets the SSH configuration for rsync
func SetSSHConfigHandler(w http.ResponseWriter, r *http.Request) {
	var request SSHConfigRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Set SSH user
	if err := utils.SetSSHUser(request.SSHUser); err != nil {
		http.Error(w, "Failed to save SSH user", http.StatusInternalServerError)
		return
	}

	// Set SSH password
	if err := utils.SetSSHPassword(request.SSHPassword); err != nil {
		http.Error(w, "Failed to save SSH password", http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
}

// LoadDatabaseHandler handles the database recovery process for primary servers
func LoadDatabaseHandler(w http.ResponseWriter, r *http.Request) {
	logger.Log("Recovery: Received database load request")

	// Check if dump file exists
	dumpFile := "/tmp/showfer_db_dump.sql"
	if _, err := os.Stat(dumpFile); err != nil {
		logger.Log("Recovery: No database dump file found, continuing with existing data")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("No dump file found, continuing with existing data"))
		return
	}

	logger.Log("Recovery: Found database dump file, attempting restore")

	// Load the database dump in a goroutine so we can respond immediately
	go func() {
		if err := loadDatabaseDump(dumpFile); err != nil {
			logger.Error("Recovery: Failed to load database dump: %v", err)
			return
		}

		logger.Log("Recovery: Database loaded successfully. Application continues running with updated data.")
	}()

	logger.Log("Recovery: Started database loading process")
	w.WriteHeader(http.StatusOK)
	w.Write([]byte("Database loading started"))
}

// loadDatabaseDump loads the database dump file using proper database restoration
func loadDatabaseDump(dumpFile string) error {
	logger.Log("Recovery: Loading database dump from %s", dumpFile)

	// Get database configuration
	dbConfig := config.GetDatabaseConfig()

	// First, terminate all existing connections to the database
	if err := terminateConnections(dbConfig); err != nil {
		logger.Log("Recovery: Warning - failed to terminate connections: %v", err)
	}

	// Drop and recreate the database
	if err := recreateDatabase(dbConfig); err != nil {
		logger.Error("Recovery: Failed to recreate database: %v", err)
		return err
	}

	// Restore from dump file
	cmd := exec.Command("psql",
		"-h", dbConfig.Host,
		"-p", dbConfig.Port,
		"-U", dbConfig.User,
		"-d", dbConfig.DBName,
		"-f", dumpFile,
		"--quiet",
	)

	// Set PGPASSWORD environment variable for authentication
	cmd.Env = append(os.Environ(), fmt.Sprintf("PGPASSWORD=%s", dbConfig.Password))

	output, err := cmd.CombinedOutput()
	if err != nil {
		logger.Error("Recovery: Failed to restore database from dump file: %v, output: %s", err, string(output))
		return err
	}

	logger.Log("Recovery: Successfully restored database from dump file")
	
	// Note: We keep the dump file so the RestoreService can monitor it for updates
	
	return nil
}

// terminateConnections terminates all connections to the target database
func terminateConnections(dbConfig config.DatabaseConfig) error {
	cmd := exec.Command("psql",
		"-h", dbConfig.Host,
		"-p", dbConfig.Port,
		"-U", dbConfig.User,
		"-d", "postgres", // Connect to postgres database to terminate connections
		"-c", fmt.Sprintf("SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = '%s' AND pid <> pg_backend_pid();", dbConfig.DBName),
		"--quiet",
	)

	cmd.Env = append(os.Environ(), fmt.Sprintf("PGPASSWORD=%s", dbConfig.Password))
	
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to terminate connections: %v, output: %s", err, string(output))
	}

	logger.Log("Recovery: Terminated existing database connections")
	return nil
}

// recreateDatabase drops and recreates the target database
func recreateDatabase(dbConfig config.DatabaseConfig) error {
	// Drop database
	dropCmd := exec.Command("psql",
		"-h", dbConfig.Host,
		"-p", dbConfig.Port,
		"-U", dbConfig.User,
		"-d", "postgres", // Connect to postgres database to drop the target
		"-c", fmt.Sprintf("DROP DATABASE IF EXISTS %s;", dbConfig.DBName),
		"--quiet",
	)

	dropCmd.Env = append(os.Environ(), fmt.Sprintf("PGPASSWORD=%s", dbConfig.Password))
	
	output, err := dropCmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to drop database: %v, output: %s", err, string(output))
	}

	logger.Log("Recovery: Dropped existing database")

	// Create database
	createCmd := exec.Command("psql",
		"-h", dbConfig.Host,
		"-p", dbConfig.Port,
		"-U", dbConfig.User,
		"-d", "postgres", // Connect to postgres database to create the target
		"-c", fmt.Sprintf("CREATE DATABASE %s;", dbConfig.DBName),
		"--quiet",
	)

	createCmd.Env = append(os.Environ(), fmt.Sprintf("PGPASSWORD=%s", dbConfig.Password))
	
	output, err = createCmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to create database: %v, output: %s", err, string(output))
	}

	logger.Log("Recovery: Created new database")
	return nil
}

// restartApplication restarts the current application with proper cleanup
func restartApplication() {
	logger.Log("Recovery: Restarting application...")

	// Get the current executable path
	executable, err := os.Executable()
	if err != nil {
		logger.Error("Recovery: Failed to get executable path: %v", err)
		return
	}

	// Kill any existing instances to prevent port conflicts
	killExistingInstances()

	// Wait a moment for the port to be freed
	time.Sleep(2 * time.Second)

	// Start a new instance of the application
	cmd := exec.Command(executable)
	cmd.Dir, _ = os.Getwd() // Set working directory

	if err := cmd.Start(); err != nil {
		logger.Error("Recovery: Failed to start new application instance: %v", err)
		return
	}

	logger.Log("Recovery: New application instance started, shutting down current instance...")
	
	// Exit the current process
	os.Exit(0)
}

// killExistingInstances kills any existing traffiq-server instances to prevent conflicts
func killExistingInstances() {
	logger.Log("Recovery: Killing existing traffiq-server instances...")

	// Kill all traffiq-server processes except current one
	cmd := exec.Command("pkill", "-f", "traffiq-server")
	if err := cmd.Run(); err != nil {
		logger.Log("Recovery: No existing instances to kill or kill failed: %v", err)
	}

	// Also kill anything using port 8080
	cmd = exec.Command("sh", "-c", "lsof -ti:8080 | xargs kill -9 2>/dev/null || true")
	if err := cmd.Run(); err != nil {
		logger.Log("Recovery: Failed to kill processes on port 8080: %v", err)
	}
}

// GetPrimarySSHConfigHandler returns the current primary server SSH configuration
func GetPrimarySSHConfigHandler(w http.ResponseWriter, r *http.Request) {
	response := PrimarySSHConfigResponse{
		PrimarySSHUser:     utils.GetPrimarySSHUser(),
		PrimarySSHPassword: utils.GetPrimarySSHPassword(),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// SetPrimarySSHConfigHandler sets the primary server SSH configuration for recovery
func SetPrimarySSHConfigHandler(w http.ResponseWriter, r *http.Request) {
	var request PrimarySSHConfigRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Set primary SSH user
	if err := utils.SetPrimarySSHUser(request.PrimarySSHUser); err != nil {
		http.Error(w, "Failed to save primary SSH user", http.StatusInternalServerError)
		return
	}

	// Set primary SSH password
	if err := utils.SetPrimarySSHPassword(request.PrimarySSHPassword); err != nil {
		http.Error(w, "Failed to save primary SSH password", http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
}

// GetTrafficBlockerStatusHandler returns the current traffic blocking status
func GetTrafficBlockerStatusHandler(w http.ResponseWriter, r *http.Request) {
	db := config.GetDB()
	if db == nil {
		http.Error(w, "Database connection not available", http.StatusInternalServerError)
		return
	}

	trafficBlocker := traffic_blocker.GetTrafficBlocker(db)
	status := trafficBlocker.GetStatus()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(status)
}

// RefreshTrafficBlockingHandler refreshes traffic blocking rules
func RefreshTrafficBlockingHandler(w http.ResponseWriter, r *http.Request) {
	db := config.GetDB()
	if db == nil {
		http.Error(w, "Database connection not available", http.StatusInternalServerError)
		return
	}

	trafficBlocker := traffic_blocker.GetTrafficBlocker(db)
	if err := trafficBlocker.RefreshBlocking(); err != nil {
		http.Error(w, fmt.Sprintf("Failed to refresh traffic blocking: %v", err), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
}