package files

import (
	"net/http"
	"showfer-web/repository"
	"strconv"

	"github.com/gin-gonic/gin"
)

// FileStatusAPI handles file status-related API endpoints
type FileStatusAPI struct {
	filesRepo *repository.FilesRepository
}

// NewFileStatusAPI creates a new FileStatusAPI
func NewFileStatusAPI(filesRepo *repository.FilesRepository) *FileStatusAPI {
	return &FileStatusAPI{
		filesRepo: filesRepo,
	}
}

// GetFilesByRecorderID handles GET /api/v1/files/recorder/:id
// Returns files associated with a recorder that are in queue or being transcoded
func (api *FileStatusAPI) GetFilesByRecorderID(c *gin.Context) {
	// Parse recorder ID
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid recorder ID"})
		return
	}

	// Get files by recorder ID
	files, err := api.filesRepo.GetFilesByRecorderID(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get files", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, files)
}
