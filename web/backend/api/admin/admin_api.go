package admin

import (
	"database/sql"
	"net/http"
	"showfer-web/models"
	"showfer-web/repository"
	"showfer-web/service/queue"
	"strconv"

	"github.com/gin-gonic/gin"
)

// AdminAPI handles admin-related API endpoints
type AdminAPI struct {
	userRepo            *repository.UserRepository
	networkRepo         *repository.NetworkRepository
	codecSettingsRepo   *repository.CodecSettingsRepository
	generalSettingsRepo *repository.GeneralSettingsRepository
}

// NewAdminAPI creates a new AdminAPI
func NewAdminAPI(db *sql.DB) *AdminAPI {
	return &AdminAPI{
		userRepo:            repository.NewUserRepository(db),
		networkRepo:         repository.NewNetworkRepository(db),
		codecSettingsRepo:   repository.NewCodecSettingsRepository(db),
		generalSettingsRepo: repository.NewGeneralSettingsRepository(db),
	}
}

// GetPendingUsers handles GET /api/v1/admin/users/pending
func (api *AdminAPI) GetPendingUsers(c *gin.Context) {
	// Get pending users
	users, err := api.userRepo.GetUsersByStatus("pending")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get pending users", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, users)
}

// ApproveUser handles POST /api/v1/admin/users/:id/approve
func (api *AdminAPI) ApproveUser(c *gin.Context) {
	// Parse user ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	// Get user by ID
	user, err := api.userRepo.GetUserByID(id)
	if err != nil {
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user", "details": err.Error()})
		}
		return
	}

	// Update user status
	input := models.UserUpdateInput{
		Username: user.Username,
		Email:    user.Email,
		Password: "", // Don't change password
		Role:     user.Role,
		Status:   "approved",
	}

	_, err = api.userRepo.UpdateUser(id, input)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to approve user", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "User approved successfully"})
}

// RejectUser handles POST /api/v1/admin/users/:id/reject
func (api *AdminAPI) RejectUser(c *gin.Context) {
	// Parse user ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	// Get user by ID
	user, err := api.userRepo.GetUserByID(id)
	if err != nil {
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user", "details": err.Error()})
		}
		return
	}

	// Protect admin user from rejection
	if user.Username == "admin" {
		c.JSON(http.StatusForbidden, gin.H{"error": "Cannot reject admin user"})
		return
	}

	// Update user status
	input := models.UserUpdateInput{
		Username: user.Username,
		Email:    user.Email,
		Password: "", // Don't change password
		Role:     user.Role,
		Status:   "rejected",
	}

	_, err = api.userRepo.UpdateUser(id, input)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to reject user", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "User rejected successfully"})
}

// GetNetworkInterfaces handles GET /api/v1/admin/network/interfaces
func (api *AdminAPI) GetNetworkInterfaces(c *gin.Context) {
	// Get ALL network interfaces (including unconfigured ones for administration)
	interfaces, err := api.networkRepo.GetNetworkInterfaces()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get network interfaces", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, interfaces)
}

// UpdateNetworkInterface handles PUT /api/v1/admin/network/interfaces/:name
func (api *AdminAPI) UpdateNetworkInterface(c *gin.Context) {
	// Parse interface name
	name := c.Param("name")
	if name == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Interface name is required"})
		return
	}

	// Parse input
	var input models.NetworkInterfaceInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input", "details": err.Error()})
		return
	}

	// Update network interface
	err := api.networkRepo.UpdateNetworkInterface(name, input)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update network interface", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Network interface updated successfully"})
}

// GetCodecSettings handles GET /api/v1/admin/codec-settings
func (api *AdminAPI) GetCodecSettings(c *gin.Context) {
	// Get codec settings
	settings, err := api.codecSettingsRepo.GetCodecSettings()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get codec settings", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, settings)
}

// UpdateCodecSettings handles PUT /api/v1/admin/codec-settings
func (api *AdminAPI) UpdateCodecSettings(c *gin.Context) {
	// Parse input
	var input models.CodecSettingsInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input", "details": err.Error()})
		return
	}

	// Validate input
	if input.VBitrate > input.MaxVBitrate {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Video bitrate cannot exceed max video bitrate"})
		return
	}

	// Update codec settings
	err := api.codecSettingsRepo.UpdateCodecSettings(input)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update codec settings", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Codec settings updated successfully"})
}

// GetGeneralSettings handles GET /api/v1/admin/general-settings
func (api *AdminAPI) GetGeneralSettings(c *gin.Context) {
	// Get general settings
	settings, err := api.generalSettingsRepo.GetGeneralSettings()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get general settings", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, settings)
}

// UpdateGeneralSettings handles PUT /api/v1/admin/general-settings
func (api *AdminAPI) UpdateGeneralSettings(c *gin.Context) {
	// Parse input
	var input models.GeneralSettingsInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input", "details": err.Error()})
		return
	}

	// Update general settings
	err := api.generalSettingsRepo.UpdateGeneralSettings(input)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update general settings", "details": err.Error()})
		return
	} else {
		if input.TranscoderThreads > 0 {
			queue.GetInstance().AdjustWorkers(input.TranscoderThreads)
		}
	}

	c.JSON(http.StatusOK, gin.H{"message": "General settings updated successfully"})
}
