package admin

import (
	"net/http"
	"showfer-web/models"
	"showfer-web/service/retranscoder"

	"github.com/gin-gonic/gin"
)

// RetranscodeAPI handles retranscoding-related API endpoints (memory-based)
type RetranscodeAPI struct {
	retranscoderService *retranscoder.RetranscoderService
}

// NewRetranscodeAPI creates a new RetranscodeAPI
func NewRetranscodeAPI(retranscoderService *retranscoder.RetranscoderService) *RetranscodeAPI {
	return &RetranscodeAPI{
		retranscoderService: retranscoderService,
	}
}

// StartRetranscoding handles POST /api/v1/admin/retranscode/start
func (api *RetranscodeAPI) StartRetranscoding(c *gin.Context) {
	err := api.retranscoderService.StartRetranscoding()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to start retranscoding", "details": err.Error()})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{"message": "Retranscoding started successfully"})
}

// GetRetranscodeStatus handles GET /api/v1/admin/retranscode/status
func (api *RetranscodeAPI) GetRetranscodeStatus(c *gin.Context) {
	status, err := api.retranscoderService.GetRetranscodeStatus()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get retranscode status", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, status)
}

// GetRetranscodeJobs handles GET /api/v1/admin/retranscode/jobs
func (api *RetranscodeAPI) GetRetranscodeJobs(c *gin.Context) {
	jobs, err := api.retranscoderService.GetRetranscodeJobs()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get retranscode jobs", "details": err.Error()})
		return
	}

	// Ensure we always return an array, never null
	if jobs == nil {
		jobs = []models.RetranscodeJob{}
	}

	c.JSON(http.StatusOK, jobs)
}

// ActivateNewCodecSettings handles POST /api/v1/admin/retranscode/activate
func (api *RetranscodeAPI) ActivateNewCodecSettings(c *gin.Context) {
	err := api.retranscoderService.ActivateNewCodecSettings()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to activate new codec settings", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "New codec settings activated successfully"})
} 