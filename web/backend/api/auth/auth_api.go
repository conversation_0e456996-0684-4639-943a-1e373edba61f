package auth

import (
	"database/sql"
	"net/http"
	"showfer-web/models"
	"showfer-web/repository"
	"showfer-web/service/auth"
	"showfer-web/service/logger"
	"strconv"

	"github.com/gin-gonic/gin"
)

// AuthAPI handles authentication-related API endpoints
type AuthAPI struct {
	userRepo *repository.UserRepository
}

// NewAuthAPI creates a new AuthAPI
func NewAuthAPI(db *sql.DB) *AuthAPI {
	return &AuthAPI{
		userRepo: repository.NewUserRepository(db),
	}
}

// Login handles user login
func (api *AuthAPI) Login(c *gin.Context) {
	// Parse login input
	var input models.LoginInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input", "details": err.Error()})
		return
	}

	// Verify credentials
	user, err := api.userRepo.VerifyPassword(input.Username, input.Password)
	if err != nil {
		logger.Error("Login failed: %v", err)
		c.<PERSON>(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}

	// Generate JWT token
	token, err := auth.GenerateToken(user)
	if err != nil {
		logger.Error("Failed to generate token: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}

	// Return token and user info
	c.JSON(http.StatusOK, models.LoginResponse{
		Token: token,
		User: models.User{
			ID:        user.ID,
			Username:  user.Username,
			Email:     user.Email,
			Role:      user.Role,
			Status:    user.Status,
			CreatedAt: user.CreatedAt,
			UpdatedAt: user.UpdatedAt,
		},
	})
}

// GetUsers handles GET /api/v1/users
func (api *AuthAPI) GetUsers(c *gin.Context) {
	// Get all users
	users, err := api.userRepo.GetAllUsers()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get users", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, users)
}

// GetUser handles GET /api/v1/users/:id
func (api *AuthAPI) GetUser(c *gin.Context) {
	// Parse user ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	// Get user by ID
	user, err := api.userRepo.GetUserByID(id)
	if err != nil {
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user", "details": err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, user)
}

// CreateUser handles POST /api/v1/users
func (api *AuthAPI) CreateUser(c *gin.Context) {
	// Parse user input
	var input models.UserInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input", "details": err.Error()})
		return
	}

	// Create user
	user, err := api.userRepo.CreateUser(input)
	if err != nil {
		if err.Error() == "username already exists" || err.Error() == "email already exists" {
			c.JSON(http.StatusConflict, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user", "details": err.Error()})
		}
		return
	}

	c.JSON(http.StatusCreated, user)
}

// UpdateUser handles PUT /api/v1/users/:id
func (api *AuthAPI) UpdateUser(c *gin.Context) {
	// Parse user ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	// Parse user input
	var input models.UserUpdateInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input", "details": err.Error()})
		return
	}

	// Update user
	user, err := api.userRepo.UpdateUser(id, input)
	if err != nil {
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		} else if err.Error() == "username already exists" || err.Error() == "email already exists" {
			c.JSON(http.StatusConflict, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update user", "details": err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, user)
}

// DeleteUser handles DELETE /api/v1/users/:id
func (api *AuthAPI) DeleteUser(c *gin.Context) {
	// Parse user ID
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	// Delete user
	err = api.userRepo.DeleteUser(id)
	if err != nil {
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete user", "details": err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "User deleted successfully"})
}

// Register handles user registration
func (api *AuthAPI) Register(c *gin.Context) {
	// Parse registration input
	var input models.UserInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input", "details": err.Error()})
		return
	}

	// Set default role and status for new users
	input.Role = "user"
	input.Status = "pending"

	// Create user
	user, err := api.userRepo.CreateUser(input)
	if err != nil {
		if err.Error() == "username already exists" || err.Error() == "email already exists" {
			c.JSON(http.StatusConflict, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to register user", "details": err.Error()})
		}
		return
	}

	// Generate JWT token
	token, err := auth.GenerateToken(user)
	if err != nil {
		logger.Error("Failed to generate token: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}

	// Return token and user info
	c.JSON(http.StatusCreated, models.LoginResponse{
		Token: token,
		User: models.User{
			ID:        user.ID,
			Username:  user.Username,
			Email:     user.Email,
			Role:      user.Role,
			Status:    user.Status,
			CreatedAt: user.CreatedAt,
			UpdatedAt: user.UpdatedAt,
		},
	})
}

// GetCurrentUser handles GET /api/v1/users/me
func (api *AuthAPI) GetCurrentUser(c *gin.Context) {
	// Get user ID from context (set by AuthMiddleware)
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User ID not found in context"})
		return
	}

	// Get user by ID
	user, err := api.userRepo.GetUserByID(userID.(int))
	if err != nil {
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user", "details": err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, user)
}
