package system

import (
	"net/http"
	"showfer-web/models"
	"showfer-web/service/system"
	
	"github.com/gin-gonic/gin"
)

// SystemAPI handles system-related API endpoints
type SystemAPI struct {}

// NewSystemAPI creates a new SystemAPI
func NewSystemAPI() *SystemAPI {
	return &SystemAPI{}
}

// GetSystemStats handles GET /api/v1/system/stats
func (api *SystemAPI) GetSystemStats(c *gin.Context) {
	// Get system monitor
	monitor := system.GetSystemMonitor()
	if monitor == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "System monitor not initialized"})
		return
	}
	
	// Get current stats
	stats := monitor.GetCurrentStats()
	
	c.JSO<PERSON>(http.StatusOK, stats)
}

// GetAlarmThresholds handles GET /api/v1/system/thresholds
func (api *SystemAPI) GetAlarmThresholds(c *gin.Context) {
	// Get system monitor
	monitor := system.GetSystemMonitor()
	if monitor == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "System monitor not initialized"})
		return
	}
	
	// Get thresholds
	thresholds := monitor.GetThresholds()
	
	c.JSON(http.StatusOK, thresholds)
}

// UpdateAlarmThresholds handles PUT /api/v1/system/thresholds
func (api *SystemAPI) UpdateAlarmThresholds(c *gin.Context) {
	// Get system monitor
	monitor := system.GetSystemMonitor()
	if monitor == nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "System monitor not initialized"})
		return
	}
	
	// Parse input
	var thresholds models.AlarmThresholds
	if err := c.ShouldBindJSON(&thresholds); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid input", "details": err.Error()})
		return
	}
	
	// Validate thresholds
	if thresholds.DiskThreshold < 0 || thresholds.DiskThreshold > 100 ||
		thresholds.CPUThreshold < 0 || thresholds.CPUThreshold > 100 ||
		thresholds.MemoryThreshold < 0 || thresholds.MemoryThreshold > 100 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Thresholds must be between 0 and 100"})
		return
	}
	
	// Update thresholds
	monitor.SetThresholds(thresholds)
	
	c.JSON(http.StatusOK, gin.H{"message": "Alarm thresholds updated successfully"})
}
