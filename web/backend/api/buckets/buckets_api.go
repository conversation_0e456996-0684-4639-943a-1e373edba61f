package buckets

import (
	"context"
	"net/http"
	"showfer-web/models"
	"showfer-web/repository"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// BucketsAPI handles bucket-related API endpoints
type BucketsAPI struct {
	bucketRepo *repository.BucketRepository
}

// NewBucketsAPI creates a new BucketsAPI
func NewBucketsAPI(mongoDB *mongo.Database) *BucketsAPI {
	return &BucketsAPI{
		bucketRepo: repository.NewBucketRepository(mongoDB),
	}
}

// GetBuckets handles GET /api/v1/buckets
func (api *BucketsAPI) GetBuckets(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	buckets, err := api.bucketRepo.GetAllBuckets(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get buckets",
			"details": err.<PERSON>rror(),
		})
		return
	}

	c.<PERSON>SO<PERSON>(http.StatusOK, buckets)
}

// GetBucketByID handles GET /api/v1/buckets/:id
func (api *BucketsAPI) GetBucketByID(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	idParam := c.Param("id")
	id, err := primitive.ObjectIDFromHex(idParam)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid bucket ID",
		})
		return
	}

	bucket, err := api.bucketRepo.GetBucketByID(ctx, id)
	if err != nil {
		if err.Error() == "bucket not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Bucket not found",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to get bucket",
				"details": err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, bucket)
}

// CreateBucket handles POST /api/v1/buckets
func (api *BucketsAPI) CreateBucket(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	var input models.BucketCreateInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	bucket, err := api.bucketRepo.CreateBucket(ctx, input)
	if err != nil {
		if err.Error() == "bucket with name '"+input.BucketName+"' already exists" {
			c.JSON(http.StatusConflict, gin.H{
				"error": err.Error(),
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to create bucket",
				"details": err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusCreated, bucket)
}

// UpdateBucket handles PUT /api/v1/buckets/:id
func (api *BucketsAPI) UpdateBucket(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	idParam := c.Param("id")
	id, err := primitive.ObjectIDFromHex(idParam)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid bucket ID",
		})
		return
	}

	var input models.BucketUpdateInput
	if err := c.ShouldBindJSON(&input); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	bucket, err := api.bucketRepo.UpdateBucket(ctx, id, input)
	if err != nil {
		if err.Error() == "bucket not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Bucket not found",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to update bucket",
				"details": err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, bucket)
}

// DeleteBucket handles DELETE /api/v1/buckets/:id
func (api *BucketsAPI) DeleteBucket(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	idParam := c.Param("id")
	id, err := primitive.ObjectIDFromHex(idParam)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid bucket ID",
		})
		return
	}

	err = api.bucketRepo.DeleteBucket(ctx, id)
	if err != nil {
		if err.Error() == "bucket not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Bucket not found",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to delete bucket",
				"details": err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Bucket deleted successfully",
	})
}

// GetBucketsByUser handles GET /api/v1/buckets/user/:userId
func (api *BucketsAPI) GetBucketsByUser(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	userIDParam := c.Param("userId")
	userID, err := primitive.ObjectIDFromHex(userIDParam)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid user ID",
		})
		return
	}

	buckets, err := api.bucketRepo.GetBucketsByUser(ctx, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get buckets for user",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, buckets)
}

// GetDefaultBucket handles GET /api/v1/buckets/default
func (api *BucketsAPI) GetDefaultBucket(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	bucket, err := api.bucketRepo.GetDefaultBucket(ctx)
	if err != nil {
		if err.Error() == "no default bucket found" {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "No default bucket found",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to get default bucket",
				"details": err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, bucket)
}

// TestConnection handles GET /api/v1/buckets/test
func (api *BucketsAPI) TestConnection(c *gin.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Test MongoDB connection by counting documents
	count, err := api.bucketRepo.TestConnection(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to connect to MongoDB",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":        "MongoDB connection successful",
		"collection":     "buckets",
		"document_count": count,
		"timestamp":      time.Now(),
	})
}
