package api

import (
	"context"
	"net/http"
	"showfer-web/models"
	"showfer-web/repository"
	"showfer-web/service/logger"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// AnalyticsAPI handles analytics-related API endpoints
type AnalyticsAPI struct {
	analyticsRepo *repository.AnalyticsRepository
}

// NewAnalyticsAPI creates a new AnalyticsAPI
func NewAnalyticsAPI(analyticsRepo *repository.AnalyticsRepository) *AnalyticsAPI {
	return &AnalyticsAPI{analyticsRepo: analyticsRepo}
}

// RegisterRoutes registers the analytics API routes
func (api *AnalyticsAPI) RegisterRoutes(router *gin.RouterGroup) {
	analyticsGroup := router.Group("/analytics")
	{
		analyticsGroup.GET("/schedule/:id", api.GetScheduleAnalytics)
		analyticsGroup.GET("/schedule/:id/content", api.GetContentAnalytics)
		analyticsGroup.GET("/schedule/:id/summary", api.GetScheduleSummary)
		analyticsGroup.GET("/content/:path", api.GetContentSummary)
		analyticsGroup.GET("/date-range", api.GetAnalyticsForDateRange)
		analyticsGroup.GET("/date-range/stats", api.GetAnalyticsStatsForDateRange)
	}
}

// GetScheduleAnalytics returns analytics for a specific schedule
func (api *AnalyticsAPI) GetScheduleAnalytics(c *gin.Context) {
	scheduleID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid schedule ID"})
		return
	}

	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(c.DefaultQuery("limit", "20"))
	if err != nil || limit < 1 || limit > 1000 {
		limit = 20
	}

	pagination := models.Pagination{
		Page:  page,
		Limit: limit,
	}

	analytics, err := api.analyticsRepo.GetContentAnalytics(scheduleID, pagination)
	if err != nil {
		logger.Error("Failed to get analytics: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get analytics"})
		return
	}

	c.JSON(http.StatusOK, analytics)
}

// GetContentAnalytics returns analytics for a specific content
func (api *AnalyticsAPI) GetContentAnalytics(c *gin.Context) {
	scheduleID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid schedule ID"})
		return
	}

	contentPath := c.Query("path")
	if contentPath == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Content path is required"})
		return
	}

	summary, err := api.analyticsRepo.GetContentSummary(scheduleID, contentPath)
	if err != nil {
		logger.Error("Failed to get content analytics: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get content analytics"})
		return
	}

	c.JSON(http.StatusOK, summary)
}

// GetScheduleSummary returns a summary of analytics for a specific schedule
func (api *AnalyticsAPI) GetScheduleSummary(c *gin.Context) {
	scheduleID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid schedule ID"})
		return
	}

	summary, err := api.analyticsRepo.GetScheduleSummary(scheduleID)
	if err != nil {
		logger.Error("Failed to get schedule summary: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get schedule summary"})
		return
	}

	c.JSON(http.StatusOK, summary)
}

// GetContentSummary returns a summary of analytics for a specific content path across all schedules
func (api *AnalyticsAPI) GetContentSummary(c *gin.Context) {
	contentPath := c.Param("path")
	if contentPath == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Content path is required"})
		return
	}

	// Use 0 for scheduleID to get analytics across all schedules
	summary, err := api.analyticsRepo.GetContentSummary(0, contentPath)
	if err != nil {
		logger.Error("Failed to get content summary: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get content summary"})
		return
	}

	c.JSON(http.StatusOK, summary)
}

// GetAnalyticsForDateRange returns analytics for a specific date range
func (api *AnalyticsAPI) GetAnalyticsForDateRange(c *gin.Context) {
	startDate := c.Query("start")
	endDate := c.Query("end")

	if startDate == "" || endDate == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Start and end dates are required"})
		return
	}

	// Validate date format
	start, err := time.Parse(time.RFC3339, startDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start date format. Use RFC3339 format (e.g., 2023-01-01T00:00:00Z)"})
		return
	}

	end, err := time.Parse(time.RFC3339, endDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end date format. Use RFC3339 format (e.g., 2023-01-01T00:00:00Z)"})
		return
	}

	// Check if date range is too large (more than 365 days) - increased from 90 days
	if end.Sub(start).Hours() > 24*365 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Date range too large. Please limit to 365 days or less."})
		return
	}

	// Get schedule ID if provided
	var scheduleID int64 = 0
	scheduleIDStr := c.Query("schedule_id")
	if scheduleIDStr != "" {
		scheduleID, err = strconv.ParseInt(scheduleIDStr, 10, 64)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid schedule ID"})
			return
		}
	}

	// Get pagination parameters
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	limit, err := strconv.Atoi(c.DefaultQuery("limit", "1000"))
	if err != nil || limit < 1 || limit > 10000 {
		limit = 1000
	}

	// Create pagination object
	pagination := models.Pagination{
		Page:  page,
		Limit: limit,
	}

	// Create a context with timeout to prevent long-running requests
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	// Replace the original context with our timeout context
	c.Request = c.Request.WithContext(ctx)

	// Use a goroutine with a channel to handle potential timeouts gracefully
	type result struct {
		analytics models.ContentAnalyticsListResult
		err       error
	}

	resultChan := make(chan result, 1)

	go func() {
		analytics, err := api.analyticsRepo.GetAnalyticsForDateRange(scheduleID, startDate, endDate, &pagination)
		resultChan <- result{analytics, err}
	}()

	// Wait for either the result or a timeout
	select {
	case <-ctx.Done():
		if ctx.Err() == context.DeadlineExceeded {
			logger.Error("Timeout getting analytics for date range")
			c.JSON(http.StatusRequestTimeout, gin.H{"error": "Request timed out. Try a smaller date range or more specific filters."})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Request canceled"})
		return

	case res := <-resultChan:
		if res.err != nil {
			logger.Error("Failed to get analytics for date range: %v", res.err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get analytics for date range"})
			return
		}

		// Return the full result with pagination info (backward compatible with frontend that expects {items: []})
		c.JSON(http.StatusOK, gin.H{
			"items":       res.analytics.Items,
			"total_items": res.analytics.TotalItems,
			"total_pages": res.analytics.TotalPages,
			"page":        res.analytics.Page,
			"limit":       res.analytics.Limit,
		})
	}
}

// GetAnalyticsStatsForDateRange returns aggregate statistics for a specific date range
func (api *AnalyticsAPI) GetAnalyticsStatsForDateRange(c *gin.Context) {
	startDate := c.Query("start")
	endDate := c.Query("end")

	if startDate == "" || endDate == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Start and end dates are required"})
		return
	}

	// Validate date format
	start, err := time.Parse(time.RFC3339, startDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start date format. Use RFC3339 format (e.g., 2023-01-01T00:00:00Z)"})
		return
	}

	end, err := time.Parse(time.RFC3339, endDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end date format. Use RFC3339 format (e.g., 2023-01-01T00:00:00Z)"})
		return
	}

	// Check if date range is too large (more than 365 days)
	if end.Sub(start).Hours() > 24*365 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Date range too large. Please limit to 365 days or less."})
		return
	}

	// Get schedule ID if provided
	var scheduleID int64 = 0
	scheduleIDStr := c.Query("schedule_id")
	if scheduleIDStr != "" {
		scheduleID, err = strconv.ParseInt(scheduleIDStr, 10, 64)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid schedule ID"})
			return
		}
	}

	// Create a context with timeout to prevent long-running requests
	ctx, cancel := context.WithTimeout(c.Request.Context(), 15*time.Second)
	defer cancel()

	// Replace the original context with our timeout context
	c.Request = c.Request.WithContext(ctx)

	// Use a goroutine with a channel to handle potential timeouts gracefully
	type result struct {
		stats models.AnalyticsStats
		err   error
	}

	resultChan := make(chan result, 1)

	go func() {
		stats, err := api.analyticsRepo.GetAnalyticsStatsForDateRange(scheduleID, startDate, endDate)
		resultChan <- result{stats, err}
	}()

	// Wait for either the result or a timeout
	select {
	case <-ctx.Done():
		if ctx.Err() == context.DeadlineExceeded {
			logger.Error("Timeout getting analytics stats for date range")
			c.JSON(http.StatusRequestTimeout, gin.H{"error": "Request timed out. Try a smaller date range or more specific filters."})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Request canceled"})
		return

	case res := <-resultChan:
		if res.err != nil {
			logger.Error("Failed to get analytics stats for date range: %v", res.err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get analytics stats for date range"})
			return
		}

		c.JSON(http.StatusOK, res.stats)
	}
}
