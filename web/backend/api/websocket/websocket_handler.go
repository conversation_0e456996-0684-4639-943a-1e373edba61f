package websocket

import (
	"encoding/json"
	"log"
	"net/http"
	"showfer-web/models"
	"showfer-web/service/files"
	"showfer-web/service/health_monitor"
	"showfer-web/service/recorder"
	"showfer-web/service/retranscoder"
	"showfer-web/service/system"
	"showfer-web/utils"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"
)

// WebSocketMessage represents a message sent over WebSocket
type WebSocketMessage struct {
	Type    string      `json:"type"`
	Payload interface{} `json:"payload"`
}

// RoleChangeMessage represents a role change message
type RoleChangeMessage struct {
	Type string `json:"type"`
	Data struct {
		NewRole string `json:"new_role"`
	} `json:"data"`
}

// RoleChangeResponse represents the response to a role change message
type RoleChangeResponse struct {
	Type string `json:"type"`
	Data struct {
		Success bool   `json:"success"`
		Message string `json:"message"`
		Role    string `json:"role"`
	} `json:"data"`
}

// WebSocketHandler handles WebSocket connections
type WebSocketHandler struct {
	upgrader websocket.Upgrader
}

// NewWebSocketHandler creates a new WebSocketHandler
func NewWebSocketHandler() *WebSocketHandler {
	return &WebSocketHandler{
		upgrader: websocket.Upgrader{
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
			CheckOrigin: func(r *http.Request) bool {
				return true // Allow all origins
			},
		},
	}
}

// HandleWebSocket handles WebSocket connections
func (h *WebSocketHandler) HandleWebSocket(c *gin.Context) {
	// Upgrade HTTP connection to WebSocket
	conn, err := h.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("Failed to upgrade connection to WebSocket: %v", err)
		return
	}

	// Create a unique client ID
	clientID := uuid.New().String()

	// Create channels for sending messages to this client
	recorderSend := make(chan models.RecorderStatusUpdate, 256)
	fileSend := make(chan models.FileStatusUpdate, 256)
	retranscodeSend := make(chan retranscoder.RetranscodeStatusUpdate, 256)

	// Create clients
	recorderClient := &recorder.Client{
		ID:   clientID,
		Send: recorderSend,
	}

	fileClient := &files.Client{
		ID:   clientID,
		Send: fileSend,
	}

	retranscodeClient := &retranscoder.Client{
		ID:   clientID,
		Send: retranscodeSend,
	}

	// Register the clients with the status managers
	recorderStatusManager := recorder.GetStatusManager()
	recorderStatusManager.RegisterClient(recorderClient)

	fileStatusManager := files.GetFileStatusManager()
	fileStatusManager.RegisterClient(fileClient)

	retranscoderService := retranscoder.GetRetranscoderService()
	retranscoderService.RegisterClient(retranscodeClient)

	// Start the client's read and write pumps
	go h.writePump(conn, recorderClient, fileClient, retranscodeClient)
	go h.readPump(conn, recorderClient, fileClient, retranscodeClient)

	// Start system stats monitoring if available
	systemMonitor := system.GetSystemMonitor()
	if systemMonitor != nil {
		go h.monitorSystemStats(conn)
	}
}

// writePump pumps messages from the hub to the WebSocket connection
func (h *WebSocketHandler) writePump(conn *websocket.Conn, recorderClient *recorder.Client, fileClient *files.Client, retranscodeClient *retranscoder.Client) {
	ticker := time.NewTicker(60 * time.Second)
	defer func() {
		ticker.Stop()
		conn.Close()
		recorder.GetStatusManager().UnregisterClient(recorderClient)
		files.GetFileStatusManager().UnregisterClient(fileClient)
		retranscoder.GetRetranscoderService().UnregisterClient(retranscodeClient)
	}()

	for {
		select {
		case message, ok := <-recorderClient.Send:
			// Set write deadline
			conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				// The hub closed the channel
				conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			// Write the message as JSON
			wrappedMessage := map[string]interface{}{
				"type":    "recorder_status",
				"payload": message,
			}
			if err := conn.WriteJSON(wrappedMessage); err != nil {
				log.Printf("Failed to write recorder message to WebSocket: %v", err)
				return
			}
		case message, ok := <-fileClient.Send:
			// Set write deadline
			conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				// The hub closed the channel
				conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			// Write the message as JSON
			wrappedMessage := map[string]interface{}{
				"type":    "file_status",
				"payload": message,
			}
			if err := conn.WriteJSON(wrappedMessage); err != nil {
				log.Printf("Failed to write file message to WebSocket: %v", err)
				return
			}
		case message, ok := <-retranscodeClient.Send:
			// Set write deadline
			conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				// The hub closed the channel
				conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			// Write the message as JSON
			wrappedMessage := map[string]interface{}{
				"type":    "retranscode_status",
				"payload": message,
			}
			if err := conn.WriteJSON(wrappedMessage); err != nil {
				log.Printf("Failed to write retranscode message to WebSocket: %v", err)
				return
			}
		case <-ticker.C:
			// Send a ping message
			conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// readPump pumps messages from the WebSocket connection to the hub
func (h *WebSocketHandler) readPump(conn *websocket.Conn, recorderClient *recorder.Client, fileClient *files.Client, retranscodeClient *retranscoder.Client) {
	defer func() {
		conn.Close()
		recorder.GetStatusManager().UnregisterClient(recorderClient)
		files.GetFileStatusManager().UnregisterClient(fileClient)
		retranscoder.GetRetranscoderService().UnregisterClient(retranscodeClient)
	}()

	// Set read deadline
	conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	conn.SetPongHandler(func(string) error {
		conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	// Read messages from the WebSocket connection
	for {
		_, message, err := conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket error: %v", err)
			}
			break
		}

		// Handle incoming messages
		h.handleIncomingMessage(conn, message)
	}
}

// handleIncomingMessage processes incoming WebSocket messages
func (h *WebSocketHandler) handleIncomingMessage(conn *websocket.Conn, data []byte) {
	var message map[string]interface{}
	if err := json.Unmarshal(data, &message); err != nil {
		log.Printf("Failed to parse WebSocket message: %v", err)
		return
	}

	messageType, ok := message["type"].(string)
	if !ok {
		log.Printf("Invalid message type in WebSocket message")
		return
	}

	switch messageType {
	case "role_change":
		h.handleRoleChange(conn, data)
	default:
		log.Printf("Unknown WebSocket message type: %s", messageType)
	}
}

// handleRoleChange processes role change messages
func (h *WebSocketHandler) handleRoleChange(conn *websocket.Conn, data []byte) {
	var roleChangeMsg RoleChangeMessage
	if err := json.Unmarshal(data, &roleChangeMsg); err != nil {
		log.Printf("Failed to parse role change message: %v", err)
		h.sendRoleChangeResponse(conn, false, "Invalid message format", "")
		return
	}

	newRole := roleChangeMsg.Data.NewRole
	log.Printf("Received role change request: %s", newRole)

	// Validate the new role
	if newRole != "primary" && newRole != "backup" {
		log.Printf("Invalid role in role change message: %s", newRole)
		h.sendRoleChangeResponse(conn, false, "Invalid role. Must be 'primary' or 'backup'", "")
		return
	}

	// If changing to backup, store the primary server IP for health monitoring
	if newRole == "backup" {
		// Extract primary IP from the connection's remote address
		remoteAddr := conn.RemoteAddr().String()
		if remoteAddr != "" {
			// Parse IP from address (format: "IP:port")
			parts := strings.Split(remoteAddr, ":")
			if len(parts) > 0 {
				primaryIP := parts[0]
				log.Printf("Storing primary server IP for health monitoring: %s", primaryIP)

				// Save primary IP for health monitoring
				if err := utils.SetPrimaryIP(primaryIP); err != nil {
					log.Printf("Failed to save primary IP: %v", err)
				} else {
					// Update health monitor with new primary IP
					health_monitor.UpdateMonitoredPrimaryIP(primaryIP)
				}
			}
		}
	}

	// Set the new server type
	if err := utils.SetServerType(newRole); err != nil {
		log.Printf("Failed to set server type to %s: %v", newRole, err)
		h.sendRoleChangeResponse(conn, false, "Failed to set server type", "")
		return
	}

	// Start health monitoring if we became a backup server
	if newRole == "backup" {
		go health_monitor.StartHealthMonitoring()
	} else if newRole == "primary" {
		// Stop health monitoring if we became primary
		health_monitor.StopHealthMonitoring()
	}

	log.Printf("Successfully changed server role to: %s", newRole)
	h.sendRoleChangeResponse(conn, true, "Role changed successfully", newRole)
}

// sendRoleChangeResponse sends a response to a role change message
func (h *WebSocketHandler) sendRoleChangeResponse(conn *websocket.Conn, success bool, message string, role string) {
	response := RoleChangeResponse{
		Type: "role_change_response",
	}
	response.Data.Success = success
	response.Data.Message = message
	response.Data.Role = role

	if err := conn.WriteJSON(response); err != nil {
		log.Printf("Failed to send role change response: %v", err)
	}
}

// monitorSystemStats monitors system stats and sends updates to the client
func (h *WebSocketHandler) monitorSystemStats(conn *websocket.Conn) {
	systemMonitor := system.GetSystemMonitor()
	if systemMonitor == nil {
		return
	}

	// Get the broadcast channel
	statsChan := systemMonitor.GetBroadcastChannel()

	// Monitor for system stats updates
	for stats := range statsChan {
		// Set write deadline
		conn.SetWriteDeadline(time.Now().Add(10 * time.Second))

		// Create WebSocket message
		message := map[string]interface{}{
			"type":    "system_stats",
			"payload": stats,
		}

		// Send the stats to the client
		if err := conn.WriteJSON(message); err != nil {
			log.Printf("Failed to write system stats to WebSocket: %v", err)
			return
		}
	}
}
