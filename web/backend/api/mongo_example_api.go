package api

import (
	"context"
	"database/sql"
	"net/http"
	"showfer-web/models"
	"showfer-web/repository"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// MongoExampleAPI demonstrates using MongoDB alongside PostgreSQL
type MongoExampleAPI struct {
	mongoRepo    *repository.MongoExampleRepository
	metadataRepo *repository.ContentMetadataRepository
	filesRepo    *repository.FilesRepository
}

// NewMongoExampleAPI creates a new MongoDB example API
func NewMongoExampleAPI(db *sql.DB) *MongoExampleAPI {
	return &MongoExampleAPI{
		mongoRepo:    repository.NewMongoExampleRepository(),
		metadataRepo: repository.NewContentMetadataRepository(),
		filesRepo:    repository.NewFilesRepository(db),
	}
}

// SetupMongoExampleRoutes sets up the MongoDB example routes
func SetupMongoExampleRoutes(router *gin.Engine, db *sql.DB) {
	api := NewMongoExampleAPI(db)
	
	v1 := router.Group("/api/v1/mongo-examples")
	{
		v1.POST("/", api.CreateExample)
		v1.GET("/:id", api.GetExample)
		v1.PUT("/:id", api.UpdateExample)
		v1.DELETE("/:id", api.DeleteExample)
		v1.GET("/", api.ListExamples)
		v1.GET("/tags/:tags", api.FindByTags)
	}

	metadata := router.Group("/api/v1/content-metadata")
	{
		metadata.POST("/", api.CreateContentMetadata)
		metadata.GET("/convert-item/:id", api.GetMetadataByConvertItem)
		metadata.PUT("/convert-item/:id/analytics", api.UpdateAnalytics)
		metadata.POST("/convert-item/:id/logs", api.AddProcessingLog)
	}
}

// CreateExample creates a new MongoDB example
func (api *MongoExampleAPI) CreateExample(c *gin.Context) {
	var example models.MongoExample
	if err := c.ShouldBindJSON(&example); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := api.mongoRepo.Create(ctx, &example); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, example)
}

// GetExample retrieves a MongoDB example by ID
func (api *MongoExampleAPI) GetExample(c *gin.Context) {
	id := c.Param("id")

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	example, err := api.mongoRepo.GetByID(ctx, id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, example)
}

// UpdateExample updates a MongoDB example
func (api *MongoExampleAPI) UpdateExample(c *gin.Context) {
	id := c.Param("id")

	var example models.MongoExample
	if err := c.ShouldBindJSON(&example); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := api.mongoRepo.Update(ctx, id, &example); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Example updated successfully"})
}

// DeleteExample deletes a MongoDB example
func (api *MongoExampleAPI) DeleteExample(c *gin.Context) {
	id := c.Param("id")

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := api.mongoRepo.Delete(ctx, id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Example deleted successfully"})
}

// ListExamples lists MongoDB examples with pagination
func (api *MongoExampleAPI) ListExamples(c *gin.Context) {
	limitStr := c.DefaultQuery("limit", "10")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.ParseInt(limitStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter"})
		return
	}

	offset, err := strconv.ParseInt(offsetStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid offset parameter"})
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	examples, err := api.mongoRepo.List(ctx, limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	total, err := api.mongoRepo.Count(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"examples": examples,
		"total":    total,
		"limit":    limit,
		"offset":   offset,
	})
}

// FindByTags finds examples by tags
func (api *MongoExampleAPI) FindByTags(c *gin.Context) {
	tagsParam := c.Param("tags")
	// Split tags by comma if multiple tags are provided
	tags := []string{tagsParam} // Simplified for this example

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	examples, err := api.mongoRepo.FindByTags(ctx, tags)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"examples": examples})
}

// CreateContentMetadata creates metadata for a content item (demonstrates hybrid usage)
func (api *MongoExampleAPI) CreateContentMetadata(c *gin.Context) {
	var metadata models.ContentMetadata
	if err := c.ShouldBindJSON(&metadata); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Verify that the convert item exists in PostgreSQL
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	_, err := api.filesRepo.GetConvertItemByID(metadata.ConvertItemID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Convert item not found in PostgreSQL"})
		return
	}

	// Create metadata in MongoDB
	if err := api.metadataRepo.CreateMetadata(ctx, &metadata); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, metadata)
}

// GetMetadataByConvertItem retrieves metadata for a convert item
func (api *MongoExampleAPI) GetMetadataByConvertItem(c *gin.Context) {
	idStr := c.Param("id")
	convertItemID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid convert item ID"})
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Get basic info from PostgreSQL
	convertItem, err := api.filesRepo.GetConvertItemByID(convertItemID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Convert item not found"})
		return
	}

	// Get rich metadata from MongoDB
	metadata, err := api.metadataRepo.GetMetadataByConvertItemID(ctx, convertItemID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	// Combine data from both databases
	response := gin.H{
		"convert_item": convertItem,
		"metadata":     metadata,
	}

	c.JSON(http.StatusOK, response)
}

// UpdateAnalytics updates analytics data for content
func (api *MongoExampleAPI) UpdateAnalytics(c *gin.Context) {
	idStr := c.Param("id")
	convertItemID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid convert item ID"})
		return
	}

	var analytics models.ContentAnalytics
	if err := c.ShouldBindJSON(&analytics); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := api.metadataRepo.UpdateAnalytics(ctx, convertItemID, analytics); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Analytics updated successfully"})
}

// AddProcessingLog adds a processing log entry
func (api *MongoExampleAPI) AddProcessingLog(c *gin.Context) {
	idStr := c.Param("id")
	convertItemID, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid convert item ID"})
		return
	}

	var log models.ProcessingLog
	if err := c.ShouldBindJSON(&log); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := api.metadataRepo.AddProcessingLog(ctx, convertItemID, log); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Processing log added successfully"})
}
