package middleware

import (
	"net/http"
	"showfer-web/service/logger"

	"github.com/gin-gonic/gin"
)

// ErrorResponse represents the structure of an error response
type ErrorResponse struct {
	Error   string `json:"error"`
	Message string `json:"message"`
	Status  int    `json:"status"`
}

// ErrorHandler middleware handles errors and returns appropriate responses
func ErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// Check if there are any errors
		if len(c.Errors) > 0 {
			// Get the last error
			err := c.Errors.Last()
			
			// Log the error
			logger.Error("API Error: %v", err.Err)

			// Return error response
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error:   "internal_server_error",
				Message: err.Error(),
				Status:  http.StatusInternalServerError,
			})
		}
	}
}
