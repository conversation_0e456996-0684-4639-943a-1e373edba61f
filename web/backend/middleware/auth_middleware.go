package middleware

import (
	"net/http"
	"showfer-web/service/auth"
	"showfer-web/service/logger"
	"strings"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware is a middleware that checks if the request has a valid JWT token
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get the Authorization header
		authHeader := c.<PERSON>eader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Error:   "unauthorized",
				Message: "Authorization header is required",
				Status:  http.StatusUnauthorized,
			})
			c.Abort()
			return
		}

		// Check if the Authorization header has the correct format
		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Error:   "unauthorized",
				Message: "Authorization header format must be Bearer {token}",
				Status:  http.StatusUnauthorized,
			})
			c.Abort()
			return
		}

		// Extract the token
		tokenString := parts[1]

		// Validate the token
		claims, err := auth.ValidateToken(tokenString)
		if err != nil {
			logger.Error("Invalid token: %v", err)
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Error:   "unauthorized",
				Message: "Invalid or expired token",
				Status:  http.StatusUnauthorized,
			})
			c.Abort()
			return
		}

		// Set user information in the context
		c.Set("userID", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("userRole", claims.Role)

		c.Next()
	}
}

// DownloadAuthMiddleware is a middleware that checks for a valid JWT token in either
// the Authorization header or the token query parameter
func DownloadAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		var tokenString string

		// First check for token in query parameter
		tokenParam := c.Query("token")
		if tokenParam != "" {
			tokenString = tokenParam
		} else {
			// If not in query, check Authorization header
			authHeader := c.GetHeader("Authorization")
			if authHeader == "" {
				c.JSON(http.StatusUnauthorized, ErrorResponse{
					Error:   "unauthorized",
					Message: "Authorization required",
					Status:  http.StatusUnauthorized,
				})
				c.Abort()
				return
			}

			// Check if the Authorization header has the correct format
			parts := strings.Split(authHeader, " ")
			if len(parts) != 2 || parts[0] != "Bearer" {
				c.JSON(http.StatusUnauthorized, ErrorResponse{
					Error:   "unauthorized",
					Message: "Authorization header format must be Bearer {token}",
					Status:  http.StatusUnauthorized,
				})
				c.Abort()
				return
			}

			// Extract the token
			tokenString = parts[1]
		}

		// Validate the token
		claims, err := auth.ValidateToken(tokenString)
		if err != nil {
			logger.Error("Invalid token: %v", err)
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Error:   "unauthorized",
				Message: "Invalid or expired token",
				Status:  http.StatusUnauthorized,
			})
			c.Abort()
			return
		}

		// Set user information in the context
		c.Set("userID", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("userRole", claims.Role)

		c.Next()
	}
}

// WebSocketAuthMiddleware is a middleware for WebSocket connections that checks for a valid JWT token
// in the query parameter since WebSocket connections can't use Authorization headers
func WebSocketAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get token from query parameter
		tokenString := c.Query("token")
		if tokenString == "" {
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Error:   "unauthorized",
				Message: "Token query parameter is required for WebSocket connections",
				Status:  http.StatusUnauthorized,
			})
			c.Abort()
			return
		}

		// Validate the token
		claims, err := auth.ValidateToken(tokenString)
		if err != nil {
			logger.Error("Invalid WebSocket token: %v", err)
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Error:   "unauthorized",
				Message: "Invalid or expired token",
				Status:  http.StatusUnauthorized,
			})
			c.Abort()
			return
		}

		// Set user information in the context
		c.Set("userID", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("userRole", claims.Role)

		c.Next()
	}
}

// RoleMiddleware checks if the user has the required role
func RoleMiddleware(roles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user role from context
		userRole, exists := c.Get("userRole")
		if !exists {
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Error:   "unauthorized",
				Message: "User role not found in context",
				Status:  http.StatusUnauthorized,
			})
			c.Abort()
			return
		}

		// Check if user has one of the required roles
		hasRole := false
		for _, role := range roles {
			if userRole == role {
				hasRole = true
				break
			}
		}

		if !hasRole {
			c.JSON(http.StatusForbidden, ErrorResponse{
				Error:   "forbidden",
				Message: "You don't have permission to access this resource",
				Status:  http.StatusForbidden,
			})
			c.Abort()
			return
		}

		c.Next()
	}
}
