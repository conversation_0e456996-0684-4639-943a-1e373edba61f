package test

import (
	"fmt"
	"log"
	"os"
	"showfer-web/service/recorder"
)

func main() {
	// Set up logging
	log.SetOutput(os.Stdout)
	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)

	// Test URLs
	urls := []string{
		"rtp://***********:5001",
		"rtp://***********:5001?iface=ens33",
		"rtp://***********:5001?localaddr=************",
		"rtp://***********:5001?iface=ens33&localaddr=************",
	}

	for _, url := range urls {
		fmt.Printf("\n--- Testing URL: %s ---\n", url)
		
		// Parse the URL
		ipInfo, err := recorder.ParseRtpURL(url)
		if err != nil {
			fmt.Printf("Error parsing URL: %v\n", err)
			continue
		}
		
		fmt.Printf("Parsed URL: %+v\n", ipInfo)
		
		// Check if the URL is working
		isWorking := recorder.CheckRtpUrlIsWorking(url)
		fmt.Printf("URL is working: %v\n", isWorking)
	}
}
