package models

import (
	"time"
)

// LogLevel represents the severity level of a log entry
type LogLevel string

const (
	LogLevelDebug LogLevel = "debug"
	LogLevelInfo  LogLevel = "info"
	LogLevelWarn  LogLevel = "warn"
	LogLevelError LogLevel = "error"
	LogLevelFatal LogLevel = "fatal"
)

// LogEntry represents a log entry in the database
type LogEntry struct {
	ID        int64     `json:"id" db:"id"`
	Timestamp time.Time `json:"timestamp" db:"timestamp"`
	Level     LogLevel  `json:"level" db:"level"`
	Message   string    `json:"message" db:"message"`
	Source    string    `json:"source" db:"source"`       // Module/service that generated the log
	UserID    *int64    `json:"user_id" db:"user_id"`     // User ID if applicable
	RequestID *string   `json:"request_id" db:"request_id"` // Request ID for tracing
	Metadata  *string   `json:"metadata" db:"metadata"`   // JSON metadata
	CreatedAt time.Time `json:"created_at" db:"created_at"`
}

// LogFilter represents filters for querying logs
type LogFilter struct {
	StartDate *time.Time `json:"start_date" form:"start_date"`
	EndDate   *time.Time `json:"end_date" form:"end_date"`
	Level     *LogLevel  `json:"level" form:"level"`
	Source    *string    `json:"source" form:"source"`
	UserID    *int64     `json:"user_id" form:"user_id"`
	Search    *string    `json:"search" form:"search"`
	Limit     int        `json:"limit" form:"limit"`
	Offset    int        `json:"offset" form:"offset"`
}

// LogStats represents statistics about logs
type LogStats struct {
	TotalLogs    int64            `json:"total_logs"`
	LogsByLevel  map[LogLevel]int `json:"logs_by_level"`
	LogsBySource map[string]int   `json:"logs_by_source"`
	LastLogTime  *time.Time       `json:"last_log_time"`
}

// LogExportRequest represents a request to export logs
type LogExportRequest struct {
	Format string    `json:"format" binding:"required"` // csv, json
	Filter LogFilter `json:"filter"`
}

// LogExportResponse represents the response for log export
type LogExportResponse struct {
	DownloadURL string `json:"download_url"`
	ExpiresAt   string `json:"expires_at"`
}
