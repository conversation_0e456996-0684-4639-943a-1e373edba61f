package models

import "time"

// ContentAnalytics represents analytics data for content playback
type ContentAnalytics struct {
	ID          int64   `json:"id"`
	ScheduleID  int64   `json:"schedule_id"`
	ItemID      int64   `json:"item_id,omitempty"`
	ContentName string  `json:"content_name"`
	ContentPath string  `json:"content_path"`
	RTPOutput   string  `json:"rtp_output"`
	PlayedAt    string  `json:"played_at"`
	Duration    float64 `json:"duration"`
	PlayType    string  `json:"play_type"` // e.g., "scheduled", "filler", "ad"
}

// NewContentAnalytics creates a new ContentAnalytics entry
func NewContentAnalytics(scheduleID int64, contentName, contentPath, rtpOutput, playType string) ContentAnalytics {
	return ContentAnalytics{
		ScheduleID:  scheduleID,
		ContentName: contentName,
		ContentPath: contentPath,
		RTPOutput:   rtpOutput,
		PlayedAt:    time.Now().UTC().Format(time.RFC3339),
		PlayType:    playType,
	}
}

// ContentAnalyticsListResult represents the result of listing content analytics entries
type ContentAnalyticsListResult struct {
	Items      []ContentAnalytics `json:"items"`
	TotalItems int                `json:"total_items"`
	TotalPages int                `json:"total_pages"`
	Page       int                `json:"page"`
	Limit      int                `json:"limit"`
}

// ContentAnalyticsSummary represents a summary of content analytics for a specific piece of content
type ContentAnalyticsSummary struct {
	ContentName    string  `json:"content_name"`
	ContentPath    string  `json:"content_path"`
	TotalPlays     int     `json:"total_plays"`
	TotalDuration  float64 `json:"total_duration"`
	LastPlayedAt   string  `json:"last_played_at"`
	OutputChannels []string `json:"output_channels"`
}

// ScheduleAnalyticsSummary represents analytics data for a specific schedule
type ScheduleAnalyticsSummary struct {
	ScheduleID      int64                  `json:"schedule_id"`
	ScheduleName    string                 `json:"schedule_name"`
	TotalContentPlayed int                 `json:"total_content_played"`
	TotalPlayTime   float64                `json:"total_play_time"`
	TopContent      []ContentAnalyticsSummary `json:"top_content"`
	RTPOutputs      []string               `json:"rtp_outputs"`
}

// AnalyticsStats represents aggregate statistics for analytics queries
type AnalyticsStats struct {
	TotalPlays      int     `json:"total_plays"`
	TotalDuration   float64 `json:"total_duration"`
	UniqueOutputs   int     `json:"unique_outputs"`
} 