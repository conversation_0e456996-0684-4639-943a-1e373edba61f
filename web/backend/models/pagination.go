package models

// Pagination represents pagination parameters
type Pagination struct {
	Page  int `json:"page" form:"page"`
	Limit int `json:"limit" form:"limit"`
}

// RecorderListResult represents the result of listing recorders with pagination
type RecorderListResult struct {
	Items      []Recorder `json:"items"`
	TotalItems int        `json:"totalItems"`
	TotalPages int        `json:"totalPages"`
	Page       int        `json:"page"`
	Limit      int        `json:"limit"`
}
