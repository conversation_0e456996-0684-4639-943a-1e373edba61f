package models

import "time"

// RetranscodeJob represents a retranscoding job
type RetranscodeJob struct {
	ID         int64     `json:"id"`
	FileID     int64     `json:"file_id"`
	Status     string    `json:"status"` // "pending", "processing", "completed", "failed"
	Progress   int       `json:"progress"` // 0-100
	StartedAt  *time.Time `json:"started_at"`
	CompletedAt *time.Time `json:"completed_at"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// RetranscodeStatus represents the overall retranscoding status
type RetranscodeStatus struct {
	IsActive       bool        `json:"is_active"`
	TotalJobs      int         `json:"total_jobs"`
	CompletedJobs  int         `json:"completed_jobs"`
	FailedJobs     int         `json:"failed_jobs"`
	ProcessingJobs int         `json:"processing_jobs"`
	PendingJobs    int         `json:"pending_jobs"`
	OverallProgress int        `json:"overall_progress"`
	CurrentJob     *CurrentJobInfo `json:"current_job,omitempty"`
}

// CurrentJobInfo represents information about the currently processing job
type CurrentJobInfo struct {
	FileID   int64  `json:"file_id"`
	Filename string `json:"filename"`
	Progress int    `json:"progress"`
}

// RetranscodeJobInput represents input for creating retranscode jobs
type RetranscodeJobInput struct {
	FileIDs []int64 `json:"file_ids" binding:"required"`
}

// RetranscodeJobStatus represents job status constants
const (
	RetranscodeStatusPending    = "pending"
	RetranscodeStatusProcessing = "processing" 
	RetranscodeStatusCompleted  = "completed"
	RetranscodeStatusFailed     = "failed"
) 