package models

// GeneralSettings represents the default general settings for recorders
type GeneralSettings struct {
	ID                int `json:"id"`
	TranscoderThreads int `json:"transcoder_threads"`
}

// GeneralSettingsInput represents the input for updating general settings
type GeneralSettingsInput struct {
	TranscoderThreads int `json:"transcoder_threads" binding:"required"`
}

// DefaultGeneralSettings returns the default general settings
func DefaultGeneralSettings() GeneralSettings {
	return GeneralSettings{
		TranscoderThreads: 3,
	}
}
