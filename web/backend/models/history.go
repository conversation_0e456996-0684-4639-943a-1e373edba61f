package models

type History struct {
	ID         int64  `json:"id"`
	ScheduleId int64  `json:"schedule_id"`
	Folder     string `json:"folder"`
	FileId     int64  `json:"file_id"`
	Episode    int    `json:"episode"`
	CreatedAt  string `json:"created_at"`
}

func NewHistory(scheduleId int64, folder string, fileId int64, episode int) History {
	return History{
		ScheduleId: scheduleId,
		Folder:     folder,
		FileId:     fileId,
		Episode:    episode,
	}
}
