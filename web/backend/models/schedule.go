package models

// ScheduleListResult represents the result of listing convert items
type ScheduleListResult struct {
	Items      []Schedule `json:"items"`
	TotalItems int        `json:"total_items"`
	TotalPages int        `json:"total_pages"`
	Page       int        `json:"page"`
	Limit      int        `json:"limit"`
}

type FileInfo struct {
	ID   int64  `json:"id"`
	Path string `json:"path"`
}

type Item struct {
	Start       string     `json:"start"`
	End         string     `json:"end"`
	Type        string     `json:"type"`
	Connection  string     `json:"connection"`
	Link        string     `json:"link"`
	Name        string     `json:"name"`
	Description string     `json:"description"`
	Port        string     `json:"port"`
	Mode        string     `json:"mode"`
	ExpireDate  string     `json:"expire_date"`
	ExpireTime  string     `json:"expire_time"`
	Folders     []string   `json:"folders"`
	Files       []FileInfo `json:"files"`
	Fillers     Fillers    `json:"fillers"`
}

type Day struct {
	Name  string `json:"name"`
	Date  string `json:"date"`
	Items []Item `json:"items"`
}

type AdLink struct {
	Url     string `json:"url"`
	Channel int    `json:"channel"`
}

type Ads struct {
	Links   []AdLink   `json:"links"`
	Folders []string   `json:"folders"`
	Files   []FileInfo `json:"files"`
}

type Fillers struct {
	Folders   []string   `json:"folders"`
	Files     []FileInfo `json:"files"`
	PreFiller FileInfo   `json:"pre_filler"`
}

type Schedule struct {
	ID               int64         `json:"id"`
	Name             string        `json:"name"`
	Icon             string        `json:"icon"`
	Timezone         string        `json:"timezone"`
	CreatedAt        string        `json:"created_at"`
	UpdatedAt        string        `json:"updated_at"`
	ShortID          string        `json:"short_id"`
	Autosave         bool          `json:"autosave"`
	OutputUrl        string        `json:"output_url"`
	NetworkInterface string        `json:"network_interface"`
	Ads              Ads           `json:"ads"`
	Channels         []interface{} `json:"channels"`
	RegularDays      []Day         `json:"regular_days"`
	SpecialDays      []Day         `json:"special_days"`
	Fillers          Fillers       `json:"fillers"`
}

// SchedulerStatus represents the status of a scheduler
type SchedulerStatus struct {
	ID        int64  `json:"id"`
	ShortID   string `json:"short_id"`
	IsActive  bool   `json:"is_active"`
	RtpSynced bool   `json:"rtp_synced"`
}
