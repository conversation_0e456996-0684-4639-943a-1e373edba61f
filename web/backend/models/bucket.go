package models

import (
	"time"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Bucket represents a storage bucket configuration
type Bucket struct {
	ID        primitive.ObjectID   `json:"_id,omitempty" bson:"_id,omitempty"`
	Title     string              `json:"title" bson:"title"`
	BucketName string             `json:"bucketName" bson:"bucketName"`
	IsShowAll bool               `json:"isShowAll" bson:"isShowAll"`
	Default   bool               `json:"default" bson:"default"`
	User      []primitive.ObjectID `json:"user" bson:"user"`
	CreatedAt time.Time          `json:"createdAt" bson:"createdAt"`
	UpdatedAt time.Time          `json:"updatedAt" bson:"updatedAt"`
	DateCache *time.Time         `json:"dateCache,omitempty" bson:"dateCache,omitempty"`
	Version   int                `json:"__v" bson:"__v"`
}

// BucketCreateInput represents the input for creating a bucket
type BucketCreateInput struct {
	Title     string              `json:"title" binding:"required"`
	BucketName string             `json:"bucketName" binding:"required"`
	IsShowAll bool               `json:"isShowAll"`
	Default   bool               `json:"default"`
	User      []primitive.ObjectID `json:"user"`
}

// BucketUpdateInput represents the input for updating a bucket
type BucketUpdateInput struct {
	Title     *string             `json:"title,omitempty"`
	BucketName *string            `json:"bucketName,omitempty"`
	IsShowAll *bool              `json:"isShowAll,omitempty"`
	Default   *bool              `json:"default,omitempty"`
	User      []primitive.ObjectID `json:"user,omitempty"`
}
