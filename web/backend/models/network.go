package models

// NetworkInterface represents a network interface configuration
type NetworkInterface struct {
	Name       string `json:"name"`
	IPAddress  string `json:"ip_address"`
	Netmask    string `json:"netmask"`
	Gateway    string `json:"gateway"`
	DNSServers string `json:"dns_servers"`
	IsActive   bool   `json:"is_active"`
	IsWireless bool   `json:"is_wireless"`
	MacAddress string `json:"mac_address"`
}

// NetworkInterfaceInput represents the input for updating a network interface
type NetworkInterfaceInput struct {
	IPAddress  string `json:"ip_address" binding:"required,ip"`
	Netmask    string `json:"netmask" binding:"required"`
	Gateway    string `json:"gateway" binding:"required,ip"`
	DNSServers string `json:"dns_servers" binding:"required"`
}
