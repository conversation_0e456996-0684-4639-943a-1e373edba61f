package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// MongoExample represents a document stored in MongoDB
// This is an example model to demonstrate MongoDB usage alongside PostgreSQL
type MongoExample struct {
	ID          primitive.ObjectID     `bson:"_id,omitempty" json:"id"`
	Title       string                 `bson:"title" json:"title"`
	Description string                 `bson:"description" json:"description"`
	Tags        []string               `bson:"tags" json:"tags"`
	Metadata    map[string]interface{} `bson:"metadata" json:"metadata"`
	CreatedAt   time.Time              `bson:"created_at" json:"created_at"`
	UpdatedAt   time.Time              `bson:"updated_at" json:"updated_at"`
	IsActive    bool                   `bson:"is_active" json:"is_active"`
}

// UserSession represents user session data stored in MongoDB
// This demonstrates storing session data in MongoDB while user data is in PostgreSQL
type UserSession struct {
	ID        primitive.ObjectID     `bson:"_id,omitempty" json:"id"`
	UserID    int64                  `bson:"user_id" json:"user_id"` // References PostgreSQL users table
	SessionID string                 `bson:"session_id" json:"session_id"`
	Data      map[string]interface{} `bson:"data" json:"data"`
	ExpiresAt time.Time              `bson:"expires_at" json:"expires_at"`
	CreatedAt time.Time              `bson:"created_at" json:"created_at"`
	UpdatedAt time.Time              `bson:"updated_at" json:"updated_at"`
}

// ContentMetadata represents rich metadata for content items
// This demonstrates storing complex metadata in MongoDB while basic content info is in PostgreSQL
type ContentMetadata struct {
	ID             primitive.ObjectID     `bson:"_id,omitempty" json:"id"`
	ConvertItemID  int64                  `bson:"convert_item_id" json:"convert_item_id"` // References PostgreSQL convert_items table
	TechnicalInfo  TechnicalInfo          `bson:"technical_info" json:"technical_info"`
	Analytics      MongoContentAnalytics  `bson:"analytics" json:"analytics"`
	CustomFields   map[string]interface{} `bson:"custom_fields" json:"custom_fields"`
	ProcessingLogs []ProcessingLog        `bson:"processing_logs" json:"processing_logs"`
	CreatedAt      time.Time              `bson:"created_at" json:"created_at"`
	UpdatedAt      time.Time              `bson:"updated_at" json:"updated_at"`
}

// TechnicalInfo represents technical information about media files
type TechnicalInfo struct {
	VideoCodec    string  `bson:"video_codec" json:"video_codec"`
	AudioCodec    string  `bson:"audio_codec" json:"audio_codec"`
	Resolution    string  `bson:"resolution" json:"resolution"`
	Bitrate       int64   `bson:"bitrate" json:"bitrate"`
	FrameRate     float64 `bson:"frame_rate" json:"frame_rate"`
	AspectRatio   string  `bson:"aspect_ratio" json:"aspect_ratio"`
	ColorSpace    string  `bson:"color_space" json:"color_space"`
	AudioChannels int     `bson:"audio_channels" json:"audio_channels"`
	SampleRate    int     `bson:"sample_rate" json:"sample_rate"`
}

// MongoContentAnalytics represents analytics data for content
type MongoContentAnalytics struct {
	ViewCount       int64         `bson:"view_count" json:"view_count"`
	LastViewed      time.Time     `bson:"last_viewed" json:"last_viewed"`
	TotalDuration   int64         `bson:"total_duration" json:"total_duration"` // in seconds
	PopularSegments []TimeSegment `bson:"popular_segments" json:"popular_segments"`
	UserRatings     []UserRating  `bson:"user_ratings" json:"user_ratings"`
}

// TimeSegment represents a time segment in content
type TimeSegment struct {
	StartTime int64 `bson:"start_time" json:"start_time"` // in seconds
	EndTime   int64 `bson:"end_time" json:"end_time"`     // in seconds
	ViewCount int64 `bson:"view_count" json:"view_count"`
}

// UserRating represents a user rating
type UserRating struct {
	UserID    int64     `bson:"user_id" json:"user_id"`
	Rating    float64   `bson:"rating" json:"rating"`
	Comment   string    `bson:"comment" json:"comment"`
	CreatedAt time.Time `bson:"created_at" json:"created_at"`
}

// ProcessingLog represents a log entry for content processing
type ProcessingLog struct {
	Timestamp time.Time              `bson:"timestamp" json:"timestamp"`
	Level     string                 `bson:"level" json:"level"`
	Message   string                 `bson:"message" json:"message"`
	Details   map[string]interface{} `bson:"details" json:"details"`
}
