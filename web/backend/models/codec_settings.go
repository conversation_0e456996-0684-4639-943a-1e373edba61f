package models

// CodecSettings represents the default codec settings for recorders
type CodecSettings struct {
	ID              int     `json:"id"`
	VCodec          string  `json:"vcodec"`
	ACodec          string  `json:"acodec"`
	Resolution      string  `json:"resolution"`
	FPS             float64 `json:"fps"`
	SampleRate      int     `json:"sample_rate"`
	VBitrate        int     `json:"vbitrate"`
	ABitrate        int     `json:"abitrate"`
	MaxVBitrate     int     `json:"max_vbitrate"`
	// New dual audio fields
	DualAudioMode   bool    `json:"dual_audio_mode"`
	Audio1Codec     string  `json:"audio1_codec"`     // 5.1 surround codec (ac3_passthrough, ac3_downmix)
	Audio1Bitrate   int     `json:"audio1_bitrate"`   // Bitrate for 5.1 track
	Audio1Channels  int     `json:"audio1_channels"`  // 6 for 5.1 surround
	Audio2Codec     string  `json:"audio2_codec"`     // Stereo codec (aac_downmix, mpeg1l2_downmix)
	Audio2Bitrate   int     `json:"audio2_bitrate"`   // Bitrate for stereo track
	Audio2Channels  int     `json:"audio2_channels"`  // 2 for stereo
}

// CodecSettingsInput represents the input for updating codec settings
type CodecSettingsInput struct {
	VCodec          string  `json:"vcodec" binding:"required"`
	ACodec          string  `json:"acodec" binding:"required"`
	Resolution      string  `json:"resolution" binding:"required"`
	FPS             float64 `json:"fps" binding:"required"`
	SampleRate      int     `json:"sample_rate" binding:"required"`
	VBitrate        int     `json:"vbitrate" binding:"required"`
	ABitrate        int     `json:"abitrate" binding:"required"`
	MaxVBitrate     int     `json:"max_vbitrate" binding:"required"`
	// New dual audio fields
	DualAudioMode   bool    `json:"dual_audio_mode"`
	Audio1Codec     string  `json:"audio1_codec"`
	Audio1Bitrate   int     `json:"audio1_bitrate"`
	Audio1Channels  int     `json:"audio1_channels"`
	Audio2Codec     string  `json:"audio2_codec"`
	Audio2Bitrate   int     `json:"audio2_bitrate"`
	Audio2Channels  int     `json:"audio2_channels"`
}

// DefaultCodecSettings returns the default codec settings
func DefaultCodecSettings() CodecSettings {
	return CodecSettings{
		VCodec:         "h264",
		ACodec:         "aac_downmix",
		Resolution:     "1920x1080i",
		FPS:            29.97,
		SampleRate:     48000,
		VBitrate:       6000,
		ABitrate:       192,
		MaxVBitrate:    10000,
		// Default dual audio settings
		DualAudioMode:  false,
		Audio1Codec:    "ac3_passthrough", // 5.1 surround passthrough
		Audio1Bitrate:  448,               // Standard AC3 5.1 bitrate
		Audio1Channels: 6,                 // 5.1 channels
		Audio2Codec:    "aac_downmix",     // Stereo downmix
		Audio2Bitrate:  192,               // Standard stereo bitrate
		Audio2Channels: 2,                 // Stereo channels
	}
}
