package models

// Recorder represents a recorder configuration
type Recorder struct {
	ID               int     `json:"id"`
	Name             string  `json:"name"`
	Input            string  `json:"input"`
	RtpUrlID         int64   `json:"rtp_url_id"`
	Duration         string  `json:"duration"`
	Status           string  `json:"status"`
	VCodec           string  `json:"VCodec"`
	ACodec           string  `json:"ACodec"`
	Resolution       string  `json:"resolution"`
	FPS              float64 `json:"FPS"`
	SampleRate       int     `json:"sampleRate"`
	VBitrate         int     `json:"VBitrate"`
	ABitrate         int     `json:"ABitrate"`
	MaxVBitrate      int     `json:"MaxVBitrate"`
	NetworkInterface string  `json:"network_interface"` // Network interface to use for receiving RTP stream
	SourceIP         string  `json:"source_ip"`         // Source IP address for iptables filtering
	ServiceID        *int    `json:"service_id"`        // Service ID selected when recording starts
	IsScheduled      bool    `json:"is_scheduled"`
	ScheduledStartTime *string `json:"scheduled_start_time"` // Pointer to allow null values
	CreatedAt        string  `json:"created_at"`
	UpdatedAt        string  `json:"updated_at"`
}

// RecorderInput represents the input for creating a recorder
type RecorderInput struct {
	Name                string  `json:"name" binding:"required"`
	Input               string  `json:"input" binding:"required"`
	Duration            string  `json:"duration" binding:"required"`
	VCodec              string  `json:"vcodec" binding:"required"`
	ACodec              string  `json:"acodec" binding:"required"`
	Resolution          string  `json:"resolution" binding:"required"`
	FPS                 float64 `json:"fps" binding:"required"`
	SampleRate          int     `json:"sample_rate" binding:"required"`
	VBitrate            int     `json:"vbitrate" binding:"required"`
	ABitrate            int     `json:"abitrate" binding:"required"`
	MaxVBitrate         int     `json:"max_vbitrate" binding:"required"`
	NetworkInterface    string  `json:"network_interface"` // Network interface to use for receiving RTP stream
	SourceIP            string  `json:"source_ip"`         // Source IP address for iptables filtering
	IsScheduled         bool    `json:"is_scheduled"`
	ScheduledStartTime  *string `json:"scheduled_start_time"` // Pointer to allow null values
}

// RecorderStatusUpdate represents a status update for a recorder
type RecorderStatusUpdate struct {
	ID     int    `json:"id"`
	Status string `json:"status"`
}

// RecorderStatus represents the status of a recorder
type RecorderStatus struct {
	ID              int           `json:"id"`
	RtpURL          string        `json:"rtp_url"`
	OutputDir       string        `json:"output_dir"`
	ElapsedSeconds  int           `json:"elapsed_seconds"`
	DurationSeconds int           `json:"duration_seconds"`
	IsActive        bool          `json:"is_active"`
	IsJoined        bool          `json:"is_joined"` // Whether the recorder is joined (either active or in joined state)
	StartTime       string        `json:"start_time"`
	TsSync          bool          `json:"ts_sync"`
	Services        []ServiceInfo `json:"services"`
	VideoInfo       VideoInfo     `json:"video_info"`
	AudioInfo       AudioInfo     `json:"audio_info"`
}

// ServiceInfo represents information about a service in the TS stream
type ServiceInfo struct {
	ServiceID        int         `json:"service_id"`
	ServiceName      string      `json:"service_name"`
	ProviderName     string      `json:"provider_name,omitempty"`
	VideoCodec       string      `json:"video_codec,omitempty"`
	AudioCodec       string      `json:"audio_codec,omitempty"`        // Legacy single audio codec (for backwards compatibility)
	VideoResolution  string      `json:"video_resolution,omitempty"`
	AudioChannels    int         `json:"audio_channels,omitempty"`      // Legacy single audio channels (for backwards compatibility)
	AudioTracks      []AudioInfo `json:"audio_tracks,omitempty"`        // Multiple audio tracks support
}

// VideoInfo represents information about the video stream
type VideoInfo struct {
	Resolution string `json:"resolution"`
	Codec      string `json:"codec"`
}

// AudioInfo represents information about the audio stream
type AudioInfo struct {
	Channels int    `json:"channels"`
	Codec    string `json:"codec"`
}