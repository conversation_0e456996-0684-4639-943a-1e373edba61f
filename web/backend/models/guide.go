package models

type Connection struct {
	Type       string `json:"type"`
	Link       string `json:"link"`
	Port       string `json:"port"`
	Mode       string `json:"mode"`
	ExpireDate string `json:"expire_date"`
	ExpireTime string `json:"expire_time"`
}

type File struct {
	FileId  int64  `json:"file_id"`
	Folder  string `json:"folder"`
	Episode string `json:"episode"`
}

type Element struct {
	Start       string     `json:"start"`
	End         string     `json:"end"`
	Title       string     `json:"title"`
	Description string     `json:"description"`
	Type        string     `json:"type"`
	Connection  Connection `json:"connection"`
	File        File       `json:"file"`
	Fillers     Fillers    `json:"fillers"`
}

type Guide struct {
	ID         int       `json:"id"`
	ScheduleId int64     `json:"schedule_id"`
	Elements   []Element `json:"elements"`
	CreatedAt  string    `json:"created_at"`
	UpdatedAt  string    `json:"updated_at"`
}

type UpdatedConvertItem struct {
	ID          int64  `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Episode     string `json:"episode"`
}

type UpdatedConvertItems struct {
	Items []UpdatedConvertItem `json:"items"`
}

type NewProgram struct {
	Id   int64  `json:"id"`
	Path string `json:"path"`
}
type ChangeProgram struct {
	OriginalProgram Element    `json:"original_program"`
	NewProgram      NewProgram `json:"new_file"`
}
