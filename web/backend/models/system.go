package models

import "time"

// SystemStats represents system resource usage statistics
type SystemStats struct {
	Timestamp time.Time `json:"timestamp"`

	// Disk usage
	DiskTotal uint64  `json:"disk_total"`
	DiskUsed  uint64  `json:"disk_used"`
	DiskFree  uint64  `json:"disk_free"`
	DiskUsage float64 `json:"disk_usage"` // Percentage (0-100)

	// CPU usage
	CPUUsage float64 `json:"cpu_usage"` // Percentage (0-100)

	// Memory usage
	MemoryTotal uint64  `json:"memory_total"`
	MemoryUsed  uint64  `json:"memory_used"`
	MemoryFree  uint64  `json:"memory_free"`
	MemoryUsage float64 `json:"memory_usage"` // Percentage (0-100)

	// GPU information (only available if GPU is detected)
	GPUAvailable bool    `json:"gpu_available"` // true if GPU is available
	GPUModel     string  `json:"gpu_model"`     // GPU model name
	GPUMemTotal  uint64  `json:"gpu_mem_total"` // Total GPU memory in bytes
	GPUMemUsed   uint64  `json:"gpu_mem_used"`  // Used GPU memory in bytes
	GPUMemFree   uint64  `json:"gpu_mem_free"`  // Free GPU memory in bytes
	GPUUsage     float64 `json:"gpu_usage"`     // GPU utilization percentage (0-100)
	GPUTemp      float64 `json:"gpu_temp"`      // GPU temperature in Celsius

	// Alarm status
	DiskAlarm   bool `json:"disk_alarm"`   // true if disk usage > threshold
	CPUAlarm    bool `json:"cpu_alarm"`    // true if CPU usage > threshold
	MemoryAlarm bool `json:"memory_alarm"` // true if memory usage > threshold
	GPUAlarm    bool `json:"gpu_alarm"`    // true if GPU usage > threshold
}

// AlarmThresholds represents the thresholds for triggering alarms
type AlarmThresholds struct {
	DiskThreshold   float64 `json:"disk_threshold"`   // Percentage (0-100)
	CPUThreshold    float64 `json:"cpu_threshold"`    // Percentage (0-100)
	MemoryThreshold float64 `json:"memory_threshold"` // Percentage (0-100)
	GPUThreshold    float64 `json:"gpu_threshold"`    // Percentage (0-100)
}

// DefaultAlarmThresholds returns the default alarm thresholds (80%)
func DefaultAlarmThresholds() AlarmThresholds {
	return AlarmThresholds{
		DiskThreshold:   80.0,
		CPUThreshold:    80.0,
		MemoryThreshold: 80.0,
		GPUThreshold:    80.0,
	}
}
