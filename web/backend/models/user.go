package models

import "time"

// User represents a user in the system
type User struct {
	ID        int       `json:"id"`
	Username  string    `json:"username"`
	Email     string    `json:"email"`
	Password  string    `json:"-"` // Password is not included in JSON responses
	Role      string    `json:"role"`
	Status    string    `json:"status"` // Status can be "pending", "approved", or "rejected"
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// UserInput represents the input for creating a user
type UserInput struct {
	Username string `json:"username" binding:"required"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
	Role     string `json:"role" binding:"required"`
	Status   string `json:"status"`
}

// UserUpdateInput represents the input for updating a user
type UserUpdateInput struct {
	Username string `json:"username" binding:"required"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"omitempty,min=6"` // Password is optional for updates
	Role     string `json:"role" binding:"required"`
	Status   string `json:"status"`
}

// LoginInput represents the input for user login
type LoginInput struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse represents the response for a successful login
type LoginResponse struct {
	Token string `json:"token"`
	User  User   `json:"user"`
}
