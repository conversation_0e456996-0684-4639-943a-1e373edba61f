package models

import (
	"time"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ConvertItemMongo represents a convert item from MongoDB
type ConvertItemMongo struct {
	ID                     primitive.ObjectID   `json:"_id,omitempty" bson:"_id,omitempty"`
	Logs                   []string            `json:"logs" bson:"logs"`
	FileName               string              `json:"fileName" bson:"fileName"`
	Status                 string              `json:"status" bson:"status"`
	Location               string              `json:"location" bson:"location"`
	Storage                []primitive.ObjectID `json:"storage" bson:"storage"`
	Version                int                 `json:"__v" bson:"__v"`
	CreatedAt              time.Time           `json:"createdAt" bson:"createdAt"`
	UpdatedAt              time.Time           `json:"updatedAt" bson:"updatedAt"`
	Filler                 bool                `json:"filler" bson:"filler"`
	Duration               int                 `json:"duration" bson:"duration"`
	Name                   string              `json:"name" bson:"name"`
	CFilename              string              `json:"c_filename" bson:"c_filename"`
	CK                     string              `json:"c_k" bson:"c_k"`
	CLocation              string              `json:"c_location" bson:"c_location"`
	IsSix                  bool                `json:"is_six" bson:"is_six"`
	Description            string              `json:"description" bson:"description"`
	Epnum                  string              `json:"epnum" bson:"epnum"`
	ID1                    string              `json:"id1" bson:"id1"`
	ID2                    string              `json:"id2" bson:"id2"`
	FTP                    bool                `json:"ftp" bson:"ftp"`
	MigrateStatus          int                 `json:"migrateStatus" bson:"migrateStatus"`
	Bucket                 []primitive.ObjectID `json:"bucket" bson:"bucket"`
	IsS3                   bool                `json:"isS3" bson:"isS3"`
	AnalyseLog             []string            `json:"analyseLog" bson:"analyseLog"`
	ResolutionsForConvert  []string            `json:"resolutionsForConvert" bson:"resolutionsForConvert"`
	NeedsReconvert         bool                `json:"needsReconvert" bson:"needsReconvert"`
}

// ConvertItemResponse represents the response format for frontend
type ConvertItemResponse struct {
	ID          string    `json:"id"`
	FileName    string    `json:"filename"`
	Location    string    `json:"location"`
	Duration    int       `json:"duration"`
	Status      string    `json:"status"`
	Size        int64     `json:"size"`
	StorageType string    `json:"storage_type"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	CLocation   string    `json:"c_location"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Episode     string    `json:"episode"`
	Width       int       `json:"width"`
	Height      int       `json:"height"`
	FPS         float64   `json:"fps"`
	VideoCodec  string    `json:"video_codec"`
	AudioCodec  string    `json:"audio_codec"`
	Bitrate     int       `json:"bitrate"`
	RecorderID  string    `json:"recorder_id"`
	CodecSettingsVersion int `json:"codec_settings_version"`
}

// ToResponse converts ConvertItemMongo to ConvertItemResponse
func (c *ConvertItemMongo) ToResponse() ConvertItemResponse {
	return ConvertItemResponse{
		ID:          c.ID.Hex(),
		FileName:    c.FileName,
		Location:    c.Location,
		Duration:    c.Duration,
		Status:      c.Status,
		Size:        0, // Not available in MongoDB schema
		StorageType: "cloud",
		CreatedAt:   c.CreatedAt,
		UpdatedAt:   c.UpdatedAt,
		CLocation:   c.CLocation,
		Name:        c.Name,
		Description: c.Description,
		Episode:     c.Epnum,
		Width:       0,  // Not available in MongoDB schema
		Height:      0,  // Not available in MongoDB schema
		FPS:         0,  // Not available in MongoDB schema
		VideoCodec:  "",  // Not available in MongoDB schema
		AudioCodec:  "",  // Not available in MongoDB schema
		Bitrate:     0,  // Not available in MongoDB schema
		RecorderID:  c.ID1,
		CodecSettingsVersion: 0, // Not available in MongoDB schema
	}
}
