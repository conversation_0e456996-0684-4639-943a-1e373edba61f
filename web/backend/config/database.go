package config

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"showfer-web/repository"
	"strings"
	"time"

	_ "github.com/lib/pq"
)

// Global database instance
var globalDB *sql.DB

// DatabaseConfig holds database configuration
type DatabaseConfig struct {
	Host     string
	Port     string
	User     string
	Password string
	DBName   string
	SSLMode  string
}

// GetDatabaseConfig reads database configuration from environment variables
func GetDatabaseConfig() DatabaseConfig {
	return DatabaseConfig{
		Host:     getEnv("DB_HOST", "localhost"),
		Port:     getEnv("DB_PORT", "5432"),
		User:     getEnv("DB_USER", "postgres"),
		Password: getEnv("DB_PASSWORD", "postgres"),
		DBName:   getEnv("DB_NAME", "showfer"),
		SSLMode:  getEnv("DB_SSLMODE", "disable"),
	}
}

// getEnv gets environment variable with fallback default value
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// restoreFromDumpIfExists checks for and restores from /tmp/showfer_db_dump.sql if it exists
func restoreFromDumpIfExists(config DatabaseConfig) error {
	dumpPath := "/tmp/showfer_db_dump.sql"
	
	// Check if dump file exists
	if _, err := os.Stat(dumpPath); os.IsNotExist(err) {
		// No dump file exists, continue with existing database
		log.Println("No database dump file found, using existing database")
		return nil
	}
	
	log.Printf("Found database dump file at %s, restoring database...", dumpPath)
	
	// First, ensure the database exists (create if it doesn't)
	if err := ensureDatabaseExists(config); err != nil {
		log.Printf("Warning: Failed to ensure database exists: %v", err)
		log.Println("Continuing with existing database...")
		return nil // Don't fail startup, just log the warning
	}
	
	// Terminate existing connections to the database
	if err := terminateConnectionsStartup(config); err != nil {
		log.Printf("Warning: Failed to terminate connections: %v", err)
		// Continue anyway
	}
	
	// Drop and recreate the database for clean restore
	if err := recreateDatabaseStartup(config); err != nil {
		log.Printf("Warning: Failed to recreate database: %v", err)
		log.Println("Continuing with existing database...")
		return nil // Don't fail startup, just log the warning
	}
	
	// Restore database from dump file
	cmd := exec.Command("psql",
		"-h", config.Host,
		"-p", config.Port,
		"-U", config.User,
		"-d", config.DBName,
		"-f", dumpPath,
		"--quiet",
	)
	
	// Set PGPASSWORD environment variable for authentication
	cmd.Env = append(os.Environ(), fmt.Sprintf("PGPASSWORD=%s", config.Password))
	
	// Execute the restore command
	output, err := cmd.CombinedOutput()
	if err != nil {
		log.Printf("Warning: Failed to restore database from dump file: %v, output: %s", err, string(output))
		log.Println("Continuing with existing database...")
		return nil // Don't fail startup, just log the warning
	}
	
	log.Println("Successfully restored database from dump file")
	
	// Note: We keep the dump file so the RestoreService can monitor it for updates
	
	return nil
}

// ensureDatabaseExists creates the database if it doesn't exist
func ensureDatabaseExists(config DatabaseConfig) error {
	// Create database if it doesn't exist
	createCmd := exec.Command("psql",
		"-h", config.Host,
		"-p", config.Port,
		"-U", config.User,
		"-d", "postgres", // Connect to postgres database to create the target
		"-c", fmt.Sprintf("CREATE DATABASE %s;", config.DBName),
		"--quiet",
	)

	createCmd.Env = append(os.Environ(), fmt.Sprintf("PGPASSWORD=%s", config.Password))
	
	output, err := createCmd.CombinedOutput()
	if err != nil {
		// Check if error is because database already exists
		outputStr := string(output)
		errStr := err.Error()
		if (outputStr != "" && strings.Contains(outputStr, "already exists")) || 
		   strings.Contains(errStr, "already exists") {
			log.Printf("Database %s already exists", config.DBName)
			return nil
		}
		return fmt.Errorf("failed to create database: %v, output: %s", err, string(output))
	}

	log.Printf("Created database %s", config.DBName)
	return nil
}

// terminateConnectionsStartup terminates all connections to the target database
func terminateConnectionsStartup(config DatabaseConfig) error {
	cmd := exec.Command("psql",
		"-h", config.Host,
		"-p", config.Port,
		"-U", config.User,
		"-d", "postgres", // Connect to postgres database to terminate connections
		"-c", fmt.Sprintf("SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = '%s' AND pid <> pg_backend_pid();", config.DBName),
		"--quiet",
	)

	cmd.Env = append(os.Environ(), fmt.Sprintf("PGPASSWORD=%s", config.Password))
	
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to terminate connections: %v, output: %s", err, string(output))
	}

	log.Println("Terminated existing database connections")
	return nil
}

// recreateDatabaseStartup drops and recreates the target database
func recreateDatabaseStartup(config DatabaseConfig) error {
	// Drop database
	dropCmd := exec.Command("psql",
		"-h", config.Host,
		"-p", config.Port,
		"-U", config.User,
		"-d", "postgres", // Connect to postgres database to drop the target
		"-c", fmt.Sprintf("DROP DATABASE IF EXISTS %s;", config.DBName),
		"--quiet",
	)

	dropCmd.Env = append(os.Environ(), fmt.Sprintf("PGPASSWORD=%s", config.Password))
	
	output, err := dropCmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to drop database: %v, output: %s", err, string(output))
	}

	log.Println("Dropped existing database")

	// Create database
	createCmd := exec.Command("psql",
		"-h", config.Host,
		"-p", config.Port,
		"-U", config.User,
		"-d", "postgres", // Connect to postgres database to create the target
		"-c", fmt.Sprintf("CREATE DATABASE %s;", config.DBName),
		"--quiet",
	)

	createCmd.Env = append(os.Environ(), fmt.Sprintf("PGPASSWORD=%s", config.Password))
	
	output, err = createCmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to create database: %v, output: %s", err, string(output))
	}

	log.Println("Created new database")
	return nil
}

// InitDB initializes the database connection
func InitDB() (*sql.DB, error) {
	// Create data directory if it doesn't exist
	dataDir := "./data"
	if err := os.MkdirAll(dataDir, 0755); err != nil {
		return nil, err
	}

	// Create recordings directory if it doesn't exist
	recordingsDir := filepath.Join(dataDir, "recordings")
	if err := os.MkdirAll(recordingsDir, 0755); err != nil {
		return nil, err
	}

	// Get database configuration
	config := GetDatabaseConfig()
	
	// Ensure database exists before attempting to restore from dump
	if err := ensureDatabaseExists(config); err != nil {
		return nil, fmt.Errorf("failed to ensure database exists: %w", err)
	}
	
	// Check for and restore from dump file if it exists
	if err := restoreFromDumpIfExists(config); err != nil {
		return nil, fmt.Errorf("failed to restore from dump file: %w", err)
	}
	
	// Build PostgreSQL connection string
	connStr := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		config.Host, config.Port, config.User, config.Password, config.DBName, config.SSLMode)

	// Open database connection
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		return nil, fmt.Errorf("failed to open database connection: %w", err)
	}

	// Test the connection
	if err := db.Ping(); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	// Configure connection pool for PostgreSQL
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(10)
	db.SetConnMaxLifetime(5 * time.Minute)

	// Create tables (this will create any missing tables after restore)
	if err := createTables(db); err != nil {
		db.Close()
		return nil, err
	}

	// Migrate tables
	if err := repository.MigrateTables(db); err != nil {
		db.Close()
		return nil, err
	}

	// Store database globally for access by other packages
	globalDB = db

	log.Println("PostgreSQL database initialized successfully")
	return db, nil
}

// createTables creates all necessary tables in the database
func createTables(db *sql.DB) error {
	// Create tables
	if err := repository.CreateScheduleTable(db); err != nil {
		return err
	}
	if err := repository.CreateRtpUrlTable(db); err != nil {
		return err
	}
	if err := repository.CreateRecorderTable(db); err != nil {
		return err
	}
	if err := repository.CreateConvertItemTable(db); err != nil {
		return err
	}
	if err := repository.CreateGuideTable(db); err != nil {
		return err
	}
	if err := repository.CreateHistoryTable(db); err != nil {
		return err
	}
	if err := repository.CreateUsersTable(db); err != nil {
		return err
	}
	if err := repository.CreateCodecSettingsTable(db); err != nil {
		return err
	}
	if err := repository.CreateGeneralSettingsTable(db); err != nil {
		return err
	}
	if err := repository.CreateContentAnalyticsTable(db); err != nil {
		return err
	}
	if err := repository.CreateLogsTable(db); err != nil {
		return err
	}

	return nil
}

// GetDB returns the global database connection
func GetDB() *sql.DB {
	return globalDB
}
