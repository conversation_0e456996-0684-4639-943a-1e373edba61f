package config

import (
	"database/sql"
	"showfer-web/repository"
)

// createTables creates all necessary tables in the database
func createTables(db *sql.DB) error {
	// Create tables
	if err := repository.CreateScheduleTable(db); err != nil {
		return err
	}
	if err := repository.CreateRtpUrlTable(db); err != nil {
		return err
	}
	if err := repository.CreateRecorderTable(db); err != nil {
		return err
	}
	if err := repository.CreateConvertItemTable(db); err != nil {
		return err
	}
	if err := repository.CreateGuideTable(db); err != nil {
		return err
	}
	if err := repository.CreateHistoryTable(db); err != nil {
		return err
	}
	if err := repository.CreateUsersTable(db); err != nil {
		return err
	}
	if err := repository.CreateCodecSettingsTable(db); err != nil {
		return err
	}
	if err := repository.CreateGeneralSettingsTable(db); err != nil {
		return err
	}
	if err := repository.CreateContentAnalyticsTable(db); err != nil {
		return err
	}
	if err := repository.CreateLogsTable(db); err != nil {
		return err
	}

	return nil
}
