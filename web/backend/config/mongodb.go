package config

import (
	"context"
	"fmt"
	"log"
	"time"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// Global MongoDB instance
var globalMongoDB *mongo.Client

// MongoConfig holds MongoDB database configuration
type MongoConfig struct {
	URI      string
	Database string
	Host     string
	Port     string
	User     string
	Password string
}

// GetMongoConfig reads MongoDB configuration from environment variables
func GetMongoConfig() MongoConfig {
	host := getEnv("MONGO_HOST", "localhost")
	port := getEnv("MONGO_PORT", "27017")
	user := getEnv("MONGO_USER", "")
	password := getEnv("MONGO_PASSWORD", "")
	database := getEnv("MONGO_DATABASE", "showfer_mongo")
	
	// Build MongoDB URI
	var uri string
	if user != "" && password != "" {
		uri = fmt.Sprintf("mongodb://%s:%s@%s:%s/%s", user, password, host, port, database)
	} else {
		uri = fmt.Sprintf("mongodb://%s:%s/%s", host, port, database)
	}
	
	return MongoConfig{
		URI:      uri,
		Database: database,
		Host:     host,
		Port:     port,
		User:     user,
		Password: password,
	}
}

// InitMongoDB initializes the MongoDB connection
func InitMongoDB() (*mongo.Client, error) {
	config := GetMongoConfig()
	
	// Set client options
	clientOptions := options.Client().ApplyURI(config.URI)
	
	// Connect to MongoDB
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	
	client, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to MongoDB: %w", err)
	}
	
	// Test the connection
	err = client.Ping(ctx, nil)
	if err != nil {
		client.Disconnect(ctx)
		return nil, fmt.Errorf("failed to ping MongoDB: %w", err)
	}
	
	// Store MongoDB client globally
	globalMongoDB = client
	
	log.Printf("MongoDB connected successfully to database: %s", config.Database)
	return client, nil
}

// GetMongoDB returns the global MongoDB client
func GetMongoDB() *mongo.Client {
	return globalMongoDB
}

// GetMongoDatabase returns the MongoDB database instance
func GetMongoDatabase() *mongo.Database {
	if globalMongoDB == nil {
		log.Fatal("MongoDB client is not initialized")
	}
	config := GetMongoConfig()
	return globalMongoDB.Database(config.Database)
}

// CloseMongoDB closes the MongoDB connection
func CloseMongoDB() error {
	if globalMongoDB != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		return globalMongoDB.Disconnect(ctx)
	}
	return nil
}
