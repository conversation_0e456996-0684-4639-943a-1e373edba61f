prefix=/usr/local
exec_prefix=${prefix}
libdir=/usr/local/lib
includedir=/usr/local/include

Name: libavdevice
Description: FFmpeg device handling library
Version: 62.0.100
Requires: 
Requires.private: libavfilter >= 11.0.100, libswscale >= 9.0.100, libavformat >= 62.0.102, libavcodec >= 62.3.101, libswresample >= 6.0.100, libavutil >= 60.3.100
Conflicts:
Libs: -L${libdir}  -lavdevice 
Libs.private: -lm -latomic -ljack -lpthread -ldrm -lopenal -lxcb -lcdio_paranoia -lcdio_cdda -lcdio -lm -lasound -lcaca -lpulse -pthread -lXv -lX11 -lXext
Cflags: -I${includedir}
