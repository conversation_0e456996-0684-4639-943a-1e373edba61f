prefix=/usr
exec_prefix=${prefix}
libdir=/lib/x86_64-linux-gnu
includedir=/usr/include/x86_64-linux-gnu

Name: libavcodec
Description: FFmpeg codec library
Version: 58.134.100
Requires: libavutil = 56.70.100
Conflicts:
Libs: -L${libdir} -lavcodec -lswresample -lm
Cflags: -I${includedir}


udpsrc multicast-group=************ port=1234 auto-multicast=true buffer-size=2097152 multicast-iface=eno2 ! queue max-size-bytes=10485760 leaky=2! identity sync=true ! application/x-packetized-mpegts ! filesink location=data/tmp/stream_************_1234_28_1748596065649040618.ts