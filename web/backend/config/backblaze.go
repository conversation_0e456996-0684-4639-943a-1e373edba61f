package config

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"strings"

	"github.com/joho/godotenv"
)

type BackblazeConfig struct {
	KeyID         string
	AppKey        string
	DefaultBucket string
}

// getRootDir returns the root directory of the project
func getRootDir() string {
	_, b, _, _ := runtime.Caller(0)
	return filepath.Join(filepath.Dir(b), "..")
}

func LoadBackblazeConfig() (*BackblazeConfig, error) {
	// Load .env file
	rootDir := getRootDir()
	envFile := filepath.Join(rootDir, ".env")
	log.Printf("Looking for .env file at: %s", envFile)

	// Try to load .env file
	if err := godotenv.Load(envFile); err != nil {
		log.Printf("Could not load .env file: %v", err)
		// If .env doesn't exist, try .env.example
		exampleEnvFile := filepath.Join(rootDir, ".env.example")
		log.Printf("Trying .env.example file at: %s", exampleEnvFile)
		if err := godotenv.Load(exampleEnvFile); err != nil {
			log.Printf("Could not load .env.example file: %v", err)
			// Try environment variables directly
			log.Printf("Checking environment variables directly...")
			return loadFromEnv()
		}
	}

	// Load configuration from environment (now includes both .env and system env)
	return loadFromEnv()
}

func loadFromEnv() (*BackblazeConfig, error) {
	// Get required values
	keyID := os.Getenv("B2_KEY_ID")
	appKey := os.Getenv("B2_APP_KEY")
	defaultBucket := os.Getenv("B2_DEFAULT_BUCKET")

	// Mask all but last 4 characters for logging
	mask := func(s string) string {
		if len(s) <= 4 {
			return "****"
		}
		return strings.Repeat("*", len(s)-4) + s[len(s)-4:]
	}
	log.Printf("Loaded B2_KEY_ID: %s", mask(keyID))
	log.Printf("Loaded B2_APP_KEY: %s", mask(appKey))
	log.Printf("Loaded B2_DEFAULT_BUCKET: %s", defaultBucket)

	if keyID == "" {
		return nil, fmt.Errorf("B2_KEY_ID is not set")
	}
	if appKey == "" {
		return nil, fmt.Errorf("B2_APP_KEY is not set")
	}
	if defaultBucket == "" {
		return nil, fmt.Errorf("B2_DEFAULT_BUCKET is not set")
	}

	log.Printf("Successfully loaded Backblaze configuration")
	return &BackblazeConfig{
		KeyID:         keyID,
		AppKey:        appKey,
		DefaultBucket: defaultBucket,
	}, nil
}
