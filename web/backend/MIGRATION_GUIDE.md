# Migration Guide: SQLite to PostgreSQL

This guide will help you migrate your Showfer application from SQLite to PostgreSQL.

## Prerequisites

1. **PostgreSQL installed** on your system
2. **Access to your current SQLite database** (`./data/showfer.db`)
3. **Go 1.23.2 or later** installed

## Step 1: Install PostgreSQL

### Ubuntu/Debian:

```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

### macOS (using Homebrew):

```bash
brew install postgresql
brew services start postgresql
```

### Docker (Alternative):

```bash
docker run --name showfer-postgres \
  -e POSTGRES_DB=showfer \
  -e POSTGRES_USER=showfer_user \
  -e POSTGRES_PASSWORD=your_password_here \
  -p 5432:5432 \
  -d postgres:15
```

## Step 2: Setup PostgreSQL Database

1. **Connect to PostgreSQL** as superuser:

   ```bash
   sudo -u postgres psql
   ```

2. **Create database and user**:

   ```sql
   CREATE DATABASE showfer;
   CREATE USER showfer_user WITH PASSWORD 'your_password_here';
   GRANT ALL PRIVILEGES ON DATABASE showfer TO showfer_user;
   \q
   ```

3. **Test connection**:
   ```bash
   psql -h localhost -U showfer_user -d showfer
   ```

## Step 3: Configure Environment Variables

Create a `.env` file in the backend directory or set environment variables:

```bash
export DB_HOST=localhost
export DB_PORT=5432
export DB_USER=showfer_user
export DB_PASSWORD=your_password_here
export DB_NAME=showfer
export DB_SSLMODE=disable
```

## Step 4: Update Dependencies

1. **Install PostgreSQL driver**:
   ```bash
   cd backend
   go get github.com/lib/pq
   go mod tidy
   ```

## Step 5: Test PostgreSQL Connection

1. **Test the new configuration** by temporarily starting the app:

   ```bash
   cd backend
   go run main.go
   ```

   You should see: `PostgreSQL database initialized successfully`

2. **Stop the application** (Ctrl+C) after confirming the connection works.

## Step 6: Migrate Your Data

1. **Ensure your SQLite database exists**:

   ```bash
   ls -la ./data/showfer.db
   ```

2. **Check for orphaned records** (optional but recommended):

   ```bash
   cd backend
   go run cmd/check-orphaned/main.go
   ```

   This will show you any orphaned records that reference non-existent parent records. These will be automatically skipped during migration.

3. **Run the migration script**:

   ```bash
   cd backend
   go run cmd/migrate_data.go
   ```

   You should see output like:

   ```
   Starting data migration from SQLite to PostgreSQL...
   Migrating schedules...
   Migrating convert_items...
   Migrating users...
   Migrating guides...
   Migrating history...
   Migrating rtp_urls...
   Migrating recorders...
   Migrating codec_settings...
   Migrating content_analytics...
   Data migration completed successfully!
   ```

## Step 7: Verify Migration

1. **Connect to PostgreSQL** and verify data:

   ```bash
   psql -h localhost -U showfer_user -d showfer
   ```

2. **Check table contents**:
   ```sql
   \dt                          -- List tables
   SELECT COUNT(*) FROM schedules;
   SELECT COUNT(*) FROM convert_items;
   SELECT COUNT(*) FROM users;
   \q
   ```

## Step 8: Start Your Application

1. **Start the application**:

   ```bash
   cd backend
   go run main.go
   ```

2. **Test functionality**:
   - Access your web interface
   - Verify that schedules, files, and users are visible
   - Test creating new items to ensure write operations work

## Step 9: Backup and Cleanup (Optional)

1. **Backup your SQLite database**:

   ```bash
   cp ./data/showfer.db ./data/showfer.db.backup
   ```

2. **After confirming everything works**, you can remove SQLite dependencies from `go.mod` if desired:
   ```bash
   go mod edit -droprequire github.com/mattn/go-sqlite3
   go mod edit -droprequire modernc.org/sqlite
   go mod tidy
   ```

## Troubleshooting

### Connection Issues

1. **Check PostgreSQL is running**:

   ```bash
   sudo systemctl status postgresql
   ```

2. **Verify connection string**:

   ```bash
   psql "host=localhost port=5432 user=showfer_user dbname=showfer sslmode=disable"
   ```

3. **Check environment variables**:
   ```bash
   echo $DB_HOST $DB_PORT $DB_USER $DB_NAME
   ```

### Migration Issues

1. **Column doesn't exist error**: The migration script handles different schema versions, but if you encounter issues, check your SQLite schema:

   ```bash
   sqlite3 ./data/showfer.db ".schema"
   sqlite3 data/showfer.db "ALTER TABLE history RENAME COLUMN filename TO file_id;"
   sudo -u postgres psql -c "ALTER USER postgres PASSWORD 'postgres';"
   ```

2. **Data type errors**: PostgreSQL is more strict about data types. Check the migration script for proper type conversions.

3. **Sequence issues**: If you encounter ID conflicts, manually reset sequences:
   ```sql
   SELECT setval('schedules_id_seq', (SELECT MAX(id) FROM schedules));
   SELECT setval('convert_items_id_seq', (SELECT MAX(id) FROM convert_items));
   -- Repeat for other tables
   ```

### Performance Optimization

1. **Analyze tables** after migration:

   ```sql
   ANALYZE;
   ```

2. **Consider additional indexes** based on your usage patterns:
   ```sql
   CREATE INDEX idx_convert_items_status ON convert_items(status);
   CREATE INDEX idx_schedules_short_id ON schedules(short_id);
   ```

## Rollback Plan

If you need to rollback to SQLite:

1. **Stop the application**
2. **Restore your SQLite backup**:
   ```bash
   cp ./data/showfer.db.backup ./data/showfer.db
   ```
3. **Revert the code changes** (checkout the previous commit)
4. **Restart the application**

## Benefits of PostgreSQL

After migration, you'll benefit from:

- **Better concurrent access** and performance
- **Advanced data types** and features
- **Better backup and recovery** options
- **Scalability** for larger datasets
- **Professional database management** tools
- **ACID compliance** and data integrity
- **Advanced indexing** capabilities

## Support

If you encounter issues during migration:

1. Check the application logs for detailed error messages
2. Verify your PostgreSQL configuration
3. Ensure all environment variables are set correctly
4. Test the database connection independently of the application
