package utils

import (
	"io/ioutil"
	"os"
	"strings"
)

const (
	PrimaryFlag           = "/tmp/primary"
	BackupFlag            = "/tmp/backup"
	BackupIPFile          = "/tmp/backup_ip"
	PrimaryIPFile         = "/tmp/primary_ip"
	SSHUserFile           = "/tmp/ssh_user"
	SSHPasswordFile       = "/tmp/ssh_password"
	PrimarySSHUserFile    = "/tmp/primary_ssh_user"
	PrimarySSHPasswordFile = "/tmp/primary_ssh_password"
)

// GetServerType returns whether the current server is primary or backup
func GetServerType() string {
	if _, err := os.Stat(BackupFlag); err == nil {
		return "backup"
	}
	return "primary"
}

// SetServerType sets the server type by creating the appropriate flag file
func SetServerType(serverType string) error {
	// Remove both flags first
	os.Remove(PrimaryFlag)
	os.Remove(BackupFlag)

	// Create the appropriate flag file
	flagFile := PrimaryFlag
	if serverType == "backup" {
		flagFile = BackupFlag
	}

	// Create an empty flag file
	file, err := os.Create(flagFile)
	if err != nil {
		return err
	}
	defer file.Close()

	return nil
}

// InitializeServerType creates the primary flag file if no flag files exist
func InitializeServerType() error {
	// Check if any flag file exists
	if _, err := os.Stat(PrimaryFlag); err == nil {
		return nil
	}
	if _, err := os.Stat(BackupFlag); err == nil {
		return nil
	}

	// Create primary flag file as default
	return SetServerType("primary")
}

// SetBackupIP saves the backup server IP to /tmp/backup_ip file
func SetBackupIP(ip string) error {
	if ip == "" {
		// Remove the file if IP is empty
		os.Remove(BackupIPFile)
		return nil
	}

	return ioutil.WriteFile(BackupIPFile, []byte(ip), 0644)
}

// GetBackupIP reads the backup server IP from /tmp/backup_ip file
func GetBackupIP() string {
	data, err := ioutil.ReadFile(BackupIPFile)
	if err != nil {
		return ""
	}
	return strings.TrimSpace(string(data))
}

// HasBackupIP checks if a backup IP is configured
func HasBackupIP() bool {
	return GetBackupIP() != ""
}

// SetPrimaryIP saves the primary server IP to /tmp/primary_ip file
func SetPrimaryIP(ip string) error {
	if ip == "" {
		// Remove the file if IP is empty
		os.Remove(PrimaryIPFile)
		return nil
	}

	return ioutil.WriteFile(PrimaryIPFile, []byte(ip), 0644)
}

// GetPrimaryIP reads the primary server IP from /tmp/primary_ip file
func GetPrimaryIP() string {
	data, err := ioutil.ReadFile(PrimaryIPFile)
	if err != nil {
		return ""
	}
	return strings.TrimSpace(string(data))
}

// HasPrimaryIP checks if a primary IP is configured
func HasPrimaryIP() bool {
	return GetPrimaryIP() != ""
}

// SetSSHUser saves the SSH username to /tmp/ssh_user file
func SetSSHUser(username string) error {
	if username == "" {
		// Remove the file if username is empty
		os.Remove(SSHUserFile)
		return nil
	}

	return ioutil.WriteFile(SSHUserFile, []byte(username), 0644)
}

// GetSSHUser reads the SSH username from /tmp/ssh_user file
func GetSSHUser() string {
	data, err := ioutil.ReadFile(SSHUserFile)
	if err != nil {
		return ""
	}
	return strings.TrimSpace(string(data))
}

// SetSSHPassword saves the SSH password to /tmp/ssh_password file
func SetSSHPassword(password string) error {
	if password == "" {
		// Remove the file if password is empty
		os.Remove(SSHPasswordFile)
		return nil
	}

	return ioutil.WriteFile(SSHPasswordFile, []byte(password), 0600) // Secure permissions for SSH password
}

// GetSSHPassword reads the SSH password from /tmp/ssh_password file
func GetSSHPassword() string {
	data, err := ioutil.ReadFile(SSHPasswordFile)
	if err != nil {
		return ""
	}
	return strings.TrimSpace(string(data))
}

// HasSSHCredentials checks if both SSH username and password are configured
func HasSSHCredentials() bool {
	return GetSSHUser() != "" && GetSSHPassword() != ""
}

// SetPrimarySSHUser saves the primary server SSH username to /tmp/primary_ssh_user file
func SetPrimarySSHUser(username string) error {
	if username == "" {
		// Remove the file if username is empty
		os.Remove(PrimarySSHUserFile)
		return nil
	}

	return ioutil.WriteFile(PrimarySSHUserFile, []byte(username), 0644)
}

// GetPrimarySSHUser reads the primary server SSH username from /tmp/primary_ssh_user file
func GetPrimarySSHUser() string {
	data, err := ioutil.ReadFile(PrimarySSHUserFile)
	if err != nil {
		return ""
	}
	return strings.TrimSpace(string(data))
}

// SetPrimarySSHPassword saves the primary server SSH password to /tmp/primary_ssh_password file
func SetPrimarySSHPassword(password string) error {
	if password == "" {
		// Remove the file if password is empty
		os.Remove(PrimarySSHPasswordFile)
		return nil
	}

	return ioutil.WriteFile(PrimarySSHPasswordFile, []byte(password), 0600) // Secure permissions for SSH password
}

// GetPrimarySSHPassword reads the primary server SSH password from /tmp/primary_ssh_password file
func GetPrimarySSHPassword() string {
	data, err := ioutil.ReadFile(PrimarySSHPasswordFile)
	if err != nil {
		return ""
	}
	return strings.TrimSpace(string(data))
}

// HasPrimarySSHCredentials checks if both primary server SSH username and password are configured
func HasPrimarySSHCredentials() bool {
	return GetPrimarySSHUser() != "" && GetPrimarySSHPassword() != ""
}