package main

import (
	"context"
	"database/sql"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"showfer-web/api"
	"showfer-web/api/logs"
	"showfer-web/config"
	"showfer-web/middleware"
	"showfer-web/repository"
	"showfer-web/service/database_sync"
	"showfer-web/service/detector"
	"showfer-web/service/files"
	"showfer-web/service/guide"
	"showfer-web/service/health_monitor"
	"showfer-web/service/logger"
	"showfer-web/service/playout"
	"showfer-web/service/queue"
	"showfer-web/service/recorder"
	"showfer-web/service/retranscoder"
	"showfer-web/service/scheduler"
	"showfer-web/service/system"
	"showfer-web/service/traffic_blocker"
	"showfer-web/service/websocket_client"
	"showfer-web/swagger"
	"showfer-web/utils"
	"strings"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

func main() {
	// Load Backblaze configuration
	b2Config, err := config.LoadBackblazeConfig()
	if err != nil {
		log.Printf("Warning: Backblaze configuration not loaded: %v", err)
		log.Printf("Please set B2_KEY_ID, B2_APP_KEY, and B2_DEFAULT_BUCKET environment variables")
		// Don't exit, allow local storage fallback
	} else {
		log.Printf("Backblaze configuration loaded successfully")
		log.Printf("Using bucket: %s", b2Config.DefaultBucket)
	}

	// Initialize logger
	// logger.Init()

	// Check and handle port conflicts before starting
	handlePortConflicts()

	// Initialize server type
	if err := utils.InitializeServerType(); err != nil {
		log.Fatalf("Failed to initialize server type: %v", err)
	}

	// Initialize PostgreSQL database
	db, err := config.InitDB()
	if err != nil {
		log.Fatalf("Failed to initialize PostgreSQL database: %v", err)
	}
	defer db.Close()

	// Create and migrate tables
	if err := createTables(db); err != nil {
		log.Fatalf("Failed to create tables: %v", err)
	}
	if err := repository.MigrateTables(db); err != nil {
		log.Fatalf("Failed to migrate tables: %v", err)
	}

	// Test PostgreSQL connection by logging tables
	logPostgreSQLTables(db)

	// Initialize MongoDB database
	_, err = config.InitMongoDB()
	if err != nil {
		log.Printf("Warning: Failed to initialize MongoDB: %v", err)
		log.Printf("MongoDB features will be disabled. Please check your MongoDB configuration.")
		// Don't exit, allow the application to run without MongoDB
	} else {
		log.Printf("MongoDB initialized successfully")

		// Test MongoDB connection by logging collections
		logMongoCollections()

		defer func() {
			if err := config.CloseMongoDB(); err != nil {
				log.Printf("Error closing MongoDB connection: %v", err)
			}
		}()
	}

	// Initialize WebSocket for real-time log streaming
	logs.InitLogsWebSocket()

	// Initialize enhanced logger with database support
	logsRepo := logs.NewBroadcastingLogsRepository(db, logs.GetLogsWebSocketManager())
	logger.InitEnhancedLogger(logsRepo)

	// Initialize recorder status manager
	recorder.InitStatusManager(db, queue.GetInstance())

	// Initialize file status manager
	files.InitFileStatusManager(db)

	// Get current working directory for file storage
	baseDir := getBaseDir()

	// Initialize storage service (either local or Backblaze)

	// Initialize queue manager first
	queueManager := queue.GetInstance()
	queueManager.Init(
		repository.NewFilesRepository(db),
		repository.NewCodecSettingsRepository(db),
		repository.NewGeneralSettingsRepository(db),
		recorder.GetStatusManager(),
		baseDir)

	// Initialize retranscoding service
	retranscoderService := retranscoder.GetRetranscoderService()
	retranscoderService.Init(
		repository.NewFilesRepository(db),
		repository.NewCodecSettingsRepository(db),
		queueManager,
		baseDir,
	)

	// Initialize streamer playout
	streamerPlayout := playout.NewPlayout(db, baseDir)

	// Initialize and start scheduler service for scheduled recordings
	recorderRepo := repository.NewRecorderRepository(db)
	schedulerService := scheduler.NewSchedulerService(recorderRepo)
	schedulerService.Start()

	// Start RTP sender cleanup task
	detector.StartCleanupTask()

	// Start database dump service for primary servers
	dumpService := database_sync.NewDumpService()
	dumpService.Start()

	// Start database restore service for backup servers
	restoreService := database_sync.NewRestoreService()
	restoreService.Start()

	// Store restore service globally for API access
	database_sync.SetRestoreServiceInstance(restoreService)

	// Set up cleanup function to stop RTP URL validation when the application exits
	defer func() {
		// Stop scheduler service
		if schedulerService != nil {
			logger.Log("Stopping scheduler service...")
			schedulerService.Stop()
		}

		statusManager := recorder.GetStatusManager()
		if statusManager != nil {
			logger.Log("Stopping RTP URL validation...")
			statusManager.StopRtpUrlValidation()
		}

		// Stop file status manager
		fileStatusManager := files.GetFileStatusManager()
		if fileStatusManager != nil {
			logger.Log("Stopping file status manager...")
			fileStatusManager.Stop()
		}

		// Stop logs WebSocket manager
		logsWSManager := logs.GetLogsWebSocketManager()
		if logsWSManager != nil {
			logger.Log("Stopping logs WebSocket manager...")
			logsWSManager.CloseAllConnections()
		}

		// Stop streamer playout and RTP monitor if running
		if streamerPlayout != nil {
			logger.Log("Stopping RTP monitor and playout services...")
			streamerPlayout.StopRTPMonitor()
		}

		// Stop queue manager
		queueManager := queue.GetInstance()
		if queueManager != nil {
			logger.Log("Stopping queue manager...")
			queueManager.Stop()
		}

		// Stop health monitoring
		logger.Log("Stopping health monitor...")
		health_monitor.StopHealthMonitoring()
	}()

	// Initialize system monitor
	dataDir := "./data"
	system.InitSystemMonitor(dataDir)

	// Create Gin router
	router := gin.Default()

	// Configure CORS
	router.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Accept", "Authorization"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
	}))

	// Add middleware
	router.Use(middleware.ErrorHandler())

	// Setup Swagger UI
	swagger.SetupSwaggerRoutes(router)

	// Setup static file serving
	router.Static("/data", "./data")

	// Serve frontend static files if they exist
	frontendDir := "../frontend/dist"
	if _, err := os.Stat(frontendDir); err == nil {
		// Serve static files
		router.Static("/assets", filepath.Join(frontendDir, "assets"))

		// Handle all other routes with index.html
		router.NoRoute(func(c *gin.Context) {
			// Don't interfere with API routes
			if strings.HasPrefix(c.Request.URL.Path, "/api") ||
				strings.HasPrefix(c.Request.URL.Path, "/ws") ||
				strings.HasPrefix(c.Request.URL.Path, "/data") {
				c.Next()
				return
			}

			// Serve index.html for all other routes
			c.File(filepath.Join(frontendDir, "index.html"))
		})

		log.Println("Serving frontend from", frontendDir)
	} else {
		log.Println("Frontend directory not found at", frontendDir)
	}

	// Initialize API routes with storage service
	api.SetupRoutes(router, db, streamerPlayout, retranscoderService)

	// Add server type routes
	router.GET("/api/server-type", func(c *gin.Context) {
		api.GetServerTypeHandler(c.Writer, c.Request)
	})

	router.POST("/api/server-type", func(c *gin.Context) {
		api.SetServerTypeHandler(c.Writer, c.Request)
	})

	// Add backup IP routes
	router.GET("/api/backup-ip", func(c *gin.Context) {
		api.GetBackupIPHandler(c.Writer, c.Request)
	})

	router.POST("/api/backup-ip", func(c *gin.Context) {
		api.SetBackupIPHandler(c.Writer, c.Request)
	})

	// Add primary IP routes
	router.GET("/api/primary-ip", func(c *gin.Context) {
		api.GetPrimaryIPHandler(c.Writer, c.Request)
	})

	router.POST("/api/primary-ip", func(c *gin.Context) {
		api.SetPrimaryIPHandler(c.Writer, c.Request)
	})

	router.POST("/api/test-backup-server", func(c *gin.Context) {
		api.TestBackupServerHandler(c.Writer, c.Request)
	})

	router.GET("/api/health-status", func(c *gin.Context) {
		api.GetHealthStatusHandler(c.Writer, c.Request)
	})

	// Add SSH configuration routes
	router.GET("/api/ssh-config", func(c *gin.Context) {
		api.GetSSHConfigHandler(c.Writer, c.Request)
	})

	router.POST("/api/ssh-config", func(c *gin.Context) {
		api.SetSSHConfigHandler(c.Writer, c.Request)
	})

	// Add primary server SSH configuration routes
	router.GET("/api/primary-ssh-config", func(c *gin.Context) {
		api.GetPrimarySSHConfigHandler(c.Writer, c.Request)
	})

	router.POST("/api/primary-ssh-config", func(c *gin.Context) {
		api.SetPrimarySSHConfigHandler(c.Writer, c.Request)
	})

	// Add restore status route
	router.GET("/api/restore-status", func(c *gin.Context) {
		api.GetRestoreStatusHandler(c.Writer, c.Request)
	})

	// Add recovery endpoint for database loading (keeping original name for compatibility)
	router.POST("/api/recovery/load-and-restart", func(c *gin.Context) {
		api.LoadDatabaseHandler(c.Writer, c.Request)
	})

	// Add traffic blocker routes
	router.GET("/api/traffic-blocker-status", func(c *gin.Context) {
		api.GetTrafficBlockerStatusHandler(c.Writer, c.Request)
	})

	router.POST("/api/refresh-traffic-blocking", func(c *gin.Context) {
		api.RefreshTrafficBlockingHandler(c.Writer, c.Request)
	})

	startTaskByTimer(db, streamerPlayout)

	err = streamerPlayout.StartAll()
	if err != nil {
		logger.Error("Failed to start playout: %v", err)
	}

	// Start backup connection if configured
	go websocket_client.StartBackupConnection()

	// Start health monitoring for backup servers
	go health_monitor.StartHealthMonitoring()

	// Initialize traffic blocker for backup servers
	go func() {
		time.Sleep(2 * time.Second) // Wait for other services to initialize
		traffic_blocker.GetTrafficBlocker(db)
	}()

	// Start the server
	log.Println("Starting server on 0.0.0.0:8080")
	if err := router.Run("0.0.0.0:8080"); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}

func getBaseDir() string {
	currentDir, err := os.Getwd()
	if err != nil {
		logger.Error("Failed to get working directory: %v", err)
		panic(err)
	}

	return filepath.Join(currentDir, "data")
}

func startTaskByTimer(db *sql.DB, streamerPlayout *playout.Playout) {
	guideGenerator := guide.NewGuideGenerator(db, streamerPlayout)

	ticker := time.NewTicker(4 * time.Hour)

	go func() {
		for {
			err := guideGenerator.UpdateAllGuides()
			if err != nil {
				logger.Error("Failed to update guides: %v", err)
			}
			<-ticker.C // wait next iteration
		}
	}()
}

// handlePortConflicts checks for and resolves port 8080 conflicts
func handlePortConflicts() {
	log.Println("Checking for port conflicts...")

	// Check if port 8080 is in use
	cmd := exec.Command("lsof", "-Pi", ":8080", "-sTCP:LISTEN", "-t")
	output, err := cmd.Output()
	if err != nil || len(output) == 0 {
		// Port is free
		log.Println("Port 8080 is available")
		return
	}

	log.Println("Port 8080 is in use. Attempting to free it...")

	// Kill processes using port 8080
	cmd = exec.Command("sh", "-c", "lsof -ti:8080 | xargs kill -9 2>/dev/null || true")
	if err := cmd.Run(); err != nil {
		log.Printf("Warning: Failed to kill processes on port 8080: %v", err)
	}

	// Also kill any traffiq-server processes
	cmd = exec.Command("pkill", "-f", "traffiq-server")
	if err := cmd.Run(); err != nil {
		log.Printf("No existing traffiq-server processes to kill")
	}

	// Wait for cleanup
	time.Sleep(2 * time.Second)

	// Verify port is now free
	cmd = exec.Command("lsof", "-Pi", ":8080", "-sTCP:LISTEN", "-t")
	output, err = cmd.Output()
	if err != nil || len(output) == 0 {
		log.Println("Port 8080 is now available")
	} else {
		log.Println("Warning: Port 8080 may still be in use")
	}
}

// createTables creates all necessary tables in the database
func createTables(db *sql.DB) error {
	// Create tables
	if err := repository.CreateScheduleTable(db); err != nil {
		return err
	}
	if err := repository.CreateRtpUrlTable(db); err != nil {
		return err
	}
	if err := repository.CreateRecorderTable(db); err != nil {
		return err
	}
	if err := repository.CreateConvertItemTable(db); err != nil {
		return err
	}
	if err := repository.CreateGuideTable(db); err != nil {
		return err
	}
	if err := repository.CreateHistoryTable(db); err != nil {
		return err
	}
	if err := repository.CreateUsersTable(db); err != nil {
		return err
	}
	if err := repository.CreateCodecSettingsTable(db); err != nil {
		return err
	}
	if err := repository.CreateGeneralSettingsTable(db); err != nil {
		return err
	}
	if err := repository.CreateContentAnalyticsTable(db); err != nil {
		return err
	}
	if err := repository.CreateLogsTable(db); err != nil {
		return err
	}

	return nil
}

// logPostgreSQLTables logs all tables in the PostgreSQL database
func logPostgreSQLTables(db *sql.DB) {
	log.Println("=== PostgreSQL Database Tables ===")

	query := `
		SELECT table_name
		FROM information_schema.tables
		WHERE table_schema = 'public'
		ORDER BY table_name;
	`

	rows, err := db.Query(query)
	if err != nil {
		log.Printf("Error querying PostgreSQL tables: %v", err)
		return
	}
	defer rows.Close()

	var tables []string
	for rows.Next() {
		var tableName string
		if err := rows.Scan(&tableName); err != nil {
			log.Printf("Error scanning table name: %v", err)
			continue
		}
		tables = append(tables, tableName)
	}

	if len(tables) == 0 {
		log.Println("No tables found in PostgreSQL database")
	} else {
		log.Printf("Found %d tables in PostgreSQL:", len(tables))
		for i, table := range tables {
			log.Printf("  %d. %s", i+1, table)
		}
	}
	log.Println("=== End PostgreSQL Tables ===")
}

// logMongoCollections logs all collections in the MongoDB database
func logMongoCollections() {
	log.Println("=== MongoDB Database Collections ===")

	mongoClient := config.GetMongoDB()
	if mongoClient == nil {
		log.Println("MongoDB client is not available")
		return
	}

	mongoDb := config.GetMongoDatabase()
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	collections, err := mongoDb.ListCollectionNames(ctx, bson.D{})
	if err != nil {
		log.Printf("Error listing MongoDB collections: %v", err)
		return
	}

	if len(collections) == 0 {
		log.Println("No collections found in MongoDB database")
	} else {
		log.Printf("Found %d collections in MongoDB:", len(collections))
		for i, collection := range collections {
			log.Printf("  %d. %s", i+1, collection)
		}
	}
	log.Println("=== End MongoDB Collections ===")
}
