# MongoDB Setup Guide

This guide will help you set up MongoDB to work alongside your existing PostgreSQL database in a hybrid configuration.

## Prerequisites

- MongoDB 4.4 or higher
- Go 1.19 or higher
- PostgreSQL (already configured)

## Installation

### Ubuntu/Debian

```bash
# Import the public key used by the package management system
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -

# Create a list file for MongoDB
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list

# Reload local package database
sudo apt-get update

# Install MongoDB packages
sudo apt-get install -y mongodb-org

# Start MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod
```

### macOS (using Homebrew)

```bash
# Install MongoDB
brew tap mongodb/brew
brew install mongodb-community

# Start MongoDB
brew services start mongodb/brew/mongodb-community
```

### Docker (Recommended for Development)

```bash
# Run MongoDB in Docker
docker run -d \
  --name mongodb \
  -p 27017:27017 \
  -e MONGO_INITDB_ROOT_USERNAME=admin \
  -e MONGO_INITDB_ROOT_PASSWORD=password \
  -v mongodb_data:/data/db \
  mongo:6.0

# Or use docker-compose (create docker-compose.yml)
```

## Configuration

### 1. Environment Variables

Copy the example environment file and configure it:

```bash
cp .env.example .env
```

Edit `.env` file:

```env
# PostgreSQL (existing)
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your_postgres_password
DB_NAME=showfer
DB_SSLMODE=disable

# MongoDB (new)
MONGO_HOST=localhost
MONGO_PORT=27017
MONGO_USER=
MONGO_PASSWORD=
MONGO_DATABASE=showfer_mongo
```

### 2. MongoDB Authentication (Optional)

If you want to enable authentication:

```bash
# Connect to MongoDB
mongosh

# Switch to admin database
use admin

# Create admin user
db.createUser({
  user: "admin",
  pwd: "your_password_here",
  roles: ["userAdminAnyDatabase", "dbAdminAnyDatabase", "readWriteAnyDatabase"]
})

# Create application user
use showfer_mongo
db.createUser({
  user: "showfer_user",
  pwd: "your_app_password_here",
  roles: ["readWrite"]
})
```

Then update your `.env` file:

```env
MONGO_USER=showfer_user
MONGO_PASSWORD=your_app_password_here
```

## Database Architecture

This application uses a hybrid database approach:

### PostgreSQL (Primary)
- User accounts and authentication
- Content items (convert_items table)
- Schedules and recordings
- System configuration
- Relational data requiring ACID transactions

### MongoDB (Secondary)
- Rich metadata for content items
- User sessions and temporary data
- Analytics and metrics
- Logs and processing history
- Document-based data with flexible schemas

## Usage Examples

### 1. Basic MongoDB Operations

```go
// Get MongoDB database
db := config.GetMongoDatabase()

// Create a repository
repo := repository.NewMongoExampleRepository()

// Create a document
example := &models.MongoExample{
    Title: "Test Document",
    Description: "This is a test",
    Tags: []string{"test", "example"},
    IsActive: true,
}

ctx := context.Background()
err := repo.Create(ctx, example)
```

### 2. Hybrid Operations (PostgreSQL + MongoDB)

```go
// Get content item from PostgreSQL
filesRepo := repository.NewFilesRepository(db)
convertItem, err := filesRepo.GetConvertItemByID(123)

// Get rich metadata from MongoDB
metadataRepo := repository.NewContentMetadataRepository()
metadata, err := metadataRepo.GetMetadataByConvertItemID(ctx, 123)

// Combine data from both databases
response := CombinedResponse{
    ConvertItem: convertItem,
    Metadata: metadata,
}
```

## API Endpoints

The application provides example API endpoints to demonstrate MongoDB usage:

### MongoDB Examples
- `POST /api/v1/mongo-examples/` - Create example document
- `GET /api/v1/mongo-examples/:id` - Get example by ID
- `PUT /api/v1/mongo-examples/:id` - Update example
- `DELETE /api/v1/mongo-examples/:id` - Delete example
- `GET /api/v1/mongo-examples/` - List examples with pagination

### Content Metadata (Hybrid)
- `POST /api/v1/content-metadata/` - Create metadata for content item
- `GET /api/v1/content-metadata/convert-item/:id` - Get combined data
- `PUT /api/v1/content-metadata/convert-item/:id/analytics` - Update analytics
- `POST /api/v1/content-metadata/convert-item/:id/logs` - Add processing log

## Testing the Setup

### 1. Test MongoDB Connection

```bash
# Run the application
go run main.go

# Look for this log message:
# "MongoDB connected successfully to database: showfer_mongo"
```

### 2. Test API Endpoints

```bash
# Create a MongoDB example
curl -X POST http://localhost:8080/api/v1/mongo-examples/ \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Test Document",
    "description": "Testing MongoDB integration",
    "tags": ["test", "mongodb"],
    "metadata": {"key": "value"},
    "is_active": true
  }'

# List examples
curl http://localhost:8080/api/v1/mongo-examples/
```

## Troubleshooting

### Connection Issues

1. **MongoDB not running**
   ```bash
   sudo systemctl status mongod
   sudo systemctl start mongod
   ```

2. **Connection refused**
   - Check if MongoDB is listening on the correct port
   - Verify firewall settings
   - Check MongoDB configuration file

3. **Authentication failed**
   - Verify username and password in `.env`
   - Check user permissions in MongoDB

### Application Issues

1. **MongoDB features disabled**
   - Check application logs for MongoDB connection errors
   - Verify environment variables are set correctly
   - Ensure MongoDB is running and accessible

2. **Import errors**
   ```bash
   go mod tidy
   go mod download
   ```

## Best Practices

1. **Use appropriate database for each use case**
   - PostgreSQL for structured, relational data
   - MongoDB for flexible, document-based data

2. **Handle connection failures gracefully**
   - The application continues to work if MongoDB is unavailable
   - MongoDB features are disabled with appropriate error messages

3. **Use contexts for timeouts**
   - All MongoDB operations use context with timeouts
   - Prevents hanging operations

4. **Index your MongoDB collections**
   ```javascript
   // Connect to MongoDB and create indexes
   use showfer_mongo
   db.examples.createIndex({"tags": 1})
   db.content_metadata.createIndex({"convert_item_id": 1})
   ```

## Monitoring

Monitor both databases:

```bash
# PostgreSQL
psql -h localhost -U postgres -d showfer -c "SELECT * FROM pg_stat_activity;"

# MongoDB
mongosh --eval "db.runCommand({serverStatus: 1})"
```
