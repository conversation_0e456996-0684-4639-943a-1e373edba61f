openapi: 3.0.3
info:
  host: http://localhost:8080
  title: Channel API
  version: 1.0.0
  description: API for managing schedules, channels, and media content in the Traffiq system

servers:
  - url: http://localhost:8080/api/v1
    description: Local development server
  - url: http://localhost:8080
    description: Local development server (base)

paths:
  /schedule:
    get:
      summary: Retrieve a paginated list of schedules
      operationId: getSchedules
      tags:
        - Schedules
      parameters:
        - name: page
          in: query
          description: Page number (starting from 1)
          required: false
          schema:
            type: integer
            default: 1
            minimum: 1
        - name: limit
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            default: 10
            minimum: 1
            maximum: 100
      responses:
        "200":
          description: A paginated list of schedule entries
          content:
            application/json:
              schema:
                type: object
                properties:
                  items:
                    type: array
                    items:
                      $ref: "#/components/schemas/Schedule"
                  page:
                    type: integer
                    example: 1
                  limit:
                    type: integer
                    example: 10
                  total_items:
                    type: integer
                    example: 12
                  total_pages:
                    type: integer
                    example: 2
        "401":
          description: Unauthorized - invalid or missing token
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "Unauthorized"
    post:
      summary: Create new scheduler
      operationId: createSchedule
      tags:
        - Schedules
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Schedule"
      responses:
        "201":
          description: Channel configuration retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Schedule"

  /schedule/status:
    get:
      summary: Get current statuses of schedules
      operationId: getScheduleStatuses
      tags:
        - Schedules
      responses:
        "200":
          description: A dictionary of schedule statuses indexed by short_id
          content:
            application/json:
              schema:
                type: object
                additionalProperties:
                  $ref: "#/components/schemas/ScheduleStatus"
        "401":
          description: Unauthorized - invalid or missing token
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "Unauthorized"
  /schedule/{id}:
    get:
      summary: Get channel by ID
      tags:
        - Schedules
      security:
        - BearerAuth: []
      description: Retrieve a full configuration and schedule of a channel, including ads, output settings, and daily/special playout schedules.
      parameters:
        - name: id
          in: path
          required: true
          description: Unique identifier of the channel
          schema:
            type: integer
            format: int64
            example: 3
      responses:
        "200":
          description: Channel configuration retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Schedule"
        "401":
          description: Unauthorized - invalid or missing token
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "Unauthorized"
        "404":
          description: Channel not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "Failed to find schedule"
                  details:
                    type: string
                    example: "sql: no rows in result set"
    put:
      summary: Update schedule by ID
      tags:
        - Schedules
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Schedule ID
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Schedule"
      responses:
        "200":
          description: Schedule successfully updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Schedule"
        "401":
          description: Unauthorized - invalid or missing token
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "Unauthorized"
        "404":
          description: Channel not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "Failed to find schedule"
                  details:
                    type: string
                    example: "sql: no rows in result set"
  /schedule/{id}/guide:
    get:
      summary: Get guide by schedule ID
      tags:
        - Schedules
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the schedule to retrieve
          schema:
            type: integer
            format: int64
            example: 3
      responses:
        "200":
          description: Successfully retrieved flat schedule
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Guide"
        "401":
          description: Unauthorized - invalid or missing token
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "Unauthorized"
        "404":
          description: Schedule not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "Failed to find schedule"
                  details:
                    type: string
                    example: "sql: no rows in result set"
    post:
      summary: Force regenerate guide by schedule ID
      tags:
        - Schedules
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the schedule to retrieve
          schema:
            type: integer
            format: int64
            example: 3
      responses:
        "200":
          description: Successfully retrieved flat schedule
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Guide"
        "401":
          description: Unauthorized - invalid or missing token
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "Unauthorized"
        "404":
          description: Schedule not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "Failed to find schedule"
                  details:
                    type: string
                    example: "sql: no rows in result set"
  /schedule/files:
    get:
      summary: Get list of media files and folders
      tags:
        - Schedules
      security:
        - BearerAuth: []
      responses:
        "200":
          description: Successfully retrieved media file list
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FileBrowserResponse"
        "401":
          description: Unauthorized - invalid or missing token
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: "Unauthorized"

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    ScheduleStatus:
      type: object
      properties:
        id:
          type: integer
          example: 3
        short_id:
          type: string
          example: rOGYu
        is_active:
          type: boolean
          example: true
        rtp_synced:
          type: boolean
          example: true

    Schedule:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: Unique channel ID
          example: 3
        name:
          type: string
          description: Channel display name
          example: "The Western Channel"
        icon:
          type: string
          description: URL or path to the icon image. Empty if not set.
          example: "/icons/rOGYu/logo.png"
        timezone:
          type: string
          description: IANA timezone name for interpreting schedule timestamps
          example: "America/Los_Angeles"
        created_at:
          type: string
          format: date-time
          description: Channel creation time (ISO 8601)
          example: "2025-05-13T08:39:03Z"
        updated_at:
          type: string
          format: date-time
          description: Last modification time (ISO 8601)
          example: "2025-06-04T09:16:18Z"
        short_id:
          type: string
          description: Short unique ID for easy reference
          example: "rOGYu"
        autosave:
          type: boolean
          description: If true, changes to the schedule are saved automatically
          example: false
        output_url:
          type: string
          description: Output stream URL (e.g. RTP, SRT, UDP)
          example: "rtp://*********:5001"
        network_interface:
          type: string
          description: Network interface used for output streaming. Empty for default.
          example: ""
        ads:
          $ref: "#/components/schemas/Ads"
        channels:
          type: array
          description: Reserved for future use
          items:
            type: object
        fillers:
          $ref: "#/components/schemas/Fillers"
        regular_days:
          type: array
          description: Weekly schedule by day
          items:
            $ref: "#/components/schemas/DaySchedule"
        special_days:
          type: array
          description: Special calendar dates with their own schedules
          items:
            $ref: "#/components/schemas/DaySchedule"

    Ads:
      type: object
      description: Ad configuration for the channel
      properties:
        links:
          type: array
          description: External ad sources
          items:
            type: object
            properties:
              channel_id:
                type: integer
                format: int64
                description: Related channel ID
                example: 2
              link:
                type: string
                description: URL of the external ad stream or content
                example: "https://ads.example.com/stream"
        folders:
          type: array
          description: List of folder paths used for ads
          items:
            type: string
            example: "/ads/folder"
        files:
          type: array
          description: List of individual ad files
          items:
            $ref: "#/components/schemas/File"

    DaySchedule:
      type: object
      description: Represents a day's schedule (either weekly or special)
      properties:
        name:
          type: string
          description: Name of the day (Sun–Sat - for regular days) or specific date (2025-06-01 - for special days)
          example: "Sun"
        date:
          type: string
          format: date
          description: Specific date if this is a special day schedule
          example: "2025-06-01"
        items:
          type: array
          description: Scheduled items for the day
          items:
            $ref: "#/components/schemas/ScheduleItem"

    ScheduleItem:
      type: object
      description: A block of content scheduled for a specific time range
      properties:
        start:
          type: string
          format: date-time
          description: Start time in UTC
          example: "2025-06-01T00:00:00Z"
        end:
          type: string
          format: date-time
          description: End time in UTC
          example: "2025-06-01T01:00:00Z"
        type:
          type: string
          enum: [folder, connection]
          description: "Indicates the source type: folder (local content) or connection (external stream)"
          example: "folder"
        connection:
          type: string
          description: "Stream protocol type (required if type = connection). Supported: rtp, udp, srt, hls, rtmp"
          example: "srt"
        link:
          type: string
          description: Stream source IP or URL (required if type = connection)
          example: "*************"
        port:
          type: string
          description: Port number of the stream source (required if type = connection)
          example: "5000"
        mode:
          type: string
          description: |
            SRT mode, if using SRT protocol. Can be `caller`, `listener`, or `rendezvous`.
          example: "listener"
        name:
          type: string
          description: Optional name or label of the scheduled block
          example: "Morning Block"
        description:
          type: string
          description: Description of the scheduled block
          example: "A set of morning shows."
        expire_date:
          type: string
          format: date
          description: Expiration date for the RTMP connection
          example: "2025-07-01"
        expire_time:
          type: string
          format: time
          description: Expiration time for the RTMP connection (HH:MM)
          example: "12:00"
        folders:
          type: array
          description: List of folder paths (used when type = folder)
          items:
            type: string
            example: "/media/movies"
        files:
          type: array
          description: List of media files to play
          items:
            $ref: "#/components/schemas/File"
        fillers:
          $ref: "#/components/schemas/Fillers"

    File:
      type: object
      description: Represents a media file
      properties:
        id:
          type: integer
          format: int64
          description: File ID in the system
          example: 24
        path:
          type: string
          description: Path to the file
          example: "/media/videos/clip.mp4"

    Fillers:
      type: object
      description: Fillers used in case of gaps or content padding
      properties:
        folders:
          type: array
          nullable: true
          description: Folders used as filler content sources
          items:
            type: string
            example: "/media/fillers"
        files:
          type: array
          nullable: true
          description: List of filler files
          items:
            $ref: "#/components/schemas/File"
        pre_filler:
          $ref: "#/components/schemas/File"

    Guide:
      type: object
      properties:
        id:
          type: integer
          format: int64
          example: 10
          description: Unique guide ID
        schedule_id:
          type: integer
          format: int64
          example: 3
          description: Reference to the parent schedule
        elements:
          type: array
          description: List of guide elements
          items:
            $ref: "#/components/schemas/GuideElement"
        created_at:
          type: string
          format: date-time
          example: "2025-06-03T18:25:15Z"
          description: Timestamp when this guide was created
        updated_at:
          type: string
          format: date-time
          example: "2025-06-04T08:48:18Z"
          description: Last update timestamp

    GuideElement:
      type: object
      properties:
        start:
          type: string
          format: date-time
          example: "2025-06-03T00:00:00Z"
          description: Start time of the scheduled element
        end:
          type: string
          format: date-time
          example: "2025-06-03T00:30:00Z"
          description: End time of the scheduled element
        title:
          type: string
          example: "Ad_1_15sec.mp4"
          description: Title of the file or stream
        description:
          type: string
          example: ""
          description: Optional description of the content
        type:
          type: string
          enum: [file, folder, connection]
          example: "file"
          description: Type of source. Can be `file`, `folder`, or `connection`.
        connection:
          $ref: "#/components/schemas/ConnectionSettings"
        file:
          $ref: "#/components/schemas/FileReference"
        fillers:
          $ref: "#/components/schemas/Fillers"

    ConnectionSettings:
      type: object
      description: External stream connection parameters (used if type is 'connection')
      properties:
        type:
          type: string
          enum: [rtp, udp, srt, hls, rtmp, ""]
          example: ""
          description: Type of external stream protocol
        link:
          type: string
          example: ""
          description: IP or URL of the external stream
        port:
          type: string
          example: ""
          description: Port number for the incoming stream
        mode:
          type: string
          example: ""
          description: Mode for SRT connections (e.g. caller, listener)
        expire_date:
          type: string
          example: ""
          description: Optional expiration date for the connection
        expire_time:
          type: string
          example: ""
          description: Optional expiration time for the connection

    FileReference:
      type: object
      description: File-based content used in the schedule
      properties:
        file_id:
          type: integer
          format: int64
          example: 40
          description: ID of the file in the database
        folder:
          type: string
          example: "/test/"
          description: Folder path where the file is located
        episode:
          type: string
          example: ""
          description: Optional episode name (if applicable)

    FileBrowserResponse:
      type: object
      description: Root object representing the current folder and its contents
      properties:
        folder:
          type: string
          example: ""
          description: Name of the current folder (empty if root)
        path:
          type: string
          example: "/"
          description: Absolute path to the current folder
        files:
          type: array
          description: List of media files in the current folder
          items:
            $ref: "#/components/schemas/MediaFile"
        folders:
          type: array
          description: List of subfolders in the current folder
          items:
            $ref: "#/components/schemas/FolderNode"

    FolderNode:
      type: object
      description: A subfolder with its own path, files, and subfolders
      properties:
        folder:
          type: string
          example: "Movies"
          description: Folder name
        path:
          type: string
          example: "/Movies"
          description: Absolute path to this folder
        files:
          type: array
          description: List of media files in this folder
          items:
            $ref: "#/components/schemas/MediaFile"
        folders:
          type: array
          description: Nested subfolders (recursive)
          items:
            $ref: "#/components/schemas/FolderNode"

    MediaFile:
      type: object
      description: Metadata for a single media file
      properties:
        id:
          type: string
          example: "24"
          description: Unique identifier of the file (stringified)
        fileName:
          type: string
          example: "PROMO BIG SHOT WITH MUSIC-_20250503202416.mp4"
          description: Actual file name on disk
        name:
          type: string
          example: "PROMO BIG SHOT"
          description: Display name
        duration:
          type: number
          format: float
          example: 35.34931
          description: Duration of the file in seconds
        episode:
          $ref: "#/components/schemas/NullableString"
        description:
          $ref: "#/components/schemas/NullableString"

    NullableString:
      type: object
      description: Nullable string used in SQL responses
      properties:
        String:
          type: string
          example: ""
          description: The actual string value
        Valid:
          type: boolean
          example: false
          description: Whether the value is considered valid (true = present)
