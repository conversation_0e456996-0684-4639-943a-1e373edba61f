FROM golang:1.20-alpine AS backend-builder

# Install build dependencies
RUN apk add --no-cache gcc musl-dev

# Set working directory
WORKDIR /app

# Copy go.mod and go.sum
COPY backend/go.mod backend/go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY backend/ ./

# Build the application
RUN CGO_ENABLED=1 GOOS=linux go build -o showfer-web .

# Node stage for building frontend
FROM node:18-alpine AS frontend-builder

# Set working directory
WORKDIR /app

# Copy package.json and package-lock.json
COPY frontend/package*.json ./

# Install dependencies
RUN npm install

# Copy frontend source code
COPY frontend/ ./

# Build frontend
RUN npm run build

# Final stage
FROM alpine:3.17

# Install runtime dependencies
RUN apk add --no-cache \
    gstreamer \
    gst-plugins-base \
    gst-plugins-good \
    gst-plugins-bad \
    gst-plugins-ugly \
    gst-libav \
    ca-certificates

# Set working directory
WORKDIR /app

# Create necessary directories
RUN mkdir -p data/recordings logs

# Copy backend binary
COPY --from=backend-builder /app/showfer-web .

# Copy frontend build
COPY --from=frontend-builder /app/dist ./public

# Expose port
EXPOSE 8080

# Set environment variables
ENV GIN_MODE=release

# Run the application
CMD ["./showfer-web"]
