#!/bin/bash

# Function to handle cleanup on exit
cleanup() {
  echo "Shutting down services..."
  kill $BACKEND_PID $FRONTEND_PID 2>/dev/null
  exit 0
}

# Set up trap to catch SIGIN<PERSON> (Ctrl+C) and SIGTERM
trap cleanup SIGINT SIGTERM

# Create necessary directories
mkdir -p data/recordings logs

# Start the backend
echo "Starting backend server..."
cd backend
go run main.go &
BACKEND_PID=$!
cd ..

# Wait a moment for the backend to start
sleep 2

# Start the frontend
echo "Starting frontend development server..."
cd frontend
npm run dev &
FRONTEND_PID=$!
cd ..

# Get the local IP address
LOCAL_IP=$(hostname -I | awk '{print $1}')

echo "Both services are running!"
echo "Backend: http://localhost:8080 (local) or http://$LOCAL_IP:8080 (remote)"
echo "Frontend: http://localhost:5173 (local) or http://$LOCAL_IP:5173 (remote)"
echo "Press Ctrl+C to stop both services"

# Wait for both processes to finish
wait $BACKEND_PID $FRONTEND_PID
