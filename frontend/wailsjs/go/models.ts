export namespace document {
	
	export class AdLink {
	    url: string;
	    channel: number;
	
	    static createFrom(source: any = {}) {
	        return new AdLink(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.url = source["url"];
	        this.channel = source["channel"];
	    }
	}
	export class Folder {
	    path: string;
	
	    static createFrom(source: any = {}) {
	        return new Folder(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.path = source["path"];
	    }
	}
	export class Ads {
	    links: AdLink[];
	    folders: Folder[];
	    files: Folder[];
	
	    static createFrom(source: any = {}) {
	        return new Ads(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.links = this.convertValues(source["links"], AdLink);
	        this.folders = this.convertValues(source["folders"], Folder);
	        this.files = this.convertValues(source["files"], Folder);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class Connection {
	    type: string;
	    link: string;
	    port: string;
	    mode: string;
	    expire_date: string;
	    expire_time: string;
	
	    static createFrom(source: any = {}) {
	        return new Connection(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.type = source["type"];
	        this.link = source["link"];
	        this.port = source["port"];
	        this.mode = source["mode"];
	        this.expire_date = source["expire_date"];
	        this.expire_time = source["expire_time"];
	    }
	}
	export class ConvertItem {
	    id: number;
	    location: string;
	    filename: string;
	    c_location: sql.NullString;
	    status: number;
	    size: number;
	    duration: number;
	    name: string;
	    description: sql.NullString;
	    episode: sql.NullString;
	    created_at: string;
	    updated_at: string;
	    storage_type: number;
	
	    static createFrom(source: any = {}) {
	        return new ConvertItem(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.id = source["id"];
	        this.location = source["location"];
	        this.filename = source["filename"];
	        this.c_location = this.convertValues(source["c_location"], sql.NullString);
	        this.status = source["status"];
	        this.size = source["size"];
	        this.duration = source["duration"];
	        this.name = source["name"];
	        this.description = this.convertValues(source["description"], sql.NullString);
	        this.episode = this.convertValues(source["episode"], sql.NullString);
	        this.created_at = source["created_at"];
	        this.updated_at = source["updated_at"];
	        this.storage_type = source["storage_type"];
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class Item {
	    start: string;
	    end: string;
	    type: string;
	    connection: string;
	    link: string;
	    name: string;
	    description: string;
	    port: string;
	    mode: string;
	    expire_date: string;
	    expire_time: string;
	    folders: Folder[];
	    files: Folder[];
	
	    static createFrom(source: any = {}) {
	        return new Item(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.start = source["start"];
	        this.end = source["end"];
	        this.type = source["type"];
	        this.connection = source["connection"];
	        this.link = source["link"];
	        this.name = source["name"];
	        this.description = source["description"];
	        this.port = source["port"];
	        this.mode = source["mode"];
	        this.expire_date = source["expire_date"];
	        this.expire_time = source["expire_time"];
	        this.folders = this.convertValues(source["folders"], Folder);
	        this.files = this.convertValues(source["files"], Folder);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class Day {
	    name: string;
	    date: string;
	    items: Item[];
	
	    static createFrom(source: any = {}) {
	        return new Day(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.name = source["name"];
	        this.date = source["date"];
	        this.items = this.convertValues(source["items"], Item);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class File {
	    file_id: number;
	    folder: string;
	    filename: string;
	    episode: sql.NullString;
	
	    static createFrom(source: any = {}) {
	        return new File(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.file_id = source["file_id"];
	        this.folder = source["folder"];
	        this.filename = source["filename"];
	        this.episode = this.convertValues(source["episode"], sql.NullString);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class Element {
	    start: string;
	    end: string;
	    title: string;
	    description: string;
	    type: string;
	    connection: Connection;
	    file: File;
	
	    static createFrom(source: any = {}) {
	        return new Element(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.start = source["start"];
	        this.end = source["end"];
	        this.title = source["title"];
	        this.description = source["description"];
	        this.type = source["type"];
	        this.connection = this.convertValues(source["connection"], Connection);
	        this.file = this.convertValues(source["file"], File);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	
	export class Fillers {
	    folders: Folder[];
	    files: Folder[];
	
	    static createFrom(source: any = {}) {
	        return new Fillers(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.folders = this.convertValues(source["folders"], Folder);
	        this.files = this.convertValues(source["files"], Folder);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	
	export class Guide {
	    id: number;
	    schedule_id: number;
	    elements: Element[];
	    updated_at: string;
	
	    static createFrom(source: any = {}) {
	        return new Guide(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.id = source["id"];
	        this.schedule_id = source["schedule_id"];
	        this.elements = this.convertValues(source["elements"], Element);
	        this.updated_at = source["updated_at"];
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	
	export class Recorder {
	    id: number;
	    name: string;
	    input: string;
	    rtp_url_id: number;
	    duration: string;
	    status: string;
	    VCodec: string;
	    ACodec: string;
	    resolution: string;
	    FPS: number;
	    sampleRate: number;
	    VBitrate: number;
	    ABitrate: number;
	    MaxVBitrate: number;
	
	    static createFrom(source: any = {}) {
	        return new Recorder(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.id = source["id"];
	        this.name = source["name"];
	        this.input = source["input"];
	        this.rtp_url_id = source["rtp_url_id"];
	        this.duration = source["duration"];
	        this.status = source["status"];
	        this.VCodec = source["VCodec"];
	        this.ACodec = source["ACodec"];
	        this.resolution = source["resolution"];
	        this.FPS = source["FPS"];
	        this.sampleRate = source["sampleRate"];
	        this.VBitrate = source["VBitrate"];
	        this.ABitrate = source["ABitrate"];
	        this.MaxVBitrate = source["MaxVBitrate"];
	    }
	}
	export class RtpUrl {
	    id: number;
	    url: string;
	    recorder_id: number;
	    created_at: string;
	    updated_at: string;
	
	    static createFrom(source: any = {}) {
	        return new RtpUrl(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.id = source["id"];
	        this.url = source["url"];
	        this.recorder_id = source["recorder_id"];
	        this.created_at = source["created_at"];
	        this.updated_at = source["updated_at"];
	    }
	}
	export class Schedule {
	    id: number;
	    name: string;
	    icon: string;
	    timezone: string;
	    created_at: string;
	    updated_at: string;
	    short_id: string;
	    autosave: boolean;
	    output_rtp_url: string;
	    ads: Ads;
	    channels: any[];
	    regular_days: Day[];
	    special_days: Day[];
	    fillers: Fillers;
	
	    static createFrom(source: any = {}) {
	        return new Schedule(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.id = source["id"];
	        this.name = source["name"];
	        this.icon = source["icon"];
	        this.timezone = source["timezone"];
	        this.created_at = source["created_at"];
	        this.updated_at = source["updated_at"];
	        this.short_id = source["short_id"];
	        this.autosave = source["autosave"];
	        this.output_rtp_url = source["output_rtp_url"];
	        this.ads = this.convertValues(source["ads"], Ads);
	        this.channels = source["channels"];
	        this.regular_days = this.convertValues(source["regular_days"], Day);
	        this.special_days = this.convertValues(source["special_days"], Day);
	        this.fillers = this.convertValues(source["fillers"], Fillers);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}

}

export namespace recorder {
	
	export class AudioInfo {
	    channels: number;
	    codec: string;
	
	    static createFrom(source: any = {}) {
	        return new AudioInfo(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.channels = source["channels"];
	        this.codec = source["codec"];
	    }
	}
	export class VideoInfo {
	    resolution: string;
	    codec: string;
	
	    static createFrom(source: any = {}) {
	        return new VideoInfo(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.resolution = source["resolution"];
	        this.codec = source["codec"];
	    }
	}
	export class ServiceInfo {
	    service_id: number;
	    service_name: string;
	
	    static createFrom(source: any = {}) {
	        return new ServiceInfo(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.service_id = source["service_id"];
	        this.service_name = source["service_name"];
	    }
	}
	export class RecorderStatus {
	    id: number;
	    rtp_url: string;
	    output_dir: string;
	    elapsed_seconds: number;
	    duration_seconds: number;
	    is_active: boolean;
	    start_time: string;
	    ts_sync: boolean;
	    services: ServiceInfo[];
	    video_info: VideoInfo;
	    audio_info: AudioInfo;
	
	    static createFrom(source: any = {}) {
	        return new RecorderStatus(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.id = source["id"];
	        this.rtp_url = source["rtp_url"];
	        this.output_dir = source["output_dir"];
	        this.elapsed_seconds = source["elapsed_seconds"];
	        this.duration_seconds = source["duration_seconds"];
	        this.is_active = source["is_active"];
	        this.start_time = source["start_time"];
	        this.ts_sync = source["ts_sync"];
	        this.services = this.convertValues(source["services"], ServiceInfo);
	        this.video_info = this.convertValues(source["video_info"], VideoInfo);
	        this.audio_info = this.convertValues(source["audio_info"], AudioInfo);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	

}

export namespace sql {
	
	export class NullString {
	    String: string;
	    Valid: boolean;
	
	    static createFrom(source: any = {}) {
	        return new NullString(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.String = source["String"];
	        this.Valid = source["Valid"];
	    }
	}

}

export namespace types {
	
	export class ConvertItemListResult {
	    items: document.ConvertItem[];
	    totalItems: number;
	
	    static createFrom(source: any = {}) {
	        return new ConvertItemListResult(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.items = this.convertValues(source["items"], document.ConvertItem);
	        this.totalItems = source["totalItems"];
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class File {
	    id: string;
	    fileName: string;
	    duration: number;
	    episode: sql.NullString;
	    name: string;
	    description: sql.NullString;
	
	    static createFrom(source: any = {}) {
	        return new File(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.id = source["id"];
	        this.fileName = source["fileName"];
	        this.duration = source["duration"];
	        this.episode = this.convertValues(source["episode"], sql.NullString);
	        this.name = source["name"];
	        this.description = this.convertValues(source["description"], sql.NullString);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class Folder {
	    folder: string;
	    path: string;
	    files: File[];
	    folders: Folder[];
	
	    static createFrom(source: any = {}) {
	        return new Folder(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.folder = source["folder"];
	        this.path = source["path"];
	        this.files = this.convertValues(source["files"], File);
	        this.folders = this.convertValues(source["folders"], Folder);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class Pagination {
	    page: number;
	    limit: number;
	    total_items: number;
	    total_pages: number;
	
	    static createFrom(source: any = {}) {
	        return new Pagination(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.page = source["page"];
	        this.limit = source["limit"];
	        this.total_items = source["total_items"];
	        this.total_pages = source["total_pages"];
	    }
	}
	export class ScheduleListResult {
	    items: document.Schedule[];
	    totalItems: number;
	
	    static createFrom(source: any = {}) {
	        return new ScheduleListResult(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.items = this.convertValues(source["items"], document.Schedule);
	        this.totalItems = source["totalItems"];
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}

}

