import type { Config } from "tailwindcss";
import { fontFamily } from "tailwindcss/defaultTheme";

const { heroui } = require("@heroui/react");

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
      },
      fontFamily: {
        everett: ["var(--font-mai)", ...fontFamily.sans],
        fira: ["var(--font-fira)", ...fontFamily.sans],
      },
      animation: {
        translateTop: "translateTop 1s forwards",
      },
      keyframes: {
        translateTop: {
          "0%": {
            transform: "translateY(-150%)",
          },
          "100%": {
            transform: "translateY(0)",
          },
        },
      },
      backdropBlur: {
        md: "12px",
      },
    },
  },
  plugins: [
    heroui({
      defaultTheme: "dark",
    }),

    require("tailwindcss-animate"),
  ],
};
export default config;
