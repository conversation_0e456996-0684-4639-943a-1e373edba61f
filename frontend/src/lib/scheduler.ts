import { document } from "../../wailsjs/go/models";
import Schedule = document.Schedule;
import { GetEPGLink } from "../../wailsjs/go/main/App";

export const getHLSLink = (schedule: Schedule, platform: string): string => {
    // TODO: change link to actual

    return `https://hls.showfer.com/master/${schedule.short_id}/${generateFileName(schedule.name)}/${platform}.m3u8`
}

export const getEPGLink = async (schedule: Schedule): Promise<string> => {
    return await GetEPGLink(schedule.short_id);
}

export const generateFileName = (name: string): string => {
    return name.trim().replace(/[^a-zA-Z0-9_ ]/g, '').replaceAll(' ', '_')
}

export const generateShortId = (length: number): string => {
    let result = '';
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const charactersLength = characters.length;
    let counter = 0;
    while (counter < length) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength));
        counter += 1;
    }
    return result;
}