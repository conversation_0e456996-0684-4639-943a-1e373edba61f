import momentTimezone from 'moment-timezone';

export const Timezones = (): { value: string, label: string }[] => {
    let offsetTmz = [];
    let timeZones = momentTimezone.tz.names();

    timeZones = timeZones.sort((a, b) => {
        let first  = 0;
        let second = 0;

        switch (true) {
            case -1 !== a.search(/US\//):
                first = 3;
                break;
            case -1 !== a.search(/America\//):
                first = 1;
                break;
            case a === a.toUpperCase() && a.length === 3:
            case a === a.toUpperCase() && a.length === 7 && /^\d+$/.test(a[3]):
                first = 2;
                break;
        }

        switch (true) {
            case -1 !== b.search(/US\//):
                second = 3;
                break;
            case -1 !== b.search(/America\//):
                second = 1;
                break;
            case b === b.toUpperCase() && b.length === 3:
            case b === b.toUpperCase() && b.length === 7 && /^\d+$/.test(b[3]):
                second = 2;
                break;
        }

        return second - first;
    })

    for (let i in timeZones) {
        offsetTmz.push({
            label: " (GMT"+momentTimezone.tz(timeZones[i]).format('Z')+") " + timeZones[i],
            value: timeZones[i]
        })
    }

    return offsetTmz;
}