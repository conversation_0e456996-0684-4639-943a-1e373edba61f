import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "../store";
import {ConvertItemsByFolder, GetSubfolders, ListConvertItems} from "../../../wailsjs/go/main/App";
import {document, types} from "../../../wailsjs/go/models";
import Pagination = types.Pagination;
import {IPagination} from "@/redux/common/common";
import ConvertItemListResult = types.ConvertItemListResult;
import ConvertItem = document.ConvertItem;

export interface IConvertItem {
    id: number|null
    location: string
    filename: string
    status: number
    size: number
    c_location: string
    duration: number
    name: string
    description: string
    episode: string
}

interface ISchedulers {
    items: IConvertItem[]
    folders: string[]
    pagination: IPagination
}

const initialState: ISchedulers = {
    items: [],
    folders: [],
    pagination: {
        page: 1,
        limit: 50,
        totalItems: 0,
        totalPages: 0,
    }
};

export const getConvertItems = async (pagination: Pagination): Promise<ConvertItemListResult> => {
    return await ListConvertItems(pagination);
};

export const getConvertItemsByLocation = async (location: string): Promise<ConvertItem[]> => {
    return await ConvertItemsByFolder(location);
}

export const getFoldersByLocation = async (location: string): Promise<string[]> => {
    return await GetSubfolders(location);
}

const fileManagerSlice = createSlice({
    name: 'file-manager',
    initialState,
    reducers: {
        setPage(state, action: PayloadAction<number>) {
            state.pagination.page = action.payload;
        },
        setLimit(state, action: PayloadAction<number>) {
            state.pagination.page = 1;
            state.pagination.limit = action.payload;
        },
        setConvertItems(state, action: PayloadAction<{
            items: any[],
            totalItems: number,
            totalPages: number,
            folders: string[],
        }>) {
            state.items = action.payload.items ?? [];
            state.folders = action.payload.folders ?? [];
            state.pagination = {
                ...state.pagination,
                totalPages: action.payload.totalPages,
                totalItems: action.payload.totalItems,
            }
        },
    }
})

export const selectConvertItems = (state: RootState) => state.fileManager.items;
export const selectFolders = (state: RootState) => state.fileManager.folders;
export const selectPagination = (state: RootState) => state.fileManager.pagination;

export const {
    setPage,
    setLimit,
    setConvertItems,
} = fileManagerSlice.actions;

export default fileManagerSlice.reducer;
