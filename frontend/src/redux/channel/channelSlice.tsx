import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "../store";

export interface IChannel {
  _id: string | null;
  title: string;
  slug: string;
}

interface IChannelSlice {
  channels: IChannel[]
}

// Initial state
const initialState: IChannelSlice = {
  channels: [],
};

export const getChannels = async (): Promise<IChannel[]> => {
  // if (!user?._id) return [];
  //
  // const { data } = await axiosApi.get(`${API_ROUTES.userChannels}/${user._id}`);
  // return data.data ?? [];

  return [];
};

export const createChannels = async (
  channels: IChannel[],
) => {

  // const { data } = await axiosApi.post(
  //   `${API_ROUTES.userChannels}/${user?._id}`,
  //   channels
  // );
  //
  // return data.data ?? [];

  return [];
};

// Slice
const channelSlice = createSlice({
  name: "channel",
  initialState,
  reducers: {
    setChannels(state, action: PayloadAction<IChannel[]>) {
      state.channels = action.payload;
    },
  },
});

// Selectors
export const selectChannels = (state: RootState) => state.channel.channels;

// Actions
export const { setChannels } =
  channelSlice.actions;

// Reducer
export default channelSlice.reducer;
