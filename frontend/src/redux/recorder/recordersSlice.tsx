import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "../store";
import { GetRecorders } from "../../../wailsjs/go/main/App";
import { document, types } from "../../../wailsjs/go/models";
import Recorder = document.Recorder;

export interface IPagination {
    page: number;
    limit: number;
    total_items: number;
    total_pages: number;
}

interface IRecorders {
    recorders: Recorder[];
    pagination: IPagination;
    isLoading: boolean;
}

const initialState: IRecorders = {
    recorders: [],
    pagination: {
        page: 1,
        limit: 50,
        total_items: 0,
        total_pages: 0,
    },
    isLoading: false
};

// This function should be updated once backend supports pagination
export const getRecordersWithPagination = async (pagination: IPagination): Promise<{items: Recorder[], totalItems: number}> => {
    // Add a timestamp to prevent caching issues
    const allRecorders = await GetRecorders();

    // Force the state to reflect what's in the database by always working with a fresh copy
    const startIndex = (pagination.page - 1) * pagination.limit;
    const endIndex = Math.min(startIndex + pagination.limit, allRecorders.length);

    return {
        items: allRecorders.slice(startIndex, endIndex),
        totalItems: allRecorders.length
    };
};

const recordersSlice = createSlice({
    name: 'recorders',
    initialState,
    reducers: {
        setPage(state, action: PayloadAction<number>) {
            state.pagination.page = action.payload;
        },
        setLimit(state, action: PayloadAction<number>) {
            state.pagination.page = 1;
            state.pagination.limit = action.payload;
        },
        setRecorders(state, action: PayloadAction<{
            recorders: Recorder[],
            totalItems: number,
            totalPages: number,
        }>) {
            state.recorders = action.payload.recorders;
            state.pagination = {
                ...state.pagination,
                total_pages: action.payload.totalPages,
                total_items: action.payload.totalItems,
            };
        },
        setLoading(state, action: PayloadAction<boolean>) {
            state.isLoading = action.payload;
        },
        updateRecorderStatus(state, action: PayloadAction<{id: number, status: 'running' | 'stopped' | 'completed' | 'failed'}>) {
            const { id, status } = action.payload;
            const recorderIndex = state.recorders.findIndex(r => r.id === id);
            if (recorderIndex !== -1) {
                state.recorders[recorderIndex].status = status;
            }
        }
    }
});

export const selectRecorders = (state: RootState) => state.recorders.recorders;
export const selectPagination = (state: RootState) => state.recorders.pagination;
export const selectIsLoading = (state: RootState) => state.recorders.isLoading;

export const {
    setPage,
    setLimit,
    setRecorders,
    setLoading,
    updateRecorderStatus
} = recordersSlice.actions;

export default recordersSlice.reducer;