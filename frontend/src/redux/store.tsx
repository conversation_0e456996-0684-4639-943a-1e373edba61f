import { configureStore } from "@reduxjs/toolkit";

import schedulerSlice from "@/redux/scheduler/schedulerSlice";
import channelSlice from "@/redux/channel/channelSlice";
import schedulersSlice from "@/redux/scheduler/schedulersSlice";
import fileManagerSlice from "@/redux/file-manager/fileManagerSlice";
import recordersSlice from "@/redux/recorder/recordersSlice";

export const makeStore = () =>
  configureStore({
    reducer: {
      scheduler: schedulerSlice,
      schedulers: schedulersSlice,
      fileManager: fileManagerSlice,
      channel: channelSlice,
      recorders: recordersSlice,
    },
  });

export type AppStore = ReturnType<typeof makeStore>;
export type RootState = ReturnType<AppStore["getState"]>;

let store: AppStore | undefined;

export const getStore = () => {
  if (typeof window === "undefined") return makeStore();
  if (!store) store = makeStore();
  return store;
};
