import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "../store";
import { EmptyScheduler } from "./schedulerSlice";
import { CreateSchedule, DeleteSchedule, ListSchedules } from "../../../wailsjs/go/main/App";
import { document, types } from "../../../wailsjs/go/models";
import Schedule = document.Schedule;
import Pagination = types.Pagination;
import ScheduleListResult = types.ScheduleListResult;

interface ISchedulers {
    schedulers: Schedule[]
    pagination: Pagination
}

const initialState: ISchedulers = {
    schedulers: [],
    pagination: {
        page: 1,
        limit: 50,
        total_items: 0,
        total_pages: 0,
    }
};

export const getSchedules = async (pagination: Pagination): Promise<ScheduleListResult> => {
    return await ListSchedules(pagination);
};

export const createSchedule = async (name: string): Promise<number> => {
    const newScheduler = EmptyScheduler;
    newScheduler.name = name;

    return await CreateSchedule(newScheduler);
}

export const deleteSchedule = async (id: number): Promise<void> => {
    return await DeleteSchedule(id);
}

const schedulersSlice = createSlice({
    name: 'schedulers',
    initialState,
    reducers: {
        setPage(state, action: PayloadAction<number>) {
            state.pagination.page = action.payload;
        },
        setLimit(state, action: PayloadAction<number>) {
            state.pagination.page = 1;
            state.pagination.limit = action.payload;
        },
        setSchedulers(state, action: PayloadAction<{
            schedulers: any[],
            totalItems: number,
            totalPages: number,
        }>) {
            state.schedulers = action.payload.schedulers;
            state.pagination = {
                ...state.pagination,
                total_pages: action.payload.totalPages,
                total_items: action.payload.totalItems,
            }
        },
    }
})

export const selectSchedulers = (state: RootState) => state.schedulers.schedulers;
export const selectPagination = (state: RootState) => state.schedulers.pagination;

export const {
    setPage,
    setLimit,
    setSchedulers,
} = schedulersSlice.actions;

export default schedulersSlice.reducer;
