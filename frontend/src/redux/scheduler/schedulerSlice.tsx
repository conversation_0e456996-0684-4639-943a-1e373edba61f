import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "../store";
import { REGULAR, SPECIAL } from "@/const/day-type";
import moment from "moment";
import { generateShortId } from "@/lib/scheduler";
import {
    FilesToNestedStructure,
    GetGuide,
    GetSchedule,
    UpdateConvertItems,
    UpdateSchedule,
    GenerateGuide,
} from "../../../wailsjs/go/main/App";
import { document, types } from "../../../wailsjs/go/models";
import Schedule = document.Schedule;
import Day = document.Day;
import Fillers = document.Fillers;
import Ads = document.Ads;
import Item = document.Item;
import Guide = document.Guide;
import { IChannel } from "@/redux/channel/channelSlice";
import File = types.File;
import ConvertItem = document.ConvertItem;

export interface FolderProps {
    path: string
}

interface ISchedulerState {
    scheduler: Schedule
    activeItem: Item|null
    guide: Guide|null
}

export const EmptyScheduler: Schedule = Schedule.createFrom({
    id: 0,
    short_id: generateShortId(5),
    name: '',
    timezone: 'America/Los_Angeles',
    icon: '',
    created_at: moment().format(),
    updated_at: moment().format(),
    output_rtp_url: '',
    ads: new Ads({
        links: [],
        folders: [],
        files: [],
    }),
    fillers: new Fillers({
        folders: [],
        files: [],
    }),
    regular_days: [
        new Day({
            name: 'Sun',
            items: [],
        }),
        new Day({
            name: 'Mon',
            items: [],
        }),
        new Day({
            name: 'Tue',
            items: [],
        }),
        new Day({
            name: 'Wed',
            items: [],
        }),
        new Day({
            name: 'Thu',
            items: [],
        }),
        new Day({
            name: 'Fri',
            items: [],
        }),
        new Day({
            name: 'Sat',
            items: [],
        }),
    ],
    special_days: [],
    autosave: false,
    channels: [],
})

const initialState: ISchedulerState = {
    scheduler: EmptyScheduler,
    activeItem: null,
    guide: Guide.createFrom({
        _id: null,
        elements: [],
    })
};

export const getSchedule = async (id: number) => {
    const schedule = await GetSchedule(id);
    const guide = await GetGuide(id);

    return {
        schedule,
        guide,
    }
};



export const getFiles = async () => {
    return await FilesToNestedStructure();
}

export const batchUpdateFiles = async (files: File[]) => {
    const updatedFiles = files.map((file) => {
        return ConvertItem.createFrom({
            id: Number(file.id),
            name: file.name,
            description: file.description,
            episode: file.episode,

        })
    });

    await UpdateConvertItems(updatedFiles)
}

export const uploadIcon = async (file: File) => {
    return [];
}

export const saveScheduler = async (schedule: Schedule) => {
    return await UpdateSchedule(schedule)
}

export const generateGuide = async (schedule: Schedule) => {
    await GenerateGuide(schedule.id)
}

const normalizeDate = (date: string, isEnd: boolean) => {
    let newDate;
    let start = moment(date).utc();
    let dayNumber = start.day();
    if (dayNumber === 0 && start.format('HH:mm:ss') === '00:00:00' && isEnd) {
        newDate = moment().add(1, 'weeks').utc().startOf('week').add(dayNumber, 'days')
    } else {
        newDate = moment().utc().startOf('week').add(dayNumber, 'days');
    }

    return newDate.format('YYYY-MM-DD') + 'T' + start.format('HH:mm:ss') + 'Z';
}

const sortItems = (scheduler: Schedule) => {
    const days: Item[][] = [];

    scheduler.regular_days.forEach(day => {
        day.items.forEach(item => {
            const dayNumber = moment(item.start).utc().day();
            if (!days.hasOwnProperty(dayNumber)) {
                days[dayNumber] = [];
            }
            days[dayNumber].push(item);
        });
    });

    scheduler.regular_days.map((day, index) => {
        if (days.hasOwnProperty(index)) {
            day.items = days[index];
        } else {
            day.items = []
        }

        return day;
    });

    scheduler.regular_days.map(day => {
        return {
            ...day,
            items: day.items.sort((a: Item, b: Item) => {
                return moment(a.start).diff(moment(b.start))
            }),
        }
    });

    scheduler.special_days.map(day => {
        return {
            ...day,
            items: day.items.sort((a: Item, b: Item) => {
                return moment(a.start).diff(moment(b.start))
            }),
        }
    });

    return scheduler;
}


const schedulerSlice = createSlice({
    name: 'scheduler',
    initialState,
    reducers: {
        openScheduler: (state, action: PayloadAction<{schedule: Schedule, guide: Guide}>) => {
            let schedule = action.payload.schedule;

            schedule.regular_days = schedule.regular_days.map((day) => {
                return Day.createFrom({
                    ...day,
                    items: day.items.map((item) => {
                        return {
                            ...item,
                            start: normalizeDate(item.start, false),
                            end: normalizeDate(item.end, true),
                        }
                    })
                });
            });

            schedule = sortItems(schedule);
            if (!schedule.ads) {
                schedule = Schedule.createFrom({
                    ...schedule,
                    ads: Ads.createFrom({
                        links: [],
                        folders: [],
                        files: [],
                    })
                })
            }

            return {
                ...state,
                guide: action.payload.guide,
                scheduler: schedule,
            };
        },
        changeSchedulerDetail: (
            state,
            action: PayloadAction<{
                field: string,
                value: string|boolean|string[]|IChannel[]|null
            }>
        ) => {
            return {
                ...state,
                scheduler: {
                    ...state.scheduler,
                    [action.payload.field]: action.payload.value,
                }
            }
        },
        addNewItem(
            state,
            action: PayloadAction<{
                type: string,
                day: string,
                item: Item
            }>
        ) {
            switch (action.payload.type) {
                case REGULAR:
                    state.scheduler.regular_days.map(day => {
                        if (day.name === action.payload.day) {
                            day.items.push(action.payload.item);
                        }
                    });
                    break;
                case SPECIAL:
                    state.scheduler.special_days = state.scheduler.special_days ?? []
                    const hasDay = state.scheduler.special_days.find((day) => day.name === action.payload.day);
                    if (!hasDay) {
                        const newDay = Day.createFrom({
                            name: action.payload.day,
                            date: action.payload.day,
                            items: [],
                        })
                        state.scheduler.special_days.push(newDay);
                    }

                    state.scheduler.special_days.map(day => {
                        if (day.name === action.payload.day) {
                            day.items.push(action.payload.item);
                        }
                    })
                    break;
            }

            state.scheduler = sortItems(state.scheduler);
        },
        editItemTime(
            state,
            action: PayloadAction<{
                type: string,
                oldStart: string,
                item: {
                    start: string,
                    end: string,
                },
            }>
        ) {
            if (action.payload.type === SPECIAL) {
                state.scheduler.special_days = state.scheduler.special_days.map(day => {
                    return Day.createFrom({
                        ...day,
                        items: day.items.map((item: Item) => {
                            if (item.start === action.payload.oldStart) {
                                return {
                                    ...item,
                                    start: action.payload.item.start,
                                    end: action.payload.item.end,
                                }
                            }

                            return item;
                        })
                    })
                });
            } else {
                const itemsByDays: { items: Item[] }[] = [];
                for (let i = 0; i < 7; i++) {
                    itemsByDays.push({items: []})
                }
                state.scheduler.regular_days.map(day => {
                    day.items.forEach((item: Item) => {
                        if (item.start === action.payload.oldStart) {
                            item = Item.createFrom({
                                ...item,
                                start: action.payload.item.start,
                                end: action.payload.item.end,
                            })
                        }
                        const dayIndex = moment(item.start).utc().day();

                        itemsByDays[dayIndex].items.push(item);
                    })
                })

                state.scheduler.regular_days = state.scheduler.regular_days.map((day, index) => {
                    return {
                        ...day,
                        items: itemsByDays[index].items
                    }
                });

                state.scheduler = sortItems(state.scheduler);
            }
        },
        editConnectionItem(
            state,
            action: PayloadAction<{
                type: string,
                start: string,
                connection: string,
                link: string,
                name: string,
                description: string|undefined,
                port: string|undefined,
                mode: string|undefined,
                expireDate: string|undefined,
                expireTime: string|undefined,
            }>
        ) {
            const days = action.payload.type === REGULAR ? state.scheduler.regular_days : state.scheduler.special_days;

            const updatedDays = days.map(day => {
                return Day.createFrom({
                    ...day,
                    items: day.items.map((item: Item) => {
                        if (item.start === action.payload.start) {
                            return {
                                ...item,
                                connection: action.payload.connection,
                                link: action.payload.link,
                                name: action.payload.name,
                                description: action.payload.description,
                            }
                        }

                        return item;
                    })
                })
            });

            switch (action.payload.type) {
                case REGULAR:
                    state.scheduler.regular_days = updatedDays;
                    break;
                default:
                    state.scheduler.special_days = updatedDays;
            }
        },
        editFolderItem(
            state,
            action: PayloadAction<{
                type: string,
                start: string,
                folders: FolderProps[],
                files: FolderProps[],
            }>
        ) {
            const days = action.payload.type === REGULAR ? state.scheduler.regular_days : state.scheduler.special_days;

            const updatedDays = days.map(day => {
                return Day.createFrom({
                    ...day,
                    items: day.items.map((item: Item) => {
                        if (item.start === action.payload.start) {
                            return {
                                ...item,
                                folders: action.payload.folders,
                                files: action.payload.files,
                            }
                        }

                        return item;
                    })
                })
            });

            switch (action.payload.type) {
                case REGULAR:
                    state.scheduler.regular_days = updatedDays;
                    break;
                default:
                    state.scheduler.special_days = updatedDays;
            }
        },
        deleteItem(
            state,
            action: PayloadAction<{
                type: string,
                start: string,
            }>
        ) {
            const days = action.payload.type === REGULAR ? state.scheduler.regular_days : state.scheduler.special_days;

            const updatedDays = days.map(day => {
                return {
                    ...day,
                    items: day.items.filter((item: Item) => {
                        return item.start !== action.payload.start
                    })
                }
            });
            state.activeItem = null;

            switch (action.payload.type) {
                case REGULAR:
                    state.scheduler.regular_days = updatedDays;
                    break;
                default:
                    state.scheduler.special_days = updatedDays.filter(day => day.items.length > 0);
            }
        },
        selectItem(
            state,
            action: PayloadAction<Item|null>
        ) {
            state.activeItem = action.payload;
        },
        deleteSpecialDay(state, action: PayloadAction<string>) {
            state.scheduler.special_days = state.scheduler.special_days.filter(day => day.name !== action.payload);
        },
        updateAds(state, action: PayloadAction<Ads>) {
            state.scheduler.ads = action.payload;
        },
        updateFillers(state, action: PayloadAction<Fillers>) {
            state.scheduler.fillers = action.payload;
        }
    }
})

export const selectSchedule  = (state: RootState) => state.scheduler.scheduler;
export const selectGuide = (state: RootState) => state.scheduler.guide;
export const selectRegularDays = (state: RootState) => state.scheduler.scheduler.regular_days;
export const selectSpecialDays = (state: RootState) => state.scheduler.scheduler.special_days;
export const selectActiveItem  = (state: RootState) => state.scheduler.activeItem;
export const selectAds  = (state: RootState) => state.scheduler.scheduler.ads;
export const selectFillers = (state: RootState) => state.scheduler.scheduler.fillers;

export const {
    openScheduler,
    changeSchedulerDetail,
    addNewItem,
    editItemTime,
    editConnectionItem,
    editFolderItem,
    selectItem,
    deleteItem,
    deleteSpecialDay,
    updateAds,
    updateFillers,
} = schedulerSlice.actions;

export default schedulerSlice.reducer;
