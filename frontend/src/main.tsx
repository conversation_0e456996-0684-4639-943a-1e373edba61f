import React from 'react'
import { createRoot } from 'react-dom/client'
import { <PERSON>hRout<PERSON> } from "react-router-dom";
import App from './App'
import {ReduxProvider} from "./redux/reduxProvider";
import {HeroUIProvider} from "@heroui/react";

import './style.css'

const container = document.getElementById('root')

const root = createRoot(container!)

root.render(
    <React.StrictMode>
        <HeroUIProvider>
            <HashRouter basename={"/"}>
                <ReduxProvider>
                    <App/>
                </ReduxProvider>
            </HashRouter>
        </HeroUIProvider>
    </React.StrictMode>
)
