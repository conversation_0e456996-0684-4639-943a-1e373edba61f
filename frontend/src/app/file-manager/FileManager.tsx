import { HiArrowUpOnSquareStack } from "react-icons/hi2";
import { useEffect, useState} from "react";
import { ProcessFiles, SelectFiles } from "../../../wailsjs/go/main/App";
import {EventsEmit, EventsOff, EventsOn, OnFileDrop, OnFileDropOff} from "../../../wailsjs/runtime";
import {
    getConvertItemsByLocation, getFoldersByLocation, IConvertItem,
    setConvertItems
} from "@/redux/file-manager/fileManagerSlice";
import { useDispatch } from "react-redux";
import ConvertItemTable from "@/components/file-manager/(table)/ConvertItemTable";
import CreateFolderModal from "@/components/file-manager/(modal)/CreateFolderModal";
import LocationMenu from "@/components/file-manager/LocationMenu";
import DeleteFilesModal from "@/components/file-manager/(modal)/DeleteFilesModal";
import Navigation from "@/components/common/Navigation";
import {document} from "../../../wailsjs/go/models";
import ConvertItem = document.ConvertItem;
import PreviewModal from "@/components/file-manager/(modal)/PreviewModal";

const FileManager = () => {
    const dispatch = useDispatch();
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [reload, setReload] = useState<boolean>(false);
    const [location, setLocation] = useState<string>("/");

    const [isShowCreateFolderModal, setIsShowCreateFolderModal] = useState<boolean>(false);
    const [deletingFile, setDeletingFile] = useState<IConvertItem|null>(null);
    const [previewFile, setPreviewFile] = useState<IConvertItem|null>(null);

    useEffect(() => {
        const fetchConvertItems = async () => {
            setIsLoading(true);

            try {
                const response = await getConvertItemsByLocation(location);
                const folders = await getFoldersByLocation(location);
                dispatch(setConvertItems({
                    items: response,
                    folders: folders,
                    totalPages: 0,
                    totalItems: 0,
                }));
            } catch (error) {
                console.error(error);
            } finally {
                setIsLoading(false);
            }
        };

        fetchConvertItems();
    }, [location, reload]);

    useEffect(() => {
        EventsOn("change:status", (_: ConvertItem) => {
            setReload(!reload);
        })

        return () => EventsOff(
            "change:status",
        )
    }, [])

    const handleSelectFiles = async () => {
        await SelectFiles()
            .then(async (result) => {
                await ProcessFiles(result, location)
                setReload(!reload);
            })
            .catch(error => {
                EventsEmit("notification:error", error)
            })
    }

    useEffect(() => {
        OnFileDrop(async (_: number, __: number, paths: string[]): Promise<void> => {
             await ProcessFiles(paths, location)
            setReload(!reload);
        }, false);

        return OnFileDropOff
    }, []);

    const addFolder = (name: string) => {
        setLocation([location === '/' ? '' : location, name].join('/'));
    }

    return (
        <>
            <Navigation />
            {
                isShowCreateFolderModal &&
                <CreateFolderModal addFolder={addFolder} close={() => setIsShowCreateFolderModal(false)} />
            }
            {
                deletingFile &&
                <DeleteFilesModal file={deletingFile} close={() => setDeletingFile(null)} reload={() => setReload(!reload)} />
            }
            {
                previewFile &&
                <PreviewModal close={() => setPreviewFile(null)} previewFile={previewFile} />
            }
            <div className={'w-full h-full p-5 flex flex-col gap-5'}>

                <div className="flex items-center justify-center w-full">
                    <div
                        onClick={handleSelectFiles}
                        className="flex flex-col items-center justify-center w-full h-32 border-2 border-[#27272a] rounded-lg cursor-pointer bg-[#18181b] hover:bg-[#27272a]"
                    >
                        <div className="flex flex-col items-center justify-center pt-5 pb-6">
                            <HiArrowUpOnSquareStack
                                className={'w-8 h-8 mb-4 text-gray-500 dark:text-gray-400'}
                            />
                            <p className="mb-2 text-sm text-gray-500 dark:text-gray-400">
                                <span className="text-[#ffa500]">Click to upload</span> or
                                drag and drop
                            </p>
                        </div>
                    </div>
                </div>
                <LocationMenu location={location} setLocation={setLocation} setIsShowCreateFolderModal={setIsShowCreateFolderModal} />
                <ConvertItemTable isLoading={isLoading} location={location} setLocation={setLocation} setDeletingFile={setDeletingFile} setPreviewFile={setPreviewFile}/>
            </div>
        </>
    );
}

export default FileManager;
