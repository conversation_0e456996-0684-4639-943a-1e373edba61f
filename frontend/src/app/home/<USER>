import logo from "../../assets/images/logo.png";
import { motion } from "motion/react";
import { Link } from "react-router-dom";
import SchedulerIcon from "../../assets/icons/SchedulerIcon";
import TranscoderIcon from "../../assets/icons/TranscoderIcon";
import FileManagerIcon from "../../assets/icons/FileManagerIcon";
import SettingIcon from "../../assets/icons/SettingIcon";
import RecorderIcon from "../../assets/icons/RecorderIcon";

const Home = () => {
    return (
        <>
            <div
                className={'w-full flex justify-center items-center h-[50%]'}
            >
                <img
                    src={logo}
                    id="logo"
                    alt="logo"
                    className={'w-[50%] min-w-[600px]'}
                />
            </div>
            <div className={'flex gap-10 w-full p-10 justify-center items-center overflow-x-auto'}>
                <motion.div
                    initial={{ opacity: 0, scale: 0.5 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{
                        duration: 0.8,
                        delay: 0.25,
                        ease: [0, 0.71, 0.2, 1.01],
                    }}
                >
                    <Link to={'/scheduler'}>
                        <div
                            className={'flex flex-col backdrop-blur-sm gap-3 text-2xl min-w-[200px] w-[200px] h-[200px] bg-[#292929c4] border-[2px] border-[#4D4D4D] rounded-[12px] justify-center items-center cursor-pointer hover:border-[#ffa5004d] hover:shadow-[0_0_20px_0_#ffa500]'}
                        >
                            <SchedulerIcon/>
                            Scheduler
                        </div>
                    </Link>
                </motion.div>
                {/*<motion.div*/}
                {/*    initial={{ opacity: 0, scale: 0.5 }}*/}
                {/*    animate={{ opacity: 1, scale: 1 }}*/}
                {/*    transition={{*/}
                {/*        duration: 0.8,*/}
                {/*        delay: 0.5,*/}
                {/*        ease: [0, 0.71, 0.2, 1.01],*/}
                {/*    }}*/}
                {/*>*/}
                {/*    <Link to={'/transcoder'}>*/}
                {/*        <div*/}
                {/*            className={'flex flex-col backdrop-blur-sm gap-3 text-2xl min-w-[200px] w-[200px] h-[200px] bg-[#292929c4] border-[2px] border-[#4D4D4D] rounded-[12px] justify-center items-center cursor-pointer hover:border-[#ffa5004d] hover:shadow-[0_0_20px_0_#ffa500]'}*/}
                {/*        >*/}
                {/*            <TranscoderIcon/>*/}
                {/*            Transcoder*/}
                {/*        </div>*/}
                {/*    </Link>*/}
                {/*</motion.div>*/}
                <motion.div
                    initial={{ opacity: 0, scale: 0.5 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{
                        duration: 0.8,
                        delay: 0.75,
                        ease: [0, 0.71, 0.2, 1.01],
                    }}
                >
                    <Link to={'/file-manager'}>
                        <div
                            className={'flex flex-col backdrop-blur-sm gap-3 text-2xl min-w-[200px] w-[200px] h-[200px] bg-[#292929c4] border-[2px] border-[#4D4D4D] rounded-[12px] justify-center items-center cursor-pointer hover:border-[#ffa5004d] hover:shadow-[0_0_20px_0_#ffa500]'}
                        >
                            <FileManagerIcon/>
                            File Manager
                        </div>
                    </Link>
                </motion.div>
                <motion.div
                    initial={{ opacity: 0, scale: 0.5 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{
                        duration: 0.8,
                        delay: 0.75,
                        ease: [0, 0.71, 0.2, 1.01],
                    }}
                >
                    <Link to={'/recorder'}>
                        <div
                            className={'flex flex-col backdrop-blur-sm gap-3 text-2xl min-w-[200px] w-[200px] h-[200px] bg-[#292929c4] border-[2px] border-[#4D4D4D] rounded-[12px] justify-center items-center cursor-pointer hover:border-[#ffa5004d] hover:shadow-[0_0_20px_0_#ffa500]'}
                        >
                            <RecorderIcon/>
                            Input Feed
                        </div>
                    </Link>
                </motion.div>
                {/*<motion.div*/}
                {/*    initial={{ opacity: 0, scale: 0.5 }}*/}
                {/*    animate={{ opacity: 1, scale: 1 }}*/}
                {/*    transition={{*/}
                {/*        duration: 0.8,*/}
                {/*        delay: 1,*/}
                {/*        ease: [0, 0.71, 0.2, 1.01],*/}
                {/*    }}*/}
                {/*>*/}
                {/*    <Link to={'/settings'}>*/}
                {/*        <div*/}
                {/*            className={'flex flex-col backdrop-blur-sm gap-3 text-2xl min-w-[200px] w-[200px] h-[200px] bg-[#292929c4] border-[2px] border-[#4D4D4D] rounded-[12px] justify-center items-center cursor-pointer hover:border-[#ffa5004d] hover:shadow-[0_0_20px_0_#ffa500]'}*/}
                {/*        >*/}
                {/*            <SettingIcon/>*/}
                {/*            Settings*/}
                {/*        </div>*/}
                {/*    </Link>*/}
                {/*</motion.div>*/}
            </div>
        </>
    )
}

export default Home;