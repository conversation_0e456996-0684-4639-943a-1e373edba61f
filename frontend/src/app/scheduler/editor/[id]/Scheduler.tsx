import "./style.css";

import React, { useEffect, useRef, useState } from "react";
import FullCalendar from "@fullcalendar/react";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";
import SideBar from "@/components/scheduler/(sidebar)/SideBar";
import AddItemModal from "@/components/scheduler/(modal)/AddItemModal/AddItemModal";
import {
  changeSchedulerDetail,
  editItemTime,
  generateGuide,
  getSchedule,
  openScheduler,
  saveScheduler,
  selectActiveItem,
  selectItem,
  selectRegularDays,
  selectSchedule,
} from "@/redux/scheduler/schedulerSlice";
import {
  DateSelectArg,
  EventChangeArg,
  EventClickArg,
  EventInput,
} from "@fullcalendar/core";
import EditItemModal from "@/components/scheduler/(modal)/EditItemModal/EditItemModal";
import ConnectionItem from "@/components/scheduler/(item)/ConnectionItem";
import FolderItem from "@/components/scheduler/(item)/FolderItem";
import CalendarSection from "@/components/scheduler/(sidebar)/CalendarSection";
import { REGULAR } from "@/const/day-type";
import Loading from "@/components/common/Loading";
import Navigation from "@/components/common/Navigation";
import {useDispatch, useSelector} from "react-redux";
import GuideSection from "@/components/scheduler/(sidebar)/GuideSection";
import PreviewModal from "@/components/scheduler/(modal)/PreviewModal/PreviewModal";
import AdsSettingModal from "@/components/scheduler/(modal)/AdsSettingModal/AdsSettingModal";
import moment from "moment";
import FillerSettingModal from "@/components/scheduler/(modal)/FillerSettingModal/FillerSettingModal";
import ContentSettingModal from "@/components/scheduler/(modal)/ContentSettingModal/ContentSettingModal";
import RtpOutputSettingModal from "@/components/scheduler/(modal)/RtpOutputSettingModal/RtpOutputSettingModal";
import {useNavigate, useParams} from "react-router-dom";
import {EventsEmit} from "../../../../../wailsjs/runtime";
import { getChannels, setChannels } from "@/redux/channel/channelSlice";
import { document } from "../../../../../wailsjs/go/models";
import Item = document.Item;

const Scheduler = () => {
  let { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  if (!id) {
    EventsEmit("notification:error", "Schedule not found")
    navigate("/scheduler");
    return <></>;
  }

  const schedule = useSelector(selectSchedule);
  const timerDebounceRef = useRef<NodeJS.Timeout | null>(null);
  const dirty = React.useRef(false);

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isChangeAction, setIsChangeAction] = useState<boolean>(false);
  const [isSaveAction, setIsSaveAction] = useState<boolean>(false);
  const [hideLeftControls, setHideLeftControls] = useState<boolean>(false);
  const [hideCalendarSection, setHideCalendarSection] = useState<boolean>(true);
  const [hideGuideSection, setHideGuideSection] = useState<boolean>(true);
  const [previewURL, setPreviewURL] = useState<string | null>(null);
  const [isAddItemModalOpen, setIsAddItemModalOpen] = useState<boolean>(false);
  const [isContentSettingModalOpen, setIsContentSettingModalOpen] = useState<boolean>(false);
  const [isEditItemModalOpen, setIsEditItemModalOpen] = useState<boolean>(false);
  const [isAdsSettingModalOpen, setIsAdsSettingModalOpen] = useState<boolean>(false);
  const [isFillerSettingModalOpen, setIsFillerSettingModalOpen] = useState<boolean>(false);
  const [isRtpOutputSettingModalOpen, setIsRtpOutputSettingModalOpen] = useState<boolean>(false);

  const [type, setType] = useState<string>(REGULAR);
  const [day, setDay] = useState<string>("");
  const [start, setStart] = useState<string>("");
  const [end, setEnd] = useState<string>("");

  const regularDays = useSelector(selectRegularDays);
  const activeItem = useSelector(selectActiveItem);

  useEffect(() => {
    const fetchSchedule = async () => {
      setIsLoading(true);
      try {
        if (id) {
          const response = await getSchedule(Number.parseInt(id));

          dispatch(openScheduler(response));
        }

        const userChannels = await getChannels();
        dispatch(setChannels(userChannels));
      } catch (err) {
        EventsEmit("notification:error", err)
        navigate("/scheduler");
      } finally {
        setIsLoading(false);
      }
    };

    fetchSchedule();
  }, [id]);

  useEffect(() => {
    dirty.current = isChangeAction;
    if (isChangeAction && schedule.autosave) {
      saveDebounce();
    }
  }, [schedule]);

  useEffect(() => {
    const onBeforeUnload = (ev: { returnValue: boolean }) => {
      if (dirty.current) {
        ev.returnValue = true;
        return true;
      }
    };
    window.addEventListener("beforeunload", onBeforeUnload);
    return () => {
      window.removeEventListener("beforeunload", onBeforeUnload);
    };
  }, []);

  const saveDebounce = () => {
    if (timerDebounceRef.current) {
      clearTimeout(timerDebounceRef.current);
    }
    timerDebounceRef.current = setTimeout(() => {
      setIsSaveAction(true);
      saveScheduler(schedule)
        .then(() => {
          setIsChangeAction(false);
          dispatch(
            changeSchedulerDetail({
              field: "updatedAt",
              value: moment().format(),
            })
          );
        })
        .finally(() => {
          setIsSaveAction(false);
        });
    }, 3000);
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      const guide = await saveScheduler(schedule);
      if (!guide.id) {
        await generateGuide(schedule);
      }
      const response = await getSchedule(schedule.id);
      dispatch(openScheduler(response));
    } catch (err) {
      EventsEmit("notification:error", err);
    } finally {
      setIsLoading(false);
      setIsChangeAction(false);
    }
  };

  const events: EventInput[] = [];
  regularDays.forEach((day) => {
    day.items.forEach((item) => {
      events.push(item);
    });
  });

  const shortDayName = (date: Date) =>
    date.toLocaleDateString("en", { weekday: "short" });

  const editItem = (arg: EventChangeArg, type: string) => {
    setIsChangeAction(true);
    dispatch(
      editItemTime({
        type: type,
        oldStart: arg.oldEvent.startStr,
        item: {
          start: arg.event.startStr,
          end: arg.event.endStr,
        },
      })
    );
  };

  const selectSection = (day: string, info: DateSelectArg, type: string) => {
    setDay(day);
    setStart(info.startStr);
    setEnd(info.endStr);
    setIsAddItemModalOpen(true);
    setType(type);
  };

  const editEvent = (arg: EventClickArg, type: string) => {
    setType(type);
    const event = arg.event;
    const props = event.extendedProps;
    setIsEditItemModalOpen(true);

    dispatch(
      selectItem(Item.createFrom({
        start: event.startStr,
        end: event.endStr,
        type: props.type,
        connection: props.connection,
        link: props.link,
        folders: props.folders,
        files: props.files,
        name: props.name,
        description: props.description,
        port: props.port,
        mode: props.mode,
        expire_date: props.expireDate,
        expire_time: props.expireTime,
      }))
    );
  };

  return (
    <>
    <Navigation />
    <div className="flex w-full min-w-full h-full min-h-full">
      {previewURL && (
        <PreviewModal
          close={() => setPreviewURL(null)}
          previewURL={previewURL}
        />
      )}
      {isAdsSettingModalOpen && (
        <AdsSettingModal
          close={() => setIsAdsSettingModalOpen(false)}
          setChangeAction={setIsChangeAction}
          setIsLoading={setIsLoading}
        />
      )}
      {isFillerSettingModalOpen && (
        <FillerSettingModal
          close={() => setIsFillerSettingModalOpen(false)}
          setChangeAction={setIsChangeAction}
          setIsLoading={setIsLoading}
        />
      )}
      {isRtpOutputSettingModalOpen && (
        <RtpOutputSettingModal
          close={() => setIsRtpOutputSettingModalOpen(false)}
          setChangeAction={setIsChangeAction}
        />
      )}
      <SideBar
        hidden={hideLeftControls}
        setHidden={setHideLeftControls}
        setChangeAction={setIsChangeAction}
        hideCalendarSection={hideCalendarSection}
        hideGuideSection={hideGuideSection}
        setHideCalendarSection={setHideCalendarSection}
        setHideGuideSection={setHideGuideSection}
        handleSave={handleSave}
        setPreviewURL={setPreviewURL}
        openSettingModal={() => setIsAdsSettingModalOpen(true)}
        openFillerModal={() => setIsFillerSettingModalOpen(true)}
        openContentSettingModal={() => setIsContentSettingModalOpen(true)}
        openRtpOutputSettingModal={() => setIsRtpOutputSettingModalOpen(true)}
        isSaveAction={isSaveAction}
      />
      <div
        style={{ width: `calc(100vw - ${hideLeftControls ? 60 : 290}px)` }}
        className="flex flex-col relative"
      >
        {!hideCalendarSection && (
          <CalendarSection
            changeView={() => setHideCalendarSection(true)}
            selectSection={selectSection}
            editItem={editItem}
            editEvent={editEvent}
          />
        )}
        {!hideGuideSection && (
          <GuideSection
            setPreviewURL={setPreviewURL}
            setIsLoading={setIsLoading}
            hide={() => setHideGuideSection(true)}
          />
        )}
        <div className="w-full h-full">
          <div className={"h-full w-full"}>
            {
              isContentSettingModalOpen &&
              <ContentSettingModal
                close={() => setIsContentSettingModalOpen(false)}
                setIsLoading={setIsLoading}
              />
            }
            {isAddItemModalOpen && (
              <AddItemModal
                type={type}
                day={day}
                start={start}
                end={end}
                setChangeAction={setIsChangeAction}
                close={() => setIsAddItemModalOpen(false)}
              />
            )}
            {isEditItemModalOpen && activeItem && (
              <EditItemModal
                type={type}
                setChangeAction={setIsChangeAction}
                close={() => setIsEditItemModalOpen(false)}
              />
            )}
            <FullCalendar
              timeZone={"UTC"}
              editable={true}
              eventOverlap={false}
              selectOverlap={false}
              dayHeaderFormat={{ weekday: "short" }}
              headerToolbar={{
                left: "",
                center: "",
                right: "",
              }}
              slotLabelFormat={{
                hour: "2-digit",
                minute: "2-digit",
              }}
              selectable={true}
              initialView={"timeGridWeek"}
              allDaySlot={false}
              scrollTime={"00:00:00"}
              plugins={[interactionPlugin, timeGridPlugin]}
              select={(info) =>
                selectSection(shortDayName(info.start), info, REGULAR)
              }
              eventClick={(arg) => editEvent(arg, REGULAR)}
              eventDrop={(arg) => editItem(arg, REGULAR)}
              eventResize={(arg) => editItem(arg, REGULAR)}
              height={"100%"}
              locale={"UTC"}
              events={events}
              eventColor={"#1E1E1E"}
              eventContent={(arg) => {
                const props = arg.event.extendedProps;
                if (props.type === "connection") {
                  return (
                    ConnectionItem ?
                      <ConnectionItem
                        connection={props.connection}
                        link={props.link}
                        name={props.name}
                        port={props.port}
                        mode={props.mode}
                      /> :
                      null
                  );
                }

                return (
                  FolderItem ?
                    <FolderItem folders={props.folders || []} files={props.files || []} /> :
                    null
                );
              }}
            />
          </div>
        </div>
      </div>
      {isLoading && <Loading />}
    </div>
    </>
  );
};

export default Scheduler;
