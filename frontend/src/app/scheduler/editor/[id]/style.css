@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
    --background: #0a0a0a;
    --foreground: #ededed;
}

@layer components {
    .react-select-container .react-select__control {
        @apply bg-[#151515] border border-[#4D4D4D] rounded-md hover:border-[#ffa500];
    }

    .react-select-container .react-select__control--is-focused {
        @apply border-[#4D4D4D] hover:border-[#ffa500] shadow-none;
    }

    .react-select-container .react-select__menu {
        @apply bg-[#151515] border border-[#4D4D4D] rounded-md;
    }

    .react-select-container .react-select__option {
        @apply text-zinc-50 bg-[#151515] hover:bg-[#1E1E1E] text-nowrap cursor-pointer;
    }

    .react-select-container .react-select__indicator-separator {
        @apply bg-[#151515];
    }

    .react-select-container .react-select__input-container,
    .react-select-container .react-select__single-value {
        @apply text-zinc-50;
    }
}

.fc-timegrid-slot {
    height: 4em!important; /* 1.5em by default */
}

.fc .fc-toolbar.fc-header-toolbar {
    margin-bottom: 0!important;
}

.fc-event {
    background: #0f0f0fe5!important;
    border: 1px solid #4D4D4D!important;
    border-radius: 5px!important;
}

.fc-event:hover {
    border-color: #ffa500!important;
    box-shadow: 0 0 4px 2px #ffa500!important;
}

.fc-theme-standard .fc-scrollgrid {
    border: 3px solid #0f0f0fe5!important;
}

.fc-theme-standard th {
    background: #0f0f0fe5!important;
}

.fc-theme-standard td, .fc-theme-standard th {
    border: 3px solid #0f0f0fe5!important;
}

.fc-theme-standard td .fc-timegrid-now-indicator-container {
    background: #202020!important;
    border-radius: 5px!important;
}

.fc-theme-standard td .fc-highlight {
    border: 1px solid #4D4D4D!important;
    background: #0f0f0fe5!important;
    border-radius: 5px!important;
}

.fc-theme-standard th .fc-scrollgrid-sync-inner {
    background: #202020!important;
    border-radius: 10px!important;
    padding: 5px!important;
}

.fc-timegrid-event-harness-inset .fc-timegrid-event {
    box-shadow: none!important;
}

.fc-scrollgrid-shrink, .fc-timegrid-slot-label {
    background: #0f0f0fe5!important;
}

.rmdp-day {
    color: #ffffff!important;
}

.rmdp-header-values {
    color: #ffffff!important;
}

.rmdp-week-day {
    color: #ffa500!important;
}

.rmdp-arrow {
    border: solid #ffa500!important;
    border-width: 0 2px 2px 0!important;
    margin: 0 !important;
}

.rmdp-arrow-container {
    display: flex;
    align-items: center;
}

.rmdp-arrow-container:hover {
    background: #ffa500!important;
}

.rmdp-arrow-container:hover .rmdp-arrow {
    border: solid #ffffff!important;
    border-width: 0 2px 2px 0!important;
}

.rmdp-day.rmdp-today span {
    background: rgba(255, 165, 0, 0.4) !important;
}

.rmdp-day.rmdp-selected span:not(.highlight) {
    background: #ffa500!important;
}

.rmdp-day:not(.rmdp-disabled,.rmdp-day-hidden) span:hover {
    background: #ffffff!important;
    color: #0f0f0fe5!important;
}

.rmdp-shadow {
    box-shadow: none!important;
}

#player-component .btn {
    width: auto!important;
    height: 44px;
}

#player-component .btn svg {
    padding: 0!important;
}