import { useDispatch, useSelector } from "react-redux";
import { useEffect, useState } from "react";
import Loading from "@/components/common/Loading";
import Navigation from "@/components/common/Navigation";
import {
    getSchedules,
    selectPagination,
    selectSchedulers,
    setSchedulers,
} from "@/redux/scheduler/schedulersSlice";
import EmptyPage from "@/components/schedulers/EmptyPage";
import CreateScheduleModal from "@/components/schedulers/(modal)/CreateScheduleModal";
import DeleteScheduleModal from "@/components/schedulers/(modal)/DeleteScheduleModal";
import SchedulerTable from "@/components/schedulers/(table)/SchedulerTable";
import { getChannels, setChannels } from "@/redux/channel/channelSlice";

const Schedulers = () => {
    const dispatch = useDispatch();
    const [isLoading, setIsLoading] = useState(false);

    const schedulers = useSelector(selectSchedulers);
    const pagination = useSelector(selectPagination);

    const [createModalOpen, setCreateModalOpen] = useState<boolean>(false);
    const [deleteModalOpen, setDeleteModalOpen] = useState<boolean>(true);
    const [scheduleId, setScheduleId] = useState<number|null>(null);
    const [reload, setReload] = useState<boolean>(false);

    useEffect(() => {
        const fetchSchedulers = async () => {
            setIsLoading(true);

            try {
                const response = await getSchedules(pagination);

                const totalPages = Math.ceil(response.totalItems / pagination.limit);

                dispatch(
                    setSchedulers({
                        schedulers: response.items,
                        totalPages: totalPages,
                        totalItems: response.totalItems,
                    })
                );
            } catch (error) {
                console.error(error);
            } finally {
                setIsLoading(false);
            }
        };

        fetchSchedulers();
    }, [pagination.page, pagination.limit, reload]);

    useEffect(() => {
        const fetch = async () => {
            try {
                const userChannels = await getChannels();
                dispatch(setChannels(userChannels));
            } catch (error) {
                console.error(error);
            } finally {
            }
        };
        fetch();
    }, [null]);

    if (isLoading) return <Loading />;

    return (
        <>
            <Navigation />
            <div className="w-full overflow-x-auto mt-[10px] px-[30px]">
                {(!schedulers || schedulers.length == 0) && (
                    <EmptyPage handleCreate={() => setCreateModalOpen(true)} />
                )}
                {createModalOpen && (
                    <CreateScheduleModal
                        setIsLoading={setIsLoading}
                        close={() => setCreateModalOpen(false)}
                        reloadPage={() => setReload(!reload)}
                    />
                )}
                {deleteModalOpen && scheduleId && (
                    <DeleteScheduleModal
                        id={scheduleId}
                        setIsLoading={setIsLoading}
                        reloadPage={() => setReload(!reload)}
                        close={() => {
                            setDeleteModalOpen(false);
                            setScheduleId(null);
                        }}
                    />
                )}
                {schedulers && schedulers.length > 0 && (
                    <SchedulerTable
                        handleCreate={() => setCreateModalOpen(true)}
                        handleDelete={(id: number|null) => {
                            setScheduleId(id);
                            setDeleteModalOpen(true);
                        }}
                    />
                )}
            </div>
        </>
    );
}

export default Schedulers;
