import { useEffect, useState } from "react";
import { HiOutlinePlusCircle } from "react-icons/hi2";
import { CreateRecorderModal } from "@/components/recorder/(modal)/CreateRecorderModal";
import EmptyPage from "@/components/recorder/EmptyPage";
import { Recorder as IRecorder, RecorderInput as IRecorderInput } from '@/types/recorder';
import { createRecorder, deleteRecorder, startRecorder, stopRecorder, updateRecorder } from '@/services/recorder';
import { useDispatch, useSelector } from "react-redux";
import {
  getRecordersWithPagination,
  selectIsLoading,
  selectPagination,
  selectRecorders,
  setLoading,
  setRecorders,
  updateRecorderStatus
} from "@/redux/recorder/recordersSlice";
import Pagination from "@/components/recorder/(table)/Pagination";
import Loading from "@/components/common/Loading";
import Navigation from "@/components/common/Navigation";
import { addToast } from "@heroui/react";
import { RecorderStatus, calculateProgress, formatSeconds, getRecorderStatus } from "@/services/recorderStatus";
import { setupRecorderStatusListener } from "@/services/recorderEvents";

// Helper function to convert seconds to HH:MM:SS format
const secondsToHHMMSS = (seconds: string): string => {
  const secs = parseInt(seconds);
  if (isNaN(secs)) return seconds; // If not a valid number, return as is

  const hours = Math.floor(secs / 3600);
  const minutes = Math.floor((secs % 3600) / 60);
  const remainingSeconds = secs % 60;

  return [hours, minutes, remainingSeconds]
    .map(val => val.toString().padStart(2, '0'))
    .join(':');
};

const Recorder = () => {
  const dispatch = useDispatch();
  const recorders = useSelector(selectRecorders);
  const pagination = useSelector(selectPagination);
  const isLoading = useSelector(selectIsLoading);

  const [isShowCreateModal, setIsShowCreateModal] = useState(false);
  const [isShowEditModal, setIsShowEditModal] = useState(false);
  const [selectedRecorder, setSelectedRecorder] = useState<IRecorder | null>(null);
  const [reload, setReload] = useState(false);
  const [recorderStatuses, setRecorderStatuses] = useState<RecorderStatus[]>([]);
  const [statusInterval, setStatusInterval] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    loadRecorders();
  }, [pagination.page, pagination.limit, reload]);

  // Set up event listener for recorder status updates
  useEffect(() => {
    // Set up the event listener and get the cleanup function
    const cleanup = setupRecorderStatusListener((update) => {
      // When we receive a status update, just update that specific recorder's status
      // without reloading the entire list
      dispatch(updateRecorderStatus({
        id: update.id,
        status: update.status
      }));
    });

    // Return the cleanup function to remove the event listener when the component unmounts
    return cleanup;
  }, []);

  // Set up polling for recorder status (only for progress updates)
  useEffect(() => {
    // Check if any recorders are running
    const hasRunningRecorders = recorders.some(r => r.status === 'running');

    // If we have running recorders, start polling for status
    if (hasRunningRecorders) {
      // Clear any existing interval
      if (statusInterval) {
        clearInterval(statusInterval);
      }

      // Set up a new interval to fetch status every second
      const interval = setInterval(fetchRecorderStatus, 1000);
      setStatusInterval(interval);
    } else {
      // If no recorders are running, clear the interval
      if (statusInterval) {
        clearInterval(statusInterval);
        setStatusInterval(null);
      }

      // Clear the statuses
      setRecorderStatuses([]);
    }

    // Cleanup on unmount
    return () => {
      if (statusInterval) {
        clearInterval(statusInterval);
      }
    };
  }, [recorders]);

  const fetchRecorderStatus = async () => {
    try {
      // Just fetch and update the status for UI display (progress bars, etc.)
      const statuses = await getRecorderStatus();
      setRecorderStatuses(statuses);

      // The backend now handles updating the database when recorders complete
      // and sends events to notify the frontend
    } catch (error) {
      console.error("Failed to fetch recorder status:", error);
    }
  };

  const loadRecorders = async () => {
    try {
      dispatch(setLoading(true));

      // Clear the current recorders before loading new ones to avoid stale data
      dispatch(
        setRecorders({
          recorders: [],
          totalItems: 0,
          totalPages: 0,
        })
      );

      const response = await getRecordersWithPagination(pagination);

      const totalPages = Math.ceil(response.totalItems / pagination.limit);

      dispatch(
        setRecorders({
          recorders: response.items,
          totalItems: response.totalItems,
          totalPages: totalPages,
        })
      );
    } catch (error) {
      console.error("Failed to load recorders:", error);
    } finally {
      dispatch(setLoading(false));
    }
  };

  const handleCreateRecorder = async (config: IRecorderInput) => {
    try {
      dispatch(setLoading(true));
      await createRecorder({
        name: config.name,
        rtp_url: config.rtp_url,
        duration: config.duration,
        vcodec: config.vcodec,
        acodec: config.acodec,
        resolution: config.resolution,
        fps: config.fps,
        sample_rate: config.sample_rate,
        vbitrate: config.vbitrate,
        abitrate: config.abitrate,
        max_vbitrate: config.max_vbitrate,
      });

      setIsShowCreateModal(false);
      // Reload the recorders list
      setReload(!reload);
    } catch (error) {
      console.error("Failed to create recorder:", error);
    } finally {
      dispatch(setLoading(false));
    }
  };

  const handleUpdateRecorder = async (id: number, config: IRecorderInput) => {
    try {
      dispatch(setLoading(true));
      // Find the current recorder to get its rtp_url_id
      const currentRecorder = recorders.find(r => r.id === id);

      const updatedRecorder: IRecorder = {
        id,
        name: config.name,
        input: config.rtp_url,
        rtp_url_id: currentRecorder?.rtp_url_id || 0, // Use existing rtp_url_id or default to 0
        duration: config.duration,
        status: 'stopped',
        VCodec: config.vcodec,
        ACodec: config.acodec,
        resolution: config.resolution,
        FPS: config.fps,
        sampleRate: config.sample_rate,
        VBitrate: config.vbitrate,
        ABitrate: config.abitrate,
        MaxVBitrate: config.max_vbitrate,
      };

      await updateRecorder(updatedRecorder);
      setIsShowEditModal(false);
      setSelectedRecorder(null);
      // Reload the recorders list
      setReload(!reload);
    } catch (error) {
      console.error("Failed to update recorder:", error);
    } finally {
      dispatch(setLoading(false));
    }
  };

  const handleDeleteRecorder = async (id: number) => {
    try {
      dispatch(setLoading(true));

      // Delete the recorder in the backend
      await deleteRecorder(id);

      // After successful deletion, reload the list to get the updated data
      // This is necessary because we need to update pagination and total counts
      loadRecorders();
    } catch (error) {
      console.error("Failed to delete recorder:", error);
      addToast({
        title: "Error",
        description: `Failed to delete recorder: ${error}`
      });
      dispatch(setLoading(false));
    }
  };

  const handleToggleRecorderStatus = async (id: number) => {
    try {
      dispatch(setLoading(true));
      const recorder = recorders.find(r => r.id === id);
      if (!recorder) return;

      // Update the UI immediately for better responsiveness
      const newStatus = recorder.status === 'running' ? 'stopped' : 'running';

      // Update the status in the Redux store
      dispatch(updateRecorderStatus({
        id: id,
        status: newStatus
      }));

      try {
        if (recorder.status === 'running') {
          await stopRecorder(id);
          // Show a notification
          addToast({
            title: "Input Feed Stopped",
            description: `Input Feed "${recorder.name}" has been stopped.`
          });
        } else {
          await startRecorder(id);
          // Show a notification
          addToast({
            title: "Input Feed Started",
            description: `Input Feed "${recorder.name}" has started recording.`
          });
        }
      } catch (error) {
        console.error("Failed to toggle recorder status:", error);

        // If there was an error, revert the UI change
        dispatch(updateRecorderStatus({
          id: id,
          status: recorder.status as 'running' | 'stopped'
        }));

        addToast({
          title: "Failed to Toggle Input Feed",
          description: `Error: ${error}`
        });

        // Return early without further updates
        dispatch(setLoading(false));
        return;
      }

      // No need to reload the entire list since we've already updated the UI
      dispatch(setLoading(false));
    } catch (error) {
      console.error("Failed to toggle recorder status:", error);
      addToast({
        title: "Error",
        description: `Failed to toggle recorder status: ${error}`
      });
      dispatch(setLoading(false));
    }
  };

  if (isLoading && recorders.length === 0) return <Loading />;

  return (
    <>
      <Navigation />
      {isShowCreateModal && (
        <CreateRecorderModal
          close={() => setIsShowCreateModal(false)}
          onSubmit={handleCreateRecorder}
        />
      )}
      {isShowEditModal && selectedRecorder && (
        <CreateRecorderModal
          close={() => {
            setIsShowEditModal(false);
            setSelectedRecorder(null);
          }}
          initialData={{
            name: selectedRecorder.name,
            rtp_url: selectedRecorder.input,
            duration: selectedRecorder.duration,
            vcodec: selectedRecorder.VCodec,
            acodec: selectedRecorder.ACodec,
            resolution: selectedRecorder.resolution,
            fps: selectedRecorder.FPS,
            sample_rate: selectedRecorder.sampleRate,
            vbitrate: selectedRecorder.VBitrate,
            abitrate: selectedRecorder.ABitrate,
            max_vbitrate: selectedRecorder.MaxVBitrate
          }}
          onSubmit={(config) => {
            handleUpdateRecorder(selectedRecorder.id, config);
          }}
          key={selectedRecorder.id} // Add a key to force re-render when a different recorder is selected
        />
      )}
      <div className="w-full h-full p-5 relative">
        {isLoading && recorders.length > 0 && (
          <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center z-10">
            <div className="loader w-10 h-10 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}
        {recorders.length === 0 ? (
          <>
            <EmptyPage handleCreate={() => setIsShowCreateModal(true)} />
          </>
        ) : (
          <>
            <div
              className="flex mt-[10px] mb-[20px] w-fit h-[40px] bg-gradient-to-r from-[#006fee] to-[#4A90E2] rounded-full cursor-pointer items-center pl-[12px] pr-[18px] gap-[8px] float-start"
              onClick={() => setIsShowCreateModal(true)}
            >
              <HiOutlinePlusCircle className="text-[#ffffff] w-[24px] h-[24px]" />
              <span className="text-[#ffffff] text-[14px] select-none">
                Create new Input Feed
              </span>
            </div>

            <div className="w-full overflow-x-auto text-white rounded-lg border border-[#4D4D4D] shadow-md">
              <table className="min-w-full border-collapse rounded-lg overflow-hidden table-fixed">
                <thead>
                  <tr className="bg-[#151515] border-b border-[#4D4D4D]">
                    <th className="p-3 text-left w-[15%]">Name</th>
                    <th className="p-3 text-left w-[15%]">Input</th>
                    <th className="p-3 text-left w-[10%]">Duration</th>
                    <th className="p-3 text-left w-[20%]">Video</th>
                    <th className="p-3 text-left w-[15%]">Audio</th>
                    <th className="p-3 text-left w-[10%]">Status</th>
                    <th className="p-3 text-left w-[15%]">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {recorders.map((recorder, index) => (
                    <tr
                      key={recorder.id}
                      className={`border-b border-[#4D4D4D] hover:bg-[#1E1E1E] transition-colors ${
                        index === recorders.length - 1 ? 'rounded-b-lg' : ''
                      }`}
                    >
                      <td className="p-3">{recorder.name}</td>
                      <td className="p-3 text-gray-400">{recorder.input}</td>
                      <td className="p-3 text-gray-400">{secondsToHHMMSS(recorder.duration)}</td>
                      <td className="p-3 text-gray-400">
                        <div className="text-xs">
                          <div>{recorder.VCodec} ({recorder.resolution})</div>
                          <div className="text-gray-500">{recorder.FPS}fps @ {recorder.VBitrate}kbps</div>
                        </div>
                      </td>
                      <td className="p-3 text-gray-400">
                        <div className="text-xs">
                          <div>{recorder.ACodec}</div>
                          <div className="text-gray-500">{recorder.sampleRate}Hz @ {recorder.ABitrate}kbps</div>
                        </div>
                      </td>
                      <td className="p-3">
                        <div className="flex flex-col gap-1 min-w-[120px]">
                          {/* Compact status indicator */}
                          <div className="flex items-center gap-1.5">
                            {/* Status indicator dot */}
                            <div className="relative">
                              <div
                                className={`w-2 h-2 rounded-full ${
                                  recorder.status === 'running' ? 'bg-green-500' :
                                  recorder.status === 'completed' ? 'bg-green-500' :
                                  recorder.status === 'failed' ? 'bg-red-500' : 'bg-red-500'
                                }`}
                              ></div>
                              {recorder.status === 'running' && (
                                <div className="absolute inset-0 w-2 h-2 rounded-full bg-green-400 animate-ping opacity-75"></div>
                              )}
                            </div>

                            {/* Status text */}
                            <span
                              className={`font-medium text-xs ${
                                recorder.status === 'running' ? 'text-green-500' :
                                recorder.status === 'completed' ? 'text-green-500' :
                                recorder.status === 'failed' ? 'text-red-500' : 'text-red-500'
                              }`}
                            >
                              {recorder.status === 'running' ? 'RUNNING' :
                               recorder.status === 'completed' ? 'COMPLETED' :
                               recorder.status === 'failed' ? 'FAILED' : 'STOPPED'}
                            </span>
                          </div>

                          {/* Show progress if recorder is running and we have status info */}
                          {recorder.status === 'running' &&
                            recorderStatuses.some(status => status.id === recorder.id) && (
                              <div className="mt-0.5">
                                {(() => {
                                  const status = recorderStatuses.find(s => s.id === recorder.id);
                                  if (!status) return null;

                                  const progress = calculateProgress(status.elapsed_seconds, status.duration_seconds);
                                  const timeRemaining = status.duration_seconds - status.elapsed_seconds;

                                  return (
                                    <>
                                      <div className="w-full bg-gray-800 rounded-sm h-1.5 mb-1 overflow-hidden">
                                        <div
                                          className="bg-blue-500 h-1.5 rounded-sm"
                                          style={{ width: `${progress}%` }}
                                        ></div>
                                      </div>
                                      <div className="flex justify-between text-[10px] leading-tight">
                                        <span className="text-blue-400">{formatSeconds(status.elapsed_seconds)}</span>
                                        <span className="text-gray-400 truncate max-w-[60px] text-center">
                                          {timeRemaining > 0 ? formatSeconds(timeRemaining) : '00:00:00'}
                                        </span>
                                        <span className="text-gray-400">{formatSeconds(status.duration_seconds)}</span>
                                      </div>

                                      {/* TS Sync Status */}
                                      <div className="mt-1 flex items-center gap-1.5">
                                        <div
                                          className={`w-2 h-2 rounded-full ${
                                            status.ts_sync ? 'bg-green-500' : 'bg-yellow-500'
                                          }`}
                                        ></div>
                                        <span
                                          className={`font-medium text-xs ${
                                            status.ts_sync ? 'text-green-500' : 'text-yellow-500'
                                          }`}
                                        >
                                          TS {status.ts_sync ? 'SYNC' : 'NO SYNC'}
                                        </span>
                                      </div>

                                      {/* Stream Information */}
                                      {status.ts_sync && (
                                        <div className="mt-1 text-[10px] text-gray-400">
                                          {/* Services */}
                                          {status.services.length > 0 && (
                                            <div className="mb-0.5">
                                              <span className="text-blue-400">Services: </span>
                                              {status.services.map((service, idx) => (
                                                <span key={service.service_id}>
                                                  {service.service_name || `ID: ${service.service_id}`}
                                                  {idx < status.services.length - 1 ? ', ' : ''}
                                                </span>
                                              ))}
                                            </div>
                                          )}

                                          {/* Video Info */}
                                          {status.video_info && status.video_info.codec !== "Unknown" && (
                                            <div className="mb-0.5">
                                              <span className="text-blue-400">Video: </span>
                                              {status.video_info.codec} ({status.video_info.resolution})
                                            </div>
                                          )}

                                          {/* Audio Info */}
                                          {status.audio_info && status.audio_info.codec !== "Unknown" && (
                                            <div>
                                              <span className="text-blue-400">Audio: </span>
                                              {status.audio_info.codec} ({status.audio_info.channels} ch)
                                            </div>
                                          )}
                                        </div>
                                      )}
                                    </>
                                  );
                                })()}
                              </div>
                            )
                          }
                        </div>
                      </td>
                      <td className="p-3">
                        <div className="flex flex-wrap gap-1 min-w-[120px]">
                          <button
                            className={`px-2 py-1 rounded-md flex items-center gap-1 text-xs font-medium transition-all ${
                              recorder.status === 'running'
                                ? 'bg-red-500/10 text-red-500 hover:bg-red-500/20 border border-red-500/30'
                                : 'bg-green-500/10 text-green-500 hover:bg-green-500/20 border border-green-500/30'
                            }`}
                            onClick={() => handleToggleRecorderStatus(recorder.id)}
                            title={recorder.status === 'running' ? 'Stop recording' : 'Start recording'}
                          >
                            {recorder.status === 'running' ? (
                              <>
                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <rect x="6" y="6" width="12" height="12" rx="2" />
                                </svg>
                                <span className="sm:inline hidden">Stop</span>
                              </>
                            ) : (
                              <>
                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <polygon points="5 3 19 12 5 21 5 3" />
                                </svg>
                                <span className="sm:inline hidden">Start</span>
                              </>
                            )}
                          </button>
                          <button
                            className={`px-2 py-1 rounded-md flex items-center gap-1 text-xs font-medium transition-all ${
                              recorder.status === 'running'
                                ? 'bg-blue-500/5 text-blue-400/50 border border-blue-500/10 cursor-not-allowed'
                                : 'bg-blue-500/10 text-blue-400 hover:bg-blue-500/20 border border-blue-500/30 cursor-pointer'
                            }`}
                            onClick={() => {
                              // Only allow editing if the recorder is not running
                              if (recorder.status !== 'running') {
                                // Make a deep copy of the recorder to prevent status updates from affecting the edit modal
                                const recorderCopy = JSON.parse(JSON.stringify(recorder));
                                setSelectedRecorder({
                                  ...recorderCopy,
                                  status: recorderCopy.status
                                });
                                setIsShowEditModal(true);
                              }
                            }}
                            disabled={recorder.status === 'running'}
                            title={recorder.status === 'running' ? "Cannot edit while recording" : "Edit recorder"}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <path d="M12 20h9"></path>
                              <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
                            </svg>
                            <span className="sm:inline hidden">Edit</span>
                          </button>
                          <button
                            className="px-2 py-1 bg-red-500/10 text-red-500 rounded-md hover:bg-red-500/20 border border-red-500/30 transition-all text-xs font-medium flex items-center gap-1"
                            onClick={() => handleDeleteRecorder(recorder.id)}
                            title="Delete recorder"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <path d="M3 6h18"></path>
                              <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                              <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                            </svg>
                            <span className="sm:inline hidden">Delete</span>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {/* Only show pagination when there are more than 5 items */}
              {pagination.total_pages > 1 && (
                <div className="mt-4 mb-2">
                  <Pagination />
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default Recorder;
