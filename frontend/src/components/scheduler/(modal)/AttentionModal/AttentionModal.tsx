import React from "react";
import { HiInformationCircle, HiOutlineXCircle } from "react-icons/hi2";
import {
  generateGuide,
  getSchedule,
  openScheduler,
  saveScheduler,
  selectSchedule,
} from "@/redux/scheduler/schedulerSlice";
import { useDispatch, useSelector } from "react-redux";
import { EventsEmit } from "../../../../../wailsjs/runtime";

interface AttentionModalProps {
  close: () => void;
  setIsLoading: (loading: boolean) => void;
}

export default function AttentionModal({
  close,
  setIsLoading,
}: AttentionModalProps) {
  const dispatch = useDispatch();
  const schedule = useSelector(selectSchedule);

  const handleGenerateGuide = async () => {
    setIsLoading(true);
    try {
      await saveScheduler(schedule);
      await generateGuide(schedule);
      const response = await getSchedule(schedule.id);
      dispatch(openScheduler(response));
    } catch (err) {
      EventsEmit("notification:error", err)
    } finally {
      close();
      setIsLoading(false);
    }
  };

  return (
    <div
      className="relative z-40 bg-[#1E1E1E]"
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div
        className="fixed inset-0 bg-[#0d0c0c] bg-opacity-50 transition-opacity"
        aria-hidden="true"
      />
      <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
        <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <div
            className="relative transform rounded-lg bg-[#1E1E1E] text-center shadow-xl transition-all sm:my-8"
            style={{ width: "800px" }}
          >
            <p className={"p-4 text-xl font-bold"}>Attention!</p>
            <div
              className={
                "absolute top-2 right-2 z-10 cursor-pointer hover:text-[#891C1CFF]"
              }
              onClick={close}
            >
              <HiOutlineXCircle className={"h-[30px] w-[30px]"} />
            </div>

            <div className="bg-[#1E1E1E] p-4 relative">
              <div
                className={"flex flex-col justify-between items-center gap-14"}
              >
                <div
                  className={"flex flex-col justify-between items-center gap-5"}
                >
                  <p className={"text-justify indent-4"}>
                    The guide updates automatically every 12 hours and creates a
                    schedule for the day that will be in 3 days. This allows you
                    to maintain a static and up-to-date EPG. If you run the
                    update now, the guide will update and overwrite the data
                    immediately after the current program. The following
                    problems may occur after the update:
                  </p>
                  <ul
                    className={
                      "text-justify marker:text-[#fffff] list-disc list-inside"
                    }
                  >
                    <li>
                      If the EPG is already in use somewhere, there may be
                      discrepancies between the previously saved EPG and the
                      current HLS
                    </li>
                    <li>
                      Sorting of episodes may be disrupted or videos that have
                      already been shown may be shown again
                    </li>
                  </ul>
                  <p
                    className={
                      "text-sm text-[#939393] flex items-center justify-center gap-2"
                    }
                  >
                    <HiInformationCircle />
                    The latest changes to the scheduler will now also be saved
                  </p>
                  <p className={"font-bold"}>
                    Are you sure you want to force update the guide now?
                  </p>
                </div>
                <div className={"w-[400px] flex gap-5"}>
                  <button
                    onClick={close}
                    className={
                      "flex justify-center items-center h-[40px] gap-3 rounded-md w-full bg-[#151515A3] border border-[#4D4D4D] hover:border-[#ffa500]"
                    }
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleGenerateGuide}
                    className={
                      "flex justify-center items-center h-[40px] gap-3 rounded-md w-full bg-[#891C1CFF] border-[#4D4D4D] border hover:border-[#FFFFFF]"
                    }
                  >
                    Update
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
