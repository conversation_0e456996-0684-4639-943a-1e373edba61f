import React from "react";
import { HiOutlineXCircle } from "react-icons/hi2";
import ConnectionBody from "@/components/scheduler/(modal)/EditItemModal/ConnectionBody";
import { selectActiveItem } from "@/redux/scheduler/schedulerSlice";
import FileBody from "@/components/scheduler/(modal)/EditItemModal/FileBody";
import { useSelector } from "react-redux";

interface EditItemModalProps {
  close: () => void;
  type: string;
  setChangeAction: (value: boolean) => void;
}

export default function EditItemModal({
  close,
  type,
  setChangeAction,
}: EditItemModalProps) {
  const activeItem = useSelector(selectActiveItem);

  return (
    <div
      className="relative z-20 bg-[#1E1E1E]"
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div
        className="fixed inset-0 bg-[#0d0c0c] bg-opacity-50 transition-opacity"
        aria-hidden="true"
      />
      <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
        <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <div
            className="relative transform rounded-lg bg-[#1E1E1E] text-left shadow-xl transition-all sm:my-8"
            style={{ width: "800px" }}
          >
            <div
              className={
                "absolute top-2 right-2 z-10 cursor-pointer hover:text-[#891C1CFF]"
              }
              onClick={close}
            >
              <HiOutlineXCircle className={"h-[30px] w-[30px]"} />
            </div>
            {activeItem &&
              activeItem.type === "connection" &&
              activeItem.connection &&
              activeItem.name && (
                <ConnectionBody
                  type={type}
                  start={activeItem.start}
                  close={close}
                  connection={activeItem.connection}
                  url={activeItem.link ?? ""}
                  name={activeItem.name}
                  description={activeItem.description}
                  port={activeItem.port}
                  mode={activeItem.mode}
                  expireDate={activeItem.expire_date}
                  expireTime={activeItem.expire_time}
                />
              )}
            {activeItem &&
              activeItem.type === "folder" &&
              activeItem.folders &&
              activeItem.files && (
                <FileBody
                  setChangeAction={setChangeAction}
                  type={type}
                  start={activeItem.start}
                  close={close}
                  addedFolders={activeItem.folders}
                  addedFiles={activeItem.files}
                />
              )}
          </div>
        </div>
      </div>
    </div>
  );
}
