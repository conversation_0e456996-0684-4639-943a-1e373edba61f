import React, { useState } from "react";
import { HiOutlineXCircle } from "react-icons/hi2";
import FileBody from "@/components/scheduler/(modal)/ContentSettingModal/FileBody";
import QuestionModal from "@/components/scheduler/(modal)/ContentSettingModal/QuestionModal";
import { batchUpdateFiles } from "@/redux/scheduler/schedulerSlice";
import { types } from "../../../../../wailsjs/go/models";
import File = types.File;
import { EventsEmit } from "../../../../../wailsjs/runtime";

interface ContentSettingModalProps {
  close: () => void;
  setIsLoading: (value: boolean) => void;
}

export default function ContentSettingModal({
  close,
  setIsLoading,
}: ContentSettingModalProps) {
  const [updatingFiles, setUpdatingFiles] = useState<File[]>([]);
  const [isOpenQuestion, setIsOpenQuestion] = useState<boolean>(false);

  const handleClose = () => {
    if (updatingFiles.length === 0) {
      return close();
    }

    setIsOpenQuestion(true);
  }

  const handleSave = async () => {
    setIsLoading(true);
    try {
      await batchUpdateFiles(updatingFiles);
    } catch (err) {
      EventsEmit("notification:error", err);
    } finally {
      setIsLoading(false);
      close();
    }
  }

  return (
    <>
      <div
        className="relative z-40 bg-[#1E1E1E]"
        aria-labelledby="modal-title"
        role="dialog"
        aria-modal="true"
      >
        <div
          className="fixed inset-0 bg-[#0d0c0c] bg-opacity-50 transition-opacity"
          aria-hidden="true"
        />
        <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <div
              className="relative transform rounded-lg bg-[#1E1E1E] text-left shadow-xl transition-all sm:my-8"
              style={{ width: "1000px", maxWidth: "100vw" }}
            >
              <div
                className={
                  "absolute top-2 right-2 z-10 cursor-pointer hover:text-[#891C1CFF]"
                }
                onClick={handleClose}
              >
                <HiOutlineXCircle className={"h-[30px] w-[30px]"} />
              </div>
              <FileBody
                close={handleClose}
                handleSave={handleSave}
                updatingFiles={updatingFiles}
                setUpdatingFiles={setUpdatingFiles}
              />
            </div>
          </div>
        </div>
      </div>
      {
        isOpenQuestion &&
        <QuestionModal
          approve={close}
          reject={() => setIsOpenQuestion(false)}
        />
      }
    </>
  );
}
