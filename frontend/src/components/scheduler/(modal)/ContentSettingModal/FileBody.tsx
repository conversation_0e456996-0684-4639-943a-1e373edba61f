import React, { useEffect, useRef, useState } from "react";
import {
  getFiles,
} from "@/redux/scheduler/schedulerSlice";
import { OrbitProgress } from "react-loading-indicators";
import {
  HiFolder,
  HiOutlineArchiveBoxXMark,
} from "react-icons/hi2";
import path from 'path-browserify';
import { sql, types } from "../../../../../wailsjs/go/models";
import Folder = types.Folder;
import File = types.File;

interface FolderBodyProps {
  close: () => void;
  handleSave: () => void;
  updatingFiles: File[];
  setUpdatingFiles: (updatingFiles: File[]) => void;
}

export default function FileBody({
  close,
  handleSave,
  updatingFiles,
  setUpdatingFiles,
}: FolderBodyProps) {
  const [loading, setLoading] = useState<boolean>(false);
  const [activeFolder, setActiveFolder] = useState<Folder | null>(null);
  const [mainFolder, setMainFolder] = useState<Folder | null>(null);
  const [activeFile, setActiveFile] = useState<File|null>(null);
  const [updatedFile, setUpdatedFile] = useState<File|null>(null);
  const myRef = useRef<HTMLDivElement | null>(null);

  const scrollToElement = () => {
    myRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    const loadFiles = async () => {
      setLoading(true);

      const files = await getFiles();
      setMainFolder(files);
      setActiveFolder(files);

      setLoading(false);
    };

    loadFiles();
  }, []);

  const selectActiveFolder = (path: string, folder: string) => {
    const index = path.search(folder);
    if (index === -1 || !mainFolder) {
      return;
    }

    const searchPath = path.substring(0, index) + folder;
    const searchFolder = searchFolderByPath(mainFolder, searchPath);

    setActiveFolder(searchFolder);
  };

  const searchFolderByPath = (data: Folder, path: string): Folder | null => {
    if (data.path === path) {
      return data;
    }

    if (data.folders && data.folders.length > 0) {
      for (const folder of data.folders) {
        const result = searchFolderByPath(folder, path);
        if (result) {
          return result;
        }
      }
    }

    return null;
  };

  const durationToTime = (time: number): string => {
    time = Math.round(time);
    let hours = Math.floor(time / 3600);
    let minutes = Math.floor((time - hours * 3600) / 60);
    let seconds = time - hours * 3600 - minutes * 60;

    return (
      (hours < 10 ? "0" + hours : hours) +
      ":" +
      (minutes < 10 ? "0" + minutes : minutes) +
      ":" +
      (seconds < 10 ? "0" + seconds : seconds)
    );
  };

  const closeEditor = () => {
    setActiveFile(null);
    setUpdatedFile(null);
  }

  const toggleActiveFile = (file: File|null) => {
    if (file && file.id !== activeFile?.id) {
      if (activeFile && updatedFile && !isEqual(activeFile, updatedFile)) {
        const alreadyAdded = updatingFiles.find(item => item.id === updatedFile.id);
        if (alreadyAdded) {
          setUpdatingFiles(updatingFiles.map(item => {
            if (item.id === updatedFile.id) {
              return updatedFile;
            }
            return item;
          }));
        } else {
          setUpdatingFiles([
            ...updatingFiles,
            updatedFile,
          ])
        }
      }

      setActiveFile(file);
      setUpdatedFile(file);
      setTimeout(() => scrollToElement(), 100);
    } else if (file && file.id === activeFile?.id) {
      if (activeFile && updatedFile && !isEqual(activeFile, updatedFile)) {
        const alreadyAdded = updatingFiles.find(item => item.id === updatedFile.id);
        if (alreadyAdded) {
          setUpdatingFiles(updatingFiles.map(item => {
            if (item.id === updatedFile.id) {
              return updatedFile;
            }
            return item;
          }));
        } else {
          setUpdatingFiles([
            ...updatingFiles,
            updatedFile,
          ])
        }
      }

      setActiveFile(null);
    } else {
      setActiveFile(file);
    }
  }

  const isEqual = (active: File, updated: File): boolean => {
    return active.name === updated.name
        && active.episode.String === updated.episode.String
        && active.description.String === updated.description.String ;
  }

  return (
    <div className="bg-[#1E1E1E] w-full relative p-3">
      <div
        className={"flex flex-col w-full justify-between items-center gap-2"}
      >
        <div
          className={"flex flex-col w-full justify-between items-center gap-2 "}
        >
          <div
            className={
              "w-full h-[600px] max-h-[calc(100vh-360px)] flex justify-between items-center gap-5 overflow-auto"
            }
          >
            {loading && (
              <div className="flex justify-center items-center w-full h-full">
                <OrbitProgress color="#ffa500" size="small" />
              </div>
            )}
            {!loading && (
              <div className={"w-full h-full"}>
                {activeFolder && (
                  <div className={"flex flex-col w-full gap-2"}>
                    <p className={"flex items-center pl-3 pb-2 gap-2 sticky top-0 bg-[#1E1E1E]"}>
                      <span
                        onClick={() => {
                          selectActiveFolder(activeFolder.path, "/");
                        }}
                        className={`${
                          activeFolder.path !== "/"
                            ? "cursor-pointer text-[grey] hover:text-[#ffa500]"
                            : ""
                        }`}
                      >
                        {"< ... >"}
                      </span>
                      {activeFolder.path
                        .split("/")
                        .filter(Boolean)
                        .map((path: string) => (
                          <span className={"flex gap-2"} key={path}>
                            /
                            <span
                              onClick={() => {
                                selectActiveFolder(activeFolder.path, path);
                              }}
                              className={`${
                                activeFolder.folder !== path
                                  ? "cursor-pointer text-[grey] hover:text-[#ffa500]"
                                  : ""
                              }`}
                            >
                              {path}
                            </span>
                          </span>
                        ))}
                    </p>
                    {activeFolder.folders.map((folder) => (
                      <div
                        className={
                          "flex w-full items-center justify-between pl-3 pr-4"
                        }
                        key={`folder-${folder.folder}`}
                      >
                        <div
                          onClick={() => setActiveFolder(folder)}
                          className={
                            "w-[calc(100%-40px)] flex items-center gap-2 text-nowrap overflow-hidden cursor-pointer hover:text-[#ffa500]"
                          }
                        >
                          <HiFolder />
                          <span>{folder.folder}</span>
                        </div>
                      </div>
                    ))}
                    {activeFolder.files.length > 0 && (
                      <table className="w-full text-left text-white table-fixed">
                        <thead className="uppercase text-[12px] bg-[#151515] h-[30px]">
                        <tr>
                          <th className={"text-center w-[30%]"}>Name</th>
                          <th className={"text-center w-[80px]"}>Duration</th>
                          <th className={"text-center w-[80px]"}>Episode</th>
                          <th className={"text-center"}>Description</th>
                        </tr>
                        </thead>
                        <tbody>
                        {activeFolder.files.map((file, index) => {
                          const hasFile = updatingFiles.find((uFile) => uFile.id === file.id);
                          if (hasFile) {
                            file = hasFile;
                          }

                          return (
                            <React.Fragment key={index}>
                              <tr
                                key={'_file-' + file.id}
                                className={`text-[15px] border-none h-[40px] cursor-pointer ${
                                  index % 2 === 0 ? "bg-[#272727]" : "bg-[#1e1e1e]"
                                }`}
                                onClick={() => toggleActiveFile(file)}
                              >
                                <td
                                  title={file.name}
                                  className={'px-2 text-nowrap overflow-hidden'}
                                >
                                  {file.name}
                                </td>
                                <td
                                  className={'px-2 text-[#ffa500] text-center'}
                                >
                                  {durationToTime(file.duration)}
                                </td>
                                <td
                                  className={'px-2 text-center'}
                                >
                                  {file.episode.String}
                                </td>
                                <td
                                  title={file.description.String ?? ''}
                                  className={'px-2 text-nowrap overflow-hidden'}
                                >
                                  {file.description.String}
                                </td>
                              </tr>
                              {updatedFile && activeFile?.id === file.id && (
                                <tr
                                  key={"editor-" + file.id}
                                  className={"text-[15px] border-none"}
                                >
                                  <td
                                    colSpan={4}
                                  >
                                    <div
                                      ref={myRef}
                                      className={'scroll-mt-20 flex flex-col w-full gap-3 p-4 bg-neutral-900 rounded-bl-xl rounded-br-xl text-[#fafafa]'}
                                    >
                                      <div className={'flex items-center justify-center text-[#ffa500]'}>
                                        {file.fileName}
                                      </div>
                                      <div className={'flex flex-col w-full gap-1'}>
                                        <label>
                                          Name
                                        </label>
                                        <input
                                          onChange={(e) => {
                                            setUpdatedFile(File.createFrom({
                                              ...updatedFile,
                                              name: e.target.value.length > 0 ? e.target.value : path.parse(file.fileName).name,
                                            }))
                                          }}
                                          className={'placeholder:text-foreground-500 focus-visible:outline-none h-46px border-2 rounded-lg px-4 py-2 bg-[#27272a] border-[#3f3f46]'}
                                          defaultValue={file.name}
                                          placeholder={'Display Name'}
                                        />
                                      </div>
                                      <div className={'flex flex-col w-full gap-1'}>
                                        <label>Episode</label>
                                        <input
                                          onChange={(e) => {
                                            setUpdatedFile(File.createFrom({
                                              ...updatedFile,
                                              episode: sql.NullString.createFrom({
                                                String: e.target.value,
                                                Valid: true,
                                              }),
                                            }))
                                          }}
                                          className={'placeholder:text-foreground-500 focus-visible:outline-none h-46px border-2 rounded-lg px-4 py-2 bg-[#27272a] border-[#3f3f46]'}
                                          defaultValue={file.episode.String ?? ''}
                                          placeholder={'S02E12'}
                                        />
                                      </div>
                                      <div className={'flex flex-col w-full gap-1'}>
                                        <label>Description</label>
                                        <textarea
                                          onChange={(e) => {

                                            setUpdatedFile(File.createFrom({
                                              ...updatedFile,
                                              description: sql.NullString.createFrom({
                                                String: e.target.value,
                                                Valid: true,
                                              }),
                                            }))
                                          }}
                                          className={'resize-none placeholder:text-foreground-500 focus-visible:outline-none h-46px border-2 rounded-lg px-4 py-2 bg-[#27272a] border-[#3f3f46]'}
                                          defaultValue={file.description.String ?? ''}
                                          placeholder={'Description'}
                                        />
                                      </div>
                                      <div className={'flex w-full gap-3 justify-center items-center'}>
                                        <button
                                          onClick={closeEditor}
                                          className={'cursor-pointer bg-[#27272a] border border-[#3f3f46] rounded-xl px-4 h-[36px]'}
                                        >
                                          Close
                                        </button>
                                        <button
                                          onClick={() => toggleActiveFile(file)}
                                          disabled={isEqual(activeFile, updatedFile)}
                                          className={'cursor-pointer bg-[#27272a] border border-[#ffa500] rounded-xl px-4 h-[36px] disabled:border-[#27272a] disabled:text-[#3f3f46] disabled:cursor-default'}
                                        >
                                          Save
                                        </button>
                                      </div>
                                    </div>
                                  </td>
                                </tr>
                              )}
                            </React.Fragment>
                          )
                        })}
                        </tbody>
                      </table>
                    )}
                  </div>
                )}
                {!activeFolder && (
                  <div
                    className={
                      "w-full h-full flex items-center justify-center gap-2 text-xl"
                    }
                  >
                    <HiOutlineArchiveBoxXMark />
                    Folder is empty
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
        <div
          className={
            "w-[400px] h-[100px] items-center justify-center flex gap-[20px]"
          }
        >
          <button
            onClick={close}
            className={
              "flex justify-center items-center h-[40px] gap-3 rounded-md w-full bg-[#151515A3] border border-[#4D4D4D] hover:border-[#ffa500]"
            }
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            disabled={updatingFiles.length === 0}
            className={
              "flex justify-center items-center h-[40px] gap-3 rounded-md w-full bg-[#151515A3] border border-[#4D4D4D] hover:border-[#ffa500] disabled:text-[#4D4D4D] disabled:hover:border-[#4D4D4D]"
            }
          >
            Save ({updatingFiles.length})
          </button>
        </div>
      </div>
    </div>
  );
}
