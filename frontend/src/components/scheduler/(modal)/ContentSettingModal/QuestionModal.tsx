import React from "react";
import { HiOutlineXCircle } from "react-icons/hi2";

interface QuestionModalProps {
  approve: () => void;
  reject: () => void;
}

export default function QuestionModal({
  approve,
  reject,
}: QuestionModalProps) {
  return (
    <div
      className="relative z-40 bg-[#1E1E1E]"
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div
        className="fixed inset-0 bg-[#0d0c0c] bg-opacity-50 transition-opacity"
        aria-hidden="true"
      />
      <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
        <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <div
            className="relative transform rounded-lg bg-[#1E1E1E] text-left shadow-xl transition-all sm:my-8"
            style={{ width: "600px", maxWidth: "100vw" }}
          >
            <div
              className={
                "absolute top-2 right-2 z-10 cursor-pointer hover:text-[#891C1CFF]"
              }
              onClick={reject}
            >
              <HiOutlineXCircle className={"h-[30px] w-[30px]"} />
            </div>
            <div className="bg-[#1E1E1E] w-full relative p-3">
              <div
                className={"flex flex-col w-full justify-between items-center gap-2"}
              >
                <div
                  className={"flex flex-col w-full justify-between items-center gap-2 "}
                >
                  <h1
                    className={'text-large py-8 text-center'}
                  >
                    Looks like you’ve made some changes.
                    <br/>
                    Do you really want to close this without saving?
                  </h1>
                </div>
              </div>
              <div
                className={
                  "w-full h-[50px] items-center justify-center flex gap-[20px]"
                }
              >
                <button
                  onClick={approve}
                  className={
                    "flex justify-center items-center h-[40px] gap-3 rounded-md w-full bg-[#151515A3] border border-[#4D4D4D] hover:border-[#f31260]"
                  }
                >
                  Discard Changes
                </button>
                <button
                  onClick={reject}
                  className={
                    "flex justify-center items-center h-[40px] gap-3 rounded-md w-full bg-[#151515A3] border border-[#4D4D4D] hover:border-[#ffa500]"
                  }
                >
                  Keep Editing
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
