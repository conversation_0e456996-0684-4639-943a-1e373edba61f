import React, { useEffect, useState } from "react";
import {
  FolderProps,
  getFiles,
  selectFillers,
  updateFillers,
} from "@/redux/scheduler/schedulerSlice";
import { OrbitProgress } from "react-loading-indicators";
import {
  HiDocument,
  HiFolder,
  HiInformationCircle,
  HiMiniPlusCircle,
  HiOutlineArchiveBoxXMark,
} from "react-icons/hi2";
import { HiDocumentRemove, HiFolderRemove } from "react-icons/hi";
import { useDispatch, useSelector } from "react-redux";
import { document, types } from "../../../../../wailsjs/go/models";
import Folder = types.Folder;
import Fillers = document.Fillers;

interface FolderBodyProps {
  close: () => void;
  setChangeAction: (value: boolean) => void;
  setIsLoading: (value: boolean) => void;
}

interface IFile {
  id: string;
  fileName: string;
  duration: number;
}

interface IFolder {
  folder: string;
  path: string;
  folders: IFolder[];
  files: IFile[];
}

export default function FileBody({
  close,
  setChangeAction,
  setIsLoading,
}: FolderBodyProps) {
  const fillers = useSelector(selectFillers);
  const dispatch = useDispatch();

  const [files, setFiles]     = useState<FolderProps[]>(fillers?.files ?? []);
  const [folders, setFolders] = useState<FolderProps[]>(fillers?.folders ?? []);
  const [loading, setLoading] = useState<boolean>(false);
  const [activeFolder, setActiveFolder] = useState<Folder | null>(null);
  const [mainFolder, setMainFolder] = useState<Folder|null>(null);

  useEffect(() => {
    const loadFiles = async () => {
      setLoading(true);

      const folder = await getFiles();
      setActiveFolder(folder);
      setMainFolder(folder);

      setLoading(false);
    };

    loadFiles();
  }, []);

  const editItem = async () => {
    setIsLoading(true);
    dispatch(
        updateFillers(Fillers.createFrom({
          folders: folders,
          files: files,
        }))
    );
    setChangeAction(true);
    setIsLoading(false);
    close();
  };

  const addFolder = (addFolder: string | null | undefined) => {
    if (!addFolder) {
      return;
    }

    const alreadyAdded = folders.find(folder => folder.path === addFolder);
    if (alreadyAdded) {
      return;
    }

    setFolders([
      ...folders,
      {
        path: addFolder,
      },
    ]);
  };

  const addFile = (addFile: string | null | undefined) => {
    if (!addFile) {
      return;
    }

    const alreadyAdded = files.find(file => file.path === addFile);
    if (alreadyAdded) {
      return;
    }

    setFiles([
      ...files,
      {
        path: addFile,
      },
    ]);
  };

  const deleteFolder = (deleteFolder: FolderProps) => {
    setFolders(
        folders.filter(
            folder => !(deleteFolder.path === folder.path)
        )
    );
  };

  const deleteFile = (deleteFile: FolderProps) => {
    setFiles(
        files.filter(
            file => !(deleteFile.path === file.path)
        )
    );
  };

  const selectActiveFolder = (path: string, folder: string) => {
    const index = path.search(folder);
    if (index === -1 || !mainFolder) {
      return;
    }

    const searchPath = path.substring(0, index) + folder;
    const searchFolder = searchFolderByPath(mainFolder, searchPath);

    setActiveFolder(searchFolder);
  };

  const searchFolderByPath = (data: Folder, path: string): Folder | null => {
    if (data.path === path) {
      return data;
    }

    if (data.folders && data.folders.length > 0) {
      for (const folder of data.folders) {
        const result = searchFolderByPath(folder, path);
        if (result) {
          return result;
        }
      }
    }

    return null;
  };

  const durationToTime = (time: number): string => {
    time = Math.round(time);
    let hours = Math.floor(time / 3600);
    let minutes = Math.floor((time - hours * 3600) / 60);
    let seconds = time - hours * 3600 - minutes * 60;

    return (
        (hours < 10 ? "0" + hours : hours) +
        ":" +
        (minutes < 10 ? "0" + minutes : minutes) +
        ":" +
        (seconds < 10 ? "0" + seconds : seconds)
    );
  };

  return (
      <div className="bg-[#1E1E1E] w-full relative p-3">
        <div
            className={"flex flex-col w-full justify-between items-center gap-2"}
        >
          <div
              className={"flex flex-col w-full justify-between items-center gap-2 "}
          >
                        <div
                className={
                  "w-full h-[400px] flex justify-between items-center gap-5 overflow-auto"
                }
            >
              {loading && (
                  <div className="flex justify-center items-center w-full h-full">
                    <OrbitProgress color="#ffa500" size="small" />
                  </div>
              )}
              {!loading && (
                  <div className={"w-full h-full"}>
                    {activeFolder && (
                        <div className={"flex flex-col w-full gap-2"}>
                          <p className={"flex items-center pl-3 pb-2 gap-2 sticky top-0 bg-[#1E1E1E]"}>
                      <span
                          onClick={() => {
                            selectActiveFolder(activeFolder.path, "/");
                          }}
                          className={`${
                              activeFolder.path !== "/"
                                  ? "cursor-pointer text-[grey] hover:text-[#ffa500]"
                                  : ""
                          }`}
                      >
                        {"< ... >"}
                      </span>
                            {activeFolder.path
                                .split("/")
                                .filter(Boolean)
                                .map((path: string) => (
                                    <span className={"flex gap-2"} key={path}>
                            /
                            <span
                                onClick={() => {
                                  selectActiveFolder(activeFolder.path, path);
                                }}
                                className={`${
                                    activeFolder.folder !== path
                                        ? "cursor-pointer text-[grey] hover:text-[#ffa500]"
                                        : ""
                                }`}
                            >
                              {path}
                            </span>
                          </span>
                                ))}
                          </p>
                          {activeFolder.folders.map((folder) => (
                              <div
                                  className={
                                    "flex w-full items-center justify-between pl-3 pr-4"
                                  }
                                  key={`folder-${folder.folder}`}
                              >
                                <div
                                    onClick={() => setActiveFolder(folder)}
                                    className={
                                      "w-[calc(100%-40px)] flex items-center gap-2 text-nowrap overflow-hidden cursor-pointer hover:text-[#ffa500]"
                                    }
                                >
                                  <HiFolder />
                                  <span>{folder.folder}</span>
                                </div>
                                <div className={"w-[30px]"}>
                                  {folder.files.length > 0 && (
                                      <button
                                          onClick={() => {
                                            addFolder(folder.path);
                                          }}
                                          className={
                                            "flex items-center justify-center w-[25px] h-[25px] rounded border-[#4D4D4D] bg-[#151515A3] hover:text-[#ffa500]"
                                          }
                                      >
                                        <HiMiniPlusCircle />
                                      </button>
                                  )}
                                </div>
                              </div>
                          ))}
                          {activeFolder.files.map((file) => (
                              <div
                                  className={
                                    "flex w-full items-center justify-between pl-4 pr-4"
                                  }
                                  key={`file-${file.id}`}
                              >
                                <div
                                    className={
                                      "w-[calc(100%-40px)] flex items-center gap-2 text-nowrap overflow-hidden"
                                    }
                                >
                                  <HiDocument />
                                  <span
                                      className={
                                        "w-[calc(100%-40px)] overflow-hidden flex gap-2 items-center"
                                      }
                                  >
                            <span className={"min-w-[68px] text-[#ffa500]"}>
                              {durationToTime(file.duration)}
                            </span>
                                    {file.fileName}
                          </span>
                                </div>
                                <div className={"w-[30px]"}>
                                  <button
                                      onClick={() =>
                                          addFile(
                                              (activeFolder?.path === "/"
                                                  ? ""
                                                  : activeFolder?.path) +
                                              "/" +
                                              file.fileName
                                          )
                                      }
                                      className={
                                        "flex items-center justify-center w-[25px] h-[25px] rounded border-[#4D4D4D] bg-[#151515A3] hover:text-[#ffa500]"
                                      }
                                  >
                                    <HiMiniPlusCircle />
                                  </button>
                                </div>
                              </div>
                          ))}
                        </div>
                    )}
                    {!activeFolder && (
                        <div
                            className={
                              "w-full h-full flex items-center justify-center gap-2 text-xl"
                            }
                        >
                          <HiOutlineArchiveBoxXMark />
                          Folder is empty
                        </div>
                    )}
                  </div>
              )}
            </div>
            <div
                className={
                  "w-full h-[100px] pt-2 gap-2 border-t-1 border-t-[#9e9e9e] overflow-auto"
                }
            >
              <div>
                {folders.length === 0 && files.length === 0 && (
                    <div
                        className={
                          "flex items-start justify-center py-[20px] px-[100px] text-center text-[#939393]"
                        }
                    >
                      <HiInformationCircle className={"h-[25px] w-[25px] min-w-[25px]"} />
                      <p className={"flex items-center justify-center"}>
                        If no file/folder is selected, the default fillers will be used.
                        The filler may not be shown in full and will end at any time
                        if it is time for the main program guide.
                      </p>
                    </div>
                )}
                {folders.map((folder) => {
                  return (
                      <div
                          key={folder.path}
                          className={
                            "flex justify-between pl-3 pr-4 items-center text-nowrap overflow-hidden"
                          }
                      >
                    <span className={"w-[calc(100%-40px)] overflow-hidden"}>
                      {folder.path}
                    </span>
                        <button
                            onClick={() => deleteFolder(folder)}
                            className={"hover:text-[#891C1CFF] w-[30px]"}
                        >
                          <HiFolderRemove className={"w-[20px] h-[20px]"} />
                        </button>
                      </div>
                  );
                })}
                {files.map((file) => {
                  return (
                      <div
                          key={file.path}
                          className={
                            "flex justify-between pl-5 pr-4 items-center text-nowrap overflow-hidden"
                          }
                      >
                    <span className={"w-[calc(100%-40px)] overflow-hidden"}>
                      {file.path}
                    </span>
                        <button
                            onClick={() => deleteFile(file)}
                            className={"hover:text-[#891C1CFF] w-[30px]"}
                        >
                          <HiDocumentRemove className={"w-[20px] h-[20px]"} />
                        </button>
                      </div>
                  );
                })}
              </div>
            </div>
          </div>
          <div
              className={
                "w-[400px] h-[100px] items-center justify-center flex gap-[20px]"
              }
          >
            <button
                onClick={close}
                className={
                  "flex justify-center items-center h-[40px] gap-3 rounded-md w-full bg-[#151515A3] border border-[#4D4D4D] hover:border-[#ffa500]"
                }
            >
              Cancel
            </button>
            <button
                onClick={editItem}
                className={
                  "flex justify-center items-center h-[40px] gap-3 rounded-md w-full bg-[#151515A3] border border-[#4D4D4D] hover:border-[#ffa500]"
                }
            >
              Save
            </button>
          </div>
        </div>
      </div>
  );
}
