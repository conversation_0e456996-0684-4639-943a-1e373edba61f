import React, { useState } from "react";
import { HiOutlineXCircle } from "react-icons/hi2";
import { useDispatch, useSelector } from "react-redux";
import { changeSchedulerDetail, selectSchedule } from "@/redux/scheduler/schedulerSlice";
import { EventsEmit } from "../../../../../wailsjs/runtime";

interface RtpOutputSettingModalProps {
  close: () => void;
  setChangeAction: (value: boolean) => void;
}

export default function RtpOutputSettingModal({
  close,
  setChangeAction,
}: RtpOutputSettingModalProps) {
  const dispatch = useDispatch();
  const schedule = useSelector(selectSchedule);
  const [rtpUrl, setRtpUrl] = useState<string>(schedule.output_rtp_url || '');

  const handleSave = () => {
    try {
      dispatch(
        changeSchedulerDetail({
          field: "output_rtp_url",
          value: rtpUrl,
        })
      );
      setChangeAction(true);
      EventsEmit("notification:success", "RTP output URL saved successfully");
      close();
    } catch (err) {
      EventsEmit("notification:error", err);
    }
  };

  return (
    <div
      className="relative z-40 bg-[#1E1E1E]"
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div
        className="fixed inset-0 bg-[#0d0c0c] bg-opacity-50 transition-opacity"
        aria-hidden="true"
      />
      <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
        <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <div
            className="relative transform rounded-lg bg-[#1E1E1E] text-left shadow-xl transition-all sm:my-8"
            style={{ width: "600px" }}
          >
            <div
              className={
                "absolute top-2 right-2 z-10 cursor-pointer hover:text-[#891C1CFF]"
              }
              onClick={close}
            >
              <HiOutlineXCircle className={"h-[30px] w-[30px]"} />
            </div>
            <div className="bg-[#1E1E1E] px-4 pb-4 pt-5 sm:p-6 sm:pb-4 relative">
              <div className="sm:flex sm:items-start">
                <div className="mt-3 w-full text-left sm:ml-4 sm:mt-0 sm:text-left">
                  <h3
                    className="text-base font-semibold leading-6 text-[#FFFFFF]"
                    id="modal-title"
                  >
                    RTP Output Settings
                  </h3>
                  <div className="w-full h-[1px] bg-[#4D4D4D] absolute left-0 mt-[12px] " />
                  <div className="mt-[40px] w-full min-w-full">
                    <div className="flex flex-col gap-4 w-full">
                      <label className="text-sm text-gray-300">RTP Output URL</label>
                      <input
                        type="text"
                        value={rtpUrl}
                        onChange={(e) => setRtpUrl(e.target.value)}
                        placeholder="rtp://224.4.11.20:5001"
                        className="bg-[#151515A3] p-3 w-full h-[40px] focus-visible:outline-0 focus:border-[#ffa500] border border-[#EDEDEDFF] rounded-md"
                      />
                      <p className="text-sm text-gray-400">
                        Enter the RTP URL where the scheduler output will be streamed.
                        Example: rtp://224.4.11.20:5001
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-[#1E1E1E] px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
              <button
                type="button"
                className="inline-flex w-full justify-center rounded-md bg-[#151515A3] px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-[#ffa500] hover:text-black sm:ml-3 sm:w-auto"
                onClick={handleSave}
              >
                Save
              </button>
              <button
                type="button"
                className="mt-3 inline-flex w-full justify-center rounded-md bg-[#151515A3] px-3 py-2 text-sm font-semibold text-white shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-[#891C1CFF] sm:mt-0 sm:w-auto"
                onClick={close}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
