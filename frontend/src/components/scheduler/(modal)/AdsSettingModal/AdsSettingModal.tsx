import React from "react";
import { HiOutlineXCircle } from "react-icons/hi2";
import FileBody from "@/components/scheduler/(modal)/AdsSettingModal/FileBody";

interface AdsSettingModalProps {
  close: () => void;
  setChangeAction: (value: boolean) => void;
  setIsLoading: (value: boolean) => void;
}

export default function AdsSettingModal({
  close,
  setChangeAction,
  setIsLoading,
}: AdsSettingModalProps) {
  return (
    <div
      className="relative z-40 bg-[#1E1E1E]"
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div
        className="fixed inset-0 bg-[#0d0c0c] bg-opacity-50 transition-opacity"
        aria-hidden="true"
      />
      <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
        <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <div
            className="relative transform rounded-lg bg-[#1E1E1E] text-left shadow-xl transition-all sm:my-8"
            style={{ width: "800px" }}
          >
            <div
              className={
                "absolute top-2 right-2 z-10 cursor-pointer hover:text-[#891C1CFF]"
              }
              onClick={close}
            >
              <HiOutlineXCircle className={"h-[30px] w-[30px]"} />
            </div>
            <FileBody
              close={close}
              setChangeAction={setChangeAction}
              setIsLoading={setIsLoading}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
