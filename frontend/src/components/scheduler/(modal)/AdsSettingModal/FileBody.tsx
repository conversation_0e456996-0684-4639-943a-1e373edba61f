import React, { useEffect, useState } from "react";
import {
  FolderProps,
  getFiles,
  selectAds,
  updateAds,
  // IAdsChannelLink,
  // changeSchedulerDetail,
  // selectChannels,
  // selectSchedule,
} from "@/redux/scheduler/schedulerSlice";
import { OrbitProgress } from "react-loading-indicators";
import {
  HiDocument,
  HiFolder,
  HiInformationCircle,
  HiMiniPlusCircle,
  HiOutlineArchiveBoxXMark,
  // HiPlus,
} from "react-icons/hi2";
import { HiDocumentRemove, HiFolderRemove } from "react-icons/hi";
import {useDispatch, useSelector} from "react-redux";
// import { generateFileName } from "@/lib/scheduler";
import { document, types } from "../../../../../wailsjs/go/models";
import Folder = types.Folder;
// import AdLink = document.AdLink;
// import { getChannels, IChannel, setChannels } from "@/redux/channel/channelSlice";
import Ads = document.Ads;

interface FolderBodyProps {
  close: () => void;
  setChangeAction: (value: boolean) => void;
  setIsLoading: (value: boolean) => void;
}

export default function FileBody({
  close,
  setChangeAction,
  setIsLoading,
}: FolderBodyProps) {
  const ads = useSelector(selectAds);
  // const schedulerChannels = useSelector(selectChannels);
  const dispatch = useDispatch();

  const [section, setSection] = useState("files");
  // let [links, setLinks] = useState<AdLink[]>(ads.links ?? []);
  // let [newLinks, setNewLinks] = useState<IAdsChannelLink[]>([]);
  const [files, setFiles] = useState<FolderProps[]>(ads?.files ?? []);
  const [folders, setFolders] = useState<FolderProps[]>(ads?.folders ?? []);
  const [loading, setLoading] = useState<boolean>(false);
  const [activeFolder, setActiveFolder] = useState<Folder | null>(null);
  const [mainFolder, setMainFolder] = useState<Folder | null>(null);
  // const channels = useSelector(setChannels);
  // const scheduler = useSelector(selectSchedule);

  useEffect(() => {
    const loadFiles = async () => {
      setLoading(true);

      const files = await getFiles();
      setMainFolder(files);
      setActiveFolder(files);

      setLoading(false);
    };

    loadFiles();
  }, []);

  // useEffect(() => {
  //   const fetchLinks = async () => {
  //     let schedulerChannels = await getChannels();
  //
  //     let uniqueChannels = new Map(
  //       [...channels, ...schedulerChannels].map((channel) => [
  //         channel.slug.toString(),
  //         { ...channel },
  //       ])
  //     );
  //
  //     let notExistLinks: IAdsChannelLink[] = [];
  //     uniqueChannels.forEach((channel) => {
  //       const adsLink: IAdsChannelLink = {
  //         channel: channel,
  //         url: null,
  //       };
  //       let findLink = links.find((link) => link.channel.slug === channel.slug);
  //       if (!findLink) {
  //         notExistLinks.push(adsLink);
  //       }
  //     });
  //     setLinks([...links, ...notExistLinks]);
  //   };
  //
  //   fetchLinks();
  // }, [channels]);

  const editItem = async () => {
    setIsLoading(true);
    // let updatedLinks = await collectAndUpdateLinks();
    // updateUserChannels();
    dispatch(
      updateAds(Ads.createFrom({
        links: [], //updatedLinks,
        folders: folders,
        files: files,
      }))
    );
    setChangeAction(true);
    setIsLoading(false);
    close();
  };

  // const collectAndUpdateLinks = async (): Promise<IAdsChannelLink[]> => {
  //   let allLinks = [...links, ...newLinks];
  //   let linksWithUrl = allLinks.filter((link) => link.url !== null);
  //   linksWithUrl = linksWithUrl.filter((link) => link.url !== "");
  //
  //   let uniqueLinksWithUrlMap = new Map(
  //     linksWithUrl.map((link) => [link.channel.slug.toString(), { ...link }])
  //   );
  //   linksWithUrl = Array.from(uniqueLinksWithUrlMap.values());
  //
  //   if (linksWithUrl.length > 0) {
  //     let newSchedulerChannels = linksWithUrl.map((link) => {
  //       return link.channel;
  //     });
  //     updateSchedulerChannels(newSchedulerChannels);
  //   }
  //
  //   return linksWithUrl;
  // };
  //
  // const updateSchedulerChannels = (newChannels: IChannel[]) => {
  //   let newSchedulerChannels = [...schedulerChannels, ...newChannels];
  //
  //   let uniqueChannels = new Map(
  //     newSchedulerChannels.map((channel) => [
  //       channel.slug.toString(),
  //       { ...channel },
  //     ])
  //   );
  //
  //   dispatch(
  //     changeSchedulerDetail({
  //       field: "channels",
  //       value: Array.from(uniqueChannels.values()),
  //     })
  //   );
  // };

  // const updateUserChannels = () => {
  //   let linksWithUrl = newLinks.filter((link) => link.url !== null);
  //   linksWithUrl = linksWithUrl.filter((link) => link.url !== "");
  //   let newUserChannels = linksWithUrl.map((link) => {
  //     return link.channel;
  //   });
  //
  //   if (newUserChannels.length > 0) {
  //     let uniqueChannels = new Map(
  //       [...userChannels, ...newUserChannels].map((channel) => [
  //         channel.slug.toString(),
  //         { ...channel },
  //       ])
  //     );
  //
  //     dispatch(setUserChannels(Array.from(uniqueChannels.values())));
  //   }
  // };

  const addFolder = (addFolder: string | null | undefined) => {
    if (!addFolder) {
      return;
    }

    const alreadyAdded = folders.find(folder => folder.path === addFolder);
    if (alreadyAdded) {
      return;
    }

    setFolders([
      ...folders,
      {
        path: addFolder,
      },
    ]);
  };

  const addFile = (addFile: string | null | undefined) => {
    if (!addFile) {
      return;
    }

    const alreadyAdded = files.find(file => file.path === addFile
    );
    if (alreadyAdded) {
      return;
    }

    setFiles([
      ...files,
      {
        path: addFile,
      },
    ]);
  };

  const deleteFolder = (deleteFolder: FolderProps) => {
    setFolders(
      folders.filter(
        (folder) =>
          !(
            deleteFolder.path === folder.path
          )
      )
    );
  };

  const deleteFile = (deleteFile: FolderProps) => {
    setFiles(
      files.filter(
        (file) =>
          !(deleteFile.path === file.path)
      )
    );
  };

  const selectActiveFolder = (path: string, folder: string) => {
    const index = path.search(folder);
    if (index === -1 || !mainFolder) {
      return;
    }

    const searchPath = path.substring(0, index) + folder;
    const searchFolder = searchFolderByPath(mainFolder, searchPath);

    setActiveFolder(searchFolder);
  };

  const searchFolderByPath = (data: Folder, path: string): Folder | null => {
    if (data.path === path) {
      return data;
    }

    if (data.folders && data.folders.length > 0) {
      for (const folder of data.folders) {
        const result = searchFolderByPath(folder, path);
        if (result) {
          return result;
        }
      }
    }

    return null;
  };

  const durationToTime = (time: number): string => {
    time = Math.round(time);
    let hours = Math.floor(time / 3600);
    let minutes = Math.floor((time - hours * 3600) / 60);
    let seconds = time - hours * 3600 - minutes * 60;

    return (
      (hours < 10 ? "0" + hours : hours) +
      ":" +
      (minutes < 10 ? "0" + minutes : minutes) +
      ":" +
      (seconds < 10 ? "0" + seconds : seconds)
    );
  };

  // const handleAddNewChannel = () => {
  //   let tmpNewLinks: IAdsChannelLink[];
  //   let newLink: IAdsChannelLink = {
  //     url: null,
  //     channel: {
  //       _id: null,
  //       title: "",
  //       slug: "",
  //     },
  //   };
  //   tmpNewLinks = [...newLinks, newLink];
  //   setNewLinks(tmpNewLinks);
  // };
  // const setUrl = (url: string, currentAds: IAdsChannelLink) => {
  //   let copyLinks = links.map((link) =>
  //     link.channel._id === currentAds.channel._id
  //       ? {
  //           url,
  //           channel: link.channel,
  //         }
  //       : link
  //   );
  //
  //   setLinks(copyLinks);
  // };
  // const setNewChannel = (title: string, currentAds: IAdsChannelLink) => {
  //   let copyNewLinks = [...newLinks];
  //   for (let i in copyNewLinks) {
  //     let link = copyNewLinks[i];
  //     if (link.channel.slug === currentAds.channel.slug) {
  //       link.channel.title = title;
  //       link.channel.slug = generateFileName(title);
  //       copyNewLinks[i] = link;
  //       break;
  //     }
  //   }
  //
  //   setNewLinks(copyNewLinks);
  // };

  // const setNewUrl = (url: string, currentAds: IAdsChannelLink) => {
  //   let copyNewLinks = newLinks.map((link) =>
  //     link.channel.slug === currentAds.channel.slug
  //       ? {
  //           url,
  //           channel: link.channel,
  //         }
  //       : link
  //   );
  //
  //   setNewLinks(copyNewLinks);
  // };

  return (
    <div className="bg-[#1E1E1E] w-full relative p-3">
      <div
        className={"flex flex-col w-full justify-between items-center gap-2"}
      >
        <div
          className={"flex flex-col w-full justify-between items-center gap-2 "}
        >
          <div className={"flex justify-center items-center gap-2"}>
            <div
              onClick={() => setSection("files")}
              className={`px-24 py-2 border-b cursor-pointer ${
                section === "files" ? "border-[#ffa500]" : "border-[grey]"
              } hover:text-[#ffa500] hover:border-[#ffa500]`}
            >
              Folders/Files
            </div>
            <div
              onClick={() => setSection("scte")}
              className={`px-24 py-2 border-b cursor-pointer ${
                section === "scte" ? "border-[#ffa500]" : "border-[grey]"
              } hover:text-[#ffa500] hover:border-[#ffa500]`}
            >
              SCTE 35
            </div>
          </div>
          {section === "files" && (
            <>
              <div
                className={
                  "w-full h-[400px] flex justify-between items-center gap-5 overflow-auto"
                }
              >
                {loading && (
                  <div className="flex justify-center items-center w-full h-full">
                    <OrbitProgress color="#ffa500" size="small" />
                  </div>
                )}
                {!loading && (
                  <div className={"w-full h-full"}>
                    {activeFolder && (
                      <div className={"flex flex-col w-full gap-2"}>
                        <p className={"flex items-center pl-3 pb-2 gap-2 sticky top-0 bg-[#1E1E1E]"}>
                          <span
                            onClick={() => {
                              selectActiveFolder(activeFolder.path, "/");
                            }}
                            className={`${
                              activeFolder.path !== "/"
                                ? "cursor-pointer text-[grey] hover:text-[#ffa500]"
                                : ""
                            }`}
                          >
                            {"< ... >"}
                          </span>
                          {activeFolder.path
                            .split("/")
                            .filter(Boolean)
                            .map((path: string) => (
                              <span className={"flex gap-2"}>
                                /
                                <span
                                  onClick={() => {
                                    selectActiveFolder(activeFolder.path, path);
                                  }}
                                  className={`${
                                    activeFolder.folder !== path
                                      ? "cursor-pointer text-[grey] hover:text-[#ffa500]"
                                      : ""
                                  }`}
                                >
                                  {path}
                                </span>
                              </span>
                            ))}
                        </p>
                        {activeFolder.folders.map((folder) => (
                          <div
                            className={
                              "flex w-full items-center justify-between pl-3 pr-4"
                            }
                            key={`folder-${folder.folder}`}
                          >
                            <div
                              onClick={() => setActiveFolder(folder)}
                              className={
                                "w-[calc(100%-40px)] flex items-center gap-2 text-nowrap overflow-hidden cursor-pointer hover:text-[#ffa500]"
                              }
                            >
                              <HiFolder />
                              <span>{folder.folder}</span>
                            </div>
                            <div className={"w-[30px]"}>
                              {folder.files.length > 0 && (
                                <button
                                  onClick={() => {
                                    addFolder(folder.path);
                                  }}
                                  className={
                                    "flex items-center justify-center w-[25px] h-[25px] rounded border-[#4D4D4D] bg-[#151515A3] hover:text-[#ffa500]"
                                  }
                                >
                                  <HiMiniPlusCircle />
                                </button>
                              )}
                            </div>
                          </div>
                        ))}
                        {activeFolder.files.map((file) => (
                          <div
                            className={
                              "flex w-full items-center justify-between pl-4 pr-4"
                            }
                            key={`folder-${file.id}`}
                          >
                            <div
                              className={
                                "w-[calc(100%-40px)] flex items-center gap-2 text-nowrap overflow-hidden"
                              }
                            >
                              <HiDocument />
                              <span
                                className={
                                  "w-[calc(100%-40px)] overflow-hidden flex gap-2 items-center"
                                }
                              >
                                <span className={"min-w-[68px] text-[#ffa500]"}>
                                  {durationToTime(file.duration)}
                                </span>
                                {file.fileName}
                              </span>
                            </div>
                            <div className={"w-[30px]"}>
                              <button
                                onClick={() =>
                                  addFile(
                                    (activeFolder?.path === "/"
                                      ? ""
                                      : activeFolder?.path) +
                                      "/" +
                                      file.fileName
                                  )
                                }
                                className={
                                  "flex items-center justify-center w-[25px] h-[25px] rounded border-[#4D4D4D] bg-[#151515A3] hover:text-[#ffa500]"
                                }
                              >
                                <HiMiniPlusCircle />
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                    {!activeFolder && (
                      <div
                        className={
                          "w-full h-full flex items-center justify-center gap-2 text-xl"
                        }
                      >
                        <HiOutlineArchiveBoxXMark />
                        Folder is empty
                      </div>
                    )}
                  </div>
                )}
              </div>
              <div
                className={
                  "w-full h-[100px] pt-2 gap-2 border-t-1 border-t-[#9e9e9e] overflow-auto"
                }
              >
                <div>
                  {folders.length === 0 && files.length === 0 && (
                    <div
                      className={
                        "flex items-start justify-center py-[20px] px-[100px] text-center text-[#939393]"
                      }
                    >
                      <HiInformationCircle className={"h-[25px] w-[25px]"} />
                      <p className={"flex items-center justify-center"}>
                        If no file/folder is selected or the duration of all
                        files is more than 2 minutes, standard fillers will be
                        used
                      </p>
                    </div>
                  )}
                  {folders.map((folder) => {
                    return (
                      <div
                        key={folder.path}
                        className={
                          "flex justify-between pl-3 pr-4 items-center text-nowrap overflow-hidden"
                        }
                      >
                        <span className={"w-[calc(100%-40px)] overflow-hidden"}>
                          {folder.path}
                        </span>
                        <button
                          onClick={() => deleteFolder(folder)}
                          className={"hover:text-[#891C1CFF] w-[30px]"}
                        >
                          <HiFolderRemove className={"w-[20px] h-[20px]"} />
                        </button>
                      </div>
                    );
                  })}
                  {files.map((file) => {
                    return (
                      <div
                        key={file.path}
                        className={
                          "flex justify-between pl-5 pr-4 items-center text-nowrap overflow-hidden"
                        }
                      >
                        <span className={"w-[calc(100%-40px)] overflow-hidden"}>
                          {file.path}
                        </span>
                        <button
                          onClick={() => deleteFile(file)}
                          className={"hover:text-[#891C1CFF] w-[30px]"}
                        >
                          <HiDocumentRemove className={"w-[20px] h-[20px]"} />
                        </button>
                      </div>
                    );
                  })}
                </div>
              </div>
            </>
          )}
          {/*{section === "scte" && (*/}
          {/*  <>*/}
          {/*    <div*/}
          {/*      className={*/}
          {/*        "w-full h-[400px] justify-between items-center gap-5 overflow-y-auto"*/}
          {/*      }*/}
          {/*    >*/}
          {/*      {links.map((link) => {*/}
          {/*        return (*/}
          {/*          <div*/}
          {/*            key={link.channel.slug}*/}
          {/*            className={*/}
          {/*              "w-full h-[60px]  flex flex-col items-center justify-center gap-2"*/}
          {/*            }*/}
          {/*          >*/}
          {/*            <div className={"flex gap-5 w-full px-28 items-center"}>*/}
          {/*              <label className={"w-[130px] min-w-[130px]"}>*/}
          {/*                {link.channel.title}*/}
          {/*              </label>*/}
          {/*              <input*/}
          {/*                value={link.url ?? ""}*/}
          {/*                className={*/}
          {/*                  "bg-[#151515A3] w-full p-3 h-[40px] focus-visible:outline-0 hover:border-[#ffa500] focus:border-[#ffa500] border border-[#4D4D4D] rounded-md"*/}
          {/*                }*/}
          {/*                placeholder={*/}
          {/*                  "https://api.showfer.com/api/adstorm/scte/67c8a7af6345a5de1ae6aabc"*/}
          {/*                }*/}
          {/*                name={link.channel.slug}*/}
          {/*                onChange={(e) => setUrl(e.target.value, link)}*/}
          {/*              />*/}
          {/*            </div>*/}
          {/*          </div>*/}
          {/*        );*/}
          {/*      })}*/}
          {/*      {newLinks.map((link) => {*/}
          {/*        return (*/}
          {/*          <div*/}
          {/*            key={'new' + link.channel.slug}*/}
          {/*            className={*/}
          {/*              "w-full h-[60px] flex flex-col items-center justify-center gap-2"*/}
          {/*            }*/}
          {/*          >*/}
          {/*            <div className={"flex gap-5 w-full px-28 items-center"}>*/}
          {/*              <input*/}
          {/*                value={link.channel.title ?? ""}*/}
          {/*                type={"channel"}*/}
          {/*                className={*/}
          {/*                  "w-[130px] min-w-[130px] bg-[#151515A3] p-3 h-[40px] focus-visible:outline-0 hover:border-[#ffa500] focus:border-[#ffa500] border border-[#4D4D4D] rounded-md"*/}
          {/*                }*/}
          {/*                placeholder={"Channel name"}*/}
          {/*                onChange={(e) => setNewChannel(e.target.value, link)}*/}
          {/*              />*/}
          {/*              <input*/}
          {/*                value={link.url ?? ""}*/}
          {/*                type={"url"}*/}
          {/*                disabled={link.channel.slug === ""}*/}
          {/*                className={*/}
          {/*                  (link.channel.slug !== ""*/}
          {/*                    ? "bg-[#151515A3]"*/}
          {/*                    : "cursor-not-allowed") +*/}
          {/*                  " w-full p-3 h-[40px] focus-visible:outline-0 hover:border-[#ffa500] focus:border-[#ffa500] border border-[#4D4D4D] rounded-md"*/}
          {/*                }*/}
          {/*                placeholder={""}*/}
          {/*                onChange={(e) => setNewUrl(e.target.value, link)}*/}
          {/*              />*/}
          {/*            </div>*/}
          {/*          </div>*/}
          {/*        );*/}
          {/*      })}*/}

          {/*      <button*/}
          {/*        onClick={handleAddNewChannel}*/}
          {/*        className="flex justify-center items-center h-[40px] gap-3 rounded-md w-full disabled:bg-[grey] hover:text-[#ffa500]"*/}
          {/*      >*/}
          {/*        <HiPlus />*/}
          {/*        Add Channel*/}
          {/*      </button>*/}
          {/*    </div>*/}
          {/*  </>*/}
          {/*)}*/}
        </div>
        <div
          className={
            "w-[400px] h-[100px] items-center justify-center flex gap-[20px]"
          }
        >
          <button
            onClick={close}
            className={
              "flex justify-center items-center h-[40px] gap-3 rounded-md w-full bg-[#151515A3] border border-[#4D4D4D] hover:border-[#ffa500]"
            }
          >
            Cancel
          </button>
          <button
            onClick={editItem}
            className={
              "flex justify-center items-center h-[40px] gap-3 rounded-md w-full bg-[#151515A3] border border-[#4D4D4D] hover:border-[#ffa500]"
            }
          >
            Save
          </button>
        </div>
      </div>
    </div>
  );
}
