import React, { useEffect, useState } from "react";
import { addNewItem } from "@/redux/scheduler/schedulerSlice";
import { HiInformationCircle } from "react-icons/hi2";
import { CopyIcon } from "@heroui/shared-icons";
import md5 from "blueimp-md5";
import { CalendarDate, DatePicker, TimeInput } from "@heroui/react";
import { parseDate, Time } from "@internationalized/date";
import moment from "moment/moment";
import { useDispatch } from "react-redux";
import { document } from "../../../../../wailsjs/go/models";
import Item = document.Item;

interface ConnectionBodyProps {
  type: string;
  day: string;
  start: string;
  end: string;
  close: () => void;
}

export default function ConnectionBody({
  type,
  close,
  day,
  start,
  end,
}: ConnectionBodyProps) {
  const dispatch = useDispatch();
  const [connectionType, setConnectionType] = useState<string>("hls");
  const [url, setUrl] = useState<string>("");
  const [port, setPort] = useState<string>("");
  const [mode, setMode] = useState<string>("listener");
  const [name, setName] = useState<string>("");
  const [description, setDescription] = useState<string>("");
  const [expireDate, setExpireDate] = useState<CalendarDate|null>(parseDate(moment().add(8, "days").format("YYYY-MM-DD")));
  const [expireTime, setExpireTime] = useState<Time|null>(new Time(0, 0));
  const [key, setKey] = useState<string>("");

  const copyLink = (link: string | null) => {
    if (!link) {
      return;
    }

    navigator.clipboard
    .writeText(link)
    .then(() => {
    })
    .catch((err) => {
      console.log(err);
    });
  };

  const addItem = () => {
    dispatch(
      addNewItem({
        type: type,
        day: day,
        item: Item.createFrom({
          type: "connection",
          start: start,
          end: end,
          connection: connectionType,
          link: url,
          port: port,
          mode: mode,
          expire_date: expireDate?.toString() ?? '',
          expire_time: expireTime?.toString() ?? '',
          folders: [],
          files: [],
          name: name,
          description: description,
        }),
      })
    );
    close();
  };

  useEffect(() => {
    const timeStamp = getTimeStamp();
    const hash = md5(`/live/${name}-${timeStamp}-airylive`);
    const newKey = `${name}?sign=${timeStamp}-${hash}`;
    setKey(newKey);
  }, [name, expireTime, expireDate]);


  const getTimeStamp = (): number => {
    return moment(expireDate?.toString() + "T" + expireTime?.toString()).unix();
  }

  const changeConnectionType = (type: string) => {
    setConnectionType(type);
    setUrl("");
    setPort("");
  }

  return (
    <div className="bg-[#1E1E1E] p-5 relative w-full">
      <div className={"flex flex-col justify-between items-center gap-14 h-full"}>
        <div className={"flex flex-col justify-between items-center gap-5 w-full "}>
          <div className={"w-full flex flex-col gap-4"}>
            <label>Title</label>
            <input
              value={name}
              type={"text"}
              onChange={(e) => setName(e.target.value)}
              className={
                "bg-[#151515A3] p-3 w-full h-[40px] focus-visible:outline-0 focus:border-[#ffa500] border border-[#EDEDEDFF] rounded-md"
              }
              placeholder={"Program title"}
            />
          </div>
          <div className={"w-full flex flex-col gap-4"}>
            <label>Description</label>
            <input
              value={description}
              type={"text"}
              onChange={(e) => setDescription(e.target.value)}
              className={
                "bg-[#151515A3] p-3 w-full h-[40px] focus-visible:outline-0 focus:border-[#ffa500] border border-[#EDEDEDFF] rounded-md"
              }
              placeholder={"Program description"}
            />
          </div>
          <div className={"w-full flex flex-col gap-4"}>
            <label>External Connection</label>
            <select
              value={connectionType}
              onChange={(e) => changeConnectionType(e.target.value)}
              className={
                "bg-[#151515A3] px-3 w-full h-[40px] focus-visible:outline-0 focus:border-[#ffa500] border border-[#EDEDEDFF] rounded-md"
              }
            >
              <option value="hls">
                HLS
              </option>
              <option value="rtp">
                RTP
              </option>
              <option value="udp">
                UDP
              </option>
              <option value="srt">
                SRT
              </option>
              <option value="rtmp">
                RTMP
              </option>
            </select>
          </div>
          { connectionType === "hls" && (
            <>
              <div className={"w-full flex flex-col gap-4"}>
                <input
                  value={url}
                  type={"text"}
                  onChange={(e) => setUrl(e.target.value)}
                  className={
                    "bg-[#151515A3] p-3 w-full h-[40px] focus-visible:outline-0 focus:border-[#ffa500] border border-[#EDEDEDFF] rounded-md"
                  }
                  placeholder={"https://hls.showfer.com/connection/example.m3u8"}
                />
              </div>
              <p
                className={
                  "flex gap-[5px] justify-center items-center text-sm text-blue-400"
                }
              >
                <HiInformationCircle />
                <div className={"flex flex-col gap-4"}>
              <span>
                The connection type only supports the HLS format for a specific
                resolution:
              </span>
                  <ul className={"flex flex-col gap-2"}>
                    <li>- Using the master file is currently not supported</li>
                    <li>
                      - In case of an invalid link, your HLS will not be available
                      at this time, make sure the stream is working
                    </li>
                  </ul>
                </div>
              </p>
            </>
          )}
          {connectionType === "rtp" && (
            <>
              <div className="w-full flex flex-col gap-4">
                <input
                  value={url}
                  type="text"
                  onChange={(e) => setUrl(e.target.value)}
                  className="bg-[#151515A3] p-3 w-full h-[40px] focus-visible:outline-0 focus:border-[#ffa500] border border-[#EDEDEDFF] rounded-md"
                  placeholder="rtp://224.4.11.20:5001 or rtp://224.4.11.20:5001/stream"
                  spellCheck={false}
                  autoComplete="off"
                />
              </div>
              <p className="flex gap-[5px] justify-center items-center text-sm text-blue-400">
                <HiInformationCircle />
                <div className="flex flex-col gap-4">
                  <span>
                    Please enter a valid RTP stream URL. Examples:
                  </span>
                  <ul className="flex flex-col gap-2">
                    <li>
                      <code>rtp://224.4.11.20:5001</code>
                    </li>
                    <li>
                      <code>rtp://224.4.11.20:5001/stream</code>
                    </li>
                  </ul>
                  <span>
                    - Both multicast and unicast RTP streams are supported.<br />
                    - Ensure your network allows RTP traffic and that the stream is currently active.<br />
                    - Invalid or unreachable URLs will not display a stream.
                  </span>
                </div>
              </p>
            </>
          )}
          { connectionType === "udp" && (
            <>
              <div className={"w-full flex flex-col gap-4"}>
                <div className={"flex justify-center items-center gap-3"}>
                  <span
                    className={'text-[#ffa500] w-[100px] text-right'}
                  >
                    udp://
                  </span>
                  <input
                    value={url}
                    type={"text"}
                    onChange={(e) => setUrl(e.target.value)}
                    className={
                      "bg-[#151515A3] p-3 w-full h-[40px] focus-visible:outline-0 focus:border-[#ffa500] border border-[#EDEDEDFF] rounded-md"
                    }
                    placeholder={"127.0.0.1"}
                  />
                  <input
                    value={port}
                    onChange={e => setPort(e.target.value)}
                    type={"number"}
                    min={1}
                    className={
                      "bg-[#151515A3] p-3 h-[40px] w-[150px] focus-visible:outline-0 focus:border-[#ffa500] border border-[#EDEDEDFF] rounded-md"
                    }
                    placeholder={"4500"}
                  />
                </div>
              </div>
              <p
                className={
                  "flex gap-[5px] justify-center items-center text-sm text-blue-400"
                }
              >
                <HiInformationCircle />
                <div className={"flex flex-col gap-4"}>
                  <span>
                    Coming soon...
                    <br />
                    UDP is not supported yet. Fillers will be shown instead
                  </span>
                </div>
              </p>
            </>
          )}
          { connectionType === "srt" && (
            <>
              <div className={"w-full flex flex-col gap-4"}>
                <div className={"flex justify-center items-center gap-3"}>
                <span
                  className={'text-[#ffa500] w-[100px] min-w-[100px] text-right'}
                >
                  srt://
                </span>
                  <input
                    value={url}
                    type={"text"}
                    onChange={(e) => setUrl(e.target.value)}
                    className={
                      "bg-[#151515A3] p-3 w-full h-[40px] focus-visible:outline-0 focus:border-[#ffa500] border border-[#EDEDEDFF] rounded-md"
                    }
                    placeholder={"127.0.0.1"}
                  />
                  <input
                    value={port}
                    onChange={e => setPort(e.target.value)}
                    type={"number"}
                    min={1}
                    className={
                      "bg-[#151515A3] p-3 h-[40px] w-[150px] focus-visible:outline-0 focus:border-[#ffa500] border border-[#EDEDEDFF] rounded-md"
                    }
                    placeholder={"4500"}
                  />
                </div>
              </div>
              <div className={"w-full flex flex-col gap-4"}>
                <div className={'flex items-center gap-6'}>
                  <label
                    className={'text-[#ffa500] w-[100px] min-w-[100px] text-right'}
                  >
                    Mode:
                  </label>
                  <div className={'flex items-center justify-center gap-2'}>
                    <input
                      id="listener"
                      type={"radio"}
                      name={"mode"}
                      value={"listener"}
                      checked={mode === "listener"}
                      onChange={(e) => setMode(e.target.value)}
                    />
                    <label
                      htmlFor={"listener"}
                    >
                      Listener
                    </label>
                  </div>
                  <div className={'flex items-center justify-center gap-2'}>
                    <input
                      id="caller"
                      type={"radio"}
                      name={"mode"}
                      value={"caller"}
                      checked={mode === "caller"}
                      onChange={(e) => setMode(e.target.value)}
                    />
                    <label htmlFor={"caller"}>
                      Caller
                    </label>
                  </div>
                </div>
              </div>
              <p
                className={
                  "flex gap-[5px] justify-center items-center text-sm text-blue-400"
                }
              >
                <HiInformationCircle />
                <div className={"flex flex-col gap-4"}>
                  <span>
                    Coming soon...
                    <br />
                    SRT is not supported yet. Fillers will be shown instead
                  </span>
                </div>
              </p>
            </>
          )}
          { connectionType === "rtmp" && (
            <>
              <div className={"w-full flex flex-col gap-4"}>
                <label>
                  Expire At
                </label>
                <div className={'flex items-center gap-3'}>
                  <DatePicker
                    value={expireDate}
                    isRequired
                    variant={'bordered'}
                    className="w-full "
                    onChange={(e) => setExpireDate(e)}
                  />
                  <TimeInput
                    value={expireTime}
                    isRequired
                    variant={'bordered'}
                    onChange={(e) => setExpireTime(e)}
                    className="w-full"
                  />
                </div>
              </div>
                <div className={"w-full flex flex-col gap-4"}>
                  <div className={"flex items-center gap-2"}>
                    <label className={'text-[#ffa500]'}>
                      Link:
                    </label>
                    <p className={'flex items-center justify-between w-full gap-3'}>
                      rtmp://stream.showfer.com:8000/live
                      <button
                        onClick={() => copyLink("rtmp://stream.showfer.com:8000/live")}
                        title={'Copy'}
                        className={'bg-[#151515A3] border border-[#ffa500] rounded-md text-[#ffa500] p-2'}
                      >
                        <CopyIcon />
                      </button>
                    </p>
                  </div>
                  { name && expireTime && expireDate &&
                    <div className={"flex items-center gap-2"}>
                      <label className={'text-[#ffa500]'}>
                        Key:
                      </label>
                      <p className={'flex items-center justify-between w-full gap-3'}>
                        { key }
                        <button
                          onClick={() => copyLink(key)}
                          title={'Copy'}
                          className={'bg-[#151515A3] border border-[#ffa500] rounded-md text-[#ffa500] p-2'}
                        >
                          <CopyIcon />
                        </button>
                      </p>
                    </div>
                  }
                </div>
            </>
          )}
        </div>
        <div className={"w-[200px]"}>
          <button
            disabled={
              !(
                name && (
                  (connectionType === "hls" && !!url)
                  || (connectionType === "rtp" && url)
                  || (connectionType === "udp" && url && port)
                  || (connectionType === "srt" && url && port && mode)
                  || (connectionType === "rtmp" && expireDate && expireTime)
                )
              )
            }
            onClick={addItem}
            className={
              "flex justify-center items-center h-[40px] gap-3 rounded-md w-full border border-[#4D4D4D] text-[#ffa500] hover:border-[#ffa500] disabled:border-[#4D4D4D] disabled:text-[#4D4D4D]"
            }
          >
            Add
          </button>
        </div>
      </div>
    </div>
  );
}
