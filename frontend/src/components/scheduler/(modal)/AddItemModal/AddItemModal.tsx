import React, { useState } from "react";
import { HiOutlineXCircle } from "react-icons/hi2";
import ConnectionBody from "@/components/scheduler/(modal)/AddItemModal/ConnectionBody";
import FileBody from "@/components/scheduler/(modal)/AddItemModal/FileBody";

interface AddItemModalProps {
  type: string;
  day: string;
  start: string;
  end: string;
  close: () => void;
  setChangeAction: (value: boolean) => void;
}

export default function AddItemModal({
  type: dayType,
  close,
  day,
  start,
  end,
  setChangeAction,
}: AddItemModalProps) {
  const [type, setType] = useState<string | null>("file");

  return (
    <div
      className="relative z-20 bg-[#1E1E1E]"
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div
        className="fixed inset-0 bg-[#0d0c0c] bg-opacity-50 transition-opacity"
        aria-hidden="true"
      />
      <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
        <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <div
            className="relative transform rounded-lg bg-[#1E1E1E] text-left shadow-xl transition-all sm:my-8"
            style={{ width: "800px" }}
          >
            <div
              className={
                "absolute top-2 right-2 z-10 cursor-pointer hover:text-[#891C1CFF]"
              }
              onClick={close}
            >
              <HiOutlineXCircle className={"h-[30px] w-[30px]"} />
            </div>
            <div className={"bg-[#1E1E1E] relative flex pt-[40px] pb-2"}>
              <div className={"w-[40px] h-full flex-col"}>
                <div
                  onClick={() => setType("file")}
                  className={`bg-[#1E1E1E] p-1 rounded-l-[6px] cursor-pointer border-l-[2px] ${
                    type === "file"
                      ? "border-[#ffa500] text-[#ffa500]"
                      : "border-[#9e9e9e]"
                  } [writing-mode:vertical-lr] rotate-[180deg] flex items-center justify-center h-[350px] hover:border-[#ffa500] hover:text-[#ffa500]`}
                >
                  File
                </div>
                <div
                  onClick={() => setType("connection")}
                  className={`bg-[#1E1E1E] p-1 rounded-l-[6px] cursor-pointer border-l-[2px] ${
                    type === "connection"
                      ? "border-[#ffa500] text-[#ffa500]"
                      : "border-[#9e9e9e]"
                  } [writing-mode:vertical-lr] rotate-[180deg] flex items-center justify-center h-[350px] hover:border-[#ffa500] hover:text-[#ffa500]`}
                >
                  Connection
                </div>
              </div>
              <div
                className={`w-[760px] min-h-[700px] flex justify-center items-center ${
                  type === "connection" ? "hidden" : ""
                }`}
              >
                <FileBody
                  type={dayType}
                  start={start}
                  day={day}
                  end={end}
                  close={close}
                  setChangeAction={setChangeAction}
                />
              </div>
              <div
                className={`w-[760px] h-[700px] flex justify-center ${
                  type === "file" ? "hidden" : ""
                }`}
              >
                <ConnectionBody
                  type={dayType}
                  start={start}
                  day={day}
                  end={end}
                  close={close}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
