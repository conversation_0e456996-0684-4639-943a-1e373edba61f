import moment from "moment/moment";
import {
  HiLink,
  HiMiniInformationCircle,
  HiMiniPlayCircle,
} from "react-icons/hi2";
import { document } from "../../../../wailsjs/go/models";
import IElement = document.Element;

interface ElementProps {
  element: IElement;
  setPreviewURL: (url: string | null) => void;
  setIsLoading: (isLoading: boolean) => void;
}

export default function Element({
  element,
  setPreviewURL,
  setIsLoading,
}: ElementProps) {
  const episode = element.file.episode.String ?? null;

  const getPreviewURL = async () => {
    setIsLoading(true);

    try {
      const url = `http://localhost:34567/data${element.file.folder + element.file.filename}`;
      setPreviewURL(url);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={"flex gap-3"}>
      <div className={"flex flex-col gap-3 p-1 text-sm w-[80px] min-w-[80px]"}>
        {moment(element.start).utc().format("hh:mm A")}
        {element.type === "file" && (
          <span
            onClick={getPreviewURL}
            title={"Preview"}
            className={
              "flex items-center justify-center text-[#ffa500] cursor-pointer"
            }
          >
            <HiMiniPlayCircle className={"w-[25px] h-[25px]"} />
          </span>
        )}
      </div>
      <div className={"flex flex-col w-[calc(100%-80px)]"}>
        <p>{element.title}</p>
        {element.type !== "filler" && (
          <p className={"text-sm text-[#9e9e9e] text-justify p-2 pl-0"}>
            {element.description}
          </p>
        )}
        {element.type === "file" && episode && (
          <p
            className={
              "flex items-center gap-2 text-sm text-[#9e9e9e] overflow-hidden text-nowrap"
            }
          >
            <span className={"text-zinc-50"}>Episode:</span> {episode}
          </p>
        )}
        {element.type === "connection" && (
          <p
            className={
              "flex items-center gap-2 text-sm text-[#9e9e9e] overflow-hidden text-nowrap overflow-ellipsis"
            }
          >
            <HiLink className={"w-[15px] h-[15px] min-w-[25px]"} />
            <span className={"text-zinc-50"}>
              {element.connection?.type.toUpperCase()}
            </span>{" "}
            {
              element.connection?.type === "hls" && (
                <span>
                  { element.connection.link }
                </span>
              )
            }
            {
              element.connection?.type === "udp" && (
                <span>
                  udp://{ element.connection.link }:{element.connection?.port}
                </span>
              )
            }
            {
              element.connection?.type === "srt" && (
                <span>
                  srt://{ element.connection.link }:{element.connection?.port}?mode={element.connection?.mode}
                </span>
              )
            }
          </p>
        )}
        {element.type === "filler" && (
          <p className={"flex items-center gap-2 text-sm text-[#9e9e9e]"}>
            <HiMiniInformationCircle
              className={"w-[20px] h-[20px] text-[#4c9eb5]"}
            />
            <span className={"w-[calc(100%-20px)] text-justify"}>
              This is a filler that will be automatically filled with some
              content. For some reason, content could not be generated for this
              time. Perhaps the time block was not filled or there was no
              element of the appropriate duration. Please check your scheduler
              again
            </span>
          </p>
        )}
      </div>
    </div>
  );
}
