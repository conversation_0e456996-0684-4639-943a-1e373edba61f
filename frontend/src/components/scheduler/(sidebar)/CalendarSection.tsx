import { I18n<PERSON><PERSON>ider } from "@react-aria/i18n";
import { Calendar } from "react-multi-date-picker";
import { HiChe<PERSON>ronLeft, HiTrash } from "react-icons/hi2";
import { useEffect, useRef, useState } from "react";
import interactionPlugin from "@fullcalendar/interaction";
import timeGridPlugin from "@fullcalendar/timegrid";
import {
  deleteSpecialDay,
  selectSpecialDays,
} from "@/redux/scheduler/schedulerSlice";
import ConnectionItem from "@/components/scheduler/(item)/ConnectionItem";
import FolderItem from "@/components/scheduler/(item)/FolderItem";
import FullCalendar from "@fullcalendar/react";
import {
  DateSelectArg,
  EventChangeArg,
  EventClickArg,
  EventInput,
} from "@fullcalendar/core";
import { SPECIAL } from "@/const/day-type";
import {useDispatch, useSelector} from "react-redux";

interface CalendarSectionProps {
  editItem: (arg: EventChangeArg, type: string) => void;
  editEvent: (arg: EventClickArg, type: string) => void;
  selectSection: (day: string, info: DateSelectArg, type: string) => void;
  changeView: () => void;
}

export default function CalendarSection({
  selectSection,
  editItem,
  editEvent,
  changeView,
}: CalendarSectionProps) {
  let events: EventInput[] = [];
  const calendarRef = useRef<FullCalendar | null>(null);
  const dispatch = useDispatch();

  const [day, setDay] = useState<string>("");
  const [dates, setDates] = useState<string[]>([]);

  const specialDays = useSelector(selectSpecialDays) ?? [];

  useEffect(() => {
    const initDates: string[] = [];
    specialDays.forEach((iDay) => {
      initDates.push(iDay.name);
    });

    setDates(initDates);
  }, []);

  const selectDay = (newDay: string) => {
    setDay(newDay);
    if (null !== calendarRef.current && newDay) {
      const calendarApi = calendarRef.current.getApi();
      calendarApi.gotoDate(newDay);
    }
  };

  const hasItems = (day: string): boolean => {
    let has = false;
    specialDays.forEach((iDay) => {
      if (iDay.name === day) {
        has = iDay.items.length > 0;
      }
    });

    return has;
  };

  if (day) {
    events = [];

    specialDays.forEach((iDay) => {
      if (iDay.name === day) {
        iDay.items.forEach((item) => {
          events = [...events, item];
        });
      }
    });
  }

  const deleteDay = (day: string) => {
    dispatch(deleteSpecialDay(day));
    setDay("");
  };

  return (
    <>
      <div
        className={`h-full left-0 top-0 z-10 absolute w-full bg-[#151515A3]`}
        onClick={changeView}
      />
      <div
        className={
          "flex flex-col gap-10 rounded-[10px] h-full absolute top-0 left-0 z-20 bg-[#202020] shadow-[0_0_4px_2px_#0a0a0a] opacity-100 w-[272px] p-2 pt-[20px]"
        }
      >
        <I18nProvider locale="en">
          <Calendar
            format="YYYY-MM-DD"
            multiple={true}
            className={"!text-[white] !bg-[#151515A3] bg-dark"}
            onFocusedDateChange={(_, dateClicked) => {
              if (dateClicked) {
                selectDay(dateClicked.toString());
                if (!dates.includes(dateClicked.toString())) {
                  setDates([
                    ...dates.filter((date) => hasItems(date)),
                    dateClicked.toString(),
                  ]);
                } else {
                  setDates(dates.filter((date) => hasItems(date)));
                }
              }
            }}
            value={dates}
            onChange={(value) => {
              if (value.length <= dates.length) {
                return false;
              }
            }}
          />
        </I18nProvider>
        <button
          onClick={changeView}
          className={
            "bg-[#151515A3] flex justify-center items-center h-[40px] gap-3 rounded-md w-full focus-visible:outline-0 text-zinc-50 hover:border-[#ffa500] border border-[#4D4D4D]"
          }
        >
          <HiChevronLeft />
        </button>
      </div>
      {day && events && (
        <div
          className={
            "h-full w-[400px] z-20 top-0 left-[274px] absolute bg-[#202020] opacity-100 rounded-[10px]"
          }
        >
          <FullCalendar
            ref={calendarRef}
            timeZone={"UTC"}
            editable={true}
            eventOverlap={false}
            initialDate={day}
            dayHeaderFormat={{
              weekday: "long",
              month: "numeric",
              day: "numeric",
            }}
            headerToolbar={{
              left: "",
              center: "",
              right: "",
            }}
            slotLabelFormat={{
              hour: "2-digit",
              minute: "2-digit",
            }}
            selectable={true}
            initialView={"timeGridWeek"}
            views={{
              timeGridWeek: {
                type: "timeGrid",
                duration: {
                  days: 1,
                },
              },
            }}
            allDaySlot={false}
            scrollTime={"00:00:00"}
            plugins={[interactionPlugin, timeGridPlugin]}
            select={(info) => selectSection(day, info, SPECIAL)}
            eventClick={(arg) => editEvent(arg, SPECIAL)}
            eventDrop={(arg) => editItem(arg, SPECIAL)}
            eventResize={(arg) => editItem(arg, SPECIAL)}
            height={"100%"}
            locale={"UTC"}
            events={events}
            eventColor={"#1E1E1E"}
            eventContent={(arg) => {
              const props = arg.event.extendedProps;
              if (props.type === "connection") {
                return (
                  <ConnectionItem
                    connection={props.connection}
                    link={props.link}
                    name={props.name}
                    port={props.port}
                    mode={props.mode}
                  />
                );
              }

              return <FolderItem folders={props.folders} files={props.files} />;
            }}
          />
          <div
            className={
              "flex flex-col gap-10 p-[8px] pl-[2px] w-[50px] absolute top-0 left-[400px] h-full bg-[#0f0f0fe5]"
            }
          >
            <button
              onClick={() => deleteDay(day)}
              className={
                "bg-[#151515A3] text-zinc-50 flex justify-center items-center h-[40px] gap-3 rounded-md w-full hover:border-[#891C1CFF] hover:bg-[#891C1CFF] focus-visible:outline-0 border border-[#4D4D4D]"
              }
            >
              <HiTrash />
            </button>
          </div>
        </div>
      )}
    </>
  );
}
