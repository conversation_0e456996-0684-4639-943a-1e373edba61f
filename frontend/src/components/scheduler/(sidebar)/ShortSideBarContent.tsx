import { <PERSON>FillSave, AiOutlineVideoCameraAdd } from "react-icons/ai";
import {
  HiCalendarDays,
  HiCheckCircle,
  HiChevronRight,
  HiMegaphone,
  HiMiniListBullet,
  HiMiniTableCells,
  HiPlay,
  HiSignal,
} from "react-icons/hi2";
import { useSelector } from "react-redux";
import { selectGuide, selectSchedule } from "@/redux/scheduler/schedulerSlice";
import moment from "moment/moment";
import { useState } from "react";
import CopyHLSScheduleModal from "@/components/schedulers/(modal)/CopyHLSScheduleModal";
import { getEPGLink, getHLSLink } from "@/lib/scheduler";
import Loading from "@/components/common/Loading";

interface ShortSideBarContentProps {
  changeView: () => void;
  setHideCalendarSection: (hidden: boolean) => void;
  hideCalendarSection: boolean;
  setHideGuideSection: (hidden: boolean) => void;
  hideGuideSection: boolean;
  isSaveAction: boolean;
  handleSave: () => void;
  copyLink: (name: string | null) => void;
  setPreviewURL: (url: string | null) => void;
  openSettingModal: () => void;
  openFillerModal: () => void;
  openContentSettingModal: () => void;
  openRtpOutputSettingModal: () => void;
}

export default function ShortSideBarContent({
  openSettingModal,
  openFillerModal,
  setPreviewURL,
  copyLink,
  handleSave,
  isSaveAction,
  changeView,
  setHideCalendarSection,
  setHideGuideSection,
  hideGuideSection,
  hideCalendarSection,
  openContentSettingModal,
  openRtpOutputSettingModal,
}: ShortSideBarContentProps) {
  const schedule = useSelector(selectSchedule);
  const guide = useSelector(selectGuide);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [activeHLSCopy, setActiveHLSCopy] = useState<boolean>(false);

  const toggleCalendar = () => {
    setHideCalendarSection(!hideCalendarSection);
    setHideGuideSection(true);
  };

  const toggleGuide = () => {
    setHideGuideSection(!hideGuideSection);
    setHideCalendarSection(true);
  };

  return (
    <div
      className={`p-2 pb-5 rounded-r-[20px] flex flex-col justify-between w-[60px] min-w-[60px] bg-[#292929c4]`}
      style={{ height: "calc(100vh)" }}
    >
      <div className={"flex flex-col gap-5"}>
        <button
          title={"Calendar"}
          onClick={toggleCalendar}
          className={
            "bg-[#151515A3] text-zinc-50 flex justify-center items-center h-[40px] gap-3 rounded-md w-full hover:border-[#ffa500] focus-visible:outline-0 border border-[#4D4D4D]"
          }
        >
          <HiCalendarDays />
        </button>
        <button
          title={"Guide"}
          onClick={toggleGuide}
          className={
            "bg-[#151515A3] text-zinc-50 flex justify-center items-center h-[40px] gap-3 rounded-md w-full hover:border-[#ffa500] focus-visible:outline-0 border border-[#4D4D4D]"
          }
        >
          <HiMiniTableCells />
        </button>
        {/*<button*/}
        {/*  title={"Preview"}*/}
        {/*  onClick={() => setPreviewURL(getHLSLink(schedule, "preview"))}*/}
        {/*  disabled={guide === null}*/}
        {/*  className={*/}
        {/*    "bg-[#151515A3] text-zinc-50 flex justify-center items-center h-[40px] gap-3 rounded-md w-full hover:border-[#ffa500] disabled:cursor-default disabled:text-[#4D4D4D] disabled:hover:border-[#4D4D4D] focus-visible:outline-0 border border-[#4D4D4D]"*/}
        {/*  }*/}
        {/*>*/}
        {/*  <HiPlay />*/}
        {/*</button>*/}
        {/*{activeHLSCopy && (*/}
        {/*  <CopyHLSScheduleModal*/}
        {/*    close={() => setActiveHLSCopy(false)}*/}
        {/*    schedule={schedule}*/}
        {/*    copyLink={copyLink}*/}
        {/*    setIsLoading={setIsLoading}*/}
        {/*  />*/}
        {/*)}*/}
        <button
          title={"Copy EPG link"}
          disabled={guide === null}
          onClick={async () => copyLink(await getEPGLink(schedule))}
          className={
            "bg-[#151515A3] text-zinc-50 flex justify-center items-center h-[40px] gap-3 rounded-md w-full hover:border-[#ffa500] disabled:cursor-default disabled:text-[#4D4D4D] disabled:hover:border-[#4D4D4D] focus-visible:outline-0 border border-[#4D4D4D]"
          }
        >
          EPG
        </button>
        {/*<button*/}
        {/*  title={"Copy HLS link"}*/}
        {/*  disabled={guide === null}*/}
        {/*  onClick={() => setActiveHLSCopy(true)}*/}
        {/*  className={*/}
        {/*    "bg-[#151515A3] text-zinc-50 flex justify-center items-center h-[40px] gap-3 rounded-md w-full hover:border-[#ffa500] disabled:cursor-default disabled:text-[#4D4D4D] disabled:hover:border-[#4D4D4D] focus-visible:outline-0 border border-[#4D4D4D]"*/}
        {/*  }*/}
        {/*>*/}
        {/*  HLS*/}
        {/*</button>*/}
        <button
          title={"Filler setting"}
          onClick={openFillerModal}
          className={
            "bg-[#151515A3] text-zinc-50 flex justify-center items-center h-[40px] gap-3 rounded-md w-full hover:border-[#ffa500] disabled:cursor-default disabled:text-[#4D4D4D] disabled:hover:border-[#4D4D4D] focus-visible:outline-0 border border-[#4D4D4D]"
          }
        >
          <AiOutlineVideoCameraAdd />
        </button>
        {/*<button*/}
        {/*  title={"Ads setting"}*/}
        {/*  onClick={openSettingModal}*/}
        {/*  className={*/}
        {/*    "bg-[#151515A3] text-zinc-50 flex justify-center items-center h-[40px] gap-3 rounded-md w-full hover:border-[#ffa500] disabled:cursor-default disabled:text-[#4D4D4D] disabled:hover:border-[#4D4D4D] focus-visible:outline-0 border border-[#4D4D4D]"*/}
        {/*  }*/}
        {/*>*/}
        {/*  <HiMegaphone />*/}
        {/*</button>*/}
        <button
          title={"Content setting"}
          onClick={openContentSettingModal}
          className={
            "bg-[#151515A3] text-zinc-50 flex justify-center items-center h-[40px] gap-3 rounded-md w-full hover:border-[#ffa500] disabled:cursor-default disabled:text-[#4D4D4D] disabled:hover:border-[#4D4D4D] focus-visible:outline-0 border border-[#4D4D4D]"
          }
        >
          <HiMiniListBullet />
        </button>
        <button
          title={"RTP Output"}
          onClick={openRtpOutputSettingModal}
          className={
            "bg-[#151515A3] text-zinc-50 flex justify-center items-center h-[40px] gap-3 rounded-md w-full hover:border-[#ffa500] disabled:cursor-default disabled:text-[#4D4D4D] disabled:hover:border-[#4D4D4D] focus-visible:outline-0 border border-[#4D4D4D]"
          }
        >
          <HiSignal />
        </button>
      </div>
      <div className={"flex flex-col gap-2"}>
        {!schedule.autosave && (
          <button
            title={"Save"}
            onClick={handleSave}
            className={
              "text-[#ffa500] flex justify-center items-center h-[40px] gap-3 rounded-md w-full border border-[#4D4D4D] hover:border-[#ffa500]"
            }
          >
            <AiFillSave />
          </button>
        )}
        <span
          className={"flex flex-col justify-center items-center gap-1 mb-5"}
        >
          <HiCheckCircle className={"text-emerald-500"} />
          <span className={"text-neutral-500 text-sm text-center"}>
            {isSaveAction ? "Save.." : moment(schedule.updated_at).format("LT")}
          </span>
        </span>
        <button
          onClick={changeView}
          className={
            "bg-[#151515A3] text-zinc-50 flex justify-center items-center h-[40px] gap-3 rounded-md w-full hover:border-[#ffa500] focus-visible:outline-0 border border-[#4D4D4D]"
          }
        >
          <HiChevronRight />
        </button>
      </div>
      {isLoading && <Loading />}
    </div>
  );
}
