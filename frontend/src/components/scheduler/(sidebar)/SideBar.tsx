import FullSideBarContent from "@/components/scheduler/(sidebar)/FullSideBarContent";
import SmallView from "@/components/scheduler/(sidebar)/ShortSideBarContent";
import { ClipboardSetText, EventsEmit } from "../../../../wailsjs/runtime";

interface SideBarProps {
  hidden: boolean;
  setHidden: (value: boolean) => void;
  setChangeAction: (value: boolean) => void;
  setHideCalendarSection: (hidden: boolean) => void;
  hideCalendarSection: boolean;
  setHideGuideSection: (hidden: boolean) => void;
  hideGuideSection: boolean;
  isSaveAction: boolean;
  handleSave: () => void;
  setPreviewURL: (url: string | null) => void;
  openSettingModal: () => void;
  openFillerModal: () => void;
  openContentSettingModal: () => void;
  openRtpOutputSettingModal: () => void;
}

export default function SideBar({
  setPreviewURL,
  openSettingModal,
  openFillerModal,
  hidden,
  setHidden,
  setChangeAction,
  isSaveAction,
  setHideCalendarSection,
  setHideGuideSection,
  handleSave,
  hideGuideSection,
  hideCalendarSection,
  openContentSettingModal,
  openRtpOutputSettingModal,
}: SideBarProps) {
  const copyLink = (link: string | null) => {
    if (!link) {
      return;
    }

    ClipboardSetText(link).then(() =>
        EventsEmit("notification:success", "Successfully copied")
    ).catch(error =>
        EventsEmit("notification:error", error)
    )
  };

  return hidden ? (
    <SmallView
      openFillerModal={openFillerModal}
      handleSave={handleSave}
      setHideCalendarSection={setHideCalendarSection}
      setHideGuideSection={setHideGuideSection}
      changeView={() => setHidden(false)}
      hideGuideSection={hideGuideSection}
      hideCalendarSection={hideCalendarSection}
      copyLink={copyLink}
      isSaveAction={isSaveAction}
      setPreviewURL={setPreviewURL}
      openSettingModal={openSettingModal}
      openContentSettingModal={openContentSettingModal}
      openRtpOutputSettingModal={openRtpOutputSettingModal}
    />
  ) : (
    <FullSideBarContent
      openFillerModal={openFillerModal}
      handleSave={handleSave}
      setChangeAction={setChangeAction}
      setHideCalendarSection={setHideCalendarSection}
      setHideGuideSection={setHideGuideSection}
      changeView={() => setHidden(true)}
      hideGuideSection={hideGuideSection}
      hideCalendarSection={hideCalendarSection}
      copyLink={copyLink}
      isSaveAction={isSaveAction}
      setPreviewURL={setPreviewURL}
      openSettingModal={openSettingModal}
      openContentSettingModal={openContentSettingModal}
      openRtpOutputSettingModal={openRtpOutputSettingModal}
    />
  );
}
