import {
  generateGuide,
  getSchedule,
  openScheduler,
  saveScheduler,
  selectGuide,
  selectSchedule,
} from "@/redux/scheduler/schedulerSlice";
import moment from "moment";
import { useState } from "react";
import { Hi<PERSON>rrowPath, HiChevronLeft } from "react-icons/hi2";
import Element from "@/components/scheduler/(sidebar)/Element";
import AttentionModal from "@/components/scheduler/(modal)/AttentionModal/AttentionModal";
import {useDispatch, useSelector} from "react-redux";
import { EventsEmit } from "../../../../wailsjs/runtime";

interface GuideSectionProps {
  hide: () => void;
  setIsLoading: (loading: boolean) => void;
  setPreviewURL: (url: string | null) => void;
}

export default function GuideSection({
  hide,
  setPreviewURL,
  setIsLoading,
}: GuideSectionProps) {
  const dispatch = useDispatch();
  const [showAttentionModal, setShowAttentionModal] = useState<boolean>(false);
  const guide = useSelector(selectGuide);
  const schedule = useSelector(selectSchedule);

  const [activeDate, setActiveDate] = useState<string>(
    moment().format("MM/DD")
  );
  const today = moment().utc().format("MM/DD");

  let dates: string[] = [];
  let days: { date: string; day: string }[] = [];

  const handleGenerateGuide = async () => {
    if (!schedule?.id) {
      return;
    }

    setIsLoading(true);
    try {
      await saveScheduler(schedule);
      await generateGuide(schedule);
      const response = await getSchedule(schedule.id);
      dispatch(openScheduler(response));
    } catch (err) {
        EventsEmit("notification:error", err)
    } finally {
      setIsLoading(false);
    }
  };

  if (!guide || !guide.id) {
    return (
      <>
        <div
          className={`h-full left-0 top-0 z-10 absolute w-full bg-[#151515A3]`}
          onClick={hide}
        />
        <div
          className={
            "rounded-[10px] h-full absolute top-0 left-0 z-20 bg-[#202020] shadow-[0_0_4px_2px_#0a0a0a] opacity-100 w-[800px] p-2"
          }
        >
          <div
            className={
              "w-full h-full flex flex-col gap-[20px] items-center justify-center"
            }
          >
            <p className={"text-large"}>The guide has not been created</p>
            <button
              onClick={handleGenerateGuide}
              className={
                "bg-[#151515A3] text-zinc-50 px-[20px] flex justify-center items-center h-[40px] gap-3 rounded-md hover:border-[#ffa500] focus-visible:outline-0 border border-[#4D4D4D]"
              }
            >
              Create a guide now
            </button>
          </div>
        </div>
      </>
    );
  }

  guide.elements.forEach((element) => {
    let date = moment(element.start).utc().format("MM/DD");
    if (!dates.includes(date)) {
      dates.push(date);
      days.push({
        date: date,
        day: moment(element.start).utc().format("ddd"),
      });
    }
  });

  return (
    <>
      {showAttentionModal && (
        <AttentionModal
          close={() => setShowAttentionModal(false)}
          setIsLoading={setIsLoading}
        />
      )}
      <div
        className={`h-full left-0 top-0 z-10 absolute w-full bg-[#151515A3]`}
        onClick={hide}
      />
      <div
        className={
          "flex flex-col gap-2 rounded-[10px] h-full absolute top-0 left-0 z-20 bg-[#202020] shadow-[0_0_4px_2px_#0a0a0a] opacity-100 w-[800px] p-2"
        }
      >
        <div className={"flex justify-between gap-2"}>
          {days.map((date) => {
            return (
              <div
                onClick={() => {
                  setActiveDate(date.date);
                }}
                className={`flex-col p-2 w-[100%] rounded-[8px] border flex items-center justify-center text-zinc-50 ${
                  activeDate === date.date
                    ? "border-[#ffa500]"
                    : "border-[#4D4D4D] cursor-pointer"
                } hover:border-[#ffa500] focus-visible:outline-0 bg-[#151515A3]`}
              >
                {today === date.date && (
                  <span className={"font-bold text-[#ffa500]"}>Today</span>
                )}
                {today !== date.date && (
                  <span className={"font-bold"}>{date.day}</span>
                )}
                <span className={"text-small text-[#9e9e9e]"}>{date.date}</span>
              </div>
            );
          })}
          <div
            onClick={() => setShowAttentionModal(true)}
            title={"Force update the guide"}
            className={
              "h-full flex items-center justify-center cursor-pointer p-2 hover:text-[#ffa500]"
            }
          >
            <HiArrowPath className={"w-[20px] h-[20px]"} />
          </div>
        </div>
        <div className={`flex flex-col gap-2 overflow-x-auto`}>
          {guide.elements.map((element) => {
            if (activeDate === moment(element.start).utc().format("MM/DD")) {
              return (
                <Element
                  setIsLoading={setIsLoading}
                  setPreviewURL={setPreviewURL}
                  element={element}
                />
              );
            }
            return null;
          })}
        </div>
      </div>
      <div
        onClick={hide}
        className={
          "flex flex-col items-center justify-center cursor-pointer gap-2 rounded-[10px] h-full absolute top-0 left-[800px] z-20 bg-[#202020] shadow-[0_0_4px_2px_#0a0a0a] opacity-100 w-[30px] p-2 hover:bg-[#151515]"
        }
      >
        <HiChevronLeft className={"w-[25px] h-[25px] text-[#9e9e9e]"} />
      </div>
    </>
  );
}
