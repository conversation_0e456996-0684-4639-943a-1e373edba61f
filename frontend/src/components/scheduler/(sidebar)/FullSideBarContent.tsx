import { AiFillSave, <PERSON><PERSON>illPicture, AiOutlineVideoCameraAdd } from "react-icons/ai";
import {
  Hi<PERSON>rrowPath,
  HiCalendarDays,
  HiCheckCircle,
  HiChevronLeft,
  HiChevronRight,
  HiMegaphone,
  HiMiniListBullet,
  HiMiniTableCells,
  HiPlay,
  HiSignal,
} from "react-icons/hi2";
import {useDispatch, useSelector} from "react-redux";
import {
  changeSchedulerDetail,
  selectGuide,
  selectSchedule,
  uploadIcon,
} from "@/redux/scheduler/schedulerSlice";
import moment from "moment";
import { Timezones } from "@/lib/timezone";
import Loading from "@/components/common/Loading";
import { useEffect, useState } from "react";
import { HiClipboardCopy } from "react-icons/hi";
import CopyHLSScheduleModal from "@/components/schedulers/(modal)/CopyHLSScheduleModal";
import AttentionModal from "@/components/scheduler/(modal)/AttentionModal/AttentionModal";
import { getEPGLink, getHLSLink } from "@/lib/scheduler";
import { Checkbox, Skeleton } from "@heroui/react";
import Select from "react-select";
import momentTimezone from "moment-timezone";

interface FullSideBarContentProps {
  changeView: () => void;
  setHideCalendarSection: (hidden: boolean) => void;
  setChangeAction: (value: boolean) => void;
  hideCalendarSection: boolean;
  setHideGuideSection: (hidden: boolean) => void;
  hideGuideSection: boolean;
  isSaveAction: boolean;
  handleSave: () => void;
  copyLink: (name: string | null) => void;
  setPreviewURL: (url: string | null) => void;
  openSettingModal: () => void;
  openFillerModal: () => void;
  openContentSettingModal: () => void;
  openRtpOutputSettingModal: () => void;
}

export default function FullSideBarContent({
  openSettingModal,
  openFillerModal,
  setPreviewURL,
  copyLink,
  isSaveAction,
  setChangeAction,
  handleSave,
  changeView,
  setHideCalendarSection,
  setHideGuideSection,
  hideGuideSection,
  hideCalendarSection,
  openContentSettingModal,
  openRtpOutputSettingModal,
}: FullSideBarContentProps) {
  const dispatch = useDispatch();
  const schedule = useSelector(selectSchedule);
  const guide = useSelector(selectGuide);
  const timezones = Timezones();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [previewSrc, setPreviewSrc] = useState<string | null>(null);
  const [activeHLSCopy, setActiveHLSCopy] = useState<boolean>(false);
  const [showAttentionModal, setShowAttentionModal] = useState<boolean>(false);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const changeValue = (field: string, value: boolean | string | null) => {
    setChangeAction(true);
    dispatch(changeSchedulerDetail({ field, value }));
  };

  const handleUploadIcon = async (file: undefined | File) => {
    // if (!file) return;
    // setIsLoading(true);
    // const response = await uploadIcon(file);
    // setPreviewSrc(URL.createObjectURL(file));
    // changeValue("icon", response.data.data);
    // setIsLoading(false);
  };

  const toggleCalendar = () => {
    setHideCalendarSection(!hideCalendarSection);
    setHideGuideSection(true);
  };

  const toggleGuide = () => {
    setHideGuideSection(!hideGuideSection);
    setHideCalendarSection(true);
  };

  if (!isClient) {
    return (
      <div
        className={`px-5 py-2 flex rounded-r-[20px] flex-col justify-between w-[290px] min-w-[290px] bg-[#292929c4] overflow-auto gap-2`}
        style={{ height: "calc(100vh - 80px)" }}
      >
        <div className={"flex flex-col gap-2"}>
          <div className={"flex flex-col gap-1"}>
            <Skeleton className={'rounded-lg h-[24px] w-[100px]'} />
            <Skeleton className={'rounded-lg h-[36px]'} />
          </div>
          <div className={"flex flex-col gap-1"}>
            <Skeleton className={'rounded-lg h-[24px] w-[100px]'} />
            <Skeleton className={'rounded-lg h-[36px]'} />
          </div>
          <div className={"flex flex-col gap-1"}>
            <div className={"flex justify-center items-center"}>
              <Skeleton className={'rounded-lg w-[100px] h-[100px]'} />
            </div>
            <Skeleton className={'rounded-lg h-[36px]'} />
          </div>
          <div className={"flex flex-col gap-2"}>
            <Skeleton className={'rounded-lg h-[36px]'} />
          </div>
          <div className={"flex gap-2"}>
            <Skeleton className={'rounded-lg h-[36px] w-[50px]'} />
            <Skeleton className={'rounded-lg h-[36px] w-[calc(100%-50px)]'} />
          </div>
          <div className={"flex flex-col gap-2"}>
            <Skeleton className={'rounded-lg h-[36px]'} />
          </div>
          <div className={"flex gap-2"}>
            <Skeleton className={'rounded-lg h-[36px] w-[50%]'} />
            <Skeleton className={'rounded-lg h-[36px] w-[50%]'} />
          </div>
          <div>
            <Skeleton className={'rounded-lg h-[36px]'} />
          </div>
          <div>
            <Skeleton className={'rounded-lg h-[36px]'} />
          </div>
        </div>
        <div className={"flex flex-col gap-2"}>
          <Skeleton className={'rounded-lg h-[36px]'} />
          <span className={"flex justify-center items-center h-[20px] gap-3"}>
            <Skeleton className={'rounded-lg h-[20px] w-[20px]'} />
            <Skeleton className={'rounded-lg h-[20px] w-[150px]'} />
          </span>
          <div className={"flex items-center justify-center gap-2 mb-5"}>
            <Skeleton className={'rounded-lg h-[20px] w-[20px]'} />
            <Skeleton className={'rounded-lg h-[20px] w-[60px]'} />
          </div>
          <Skeleton className={'rounded-lg h-[36px]'} />
        </div>
      </div>
    )
  }

  return (
    <>
      {showAttentionModal && (
        <AttentionModal
          close={() => setShowAttentionModal(false)}
          setIsLoading={setIsLoading}
        />
      )}
      <div
        className={`px-5 py-2 flex rounded-r-[20px] flex-col justify-between w-[290px] min-w-[290px] bg-[#292929c4] overflow-auto gap-2`}
        style={{ height: "calc(100vh)" }}
      >
        <div className={"flex flex-col gap-2"}>
          <div className={"flex flex-col gap-1"}>
            <label className={"text-zinc-50"}>Title</label>
            <input
              className={
                "bg-[#151515A3] p-3 h-[40px] focus-visible:outline-0 hover:border-[#ffa500] focus:border-[#ffa500] border border-[#4D4D4D] rounded-md"
              }
              type={"text"}
              placeholder={"Channel name"}
              value={schedule.name}
              onChange={(event) => changeValue("name", event.target.value)}
            />
          </div>
          <div className={"flex flex-col gap-1"}>
            <label className={"text-zinc-50"}>Timezone</label>
            <Select
              options={timezones}
              defaultValue={{
                value: schedule.timezone,
                label:
                  " (GMT" +
                  momentTimezone.tz(schedule.timezone).format("Z") +
                  ") " +
                  schedule.timezone,
              }}
              onChange={(data) =>
                data?.value ? changeValue("timezone", data.value) : ""
              }
              onMenuClose={() => {}}
              onMenuOpen={() => {}}
              className={"react-select-container w-full"}
              classNamePrefix="react-select"
            />
          </div>
          <div className={"flex flex-col gap-1"}>
            <div className={"flex justify-center items-center"}>
              {schedule.icon && (
                <div
                  className={
                    "w-[200px] h-[100px] flex justify-center items-center"
                  }
                >
                  <img
                    className={"max-w-[200px] max-h-[100px]"}
                    src={previewSrc ?? schedule.icon}
                    alt={"Icon"}
                  />
                </div>
              )}
              {!schedule.icon && (
                <AiFillPicture
                  className={"w-[100px] h-[100px] text-neutral-500"}
                />
              )}
            </div>
            <label
              className={
                "bg-[#151515A3] text-zinc-50 flex cursor-pointer justify-center items-center h-[40px] gap-3 rounded-md w-full hover:border-[#ffa500] focus-visible:outline-0 border border-[#4D4D4D]"
              }
            >
              Upload channel icon
              <input
                onChange={(event) => handleUploadIcon(event.target.files?.[0])}
                className={"absolute z-[-1]"}
                type={"file"}
                accept={"image/*"}
              />
            </label>
          </div>
          <div className={"flex flex-col gap-2"}>
            <button
              onClick={toggleCalendar}
              className={
                "bg-[#151515A3] text-zinc-50 flex justify-center items-center h-[40px] gap-3 rounded-md w-full hover:border-[#ffa500] focus-visible:outline-0 border border-[#4D4D4D]"
              }
            >
              <HiCalendarDays />
              Calendar
              <HiChevronRight />
            </button>
          </div>
          <div className={"flex gap-2"}>
            <button
              onClick={() => setShowAttentionModal(true)}
              disabled={guide === null}
              title={"Force update the guide"}
              className={
                "bg-[#151515A3] text-zinc-50 flex justify-center items-center h-[40px] gap-3 rounded-md w-[50px] hover:border-[#ffa500] focus-visible:outline-0 border border-[#4D4D4D] disabled:cursor-default disabled:text-[#4D4D4D] disabled:hover:border-[#4D4D4D]"
              }
            >
              <HiArrowPath />
            </button>
            <button
              onClick={toggleGuide}
              className={
                "bg-[#151515A3] text-zinc-50 flex justify-center items-center h-[40px] gap-3 rounded-md w-full hover:border-[#ffa500] focus-visible:outline-0 border border-[#4D4D4D]"
              }
            >
              <HiMiniTableCells />
              Guide
              <HiChevronRight />
            </button>
          </div>
          {/*<div className={"flex flex-col gap-2"}>*/}
          {/*  <button*/}
          {/*    disabled={guide === null}*/}
          {/*    onClick={() => setPreviewURL(getHLSLink(schedule, "preview"))}*/}
          {/*    className={*/}
          {/*      "bg-[#151515A3] text-zinc-50 flex justify-center items-center h-[40px] gap-3 rounded-md w-full hover:border-[#ffa500] disabled:cursor-default disabled:text-[#4D4D4D] disabled:hover:border-[#4D4D4D] focus-visible:outline-0 border border-[#4D4D4D]"*/}
          {/*    }*/}
          {/*  >*/}
          {/*    <HiPlay />*/}
          {/*    Preview*/}
          {/*  </button>*/}
          {/*</div>*/}
          <div className={"flex gap-2"}>
            {activeHLSCopy && (
                <CopyHLSScheduleModal
                close={() => setActiveHLSCopy(false)}
                copyLink={copyLink}
                schedule={schedule}
                setIsLoading={setIsLoading}
              />
            )}
            <button
              disabled={guide === null}
              onClick={async () => copyLink(await getEPGLink(schedule))}
              className={
                "bg-[#151515A3] text-zinc-50 flex justify-center items-center h-[40px] gap-3 rounded-md w-full hover:border-[#ffa500] disabled:cursor-default disabled:text-[#4D4D4D] disabled:hover:border-[#4D4D4D] focus-visible:outline-0 border border-[#4D4D4D]"
              }
            >
              <HiClipboardCopy />
              EPG
            </button>
            {/*<button*/}
            {/*  disabled={guide === null}*/}
            {/*  onClick={() => setActiveHLSCopy(true)}*/}
            {/*  className={*/}
            {/*    "bg-[#151515A3] text-zinc-50 flex justify-center items-center h-[40px] gap-3 rounded-md w-full hover:border-[#ffa500] disabled:cursor-default disabled:text-[#4D4D4D] disabled:hover:border-[#4D4D4D] focus-visible:outline-0 border border-[#4D4D4D]"*/}
            {/*  }*/}
            {/*>*/}
            {/*  <HiClipboardCopy />*/}
            {/*  HLS*/}
            {/*</button>*/}
          </div>
          <div>
            <button
              onClick={openFillerModal}
              className={"bg-[#151515A3] text-zinc-50 flex justify-center items-center h-[40px] gap-3 rounded-md w-full hover:border-[#ffa500] disabled:cursor-default disabled:text-[#4D4D4D] disabled:hover:border-[#4D4D4D] focus-visible:outline-0 border border-[#4D4D4D]"}
            >
              <AiOutlineVideoCameraAdd />
              Filler setting
            </button>
          </div>
          {/*<div>*/}
          {/*  <button*/}
          {/*    onClick={openSettingModal}*/}
          {/*    className={*/}
          {/*      "bg-[#151515A3] text-zinc-50 flex justify-center items-center h-[40px] gap-3 rounded-md w-full hover:border-[#ffa500] disabled:cursor-default disabled:text-[#4D4D4D] disabled:hover:border-[#4D4D4D] focus-visible:outline-0 border border-[#4D4D4D]"*/}
          {/*    }*/}
          {/*  >*/}
          {/*    <HiMegaphone />*/}
          {/*    Ads setting*/}
          {/*  </button>*/}
          {/*</div>*/}
          <div>
            <button
              onClick={openContentSettingModal}
              className={"bg-[#151515A3] text-zinc-50 flex justify-center items-center h-[40px] gap-3 rounded-md w-full hover:border-[#ffa500] disabled:cursor-default disabled:text-[#4D4D4D] disabled:hover:border-[#4D4D4D] focus-visible:outline-0 border border-[#4D4D4D]"}
            >
              <HiMiniListBullet />
              Content setting
            </button>
          </div>
          <div>
            <button
              onClick={openRtpOutputSettingModal}
              className={"bg-[#151515A3] text-zinc-50 flex justify-center items-center h-[40px] gap-3 rounded-md w-full hover:border-[#ffa500] disabled:cursor-default disabled:text-[#4D4D4D] disabled:hover:border-[#4D4D4D] focus-visible:outline-0 border border-[#4D4D4D]"}
            >
              <HiSignal />
              RTP Output
            </button>
          </div>
        </div>
        <div className={"flex flex-col gap-2"}>
          {!schedule.autosave && (
            <button
              onClick={handleSave}
              className={
                "text-[#ffa500] flex justify-center items-center h-[40px] gap-3 rounded-md w-full border border-[#4D4D4D] hover:border-[#ffa500]"
              }
            >
              <AiFillSave />
              <span>Save</span>
            </button>
          )}
          <span className={"flex justify-center items-center h-[20px] gap-3"}>
            {!isSaveAction && schedule.updated_at && (
              <HiCheckCircle className={"text-emerald-500"} />
            )}
            {isSaveAction && <HiArrowPath className={"text-emerald-500"} />}
            <span className={"text-neutral-500 text-sm"}>
              {isSaveAction
                ? "Saving..."
                : schedule.updated_at
                ? moment(schedule.updated_at).format("L LTS")
                : ""}
            </span>
          </span>
          <div className={"flex items-center justify-center gap-2 mb-5"}>
            <Checkbox
              isSelected={schedule.autosave}
              onChange={() => changeValue("autosave", !schedule.autosave)}
              id={"autosave"}
              name={"autosave"}
            ></Checkbox>
            <label htmlFor={"autosave"} className={"text-small"}>
              Autosave
            </label>
          </div>
          <button
            className={
              "bg-[#151515A3] text-zinc-50 flex justify-center items-center h-[40px] gap-3 rounded-md w-full hover:border-[#ffa500] focus-visible:outline-0 border border-[#4D4D4D]"
            }
            onClick={changeView}
          >
            <HiChevronLeft />
          </button>
        </div>
      </div>
      {isLoading && <Loading />}
    </>
  );
}
