import {
  HiDocument,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  HiMiniArrowRightCircle,
} from "react-icons/hi2";
import { FolderProps } from "@/redux/scheduler/schedulerSlice";

interface FolderItemProps {
  folders: FolderProps[];
  files: FolderProps[];
}

export default function FolderItem({ folders, files }: FolderItemProps) {
  return (
    <div className={"p-1 flex flex-col gap-2 overflow-hidden h-full"}>
      {folders.length > 0 && (
        <>
          <div className={"flex justify-center items-center gap-2 uppercase"}>
            <HiFolderOpen className={"w-[15px] h-[15px]"} />
          </div>
          <div className={"text-nowrap overflow-hidden h-full"}>
            <ul>
              {folders.map((folder: FolderProps) => {
                let paths = folder.path.split("/");
                let path = "/" + paths[paths.length - 1];
                if (paths.length > 2) {
                  path = "/" + paths[paths.length - 2] + path;
                }

                return (
                  <li
                    title={folder.path}
                    key={folder.path}
                    className={"flex gap-2 items-center"}
                  >
                    <HiMiniArrowRightCircle
                      className={"min-w-[5px] w-[5px] h-[5px]"}
                    />
                    {path}
                  </li>
                );
              })}
            </ul>
          </div>
        </>
      )}
      {files.length > 0 && (
        <>
          <div className={"flex justify-center items-center gap-2 uppercase"}>
            <HiDocument className={"w-[15px] h-[15px]"} />
          </div>
          <div className={"text-nowrap overflow-hidden h-full"}>
            <ul>
              {files.map((file: FolderProps) => {
                let paths = file.path.split("/");
                let path = "/" + paths[paths.length - 1];
                if (paths.length > 2) {
                  path = "/" + paths[paths.length - 2] + path;
                }

                return (
                  <li
                    title={file.path}
                    key={file.path}
                    className={"flex gap-2 items-center"}
                  >
                    <HiMiniArrowRightCircle
                      className={"min-w-[5px] w-[5px] h-[5px]"}
                    />
                    {path}
                  </li>
                );
              })}
            </ul>
          </div>
        </>
      )}
    </div>
  );
}
