import { HiLink } from "react-icons/hi2";

interface ConnectionItemProps {
  connection: string;
  link: string;
  name: string;
  port: string|undefined;
  mode: string|undefined;
}

export default function ConnectionItem({
  connection,
  link,
  name,
  port,
  mode,
}: ConnectionItemProps) {
  return (
    <div
      title={`${connection.toUpperCase()}\r\n${link}`}
      className={"p-1 flex flex-col gap-2 overflow-hidden h-full"}
    >
      <div className={"flex justify-center items-center gap-2 uppercase"}>
        <HiLink className={"w-[15px] h-[15px]"} />
        {connection}
      </div>
      <div className={"flex justify-center items-center"}>{name}</div>
      <div className={"text-nowrap overflow-hidden h-full"}>
        {link}
        {
          port ? ":" + port : ""
        }
        {
          connection === "srt" && mode ? "?mode=" + mode : ""
        }
      </div>
    </div>
  );
}
