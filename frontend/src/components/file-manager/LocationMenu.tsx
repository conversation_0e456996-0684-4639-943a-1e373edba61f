import {Fragment} from "react";
import {HiFolderPlus} from "react-icons/hi2";

interface LocationMenuProps {
    location: string,
    setLocation: (location: string) => void,
    setIsShowCreateFolderModal: (show: boolean) => void,
}

const LocationMenu = (props: LocationMenuProps) => {
    return (
        <div
            className={'flex gap-2 items-center text-lg '}
        >
            { props.location.split("/").map((folder, index, arr) => {
                if (index > 0 && !folder) return null;

                const isRoot = index === 0 && !folder;
                const currentPath = "/" + arr.slice(1, index + 1).join("/");

                return (
                    <Fragment key={index}>
                        <span
                            onClick={() => props.setLocation(isRoot ? "/" : currentPath)}
                            className={`${index !== arr.length - 1 ? "cursor-pointer hover:text-[#ffa500]" : "text-[#ffa500]" } p-2`}
                        >
                            {isRoot ? "< ... >" : folder}
                        </span>
                        <span className={'text-[#ffa500]'}>/</span>
                    </Fragment>
                )
            }) }
            <span
                className={'cursor-pointer p-2 hover:text-[#ffa500]'}
            >
                <HiFolderPlus onClick={() => props.setIsShowCreateFolderModal(true)}/>
            </span>
        </div>
    )
}

export default LocationMenu;