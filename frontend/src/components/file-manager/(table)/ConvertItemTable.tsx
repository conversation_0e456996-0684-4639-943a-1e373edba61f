import {Chip, Table, TableBody, Table<PERSON>ell, TableColumn, <PERSON><PERSON>eader, TableRow} from "@heroui/react";
import { useSelector } from "react-redux";
import {IConvertItem, selectConvertItems, selectFolders} from "@/redux/file-manager/fileManagerSlice";
import {formatBytes} from "@/lib/file-manager";
import {<PERSON><PERSON><PERSON>, HiFolder, HiMiniTrash} from "react-icons/hi2";
import React from "react";

const colorByStatus = (status: number): "success" | "default" | "danger" | "primary" | "warning" | "secondary" | undefined => {
    switch (status) {
        case 0:
            return "success";
        case 1:
            return "default";
        case 2:
            return "danger";
        case 3:
            return "primary";
        default:
            return "warning";
    }
}

const statusName = (status: number): string => {
    switch (status) {
        case 0:
            return "Success";
        case 1:
            return "Queue";
        case 2:
            return "Failed";
        case 3:
            return "Transcoding";
        default:
            return "";
    }
}

interface ConvertItemTableProps {
    location: string,
    setLocation: (location:string) => void,
    isLoading: boolean,
    setDeletingFile: (file: IConvertItem) => void,
    setPreviewFile: (file: IConvertItem) => void,
}

const ConvertItemTable = (props: ConvertItemTableProps) => {
    const items = useSelector(selectConvertItems);
    const folders = useSelector(selectFolders);

    return (
        <Table
            className="w-full overflow-auto text-left text-white px-[10px]"
            classNames={{
                base: "max-h-[calc(100vh-230px)] min-h-[calc(100vh-230px)] overflow-scroll",
            }}
        >
            <TableHeader className="text-white text-left uppercase bg-[#151515] mt-[70px]">
                <TableColumn className="px-2 py-3 text-nowrap w-[70%] text-[16px]">
                    Name
                </TableColumn>
                <TableColumn className="px-2 py-3 text-nowrap w-[15%] text-[16px] text-center">
                    Size
                </TableColumn>
                <TableColumn className="px-2 py-3 text-nowrap w-[15%] text-[16px] text-center">
                    Status
                </TableColumn>
                <TableColumn className="px-2 py-3 text-nowrap w-[15%] text-[16px] text-center">
                    Action
                </TableColumn>
            </TableHeader>
            <TableBody isLoading={props.isLoading}>
                <>
                    { folders.map((folder, index) => (
                        <TableRow
                            key={`folder-${index}`}
                            onClick={() => props.setLocation([props.location === '/' ? '' : props.location, folder].join('/'))}
                            className={"cursor-pointer"}
                        >
                            <TableCell
                                colSpan={4}
                                className={"flex gap-3 h-[44px] items-center hover:text-[#ffa500]"}
                            >
                                <HiFolder
                                    className={'w-[20px] h-[20px]'}
                                />
                                { folder }
                            </TableCell>
                        </TableRow>
                    ))}
                    { items.map((item, index) => (
                        <TableRow key={index}>
                            <TableCell>
                                { item.filename }
                            </TableCell>
                            <TableCell className={'text-center'}>
                                { formatBytes(item.size) }
                            </TableCell>
                            <TableCell className={'text-center'}>
                                <Chip color={colorByStatus(item.status)} variant="shadow">
                                    { statusName(item.status) }
                                </Chip>
                            </TableCell>
                            <TableCell className={'p-1 flex gap-2 items-center justify-center'}>
                                <button
                                    title={'Preview'}
                                    onClick={() => props.setPreviewFile(item)}
                                    className={'p-2 flex justify-center items-center border text-[#a1a1aa] border-[#a1a1aa] rounded text-lg hover:text-[#ffa500] hover:border-[#ffa500]'}
                                >
                                    <HiEye />
                                </button>
                                <button
                                    title={'Delete'}
                                    onClick={() => props.setDeletingFile(item)}
                                    className={'p-2 flex justify-center items-center border text-[#a1a1aa] border-[#a1a1aa] rounded text-lg hover:text-red-600 hover:border-red-600'}
                                >
                                    <HiMiniTrash />
                                </button>
                            </TableCell>
                        </TableRow>
                    )) }
                </>
            </TableBody>
        </Table>
    );
}

export default ConvertItemTable;