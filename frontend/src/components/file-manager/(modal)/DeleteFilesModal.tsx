import React from "react";
import { HiInformationCircle, HiOutlineXCircle } from "react-icons/hi2";
import { IConvertItem } from "@/redux/file-manager/fileManagerSlice";
import { DeleteConvertItems } from "../../../../wailsjs/go/main/App";

interface createFolderModalProps {
  file: IConvertItem;
  close: () => void;
  reload: () => void;
}

const DeleteFilesModal = ({
  close,
  reload,
  file,
}: createFolderModalProps)=> {
  const handleDelete = async () => {
    if (file.id) {
      await DeleteConvertItems([file.id]);
      reload();
    }
    close();
  };

  return (
    <div
      className="relative z-20 bg-[#1E1E1E]"
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div
        className="fixed inset-0 bg-[#0d0c0c] bg-opacity-50 transition-opacity"
        aria-hidden="true"
      />
      <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
        <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <div
            className="relative transform rounded-lg bg-[#1E1E1E] text-left shadow-xl transition-all sm:my-8"
            style={{ width: "615px" }}
          >
            <div
              className={
                "absolute top-2 right-2 z-10 cursor-pointer text-[#FFFFFF] hover:text-[#891C1CFF]"
              }
              onClick={close}
            >
              <HiOutlineXCircle className={"h-[30px] w-[30px]"} />
            </div>
            <div className="bg-[#1E1E1E] px-4 pb-4 pt-5 sm:p-6 sm:pb-4 relative">
              <div className="sm:flex sm:items-start">
                <div className="mt-3 text-left sm:ml-4 sm:mt-0 sm:text-left">
                  <h3
                    className="text-base font-semibold leading-6 text-[#FFFFFF]"
                    id="modal-title"
                  >
                    Delete File
                  </h3>
                  <div className="w-full h-[1px] bg-[#4D4D4D] absolute left-0 mt-[12px] " />
                  <div className="mt-[40px] w-full min-w-full">
                    <div className="flex flex-col w-full min-w-full justify-between items-center">
                      <p
                       className={'text-lg text-center'}
                      >
                        Are you sure you want to delete the file?
                        <br />
                        <span
                          className={'pt-10 text-sm text-[#ffa500]'}
                        >
                          { file.location }{ file.filename }
                        </span>
                      </p>
                      <p
                       className={'text-blue-400 flex gap-3 justify-center items-center pt-3 text-sm'}
                      >
                        <HiInformationCircle />
                        This will also remove all transcoded files and clear all connections in the scheduler
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-[#1E1E1E] px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
              <button
                type="button"
                className="inline-flex w-[100px] min-w-[100px] justify-center rounded-md border border-red-600 px-4 py-2 text-sm text-red-600 shadow-sm sm:ml-3 sm:w-auto"
                onClick={handleDelete}
              >
                Delete
              </button>
              <button
                type="button"
                className="mt-3 inline-flex w-[100px] min-w-[100px] justify-center rounded-md px-4 py-2 text-sm text-[#FFFFFF] shadow-sm ring-1 ring-inset ring-gray-300 sm:mt-0 sm:w-auto"
                onClick={close}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default DeleteFilesModal;
