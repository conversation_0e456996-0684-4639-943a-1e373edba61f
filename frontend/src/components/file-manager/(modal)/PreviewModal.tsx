import React, { useEffect, useState } from "react";
import { HiOutlineXCircle } from "react-icons/hi2";
import { IConvertItem } from "@/redux/file-manager/fileManagerSlice";
import ReactPlayer from "react-player";
import { ThreeDot } from "react-loading-indicators";

interface PreviewModalProps {
  close: () => void;
  previewFile: IConvertItem;
}

const PreviewModal = ({ close, previewFile }: PreviewModalProps) => {
  const [isPlayerLoading, setIsPlayerLoading] = useState<boolean>(true);
  const [previewURL, setPreviewURL] = useState<string>("");

  useEffect(() => {
    const loadPreview = async () => {
      const url = `http://localhost:34567/data${previewFile.location + previewFile.filename}`;
      setPreviewURL(url);
    }

    loadPreview();
  }, [previewFile]);

  if (!previewURL) {
    return <></>;
  }

  const handlePlayerPlay = () => {
    setIsPlayerLoading(false);
  };
  return (
    <div
      className="relative z-40 bg-[#1E1E1E]"
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div
        className="fixed inset-0 bg-[#0d0c0c] bg-opacity-50 transition-opacity"
        aria-hidden="true"
      />
      <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
        <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <div className="relative transform rounded-lg bg-[#1E1E1E] text-center shadow-xl transition-all sm:my-8">
            {isPlayerLoading && (
              <div className="absolute top-[30px] mx-[16px] my-[16px] inset-0 flex items-center justify-center bg-black bg-opacity-80 z-20">
                <ThreeDot
                  color={"#ffa500"}
                  size="large"
                  text="Loading..."
                  textColor={"#ffa500"}
                />
              </div>
            )}
            <p className={"p-4 text-xl font-bold"}></p>
            <div
              className={
                "absolute top-2 right-2 z-10 cursor-pointer hover:text-[#891C1CFF]"
              }
              onClick={close}
            >
              <HiOutlineXCircle className={"h-[30px] w-[30px]"} />
            </div>

            <div className="bg-[#1E1E1E] p-4 relative">
              <div
                className={"flex flex-col justify-between items-center gap-14"}
              >
                <div
                  className={
                    "flex flex-col justify-between items-center gap-5 w-[720px] h-[405px]"
                  }
                >
                  <ReactPlayer
                    width={'100%'}
                    height={'100%'}
                    key={previewURL}
                    url={previewURL}
                    controls={true}
                    volume={0}
                    muted={true}
                    playing={true}
                    onPlay={handlePlayerPlay}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default PreviewModal;
