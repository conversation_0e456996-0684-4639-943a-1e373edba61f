import { HiOutlinePlusCircle } from "react-icons/hi2";

export default function EmptyPage({handleCreate}: { handleCreate: () => void}) {
    return (
        <div className={'w-full h-[calc(100vh-230px)] flex items-center justify-center flex-col gap-5'}>
            <div>
                No schedule has been created
            </div>
            <div
                className="flex mt-[10px] mb-[20px] w-fit h-[60px] bg-[#151515] border border-[#ffffff] rounded-full hover:bg-[#272727] cursor-pointer items-center pl-[22px] pr-[28px] gap-[8px] float-end"
                onClick={handleCreate}
            >
                <HiOutlinePlusCircle className="text-[#ffffff] w-[24px] h-[24px]"/>
                <span className="text-[#ffffff] text-[14px] select-none">
                    Create new schedule
                </span>
            </div>
        </div>
    );
}
