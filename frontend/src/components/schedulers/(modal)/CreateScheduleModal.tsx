import React, { useRef, useState } from "react";
import { HiOutlineXCircle } from "react-icons/hi2";
import { createSchedule } from "@/redux/scheduler/schedulersSlice";
import { useNavigate } from "react-router-dom";
import { EventsEmit } from "../../../../wailsjs/runtime";

interface CreateScheduleModalProps {
  setIsLoading: (loading: boolean) => void;
  reloadPage: () => void;
  close: () => void;
}

export default function CreateScheduleModal({
  close,
  setIsLoading,
}: CreateScheduleModalProps) {
  const navigate = useNavigate();
  const [name, setName] = useState<string>("");
  const inputRef = useRef<HTMLInputElement>(null);

  const handleCreate = async () => {
    if (!name) {
      if (inputRef.current) {
        inputRef.current.style.borderColor = "red";
      }
      return;
    }

    setIsLoading(true);
    try {
      const response = await createSchedule(name);
      navigate(`/scheduler/editor/${response}`)
    } catch (err) {
      EventsEmit("notification:error", err);
    } finally {
      close();
      setIsLoading(false);
    }
  };

  return (
    <div
      className="relative z-20 bg-[#1E1E1E]"
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div
        className="fixed inset-0 bg-[#0d0c0c] bg-opacity-50 transition-opacity"
        aria-hidden="true"
      />
      <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
        <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <div
            className="relative transform rounded-lg bg-[#1E1E1E] text-left shadow-xl transition-all sm:my-8"
            style={{ width: "615px" }}
          >
            <div
              className={
                "absolute top-2 right-2 z-10 cursor-pointer text-[#FFFFFF] hover:text-[#891C1CFF]"
              }
              onClick={close}
            >
              <HiOutlineXCircle className={"h-[30px] w-[30px]"} />
            </div>
            <div className="bg-[#1E1E1E] px-4 pb-4 pt-5 sm:p-6 sm:pb-4 relative">
              <div className="sm:flex sm:items-start">
                <div className="mt-3 text-left sm:ml-4 sm:mt-0 sm:text-left">
                  <h3
                    className="text-base font-semibold leading-6 text-[#FFFFFF]"
                    id="modal-title"
                  >
                    Create Schedule
                  </h3>
                  <div className="w-full h-[1px] bg-[#4D4D4D] absolute left-0 mt-[12px] " />
                  <div className="mt-[40px] w-full min-w-full">
                    <div className="flex w-full min-w-full h-[40px] justify-between items-center">
                      <div className="text-[#FFFFFF] text-[16px] w-[160px]">
                        Name
                      </div>
                      <input
                        ref={inputRef}
                        className={
                          "w-[390px] min-w-[390px] bg-[#151515A3] p-3 h-[40px] text-[#FFFFFF] focus-visible:outline-0 hover:border-[#ffa500] focus:border-[#ffa500] border border-[#4D4D4D] rounded-md"
                        }
                        placeholder="Scheduler Name"
                        value={name}
                        onChange={(e) => {
                          setName(e.target.value);
                          if (e.target.value) {
                            e.target.style.borderColor = "#4D4D4D";
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-[#1E1E1E] px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
              <button
                type="button"
                className="inline-flex w-[100px] min-w-[100px] justify-center rounded-md border border-[#ffa500] px-4 py-2 text-sm text-[#ffa500] shadow-sm sm:ml-3 sm:w-auto"
                onClick={handleCreate}
              >
                Create
              </button>
              <button
                type="button"
                className="mt-3 inline-flex w-[100px] min-w-[100px] justify-center rounded-md px-4 py-2 text-sm text-[#FFFFFF] shadow-sm ring-1 ring-inset ring-gray-300 sm:mt-0 sm:w-auto"
                onClick={close}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
