import React, { useEffect } from "react";
import { HiOutlineXCircle } from "react-icons/hi2";
import Creatable from "react-select/creatable";
import { generateFileName, getHLSLink } from "@/lib/scheduler";
import { OnChangeValue } from "react-select";
import {useDispatch, useSelector} from "react-redux";
import { document } from "../../../../wailsjs/go/models";
import Schedule = document.Schedule;
import { createChannels, getChannels, IChannel, selectChannels, setChannels } from "@/redux/channel/channelSlice";

interface CopyHLSScheduleModalProps {
  copyLink: (link: string | null) => void;
  schedule: Schedule;
  close: () => void;
  setIsLoading: (value: boolean) => void;
}

export default function CopyHLSScheduleModal({
  close,
  copyLink,
  schedule,
  setIsLoading,
}: CopyHLSScheduleModalProps) {
  const dispatch = useDispatch();
  let userChannels = useSelector(selectChannels);

  const options = userChannels.map((channel) => {
    return {
      label: channel.title,
      value: channel.slug,
    };
  });

  useEffect(() => {
    const fetchChannels = async () => {
      let schedulerUserChannels = await getChannels();

      let uniqueChannels = new Map(
        [...userChannels, ...schedulerUserChannels, ...schedule.channels].map(
          (channel) => [channel.slug.toString(), { ...channel }]
        )
      );

      dispatch(setChannels(Array.from(uniqueChannels.values())));
    };

    fetchChannels();
  }, [userChannels]);

  const handleCopy = async (channel: OnChangeValue<any, false>) => {
    if (!channel) return;

    let slug = channel["value"];
    if (channel["__isNew__"]) {
      let slug = generateFileName(channel["label"]);
      setIsLoading(true);
      await addNewUserChannel({
        _id: null,
        title: channel["label"],
        slug: slug,
      });
    }
    setIsLoading(false);
    const link = getHLSLink(schedule, slug);
    copyLink(link);
    close();
  };

  const addNewUserChannel = async (newChannel: IChannel) => {
    let channel = await createChannels([newChannel]);
    if (channel) {
      let channels = [...userChannels, ...channel];
      let uniqueChannels = new Map(
        channels.map((channel) => [channel.slug.toString(), { ...channel }])
      );

      dispatch(setChannels(Array.from(uniqueChannels.values())));
    }
  };

  return (
    <div
      className="relative z-30 bg-[#1E1E1E]"
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div
        className="fixed inset-0 bg-[#0d0c0c] bg-opacity-50 transition-opacity"
        aria-hidden="true"
      />
      <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
        <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <div
            className="relative transform rounded-lg bg-[#1E1E1E] text-left shadow-xl transition-all sm:my-8"
            style={{ width: "615px" }}
          >
            <div
              className={
                "absolute top-2 right-2 z-10 cursor-pointer text-[#FFFFFF] hover:text-[#891C1CFF]"
              }
              onClick={close}
            >
              <HiOutlineXCircle className={"h-[30px] w-[30px]"} />
            </div>
            <div className="bg-[#1E1E1E] px-4 pb-4 pt-5 sm:p-6 sm:pb-4 relative">
              <div className="sm:flex sm:items-start">
                <div className="mt-3 w-full text-left sm:ml-4 sm:mt-0 sm:text-left">
                  <h3
                    className="text-base font-semibold leading-6 text-[#FFFFFF]"
                    id="modal-title"
                  >
                    HLS
                  </h3>
                  <div className="w-full h-[1px] bg-[#4D4D4D] absolute left-0 mt-[12px] " />
                  <div className="mt-[40px] w-full min-w-full">
                    <div className="flex gap-[4px] w-full min-w-full h-[40px] justify-between items-center">
                      <Creatable
                        placeholder={"Select a channel or create a new one..."}
                        options={options}
                        onChange={(value) => handleCopy(value)}
                        className="react-select-container w-full"
                        classNamePrefix="react-select"
                      />
                      <span>.m3u8</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-[#1E1E1E] px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
              <button
                type="button"
                className="mt-3 inline-flex w-[100px] min-w-[100px] justify-center rounded-md px-4 py-2 text-sm text-[#FFFFFF] shadow-sm ring-1 ring-inset ring-gray-300 sm:mt-0 sm:w-auto"
                onClick={close}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
