import { HiOutlinePlusCircle } from "react-icons/hi2";
import { selectSchedulers } from "@/redux/scheduler/schedulersSlice";
import { useState } from "react";
import Pagination from "./Pagination";
import {
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from "@heroui/react";
import CopyHLSScheduleModal from "../(modal)/CopyHLSScheduleModal";
import { getEPGLink } from "@/lib/scheduler";
import { HiClipboardCopy } from "react-icons/hi";
import moment from "moment";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";
import { ClipboardSetText, EventsEmit } from "../../../../wailsjs/runtime";
import { document } from "../../../../wailsjs/go/models";
import Schedule = document.Schedule;

interface TableProps {
  handleCreate: () => void;
  handleDelete: (id: number|null) => void;
}

export default function SchedulerTable({
  handleDelete,
  handleCreate,
}: TableProps) {
  const [activeSchedule, setActiveSchedule] = useState<Schedule | null>(null);
  const schedulers = useSelector(selectSchedulers);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const copyLink = (link: string | null) => {
    if (!link) {
      return;
    }

    ClipboardSetText(link).then(() =>
        EventsEmit("notification:success", "Successfully copied")
    ).catch(error =>
        EventsEmit("notification:error", error)
    )
  };

  return (
    <>
      <div
        className="flex mt-[10px] mb-[20px] w-fit h-[40px] bg-gradient-to-r from-[#006fee] to-[#4A90E2] rounded-full cursor-pointer items-center pl-[12px] pr-[18px] gap-[8px] float-start"
        onClick={handleCreate}
      >
        <HiOutlinePlusCircle className="text-[#ffffff] w-[24px] h-[24px]" />
        <span className="text-[#ffffff] text-[14px] select-none">
          Create new schedule
        </span>
      </div>

      <Table
        className="w-full overflow-auto text-left text-white px-[10px]"
        classNames={{
          base: "max-h-[calc(100vh-230px)] min-h-[calc(100vh-230px)] overflow-scroll",
        }}
      >
        <TableHeader className="text-white text-left uppercase bg-[#151515] mt-[70px]">
          <TableColumn className="px-2 py-3 text-nowrap w-[30%] text-[16px]">
            Name
          </TableColumn>
          <TableColumn className="px-2 py-3 text-nowrap w-[15%] text-[16px] text-center">
            Last updated
          </TableColumn>
          <TableColumn className="px-2 py-3 text-nowrap w-[10%] text-[16px] text-center">
            Links
          </TableColumn>
          <TableColumn className="px-2 py-3 text-nowrap w-[10%] text-[16px] text-center">
            Action
          </TableColumn>
        </TableHeader>
        <TableBody isLoading={isLoading}>
          {schedulers.map(
            (item, index) => (
              item && (
                  <TableRow key={index}>
                    <TableCell className="px-2 py-2 w-[30%] text-nowrap">
                      {item.name}
                    </TableCell>
                    <TableCell className="px-2 py-2 w-[15%] text-nowrap  text-center">
                      {moment(item.updated_at).format("L LT")}
                    </TableCell>
                    <TableCell className="px-2 py-2 w-[10%] text-nowrap">
                      <div className="flex gap-[4px] items-center justify-center">
                        {/*{item.short_id && activeSchedule?.short_id === item.short_id && (*/}
                        {/*    <CopyHLSScheduleModal*/}
                        {/*        close={() => setActiveSchedule(null)}*/}
                        {/*        setIsLoading={setIsLoading}*/}
                        {/*        schedule={item}*/}
                        {/*        copyLink={copyLink}*/}
                        {/*    />*/}
                        {/*)}*/}
                        <button
                            className="w-[91px] bg-[#151515A3] px-[10px] text-zinc-50 flex justify-center items-center h-[30px] gap-3 rounded-md hover:border-[#ffa500] focus-visible:outline-0 border border-[#4D4D4D]"
                            onClick={async () => copyLink(await getEPGLink(item))}
                        >
                          <HiClipboardCopy/>
                          EPG
                        </button>
                        {/*<button*/}
                        {/*    className="w-[91px] bg-[#151515A3] px-[10px] text-zinc-50 flex justify-center items-center h-[30px] gap-3 rounded-md hover:border-[#ffa500] focus-visible:outline-0 border border-[#4D4D4D]"*/}
                        {/*    onClick={() => setActiveSchedule(item)}*/}
                        {/*>*/}
                        {/*  <HiClipboardCopy/>*/}
                        {/*  HLS*/}
                        {/*</button>*/}
                      </div>
                    </TableCell>
                    <TableCell className="px-4 py-2 flex h-[80px] items-center justify-center gap-[10px]">
                      <Link
                          className="text-[15px] text-[#ffa500] text-nowrap cursor-pointer"
                          to={`/scheduler/editor/${item.id}`}
                      >
                        Edit
                      </Link>
                      <div
                          onClick={() => handleDelete(item.id)}
                          className="text-[15px] text-red-600 dark:text-red-500 ms-3 cursor-pointer"
                      >
                        Delete
                      </div>
                    </TableCell>
                  </TableRow>
              )
            )
          )}
        </TableBody>
      </Table>
      <Pagination />
    </>
  );
}
