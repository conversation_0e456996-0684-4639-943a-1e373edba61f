import {
  selectPagination,
  setLimit,
  setPage,
} from "@/redux/scheduler/schedulersSlice";
import {useDispatch, useSelector} from "react-redux";

export default function Pagination() {
  const dispatch = useDispatch();
  const pagination = useSelector(selectPagination);

  const setCurrentPage = (page: number) => {
    dispatch(setPage(page));
  };

  const setItemsPerPage = (limit: number) => {
    dispatch(setLimit(limit));
  };

  return (
    <div className="flex justify-center items-center space-x-2 mt-4 text-[12px] text-white">
      <button
        onClick={() => setCurrentPage(Math.max(pagination.page - 1, 1))}
        disabled={pagination.page === 1}
        className="px-3 py-1 rounded bg-[#151515] disabled:opacity-50"
      >
        Previous
      </button>

      <span className="px-4">
        Page {pagination.page} of {pagination.total_pages} (
        {pagination.total_items} items)
      </span>

      <button
        onClick={() =>
          setCurrentPage(Math.min(pagination.page + 1, pagination.total_pages))
        }
        disabled={pagination.page === pagination.total_pages}
        className="px-3 py-1 rounded bg-[#151515] disabled:opacity-50"
      >
        Next
      </button>

      <select
        value={pagination.limit}
        onChange={(e) => {
          setItemsPerPage(Number(e.target.value));
          setCurrentPage(1);
        }}
        className="px-3 py-1 rounded bg-[#151515] text-white"
      >
        <option value={20}>20</option>
        <option value={50}>50</option>
        <option value={100}>100</option>
        <option value={200}>200</option>
      </select>
    </div>
  );
}
