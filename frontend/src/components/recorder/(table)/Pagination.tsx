import {
  selectPagination,
  setLimit,
  setPage,
  selectRecorders
} from "@/redux/recorder/recordersSlice";
import {useDispatch, useSelector} from "react-redux";

export default function Pagination() {
  const dispatch = useDispatch();
  const pagination = useSelector(selectPagination);
  const recorders = useSelector(selectRecorders);
  const hasRecorders = recorders.length > 0;
  const totalPages = pagination.total_pages || 1;

  const setCurrentPage = (page: number) => {
    dispatch(setPage(page));
  };

  const setItemsPerPage = (limit: number) => {
    dispatch(setLimit(limit));
  };

  return (
    <div className="flex flex-wrap justify-center items-center gap-2 mt-4 text-[11px] text-white">
      <button
        onClick={() => setCurrentPage(Math.max(pagination.page - 1, 1))}
        disabled={pagination.page === 1 || !hasRecorders}
        className="px-2 py-1 rounded bg-[#151515] disabled:opacity-50"
        title="Previous page"
      >
        <span className="hidden sm:inline">Previous</span>
        <span className="sm:hidden">←</span>
      </button>

      <span className="px-2 text-center">
        <span className="hidden sm:inline">Page </span>
        {pagination.page} / {totalPages}
        <span className="hidden sm:inline"> ({pagination.total_items})</span>
      </span>

      <button
        onClick={() =>
          setCurrentPage(Math.min(pagination.page + 1, totalPages))
        }
        disabled={pagination.page === totalPages || !hasRecorders}
        className="px-2 py-1 rounded bg-[#151515] disabled:opacity-50"
        title="Next page"
      >
        <span className="hidden sm:inline">Next</span>
        <span className="sm:hidden">→</span>
      </button>

      <select
        value={pagination.limit}
        onChange={(e) => {
          setItemsPerPage(Number(e.target.value));
          setCurrentPage(1);
        }}
        disabled={!hasRecorders}
        className="px-2 py-1 rounded bg-[#151515] text-white"
        title="Items per page"
      >
        <option value={20}>20</option>
        <option value={50}>50</option>
        <option value={100}>100</option>
        <option value={200}>200</option>
      </select>
    </div>
  );
}