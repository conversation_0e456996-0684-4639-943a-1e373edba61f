import { useEffect, useState } from 'react';
import { RecorderInput } from '../../../types/recorder';
import { HiChevronDown, HiChevronUp } from 'react-icons/hi2';
import { GetRtpUrls } from '../../../../wailsjs/go/main/App';
import { document } from '../../../../wailsjs/go/models';

// Define the RtpUrl interface
interface RtpUrl {
    id: number;
    url: string;
    created_at?: string;
    updated_at?: string;
}

interface CreateRecorderModalProps {
    close: () => void;
    onSubmit: (input: RecorderInput) => void;
    initialData?: RecorderInput;
}

// Helper functions for time conversion
const secondsToHHMMSS = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    return [hours, minutes, secs]
        .map(val => val.toString().padStart(2, '0'))
        .join(':');
};

const hhmmssToSeconds = (hhmmss: string): number => {
    // Simple validation to ensure the format is correct
    const timePattern = /^(\d{2}):(\d{2}):(\d{2})$/;
    if (!timePattern.test(hhmmss)) {
        return 0; // Return 0 for invalid format
    }

    const [hours, minutes, seconds] = hhmmss.split(':').map(Number);
    return (hours * 3600) + (minutes * 60) + seconds;
};

export const CreateRecorderModal = ({ close, onSubmit, initialData }: CreateRecorderModalProps) => {
    const [formData, setFormData] = useState<RecorderInput>({
        name: '',
        rtp_url: '',
        duration: '0', // Store as string for compatibility with RecorderInput
        vcodec: 'h264',
        acodec: 'ac3_downmix',
        resolution: '1920x1080i',
        fps: 29.97,
        sample_rate: 48000,
        vbitrate: 6000,
        abitrate: 192,
        max_vbitrate: 10000
    });

    const [durationDisplay, setDurationDisplay] = useState('00:00:00');
    const [durationError, setDurationError] = useState('');
    const [rtpUrlError, setRtpUrlError] = useState('');
    const [bitrateError, setBitrateError] = useState('');
    const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);
    const [savedRtpUrls, setSavedRtpUrls] = useState<{id: number, url: string}[]>([]);
    const [showRtpUrlDropdown, setShowRtpUrlDropdown] = useState(false);
    const [filteredRtpUrls, setFilteredRtpUrls] = useState<{id: number, url: string}[]>([]);

    // Load saved RTP URLs when the component mounts
    useEffect(() => {
        const loadRtpUrls = async () => {
            try {
                // Fetch RTP URLs from the database
                const urls = await GetRtpUrls();
                if (urls && urls.length > 0) {
                    const rtpUrls = urls.map((url: RtpUrl) => ({ id: url.id, url: url.url }));
                    setSavedRtpUrls(rtpUrls);
                    setFilteredRtpUrls(rtpUrls); // Initialize filtered URLs with all URLs
                } else {
                    console.log('No RTP URLs found in the database');
                    // Set some default URLs if none are found
                    const defaultUrls = [{ id: 0, url: 'rtp://*********:5000' }];
                    setSavedRtpUrls(defaultUrls);
                    setFilteredRtpUrls(defaultUrls);
                }
            } catch (error) {
                console.error('Failed to load RTP URLs:', error);
                // Set some default URLs if there's an error
                const defaultUrls = [{ id: 0, url: 'rtp://*********:5000' }];
                setSavedRtpUrls(defaultUrls);
                setFilteredRtpUrls(defaultUrls);
            }
        };

        loadRtpUrls();
    }, []);

    // Filter RTP URLs based on input value
    useEffect(() => {
        if (formData.rtp_url) {
            // Filter URLs that include the input value (case-insensitive)
            const filtered = savedRtpUrls.filter(url =>
                url.url.toLowerCase().includes(formData.rtp_url.toLowerCase())
            );
            setFilteredRtpUrls(filtered);
        } else {
            // If input is empty, show all saved URLs
            setFilteredRtpUrls(savedRtpUrls);
        }
    }, [formData.rtp_url, savedRtpUrls]);

    // Store a copy of the initial data to prevent updates while editing
    const [initialDataCopy, setInitialDataCopy] = useState<RecorderInput | undefined>(undefined);

    // Only set the form data once when the modal opens
    useEffect(() => {
        if (initialData && !initialDataCopy) {
            // Make a deep copy of the initial data
            const dataCopy = JSON.parse(JSON.stringify(initialData));
            setInitialDataCopy(dataCopy);
            setFormData(dataCopy);

            // Check if video bitrate exceeds max video bitrate in the initial data
            if (dataCopy.vbitrate > dataCopy.max_vbitrate) {
                setBitrateError('Video Bitrate cannot exceed Max Video Bitrate');
                setShowAdvancedSettings(true); // Show advanced settings to make the error visible
            } else {
                setBitrateError('');
            }

            // Convert the duration seconds to HH:MM:SS for display
            try {
                const seconds = parseInt(dataCopy.duration);
                if (!isNaN(seconds)) {
                    setDurationDisplay(secondsToHHMMSS(seconds));
                } else {
                    // If it's already in HH:MM:SS format, check if it's valid
                    const timePattern = /^(\d{2}):(\d{2}):(\d{2})$/;
                    if (timePattern.test(dataCopy.duration)) {
                        setDurationDisplay(dataCopy.duration);
                    } else {
                        setDurationDisplay('00:00:00');
                    }
                }
            } catch (error) {
                setDurationDisplay('00:00:00');
            }
        }
    }, [initialData, initialDataCopy]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        // Check for existing validation errors
        if (durationError || rtpUrlError || bitrateError) {
            return; // Don't submit if there's a validation error
        }

        // Additional validation for duration at submission time
        const durationSeconds = parseInt(formData.duration);
        if (durationSeconds === 0) {
            setDurationError('Duration cannot be zero (00:00:00)');
            return;
        }

        // Additional validation for RTP URL at submission time
        if (!formData.rtp_url) {
            setRtpUrlError('RTP URL is required');
            return;
        }

        // Validate RTP URL format if not already validated
        // Allow for query parameters after the basic URL
        const rtpBasePattern = /^rtp:\/\/(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}):(\d{1,5})/;
        if (!rtpBasePattern.test(formData.rtp_url)) {
            setRtpUrlError('RTP URL must start with format: rtp://IP:PORT (e.g., rtp://*********:5000)');
            return;
        }

        // Validate that video bitrate doesn't exceed max video bitrate
        if (formData.vbitrate > formData.max_vbitrate) {
            setBitrateError('Video Bitrate cannot exceed Max Video Bitrate');
            setShowAdvancedSettings(true); // Show advanced settings to make the error visible
            return;
        }

        // Reset the initialDataCopy before submitting
        setInitialDataCopy(undefined);
        onSubmit(formData);
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;

        if (name === 'duration') {
            setDurationDisplay(value);

            // Validate HH:MM:SS format
            const timePattern = /^(\d{2}):(\d{2}):(\d{2})$/;
            if (!timePattern.test(value)) {
                setDurationError('Duration must be in HH:MM:SS format');
                return;
            }

            // Convert HH:MM:SS to seconds for storage, but keep as string
            const seconds = hhmmssToSeconds(value);

            // Validate that duration is not zero
            if (seconds === 0) {
                setDurationError('Duration cannot be zero (00:00:00)');
                return;
            } else {
                setDurationError('');
            }

            setFormData(prev => ({
                ...prev,
                duration: seconds.toString()
            }));
        } else if (name === 'rtp_url') {
            // Show dropdown when typing in the RTP URL field
            setShowRtpUrlDropdown(true);

            // Validate RTP URL format only if it's a complete URL
            if (value && value.includes('://') && value.includes(':')) {
                const rtpPattern = /^rtp:\/\/(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}):(\d{1,5})$/;
                if (!rtpPattern.test(value)) {
                    setRtpUrlError('RTP URL must be in format: rtp://IP:PORT (e.g., rtp://*********:5000)');
                } else {
                    // Further validate IP address and port
                    const match = value.match(rtpPattern);
                    if (match) {
                        const [, ip, port] = match;

                        // Validate IP address parts
                        const ipParts = ip.split('.').map(Number);
                        const validIp = ipParts.every(part => part >= 0 && part <= 255);

                        // Validate port number
                        const portNum = parseInt(port);
                        const validPort = portNum > 0 && portNum <= 65535;

                        if (!validIp) {
                            setRtpUrlError('Invalid IP address. Each part must be between 0-255.');
                        } else if (!validPort) {
                            setRtpUrlError('Invalid port number. Must be between 1-65535.');
                        } else {
                            setRtpUrlError('');
                        }
                    }
                }
            } else {
                // Clear error if the user is still typing
                setRtpUrlError('');
            }

            setFormData(prev => ({
                ...prev,
                rtp_url: value
            }));
        } else {
            // Update the form data
            const updatedFormData = {
                ...formData,
                [name]: name === 'fps'
                    ? parseFloat(value)
                    : name.includes('rate') || name.includes('bitrate')
                        ? parseInt(value)
                        : value
            };

            setFormData(updatedFormData);

            // Check bitrate validation when either vbitrate or max_vbitrate changes
            if (name === 'vbitrate' || name === 'max_vbitrate') {
                const vbitrate = name === 'vbitrate' ? parseInt(value) : formData.vbitrate;
                const maxVbitrate = name === 'max_vbitrate' ? parseInt(value) : formData.max_vbitrate;

                if (vbitrate > maxVbitrate) {
                    setBitrateError('Video Bitrate cannot exceed Max Video Bitrate');
                } else {
                    setBitrateError('');
                }
            }
        }
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-[#1E1E1E] rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
                <h2 className="text-xl font-bold text-white mb-4">{initialData ? 'Edit Input Feed' : 'Create New Input Feed'}</h2>
                <form onSubmit={handleSubmit} className="space-y-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">Input Feed Name</label>
                        <input
                            className="w-full px-3 py-2 bg-[#2D2D2D] text-white rounded-md border border-[#3D3D3D] focus:outline-none focus:ring-2 focus:ring-[#ffa500] focus:border-transparent"
                            name="name"
                            value={formData.name}
                            onChange={handleChange}
                            placeholder="Recorder Name"
                            required
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">RTP URL</label>
                        <div className="relative">
                            <input
                                className={`w-full px-3 py-2 bg-[#2D2D2D] text-white rounded-md border ${rtpUrlError ? 'border-red-500' : 'border-[#3D3D3D]'} focus:outline-none focus:ring-2 focus:ring-[#ffa500] focus:border-transparent`}
                                name="rtp_url"
                                value={formData.rtp_url}
                                onChange={handleChange}
                                placeholder="rtp://*********:5000"
                                pattern="rtp:\/\/(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}):(\d{1,5})"
                                title="Format: rtp://IP:PORT (e.g., rtp://*********:5000)"
                                required
                                onFocus={() => setShowRtpUrlDropdown(true)}
                                // Use a delay to allow clicking on dropdown items
                                onBlur={() => setTimeout(() => setShowRtpUrlDropdown(false), 200)}
                            />
                            <button
                                type="button"
                                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                                onClick={() => setShowRtpUrlDropdown(!showRtpUrlDropdown)}
                            >
                                {showRtpUrlDropdown ? <HiChevronUp className="w-5 h-5" /> : <HiChevronDown className="w-5 h-5" />}
                            </button>

                            {showRtpUrlDropdown && (
                                <div className="absolute z-10 mt-1 w-full bg-[#2D2D2D] border border-[#3D3D3D] rounded-md shadow-lg max-h-60 overflow-auto">
                                    {filteredRtpUrls.length > 0 ? (
                                        filteredRtpUrls.map(url => (
                                            <div
                                                key={url.id}
                                                className="px-3 py-2 hover:bg-[#3D3D3D] cursor-pointer text-white"
                                                onClick={() => {
                                                    setFormData(prev => ({
                                                        ...prev,
                                                        rtp_url: url.url
                                                    }));
                                                    setRtpUrlError('');
                                                    setShowRtpUrlDropdown(false);
                                                }}
                                            >
                                                {url.url}
                                            </div>
                                        ))
                                    ) : (
                                        <div className="px-3 py-2 text-gray-400 italic">
                                            No matching RTP URLs found
                                        </div>
                                    )}
                                </div>
                            )}
                        </div>
                        {rtpUrlError && (
                            <p className="text-red-500 text-xs mt-1">{rtpUrlError}</p>
                        )}
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-300 mb-1">Duration (HH:MM:SS)</label>
                        <input
                            className={`w-full px-3 py-2 bg-[#2D2D2D] text-white rounded-md border ${durationError ? 'border-red-500' : 'border-[#3D3D3D]'} focus:outline-none focus:ring-2 focus:ring-[#ffa500] focus:border-transparent`}
                            name="duration"
                            value={durationDisplay}
                            onChange={handleChange}
                            placeholder="Duration (HH:MM:SS)"
                            pattern="[0-9]{2}:[0-9]{2}:[0-9]{2}"
                            title="Format: HH:MM:SS"
                            required
                        />
                        {durationError && (
                            <p className="text-red-500 text-xs mt-1">{durationError}</p>
                        )}
                    </div>

                    <div
                        onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}
                        className="flex items-center gap-1 mt-4 mb-2 text-sm text-gray-300 cursor-pointer hover:text-white"
                    >
                        {showAdvancedSettings ? <HiChevronUp className="w-4 h-4" /> : <HiChevronDown className="w-4 h-4" />}
                        <span>Advanced Settings</span>
                    </div>

                    {showAdvancedSettings && (
                        <div className="space-y-4 pt-2 border-t border-[#3D3D3D]">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-300 mb-1">Video Codec</label>
                                    <select
                                        className="w-full px-2 py-1 bg-[#2D2D2D] text-white rounded-md border border-[#3D3D3D] focus:outline-none focus:ring-2 focus:ring-[#ffa500] focus:border-transparent"
                                        name="vcodec"
                                        value={formData.vcodec}
                                        onChange={handleChange}
                                        required
                                    >
                                        <option value="auto">Auto</option>
                                        <option value="h265">H.265 (HEVC)</option>
                                        <option value="h264">H.264 (AVC)</option>
                                        <option value="mpeg2">MPEG-2</option>
                                    </select>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-300 mb-1">Audio Codec</label>
                                    <select
                                        className="w-full px-2 py-1 bg-[#2D2D2D] text-white rounded-md border border-[#3D3D3D] focus:outline-none focus:ring-2 focus:ring-[#ffa500] focus:border-transparent"
                                        name="acodec"
                                        value={formData.acodec}
                                        onChange={handleChange}
                                        required
                                    >
                                        <option value="auto">Auto</option>
                                        <option value="ac3_passthrough">AC3 Passthrough</option>
                                        <option value="ac3_downmix">AC3 Downmix</option>
                                        <option value="aac_downmix">AAC Downmix</option>
                                        <option value="mpeg1l2_downmix">MPEG1L2 Downmix</option>
                                    </select>
                                </div>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-300 mb-1">Resolution</label>
                                <select
                                    className="w-full px-2 py-1 bg-[#2D2D2D] text-white rounded-md border border-[#3D3D3D] focus:outline-none focus:ring-2 focus:ring-[#ffa500] focus:border-transparent"
                                    name="resolution"
                                    value={formData.resolution}
                                    onChange={handleChange}
                                    required
                                >
                                    <option value="auto">Auto</option>
                                    <option value="1920x1080p">1920x1080p</option>
                                    <option value="1920x1080i">1920x1080i</option>
                                    <option value="1280x720p">1280x720p</option>
                                    <option value="720x480p">720x480p</option>
                                    <option value="720x480i">720x480i</option>
                                </select>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-300 mb-1">FPS</label>
                                    <select
                                        className="w-full px-2 py-1 bg-[#2D2D2D] text-white rounded-md border border-[#3D3D3D] focus:outline-none focus:ring-2 focus:ring-[#ffa500] focus:border-transparent"
                                        name="fps"
                                        value={formData.fps}
                                        onChange={handleChange}
                                        required
                                    >
                                        <option value="auto">Auto</option>
                                        <option value="60">60</option>
                                        <option value="59.94">59.94</option>
                                        <option value="30">30</option>
                                        <option value="29.97">29.97</option>
                                    </select>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-300 mb-1">Sample Rate</label>
                                    <select
                                        className="w-full px-2 py-1 bg-[#2D2D2D] text-white rounded-md border border-[#3D3D3D] focus:outline-none focus:ring-2 focus:ring-[#ffa500] focus:border-transparent"
                                        name="sample_rate"
                                        value={formData.sample_rate}
                                        onChange={handleChange}
                                        required
                                    >
                                        <option value="48000">48000 Hz (Broadcast)</option>
                                        <option value="44100">44100 Hz</option>
                                        <option value="32000">32000 Hz</option>
                                    </select>
                                </div>
                            </div>

                            <div className="grid grid-cols-3 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-300 mb-1">Video Bitrate (kbps)</label>
                                    <input
                                        type="number"
                                        className={`w-full px-3 py-2 bg-[#2D2D2D] text-white rounded-md border ${bitrateError ? 'border-red-500' : 'border-[#3D3D3D]'} focus:outline-none focus:ring-2 focus:ring-[#ffa500] focus:border-transparent`}
                                        name="vbitrate"
                                        value={formData.vbitrate}
                                        onChange={handleChange}
                                        min="1"
                                        placeholder="Enter video bitrate in kbps"
                                        required
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-300 mb-1">Audio Bitrate (kbps)</label>
                                    <select
                                        className="w-full px-2 py-1 bg-[#2D2D2D] text-white rounded-md border border-[#3D3D3D] focus:outline-none focus:ring-2 focus:ring-[#ffa500] focus:border-transparent"
                                        name="abitrate"
                                        value={formData.abitrate}
                                        onChange={handleChange}
                                        required
                                    >
                                        <option value="384">384</option>
                                        <option value="256">256</option>
                                        <option value="192">192</option>
                                        <option value="128">128</option>
                                    </select>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-300 mb-1">Max Video Bitrate (kbps)</label>
                                    <input
                                        type="number"
                                        className={`w-full px-3 py-2 bg-[#2D2D2D] text-white rounded-md border ${bitrateError ? 'border-red-500' : 'border-[#3D3D3D]'} focus:outline-none focus:ring-2 focus:ring-[#ffa500] focus:border-transparent`}
                                        name="max_vbitrate"
                                        value={formData.max_vbitrate}
                                        onChange={handleChange}
                                        min="1"
                                        placeholder="Enter max video bitrate in kbps"
                                        required
                                    />
                                </div>
                            </div>
                            {bitrateError && (
                                <p className="text-red-500 text-xs mt-1">{bitrateError}</p>
                            )}
                        </div>
                    )}

                    <div className="flex justify-end space-x-3 mt-6">
                        <button
                            type="button"
                            onClick={() => {
                                // Reset the initialDataCopy when closing the modal
                                setInitialDataCopy(undefined);
                                close();
                            }}
                            className="px-4 py-2 bg-[#3D3D3D] text-white rounded-md hover:bg-[#4D4D4D] transition-colors"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            className="px-4 py-2 bg-[#ffa500] text-white rounded-md hover:bg-[#ff8c00] transition-colors"
                        >
                            {initialData ? 'Update' : 'Create'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};
