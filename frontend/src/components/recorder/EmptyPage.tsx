import { HiOutlinePlusCircle } from "react-icons/hi2";
import RecorderIcon from "@/assets/icons/RecorderIcon";

export default function EmptyPage({handleCreate}: { handleCreate: () => void}) {
    return (
        <div className="w-full h-[calc(100vh-230px)] flex items-center justify-center flex-col gap-6">
            <div className="flex flex-col items-center gap-4">
                <div className="w-16 h-16 text-gray-400">
                    <RecorderIcon />
                </div>
                <div className="text-center">
                    <h3 className="text-xl font-semibold text-white mb-2">No Input Feeds</h3>
                    <p className="text-gray-400">Create your first Input Feed to start capturing media</p>
                </div>
            </div>
            <button
                className="flex h-[40px] bg-gradient-to-r from-[#006fee] to-[#4A90E2] rounded-full cursor-pointer items-center px-4 gap-2 hover:opacity-90 transition-opacity"
                onClick={handleCreate}
            >
                <HiOutlinePlusCircle className="text-white w-5 h-5"/>
                <span className="text-white text-sm font-medium">
                    Create new Input Feed
                </span>
            </button>
        </div>
    );
}
