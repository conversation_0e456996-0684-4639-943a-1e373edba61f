import { useEffect, useState } from 'react';
import { createRecorder, deleteRecorder, getRecorders, startRecorder, stopRecorder } from '../../services/recorder';
import { Recorder, RecorderInput } from '../../types/recorder';
import { CreateRecorderModal } from './(modal)/CreateRecorderModal';

export const RecorderComponent = () => {
    const [recorders, setRecorders] = useState<Recorder[]>([]);
    const [isShowCreateModal, setIsShowCreateModal] = useState(false);

    useEffect(() => {
        loadRecorders();
    }, []);

    const loadRecorders = async () => {
        const data = await getRecorders();
        // Convert backend model to frontend model
        const frontendRecorders = data.map(recorder => {
            // Use type assertion to handle property name differences
            const backendRecorder = recorder as any;
            return {
                id: backendRecorder.id,
                name: backendRecorder.name,
                input: backendRecorder.input,
                duration: backendRecorder.duration,
                status: backendRecorder.status as 'running' | 'stopped',
                vcodec: backendRecorder.VCodec,
                acodec: backendRecorder.ACodec,
                resolution: backendRecorder.resolution,
                fps: backendRecorder.FPS,
                sample_rate: backendRecorder.sampleRate,
                vbitrate: backendRecorder.VBitrate,
                abitrate: backendRecorder.ABitrate,
                max_vbitrate: backendRecorder.MaxVBitrate
            };
        });
        setRecorders(frontendRecorders.map(r => ({
            id: r.id,
            name: r.name,
            input: r.input,
            rtp_url_id: 0, // Default value since we don't have this in the frontend model yet
            duration: r.duration,
            status: r.status,
            VCodec: r.vcodec,
            ACodec: r.acodec,
            resolution: r.resolution,
            FPS: r.fps,
            sampleRate: r.sample_rate,
            VBitrate: r.vbitrate,
            ABitrate: r.abitrate,
            MaxVBitrate: r.max_vbitrate
        })));
    };

    const handleCreateRecorder = async (input: RecorderInput) => {
        await createRecorder(input);
        await loadRecorders();
        setIsShowCreateModal(false);
    };

    const handleStartRecorder = async (id: number) => {
        await startRecorder(id);
        await loadRecorders();
    };

    const handleStopRecorder = async (id: number) => {
        await stopRecorder(id);
        await loadRecorders();
    };

    const handleDeleteRecorder = async (id: number) => {
        await deleteRecorder(id);
        await loadRecorders();
    };

    return (
        <div className="w-full">
            <button onClick={() => setIsShowCreateModal(true)}>Create New Recorder</button>

            {isShowCreateModal && (
                <CreateRecorderModal
                    close={() => setIsShowCreateModal(false)}
                    onSubmit={handleCreateRecorder}
                />
            )}

            <div>
                {recorders.map((recorder) => (
                    <div key={recorder.id}>
                        <h3>{recorder.name}</h3>
                        <p>Status: {recorder.status}</p>
                        <button
                            onClick={() => recorder.status === 'stopped'
                                ? handleStartRecorder(recorder.id)
                                : handleStopRecorder(recorder.id)}
                        >
                            {recorder.status === 'stopped' ? 'Start' : 'Stop'}
                        </button>
                        <button onClick={() => handleDeleteRecorder(recorder.id)}>Delete</button>
                    </div>
                ))}
            </div>
        </div>
    );
};
