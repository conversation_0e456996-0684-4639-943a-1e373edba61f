import { Link, useLocation } from "react-router-dom";
import { HiHome } from "react-icons/hi2";
import SchedulerIcon from "../../assets/icons/SchedulerIcon";
import FileManagerIcon from "../../assets/icons/FileManagerIcon";
import RecorderIcon from "../../assets/icons/RecorderIcon";

const Navigation = () => {
    const location = useLocation();
    const currentPath = location.pathname;
    
    // Determine if a path is active (exact match or starts with for nested routes)
    const isActive = (path: string) => {
        if (path === '/') {
            return currentPath === '/';
        }
        return currentPath === path || currentPath.startsWith(`${path}/`);
    };

    return (
        <div className="w-full bg-[#292929c4] border-b border-[#4D4D4D] mb-4">
            <div className="flex items-center px-4 py-2 overflow-x-auto">
                <Link 
                    to="/" 
                    className={`flex items-center gap-2 px-4 py-2 rounded-md mr-4 ${
                        isActive('/') 
                            ? 'bg-[#ffa500] text-black' 
                            : 'text-white hover:bg-[#3d3d3d]'
                    }`}
                >
                    <HiHome className="w-5 h-5" />
                    <span>Home</span>
                </Link>
                
                <Link 
                    to="/scheduler" 
                    className={`flex items-center gap-2 px-4 py-2 rounded-md mr-4 ${
                        isActive('/scheduler') 
                            ? 'bg-[#ffa500] text-black' 
                            : 'text-white hover:bg-[#3d3d3d]'
                    }`}
                >
                    <SchedulerIcon className="w-5 h-5" />
                    <span>Scheduler</span>
                </Link>
                
                <Link 
                    to="/file-manager" 
                    className={`flex items-center gap-2 px-4 py-2 rounded-md mr-4 ${
                        isActive('/file-manager') 
                            ? 'bg-[#ffa500] text-black' 
                            : 'text-white hover:bg-[#3d3d3d]'
                    }`}
                >
                    <FileManagerIcon className="w-5 h-5" />
                    <span>File Manager</span>
                </Link>
                
                <Link 
                    to="/recorder" 
                    className={`flex items-center gap-2 px-4 py-2 rounded-md ${
                        isActive('/recorder') 
                            ? 'bg-[#ffa500] text-black' 
                            : 'text-white hover:bg-[#3d3d3d]'
                    }`}
                >
                    <RecorderIcon className="w-5 h-5" />
                    <span>Input Feed</span>
                </Link>
            </div>
        </div>
    );
};

export default Navigation;
