import { GetRecorderStatus } from '../../wailsjs/go/main/App';

// Service information from the TS stream
export interface ServiceInfo {
  service_id: number;
  service_name: string;
}

// Video stream information
export interface VideoInfo {
  resolution: string;
  codec: string;
}

// Audio stream information
export interface AudioInfo {
  channels: number;
  codec: string;
}

export interface RecorderStatus {
  id: number;
  rtp_url: string;
  output_dir: string;
  elapsed_seconds: number;
  duration_seconds: number;
  is_active: boolean;
  start_time: string;

  // New fields for stream information
  ts_sync: boolean;
  services: ServiceInfo[];
  video_info: VideoInfo;
  audio_info: AudioInfo;
}

export const getRecorderStatus = async (): Promise<RecorderStatus[]> => {
  try {
    const response = await GetRecorderStatus();
    return response || [];
  } catch (error) {
    console.error('Failed to get recorder status:', error);
    return [];
  }
};

// Helper function to format seconds to HH:MM:SS
export const formatSeconds = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  return [hours, minutes, secs]
    .map(val => val.toString().padStart(2, '0'))
    .join(':');
};

// Helper function to calculate progress percentage
export const calculateProgress = (elapsed: number, total: number): number => {
  if (total <= 0) return 0;
  const progress = (elapsed / total) * 100;
  return Math.min(Math.max(progress, 0), 100); // Clamp between 0 and 100
};
