import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Delete<PERSON><PERSON>order, Get<PERSON><PERSON><PERSON><PERSON>, StartRecorder, StopRecorder, UpdateRecorder } from "../../wailsjs/go/main/App";
import { document } from "../../wailsjs/go/models";
import Recorder = document.Recorder;

export interface RecorderInput {
    name: string;
    rtp_url: string;
    duration: string;
    vcodec: string;
    acodec: string;
    resolution: string;
    fps: number;
    sample_rate: number;
    vbitrate: number;
    abitrate: number;
    max_vbitrate: number;
}

export const createRecorder = async (input: RecorderInput): Promise<number> => {
    // Use type assertion to handle property name differences
    const backendRecorder: any = {
        id: 0, // Will be ignored by backend
        name: input.name,
        input: input.rtp_url,
        rtp_url_id: 0, // Will be set by the backend
        duration: input.duration,
        status: "stopped",
        VCodec: input.vcodec,
        ACodec: input.acodec,
        resolution: input.resolution,
        FPS: input.fps,
        sampleRate: input.sample_rate,
        VBitrate: input.vbitrate,
        ABitrate: input.abitrate,
        MaxVBitrate: input.max_vbitrate
    };
    return await CreateRecorder(backendRecorder);
};

export const updateRecorder = async (recorder: Recorder): Promise<void> => {
    // Convert frontend model to backend model
    const backendRecorder: any = {
        id: recorder.id,
        name: recorder.name,
        input: recorder.input,
        rtp_url_id: recorder.rtp_url_id || 0, // Include rtp_url_id, default to 0 if not provided
        duration: recorder.duration,
        status: recorder.status,
        VCodec: recorder.VCodec,
        ACodec: recorder.ACodec,
        resolution: recorder.resolution,
        FPS: recorder.FPS,
        sampleRate: recorder.sampleRate,
        VBitrate: recorder.VBitrate,
        ABitrate: recorder.ABitrate,
        MaxVBitrate: recorder.MaxVBitrate
    };
    await UpdateRecorder(backendRecorder);
};

// TODO: Backend improvement needed
// This function should be updated in the future to accept pagination parameters
// Similar to how the scheduler API works with ListSchedules(pagination)
// For now, pagination is handled on the frontend side
export const getRecorders = async (): Promise<Recorder[]> => {
    return await GetRecorders();
};

export const startRecorder = async (id: number): Promise<void> => {
    await StartRecorder(id);
};

export const stopRecorder = async (id: number): Promise<void> => {
    await StopRecorder(id);
};

export const deleteRecorder = async (id: number): Promise<void> => {
    await DeleteRecorder(id);
};
