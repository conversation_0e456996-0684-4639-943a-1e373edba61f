import { EventsOn, EventsOff } from '../../wailsjs/runtime';
import { addToast } from '@heroui/react';
import { Recorder } from '@/types/recorder';

export interface RecorderStatusUpdate {
  id: number;
  status: 'running' | 'stopped' | 'completed' | 'failed';
}

// Set up a listener for recorder status updates
export const setupRecorderStatusListener = (
  onStatusUpdate: (update: RecorderStatusUpdate) => void
): (() => void) => {
  // Listen for recorder status update events
  EventsOn('recorder:status-update', async (update: RecorderStatusUpdate) => {
    console.log('Received recorder status update:', update);

    // Call the callback with the update info
    onStatusUpdate(update);

    // Show a notification with appropriate message based on status
    let title = 'Input Feed Status Changed';
    let description = `Input Feed #${update.id} is now ${update.status}.`;

    if (update.status === 'completed') {
      title = 'Input Feed Completed';
      description = `Input Feed #${update.id} has completed successfully.`;
    } else if (update.status === 'failed') {
      title = 'Input Feed Failed';
      description = `Input Feed #${update.id} has failed.`;
    } else if (update.status === 'running') {
      title = 'Input Feed Started';
      description = `Input Feed #${update.id} is now running.`;
    } else if (update.status === 'stopped') {
      title = 'Input Feed Stopped';
      description = `Input Feed #${update.id} has been stopped.`;
    }

    addToast({
      title,
      description
    });
  });

  // Return a cleanup function
  return () => {
    EventsOff('recorder:status-update');
  };
};
