const TranscoderIcon = () => (
    <svg width="64" height="64" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path id="path" d="M22.676 35.97C22.5842 35.8742 22.4741 35.7976 22.3524 35.7447C22.2306 35.6919 22.0996 35.6638 21.9668 35.6621C21.8341 35.6604 21.7024 35.6852 21.5793 35.7349C21.4562 35.7847 21.3443 35.8584 21.2501 35.9519C21.1558 36.0454 21.0811 36.1567 21.0303 36.2793C20.9795 36.4019 20.9536 36.5335 20.9542 36.6662C20.9547 36.7989 20.9817 36.9302 21.0335 37.0524C21.0854 37.1746 21.161 37.2853 21.256 37.378L23.862 40.006C21.2415 39.9822 18.6668 39.3155 16.364 38.0644C14.0613 36.8133 12.1008 35.0161 10.6547 32.8305C9.20859 30.645 8.32109 28.1378 8.07008 25.5292C7.81906 22.9206 8.21222 20.2902 9.21503 17.869C10.2519 15.379 11.8972 13.1891 14 11.5C14.1057 11.4189 14.1942 11.3176 14.2602 11.2018C14.3262 11.0861 14.3684 10.9584 14.3843 10.8261C14.4003 10.6939 14.3896 10.5598 14.3531 10.4317C14.3165 10.3036 14.2546 10.1841 14.1712 10.0803C14.0878 9.9764 13.9845 9.89025 13.8673 9.82687C13.7502 9.76348 13.6215 9.72415 13.4889 9.71118C13.3564 9.6982 13.2225 9.71184 13.0953 9.75131C12.9681 9.79077 12.85 9.85525 12.748 9.94097C10.3419 11.8668 8.47049 14.3781 7.3131 17.2344C6.15572 20.0908 5.75111 23.1964 6.13803 26.254C6.67873 30.5297 8.73468 34.4698 11.9326 37.3589C15.1306 40.2481 19.2585 41.8948 23.567 42L21.261 44.29C21.0727 44.4768 20.9664 44.7308 20.9653 44.9961C20.9643 45.2614 21.0687 45.5162 21.2555 45.7045C21.4424 45.8928 21.6964 45.9991 21.9616 46.0002C22.2269 46.0012 22.4817 45.8968 22.67 45.71L26.832 41.581C27.0203 41.3942 27.1267 41.1404 27.1278 40.8752C27.1289 40.6101 27.0247 40.3553 26.838 40.167L22.676 35.97Z" fill="#fff"></path>
        <path d="M41.861 21.746C41.3207 17.4702 39.265 13.53 36.0672 10.6408C32.8693 7.75163 28.7414 6.10501 24.433 5.99997L26.739 3.70997C26.9273 3.52312 27.0337 3.26913 27.0347 3.00386C27.0357 2.73858 26.9313 2.48377 26.7445 2.29547C26.5576 2.10716 26.3037 2.00079 26.0384 1.99976C25.7731 1.99873 25.5183 2.10312 25.33 2.28997L21.168 6.41897C20.9797 6.6057 20.8733 6.85956 20.8722 7.12472C20.8711 7.38989 20.9753 7.64464 21.162 7.83297L25.324 12.033C25.4159 12.1288 25.5259 12.2054 25.6476 12.2582C25.7694 12.3111 25.9005 12.3392 26.0332 12.3409C26.1659 12.3425 26.2977 12.3178 26.4207 12.268C26.5438 12.2183 26.6557 12.1445 26.75 12.051C26.8442 11.9576 26.9189 11.8463 26.9697 11.7236C27.0205 11.601 27.0464 11.4695 27.0458 11.3367C27.0453 11.204 27.0183 11.0727 26.9665 10.9505C26.9147 10.8283 26.839 10.7176 26.744 10.625L24.138 7.99997C26.758 8.02428 29.3319 8.69115 31.6341 9.94207C33.9363 11.193 35.8963 12.9897 37.3423 15.1747C38.7882 17.3597 39.6759 19.8661 39.9275 22.4741C40.179 25.0821 39.7867 27.7119 38.785 30.133C37.7477 32.6222 36.1025 34.8113 34 36.5C33.8975 36.5821 33.8122 36.6836 33.7489 36.7987C33.6857 36.9139 33.6457 37.0403 33.6313 37.1709C33.6169 37.3014 33.6284 37.4335 33.665 37.5597C33.7017 37.6858 33.7629 37.8035 33.845 37.906C33.9271 38.0085 34.0287 38.0938 34.1438 38.157C34.2589 38.2203 34.3853 38.2603 34.5159 38.2747C34.6465 38.2891 34.7786 38.2776 34.9047 38.2409C35.0308 38.2043 35.1485 38.1431 35.251 38.061C37.6571 36.1352 39.5285 33.6238 40.6859 30.7675C41.8433 27.9112 42.2479 24.8055 41.861 21.748V21.746Z" fill="#fff"></path>
        <path d="M32.075 22.268L21 15.874C20.696 15.6985 20.3511 15.6061 20 15.6061C19.6489 15.6061 19.3041 15.6985 19 15.874C18.696 16.0495 18.4435 16.302 18.268 16.606C18.0924 16.9101 18 17.255 18 17.606V30.394C18 30.7451 18.0924 31.09 18.268 31.394C18.4435 31.698 18.696 31.9505 19 32.126C19.3041 32.3016 19.6489 32.394 20 32.394C20.3511 32.394 20.696 32.3016 21 32.126L32.075 25.732C32.379 25.5565 32.6315 25.304 32.807 25C32.9825 24.696 33.0749 24.3511 33.0749 24C33.0749 23.649 32.9825 23.3041 32.807 23.0001C32.6315 22.696 32.379 22.4436 32.075 22.268V22.268ZM20 30.394V17.606L31.075 24L20 30.394Z" fill="#fff"></path>

        <animateMotion xlinkHref="#plane" dur="5s" repeatCount="indefinite" rotate="auto">
            <mpath xlinkHref="#planePath"/>
        </animateMotion>
    </svg>
);

export default TranscoderIcon;
