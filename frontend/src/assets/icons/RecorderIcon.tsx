interface IconProps {
    className?: string;
}

const RecorderIcon = ({ className }: IconProps) => (
    <svg className={className} width="64" height="64" viewBox="0 0 104 94" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd" strokeLinecap="round" strokeLinejoin="round">
        <g stroke="#fff" strokeWidth="3.5">
          {/* Outer rectangle, similar to other icons' background */}
          <rect x="12" y="10" width="80" height="74" rx="12"></rect>
          {/* Main "record" circle */}
          <circle cx="52" cy="47" r="18"></circle>
          {/* Small inner circle (record light) */}
          <circle cx="52" cy="47" r="7"></circle>
          {/* Bottom rectangle for a "tape" look */}
          <rect x="34" y="73" width="36" height="7" rx="2"></rect>
        </g>
      </g>
    </svg>
  );

  export default RecorderIcon;