interface IconProps {
    className?: string;
}

const SchedulerIcon = ({ className }: IconProps) => (
    <svg xmlns="http://www.w3.org/2000/svg" xmlSpace="preserve" className={className} width="50" height="64" version="1.1" viewBox="0 0 512 512" xmlnsXlink="http://www.w3.org/1999/xlink">
        <g id="Layer_x0020_1">
            <metadata id="CorelCorpID_0Corel-Layer"/>
            <g id="_466056856">
                <g>
                    <path className="fil0" fill={"#fff"} d="M261 512l-60 0c-4,0 -7,-4 -7,-8l0 -57c-10,-3 -20,-7 -30,-12l-40 41c-2,1 -4,2 -5,2 0,0 0,0 0,0 -2,0 -4,-1 -5,-2l-78 -78c-3,-3 -3,-7 0,-10l41 -41c-5,-9 -9,-19 -13,-29l-56 0c-5,0 -8,-4 -8,-8l0 -109c0,-4 3,-7 8,-7l57 0c3,-10 7,-20 12,-30l-40 -40c-2,-1 -2,-3 -2,-5 0,-2 0,-4 2,-5l77 -77c3,-3 7,-3 10,0l40 40c10,-5 20,-9 30,-13l0 -57c0,-4 3,-7 7,-7l61 0c0,0 0,0 0,0 65,0 127,26 174,73 49,49 76,114 76,183 0,68 -26,132 -75,181 -47,48 -109,75 -176,75zm-52 -15l52 1c128,0 236,-111 236,-242 0,-65 -25,-126 -71,-172 -45,-45 -103,-69 -164,-69 0,0 -1,0 -1,0l-52 0 0 55c0,4 -3,6 -6,7 -12,4 -24,9 -36,16 -3,1 -7,1 -9,-2l-39 -39 -67 67 39 39c2,3 3,6 1,9 -7,12 -12,24 -15,36 -1,3 -4,6 -7,6l-55 0 0 95 55 0c3,0 6,2 7,5 4,13 9,25 15,37 2,3 2,6 -1,8l-39 40 67 67 39 -39c2,-2 6,-3 9,-1 12,6 25,11 37,15 2,0 5,3 5,7l0 54 0 0z"/>
                </g>
                <g>
                    <path className="fil0" fill={"#fff"} d="M256 390c-74,0 -134,-60 -134,-134 0,-74 60,-134 134,-134 4,0 7,3 7,7 0,4 -3,8 -7,8 -66,0 -120,53 -120,120 0,65 54,119 120,119 4,0 7,3 7,8 0,4 -3,6 -7,6z"/>
                </g>
                <g>
                    <path className="fil0" fill={"#fff"} d="M256 465c-4,0 -7,-4 -7,-8 0,-4 3,-7 7,-7 107,0 194,-87 194,-194 0,-107 -87,-195 -194,-195 -4,0 -7,-3 -7,-7 0,-4 3,-7 7,-7 115,0 208,93 208,208 0,116 -93,210 -208,210z"/>
                </g>
                <g>
                    <path className="fil0" fill={"#fff"} d="M256 111c-4,0 -7,-3 -7,-7l0 -50c0,-4 3,-7 7,-7 4,0 7,3 7,7l0 50c0,4 -3,7 -7,7z"/>
                </g>
                <g>
                    <path className="fil0" fill={"#fff"} d="M332 132c-2,0 -3,-1 -4,-1 -3,-2 -5,-7 -3,-10l25 -43c3,-4 7,-5 10,-3 4,2 5,7 3,10l-25 43c-1,3 -4,4 -6,4z"/>
                </g>
                <g>
                    <path className="fil0" fill={"#fff"} d="M387 187c-2,0 -5,-1 -6,-4 -2,-3 -1,-8 3,-10l43 -25c3,-2 7,0 10,3 2,3 0,8 -3,10l-43 25c-1,1 -2,1 -4,1z"/>
                </g>
                <g>
                    <path className="fil0" fill={"#fff"} d="M457 263l-49 0c-4,0 -8,-3 -8,-7 0,-4 4,-8 8,-8l49 0c5,0 8,4 8,8 0,4 -4,7 -8,7z"/>
                </g>
                <g>
                    <path className="fil0" fill={"#fff"} d="M430 364c-1,0 -2,0 -3,-1l-43 -25c-4,-2 -5,-6 -3,-10 2,-3 7,-4 10,-2l43 25c3,2 5,6 3,10 -2,2 -4,3 -7,3z"/>
                </g>
                <g>
                    <path className="fil0" fill={"#fff"} d="M357 438c-3,0 -5,-2 -7,-4l-25 -43c-2,-3 0,-8 3,-10 3,-2 8,-1 10,3l25 42c2,4 1,8 -3,10 -1,1 -2,2 -3,2z"/>
                </g>
                <g>
                    <path className="fil0" fill={"#fff"} d="M256 465c-4,0 -7,-4 -7,-8l0 -49c0,-4 3,-7 7,-7 4,0 7,3 7,7l0 50c0,3 -3,7 -7,7z"/>
                </g>
                <g>
                    <path className="fil0" fill={"#fff"} d="M256 295c-22,0 -39,-17 -39,-39 0,-21 17,-39 39,-39 22,0 39,18 39,39 0,21 -17,39 -39,39zm0 -63c-14,0 -25,10 -25,24 0,14 11,25 25,25 13,0 25,-11 25,-25 0,-14 -12,-24 -25,-24z"/>
                </g>
                <g>
                    <path className="fil0" fill={"#fff"} d="M256 232c-4,0 -7,-4 -7,-8l0 -52c0,-4 3,-7 7,-7 4,0 7,3 7,7l0 52c0,4 -3,8 -7,8z"/>
                </g>
                <g>
                    <path className="fil0" fill={"#fff"} d="M352 359c-2,0 -4,0 -5,-2l-74 -73c-3,-3 -3,-8 0,-11 3,-3 8,-3 10,0l74 74c3,3 3,7 0,10 -2,2 -3,2 -5,2z"/>
                </g>
            </g>
        </g>
    </svg>

)

export default SchedulerIcon;