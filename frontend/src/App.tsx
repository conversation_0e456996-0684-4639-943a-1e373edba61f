import { useEffect } from 'react';
import { Route, Routes, useNavigate } from "react-router-dom";
import SchedulersPage from "@/app/scheduler/Schedulers";
import Home from "@/app/home/<USER>";
import FileManager from "@/app/file-manager/FileManager";
import { EventsOff, EventsOn } from "../wailsjs/runtime";
import { addToast, ToastProvider } from "@heroui/react";
import Scheduler from "@/app/scheduler/editor/[id]/Scheduler";
import Recorder from "@/app/recorder/Recorder";

function App() {
    const navigate = useNavigate();

    useEffect(() => {
        EventsOn("navigate:home", () => navigate("/"))
        EventsOn("navigate:settings", () => navigate("/settings"))
        EventsOn("navigate:scheduler", () => navigate("/scheduler"))
        EventsOn("navigate:transcoder", () => navigate("/transcoder"))
        EventsOn("navigate:file-manager", () => navigate("/file-manager"))
        EventsOn("navigate:recorder", () => navigate("/recorder"))
        EventsOn("notification:success", (message: string) => addToast({
            description: message,
            color: "success",
        }))
        EventsOn("notification:error", (message: string) => addToast({
            description: message,
            color: "danger",
        }))
        EventsOn("notification:warning", (message: string) => addToast({
            description: message,
            color: "warning",
        }))

        return () => EventsOff(
            "navigate:home",
            "navigate:settings",
            "navigate:scheduler",
            "navigate:transcoder",
            "navigate:file-manager",
            "navigate:recorder",
            "notification:success",
            "notification:error",
            "notification:warning",
            "recorder:status-update"
        )
    }, [navigate])

    return (
        <div id="App">
            <ToastProvider placement={'bottom-center'} toastOffset={0} />
            <Routes >
                <Route path={"/"} element={<Home />} />
                <Route path={"/scheduler"} element={<SchedulersPage />} />
                <Route path={"/scheduler/editor/:id"} element={<Scheduler />} />
                {/*<Route path={"/transcoder"} element={<Transcoder />} />*/}
                <Route path={"/file-manager"} element={<FileManager />} />
                <Route path={"/recorder"} element={<Recorder />} />
                {/*<Route path={"/settings"} element={<Settings />} />*/}
            </Routes>
        </div>
    )
}

export default App
