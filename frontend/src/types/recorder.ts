export interface Recorder {
    id: number;
    name: string;
    input: string;
    rtp_url_id: number; // Required to match Go model
    duration: string;
    status: RecorderStatus;
    VCodec: string;
    ACodec: string;
    resolution: string;
    FPS: number;
    sampleRate: number;
    VBitrate: number;
    ABitrate: number;
    MaxVBitrate: number;
}

export type RecorderStatus = 'running' | 'stopped' | 'completed' | 'failed';

export interface RecorderInput {
    name: string;
    rtp_url: string;
    duration: string;
    vcodec: string;
    acodec: string;
    resolution: string;
    fps: number;
    sample_rate: number;
    vbitrate: number;
    abitrate: number;
    max_vbitrate: number;
}