@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
    --background: #0a0a0a;
    --foreground: #ededed;
}

#App {
    height: 100vh;
    width: 100vw;
}

body {
    color: var(--foreground);
    background: var(--background);
}

@layer utilities {
    .text-balance {
        text-wrap: balance;
    }
}

.custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #3b3d43 transparent;
}

.custom-scrollbar::-webkit-scrollbar {
    width: 1px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 5px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: #3b3d43;
    border-radius: 5px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: #3b3d43;
}

.hidden-scrollbar {
    overflow: hidden; /* or any other style to hide scrollbar */
}

body {
    margin: 0;
    color: white;
    font-family: "Nunito", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto",
    "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
}

@font-face {
    font-family: "Nunito";
    font-style: normal;
    font-weight: 400;
    src: local(""),
    url("assets/fonts/nunito-v16-latin-regular.woff2") format("woff2");
}

#app {
    height: 100vh;
    text-align: center;
}
