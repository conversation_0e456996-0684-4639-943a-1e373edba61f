package LocalStorage

import (
	"io"
	"os"
	"path/filepath"
	"scheduler-desktop/src/helper"
)

func UploadOriginalVideo(path string, location string, directory string) (string, error) {
	fileName := filepath.Base(path)
	folder := filepath.Join(directory, location)
	helper.CreateFolderIfNotExist(folder)
	targetPath := filepath.Join(folder, fileName)

	in, err := os.Open(path)
	if err != nil {
		return "", err
	}
	defer in.Close()

	out, err := os.Create(targetPath)
	if err != nil {
		return "", err
	}
	defer out.Close()

	if _, err = io.Copy(out, in); err != nil {
		return "", err
	}

	return targetPath, nil
}

func DeleteFiles(filePath string, directory string) {
	err := os.RemoveAll(filepath.Join(directory, filePath))
	if err != nil {
		panic(err)
	}
}
