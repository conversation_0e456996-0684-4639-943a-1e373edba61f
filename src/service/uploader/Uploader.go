package uploader

import (
	"errors"
	"os"
	"path/filepath"
	"scheduler-desktop/src/document"
	"scheduler-desktop/src/enum"
	"scheduler-desktop/src/helper"
	"scheduler-desktop/src/repository/ConvertItemRepository"
	"scheduler-desktop/src/service/converter"
	"scheduler-desktop/src/service/logger"
	"scheduler-desktop/src/service/queue"
	"scheduler-desktop/src/service/uploader/storage/LocalStorage"
	"time"
)

func Upload(paths []string, location string, directory string) {
	storage := enum.LOCAL

	// TODO: run goroutine

	for _, path := range paths {
		filename := filepath.Base(path)
		existItem := ConvertItemRepository.FindConvertItemByFilenameAndLocation(filename, location)
		if existItem.ID != 0 {
			logger.Error("File with name '%s' in folder '%s' already exists", filename, location)
		}

		var err error
		switch storage {
		case enum.LOCAL:
			_, err = LocalStorage.UploadOriginalVideo(path, location, directory)
			break
		default:
			err = errors.New("not Supported Storage")
		}

		if err != nil {
			logger.Warn("Upload Error: ", err)
			return
		}

		duration, _ := converter.GetDuration(path)
		fileInfo, _ := os.Stat(path)

		convertItem := document.ConvertItem{
			Filename:    filename,
			Name:        helper.FolderNameByFilename(filename),
			Location:    location,
			Duration:    duration,
			Status:      int(enum.SUCCESS),
			Size:        fileInfo.Size(),
			StorageType: int(enum.LOCAL),
			CreatedAt:   time.Now().UTC().Format(time.RFC3339),
			UpdatedAt:   time.Now().UTC().Format(time.RFC3339),
		}

		convertItem.ID = ConvertItemRepository.CreateConvertItem(convertItem)

		// Add the item to the conversion queue
		queueManager := queue.GetInstance()
		queueManager.AddToQueue(convertItem.ID)

		// TODO: if using cloud storage: upload original file and ts/m3u8 files to storage
		// TODO: if using cloud storage: delete local files
	}
}
