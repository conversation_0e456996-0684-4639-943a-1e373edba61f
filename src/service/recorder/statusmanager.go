package recorder

import (
	"context"
	"database/sql"
	"scheduler-desktop/src/repository/RecorderRepository"
	"scheduler-desktop/src/service/logger"
	"sync"

	"github.com/wailsapp/wails/v2/pkg/runtime"
)

// RecorderStatusManager handles the status updates for recorders
type RecorderStatusManager struct {
	db            *sql.DB
	ctx           context.Context
	statusMutex   sync.Mutex
	statusUpdates map[int]bool // Tracks which recorders have pending status updates
}

var statusManager *RecorderStatusManager

// InitStatusManager initializes the recorder status manager
func InitStatusManager(ctx context.Context, db *sql.DB) {
	statusManager = &RecorderStatusManager{
		db:            db,
		ctx:           ctx,
		statusUpdates: make(map[int]bool),
	}
}

// GetStatusManager returns the singleton instance of the status manager
func GetStatusManager() *RecorderStatusManager {
	return statusManager
}

// RecorderStatusUpdate represents a status update for a recorder
type RecorderStatusUpdate struct {
	ID     int    `json:"id"`
	Status string `json:"status"`
}

// UpdateRecorderStatus updates the status of a recorder in the database
func (m *RecorderStatusManager) UpdateRecorderStatus(id int, status string) error {
	m.statusMutex.Lock()
	defer m.statusMutex.Unlock()

	// Check if we already have a pending update for this recorder
	if _, exists := m.statusUpdates[id]; exists {
		return nil // Skip duplicate updates
	}

	// Mark this recorder as having a pending update
	m.statusUpdates[id] = true

	var err error
	switch status {
	case "running":
		err = RecorderRepository.StartRecorder(m.db, id)
	case "stopped":
		err = RecorderRepository.StopRecorder(m.db, id)
	case "completed":
		err = RecorderRepository.CompleteRecorder(m.db, id)
	case "failed":
		err = RecorderRepository.FailRecorder(m.db, id)
	default:
		logger.Error("Unknown recorder status: %s", status)
		delete(m.statusUpdates, id)
		return nil
	}

	if err != nil {
		logger.Error("Failed to update recorder status: %v", err)
		delete(m.statusUpdates, id)
		return err
	}

	// Emit an event to notify the frontend
	runtime.EventsEmit(m.ctx, "recorder:status-update", RecorderStatusUpdate{
		ID:     id,
		Status: status,
	})

	// Remove from pending updates
	delete(m.statusUpdates, id)
	return nil
}

// NotifyRecorderStopped is called when a recorder stops naturally
func (m *RecorderStatusManager) NotifyRecorderStopped(id int, completed bool) {
	// Update the database status based on whether the recorder completed successfully
	status := "completed"
	if !completed {
		status = "stopped"
	}

	err := m.UpdateRecorderStatus(id, status)
	if err != nil {
		logger.Error("Failed to update recorder status after completion: %v", err)
	} else {
		logger.Log("Recorder %d finished with status: %s", id, status)
	}
}

// NotifyRecorderFailed is called when a recorder fails
func (m *RecorderStatusManager) NotifyRecorderFailed(id int, errorMsg string) {
	err := m.UpdateRecorderStatus(id, "failed")
	if err != nil {
		logger.Error("Failed to update recorder status after failure: %v", err)
	} else {
		logger.Log("Recorder %d failed: %s", id, errorMsg)
	}
}
