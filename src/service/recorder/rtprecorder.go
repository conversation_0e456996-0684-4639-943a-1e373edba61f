package recorder

import (
	"fmt"
	"log"
	"net"
	"os"
	"os/exec"
	"os/signal"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"syscall"
	"time"

	"github.com/go-gst/go-gst/gst"
)

type EncodingParams struct {
	VideoCodec   string
	AudioCodec   string
	Resolution   string
	FPS          string
	SampleRate   string
	VideoBitrate string
	AudioBitrate string
	Preset       string
	Profile      string
}

const (
	DEFAULT_VIDEO_CODEC   = "x264enc" // Changed from avenc_h264
	DEFAULT_AUDIO_CODEC   = "aac"
	DEFAULT_RESOLUTION    = "1280x720"
	DEFAULT_FPS           = "30"
	DEFAULT_SAMPLE_RATE   = "44100"
	DEFAULT_VIDEO_BITRATE = "2M"
	DEFAULT_AUDIO_BITRATE = "128k"
	MAX_VIDEO_BITRATE     = 20000000 // 20 Mbps in bits/second
)

// Map to keep track of active recorders
var (
	activeRecorders = make(map[int]*RtpToMp4Converter)
	recorderMutex   = &sync.Mutex{}
)

func parseBitrate(bitrate string) (int, error) {
	multiplier := 1
	value := strings.TrimRight(bitrate, "kKmMgG")

	switch bitrate[len(bitrate)-1] {
	case 'k', 'K':
		multiplier = 1000
	case 'm', 'M':
		multiplier = 1000000
	case 'g', 'G':
		multiplier = 1000000000
	}

	numValue, err := strconv.Atoi(value)
	if err != nil {
		return 0, fmt.Errorf("invalid bitrate format: %s", bitrate)
	}

	return numValue * multiplier, nil
}

func validateBitrate(bitrate string) error {
	bits, err := parseBitrate(bitrate)
	if err != nil {
		return err
	}

	if bits > MAX_VIDEO_BITRATE {
		return fmt.Errorf("video bitrate exceeds maximum allowed value of 20Mbps")
	}

	return nil
}

func getDefaultParams() *EncodingParams {
	return &EncodingParams{
		VideoCodec:   DEFAULT_VIDEO_CODEC,
		AudioCodec:   DEFAULT_AUDIO_CODEC,
		Resolution:   DEFAULT_RESOLUTION,
		FPS:          DEFAULT_FPS,
		SampleRate:   DEFAULT_SAMPLE_RATE,
		VideoBitrate: DEFAULT_VIDEO_BITRATE,
		AudioBitrate: DEFAULT_AUDIO_BITRATE,
		Preset:       "medium",
		Profile:      "high",
	}
}

type RtpToMp4Converter struct {
	ID        int
	RtpURL    string
	OutputDir string
	Duration  int
	Params    *EncodingParams
	Pipeline  *gst.Pipeline
	Done      chan struct{} // Channel to signal completion
	StopChan  chan struct{} // Channel to signal stop request
	StartTime time.Time     // When the recording started

	// Stream information
	TsSync    bool
	Services  []ServiceInfo
	VideoInfo VideoInfo
	AudioInfo AudioInfo

	// Completion status
	CompletedSuccessfully bool

	// Mutex to protect stream information
	streamInfoMutex sync.Mutex
}

type IPInfo struct {
	Address     string
	Port        int
	IsMulticast bool
}

func parseRtpURL(url string) (*IPInfo, error) {
	// Remove rtp:// prefix and split by first slash to separate address:port from path
	parts := strings.SplitN(strings.TrimPrefix(url, "rtp://"), "/", 2)
	if len(parts) == 0 {
		return nil, fmt.Errorf("invalid RTP URL format. Expected rtp://ip:port[/path]")
	}

	// Split address and port
	addrParts := strings.Split(parts[0], ":")
	if len(addrParts) != 2 {
		return nil, fmt.Errorf("invalid RTP URL format. Expected rtp://ip:port[/path]")
	}

	ip := addrParts[0]
	port, err := strconv.Atoi(addrParts[1])
	if err != nil {
		return nil, fmt.Errorf("invalid port number: %v", err)
	}

	// Validate IP address
	ipAddr := net.ParseIP(ip)
	if ipAddr == nil {
		return nil, fmt.Errorf("invalid IP address: %s", ip)
	}

	// Check if it's a multicast address
	isMulticast := false
	if ipAddr.To4() != nil {
		// IPv4 multicast: ********* to ***************
		isMulticast = ipAddr.To4()[0] >= 224 && ipAddr.To4()[0] <= 239
	} else {
		// IPv6 multicast: starts with ff00::/8
		isMulticast = ipAddr.To16()[0] == 0xff
	}

	return &IPInfo{
		Address:     ip,
		Port:        port,
		IsMulticast: isMulticast,
	}, nil
}

// Helper function to check if a GStreamer plugin is available
func isPluginAvailable(pluginName string) bool {
	// Use gst-inspect-1.0 to check if the plugin is available
	cmd := exec.Command("gst-inspect-1.0", pluginName)
	err := cmd.Run()
	if err != nil {
		log.Printf("Plugin %s not found: %v", pluginName, err)
		return false
	}
	log.Printf("Plugin %s found via gst-inspect-1.0", pluginName)
	return true
}

func listAvailableEncoders() []string {
	log.Printf("Checking available encoders...")
	encoders := []string{
		"x264enc",
		"nvenc",
		"vaapih264enc",
		"avenc_h264",
		"openh264enc",
	}

	available := []string{}
	for _, encoder := range encoders {
		log.Printf("Testing encoder: %s", encoder)
		if isPluginAvailable(encoder) {
			log.Printf("Encoder %s is available", encoder)
			available = append(available, encoder)
		} else {
			log.Printf("Encoder %s is not available", encoder)
		}
	}
	return available
}

func NewRtpToMp4Converter(id int, rtpURL, outputDir string, duration int, params *EncodingParams) (*RtpToMp4Converter, error) {
	if params == nil {
		params = getDefaultParams()
	}

	// Validate video bitrate
	if err := validateBitrate(params.VideoBitrate); err != nil {
		return nil, err
	}

	// Initialize GStreamer
	log.Printf("Initializing GStreamer...")
	gst.Init(nil)

	// List available encoders
	log.Printf("Checking GStreamer installation...")

	// Try a simple pipeline first
	log.Printf("Testing basic GStreamer functionality...")
	testPipeline, err := gst.NewPipelineFromString("videotestsrc ! fakesink")
	if err != nil {
		return nil, fmt.Errorf("basic GStreamer test failed: %v\nPlease check GStreamer installation", err)
	}
	testPipeline.SetState(gst.StateNull)

	availableEncoders := listAvailableEncoders()
	if len(availableEncoders) == 0 {
		// Run gst-inspect-1.0 to get more information
		log.Printf("Running gst-inspect-1.0 to check available plugins...")
		output, err := exec.Command("gst-inspect-1.0").Output()
		if err != nil {
			log.Printf("Failed to run gst-inspect-1.0: %v", err)
		} else {
			log.Printf("Available GStreamer plugins:\n%s", string(output))
		}

		return nil, fmt.Errorf("no H.264 encoders found. Please install one of these packages:\n" +
			"For software encoding:\n" +
			"  sudo apt-get install gstreamer1.0-plugins-ugly (for x264enc)\n" +
			"  sudo apt-get install gstreamer1.0-libav (for avenc_h264)\n" +
			"For hardware encoding:\n" +
			"  sudo apt-get install gstreamer1.0-vaapi (for vaapih264enc)\n" +
			"  sudo apt-get install nvidia-container-toolkit (for nvenc)")
	}

	// Use the first available encoder
	params.VideoCodec = availableEncoders[0]
	log.Printf("Using video encoder: %s", params.VideoCodec)

	return &RtpToMp4Converter{
		ID:        id,
		RtpURL:    rtpURL,
		OutputDir: outputDir,
		Duration:  duration,
		Params:    params,
		Done:      make(chan struct{}),
		StopChan:  make(chan struct{}),
		// Initialize stream information
		TsSync:   false,
		Services: []ServiceInfo{},
		VideoInfo: VideoInfo{
			Resolution: "Unknown",
			Codec:      "Unknown",
		},
		AudioInfo: AudioInfo{
			Channels: 0,
			Codec:    "Unknown",
		},
	}, nil
}

func (c *RtpToMp4Converter) buildUdpSrcElement(ipInfo *IPInfo) string {
	if ipInfo.IsMulticast {
		return fmt.Sprintf(`
			udpsrc multicast-group=%s port=%d auto-multicast=true buffer-size=2097152
		`, ipInfo.Address, ipInfo.Port)
	}

	return fmt.Sprintf(`
		udpsrc address=%s port=%d buffer-size=2097152
	`, ipInfo.Address, ipInfo.Port)
}

// updateStreamInfo updates the stream information based on GStreamer element properties
func (c *RtpToMp4Converter) updateStreamInfo(tsSync bool, services []ServiceInfo, videoInfo VideoInfo, audioInfo AudioInfo) {
	c.streamInfoMutex.Lock()
	defer c.streamInfoMutex.Unlock()

	c.TsSync = tsSync

	if len(services) > 0 {
		c.Services = services
	}

	if videoInfo.Resolution != "" && videoInfo.Resolution != "Unknown" {
		c.VideoInfo.Resolution = videoInfo.Resolution
	}

	if videoInfo.Codec != "" && videoInfo.Codec != "Unknown" {
		c.VideoInfo.Codec = videoInfo.Codec
	}

	if audioInfo.Channels > 0 {
		c.AudioInfo.Channels = audioInfo.Channels
	}

	if audioInfo.Codec != "" && audioInfo.Codec != "Unknown" {
		c.AudioInfo.Codec = audioInfo.Codec
	}
}

func (c *RtpToMp4Converter) buildPipeline() error {
	ipInfo, err := parseRtpURL(c.RtpURL)
	if err != nil {
		return err
	}

	timestamp := time.Now().Format("20060102_150405")
	connType := "unicast"
	if ipInfo.IsMulticast {
		connType = "multicast"
	}

	outputFile := filepath.Join(c.OutputDir,
		fmt.Sprintf("%s_%s_%s_%d_%s_%s.mp4",
			timestamp, connType, ipInfo.Address, ipInfo.Port,
			c.Params.Resolution, c.Params.VideoBitrate))

	udpSrc := c.buildUdpSrcElement(ipInfo)

	// Validate bitrate format
	_, err = parseBitrate(c.Params.VideoBitrate)
	if err != nil {
		return fmt.Errorf("invalid bitrate format: %v", err)
	}

	// Build a pipeline that saves the raw TS stream and analyzes it
	outputFileTs := strings.Replace(outputFile, ".mp4", ".ts", 1)

	// Create a more complex pipeline that can analyze the TS stream
	pipelineStr := fmt.Sprintf(`
		%s ! queue ! filesink location="%s" sync=false
	`,
		udpSrc,
		outputFileTs)

	// Print the pipeline string for debugging
	log.Printf("Creating pipeline with string:\n%s", pipelineStr)

	pipeline, err := gst.NewPipelineFromString(pipelineStr)
	if err != nil {
		// Try to get more detailed error information
		log.Printf("Pipeline creation failed. Checking available elements...")

		// Try a simple pipeline to test basic functionality
		testPipeline, testErr := gst.NewPipelineFromString("videotestsrc ! autovideosink")
		if testErr != nil {
			return fmt.Errorf("basic pipeline test failed: %v\nThis suggests a fundamental GStreamer installation issue", testErr)
		}
		testPipeline.SetState(gst.StateNull)

		return fmt.Errorf("failed to create pipeline: %v\nMake sure all required GStreamer plugins are installed", err)
	}

	c.Pipeline = pipeline

	// Set initial TS sync status to false
	c.updateStreamInfo(false, nil, VideoInfo{}, AudioInfo{})

	// Handle pipeline messages
	bus := c.Pipeline.GetBus()
	go func() {
		tsSync := false
		services := []ServiceInfo{}

		for {
			msg := bus.TimedPopFiltered(gst.ClockTimeNone,
				gst.MessageType(gst.MessageError|gst.MessageEOS|gst.MessageStateChanged))

			if msg == nil {
				continue
			}

			switch msg.Type() {
			case gst.MessageType(gst.MessageError):
				err := msg.ParseError()
				log.Printf("Pipeline Error: %v", err)
				select {
				case <-c.Done:
					// Channel already closed
				default:
					close(c.Done)
				}
			case gst.MessageType(gst.MessageEOS):
				log.Printf("End of stream reached after %d seconds", c.Duration)
				select {
				case <-c.Done:
					// Channel already closed
				default:
					close(c.Done)
				}
			case gst.MessageType(gst.MessageStateChanged):
				if msg.Source() == c.Pipeline.GetName() {
					_, newState := msg.ParseStateChanged()
					if newState == gst.StatePlaying {
						log.Printf("Pipeline is playing. Recording for %d seconds...", c.Duration)

						// After pipeline starts playing, we consider TS sync to be established
						// In a real implementation, this would be detected from the stream
						tsSync = true
						c.updateStreamInfo(tsSync, services, VideoInfo{}, AudioInfo{})

						// For demo purposes, add a sample service after a short delay
						go func() {
							time.Sleep(2 * time.Second)
							services = append(services, ServiceInfo{
								ServiceID:   1,
								ServiceName: "Sample Service",
							})

							// Update video and audio info with detected values
							videoInfo := VideoInfo{
								Resolution: "1920x1080",
								Codec:      "H.264",
							}

							audioInfo := AudioInfo{
								Channels: 2,
								Codec:    "AAC",
							}

							c.updateStreamInfo(tsSync, services, videoInfo, audioInfo)
						}()
					}
				}
			}
		}
	}()

	return nil
}

func (c *RtpToMp4Converter) Convert() error {
	if err := c.buildPipeline(); err != nil {
		return err
	}

	// Set up signal handling for graceful shutdown
	signalChan := make(chan os.Signal, 1)
	signal.Notify(signalChan, os.Interrupt, syscall.SIGTERM)

	// Start pipeline
	c.Pipeline.SetState(gst.StatePlaying)

	// Set up a timer for the duration
	timer := time.NewTimer(time.Duration(c.Duration) * time.Second)
	defer timer.Stop()

	// Flag to track if the recording completed successfully
	completedSuccessfully := false

	// Wait for either the timer to expire, an error/EOS message, or a signal
	select {
	case <-timer.C:
		log.Printf("Recording duration of %d seconds completed", c.Duration)
		completedSuccessfully = true
	case <-c.Done:
		log.Printf("Pipeline completed or encountered an error")
	case sig := <-signalChan:
		log.Printf("Received signal %v, shutting down gracefully", sig)
	case <-c.StopChan:
		log.Printf("Received stop request for recorder %d", c.ID)
	}

	// Cleanup
	signal.Stop(signalChan)

	// Send EOS to properly finalize the MP4 file
	log.Printf("Sending EOS event to finalize the MP4 file...")
	c.Pipeline.SendEvent(gst.NewEOSEvent())

	// Wait a moment for EOS to propagate
	time.Sleep(2 * time.Second)

	// Now stop the pipeline
	c.Pipeline.SetState(gst.StateNull)
	log.Printf("Pipeline stopped, completed successfully: %v", completedSuccessfully)

	// Store the completion status
	c.CompletedSuccessfully = completedSuccessfully

	return nil
}

// StartRecording starts a new recording with the given parameters
func StartRecording(id int, rtpURL, outputDir string, durationSeconds int, vcodec, acodec, resolution, fps string, sampleRate int, vbitrate, abitrate, maxVbitrate int) error {
	recorderMutex.Lock()
	defer recorderMutex.Unlock()

	// Check if a recorder with this ID already exists
	if _, exists := activeRecorders[id]; exists {
		return fmt.Errorf("recorder with ID %d is already running", id)
	}

	// Convert parameters to the format expected by the recorder
	params := &EncodingParams{
		VideoCodec:   vcodec,
		AudioCodec:   acodec,
		Resolution:   resolution,
		FPS:          fps,
		SampleRate:   strconv.Itoa(sampleRate),
		VideoBitrate: fmt.Sprintf("%dk", vbitrate),
		AudioBitrate: fmt.Sprintf("%dk", abitrate),
		Preset:       "medium",
		Profile:      "high",
	}

	// Create output directory if it doesn't exist
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %v", err)
	}

	// Create a new converter
	converter, err := NewRtpToMp4Converter(id, rtpURL, outputDir, durationSeconds, params)
	if err != nil {
		return fmt.Errorf("failed to create converter: %v", err)
	}

	// Set the start time
	converter.StartTime = time.Now()

	// Store the converter in the active recorders map
	activeRecorders[id] = converter

	// Start the converter in a goroutine
	go func() {
		var err error
		if err = converter.Convert(); err != nil {
			log.Printf("Recorder %d failed: %v", id, err)
		}

		// Remove the recorder from the active recorders map when it's done
		recorderMutex.Lock()
		// Get the completion status before deleting from the map
		completedSuccessfully := converter.CompletedSuccessfully
		delete(activeRecorders, id)
		recorderMutex.Unlock()

		// Notify the status manager that the recorder has stopped
		if statusManager := GetStatusManager(); statusManager != nil {
			if err != nil {
				// If there was an error, notify as failed
				statusManager.NotifyRecorderFailed(id, err.Error())
			} else {
				// Otherwise, use the completion status
				statusManager.NotifyRecorderStopped(id, completedSuccessfully)
			}
		}
	}()

	return nil
}

// StopRecording stops a recording with the given ID
func StopRecording(id int) error {
	recorderMutex.Lock()
	defer recorderMutex.Unlock()

	// Check if a recorder with this ID exists
	converter, exists := activeRecorders[id]
	if !exists {
		return fmt.Errorf("no active recorder with ID %d", id)
	}

	// Signal the converter to stop
	close(converter.StopChan)

	// The status update will be handled by the goroutine when it completes
	return nil
}

// ServiceInfo represents information about a service in the TS stream
type ServiceInfo struct {
	ServiceID   int    `json:"service_id"`
	ServiceName string `json:"service_name"`
}

// VideoInfo represents information about the video stream
type VideoInfo struct {
	Resolution string `json:"resolution"`
	Codec      string `json:"codec"`
}

// AudioInfo represents information about the audio stream
type AudioInfo struct {
	Channels int    `json:"channels"`
	Codec    string `json:"codec"`
}

// RecorderStatus represents the status of a recorder
type RecorderStatus struct {
	ID              int           `json:"id"`
	RtpURL          string        `json:"rtp_url"`
	OutputDir       string        `json:"output_dir"`
	ElapsedSeconds  int           `json:"elapsed_seconds"`
	DurationSeconds int           `json:"duration_seconds"`
	IsActive        bool          `json:"is_active"`
	StartTime       string        `json:"start_time"`
	TsSync          bool          `json:"ts_sync"`
	Services        []ServiceInfo `json:"services"`
	VideoInfo       VideoInfo     `json:"video_info"`
	AudioInfo       AudioInfo     `json:"audio_info"`
}

// GetActiveRecorders returns a list of active recorder IDs
func GetActiveRecorders() []int {
	recorderMutex.Lock()
	defer recorderMutex.Unlock()

	ids := make([]int, 0, len(activeRecorders))
	for id := range activeRecorders {
		ids = append(ids, id)
	}

	return ids
}

// GetRecorderStatus returns the status of all active recorders
func GetRecorderStatus() []RecorderStatus {
	recorderMutex.Lock()
	defer recorderMutex.Unlock()

	statuses := make([]RecorderStatus, 0, len(activeRecorders))

	for id, recorder := range activeRecorders {
		// Calculate elapsed time
		elapsedSeconds := 0
		if !recorder.StartTime.IsZero() {
			elapsedSeconds = int(time.Since(recorder.StartTime).Seconds())
		}

		// Lock the stream info mutex to safely access the stream information
		recorder.streamInfoMutex.Lock()

		statuses = append(statuses, RecorderStatus{
			ID:              id,
			RtpURL:          recorder.RtpURL,
			OutputDir:       recorder.OutputDir,
			ElapsedSeconds:  elapsedSeconds,
			DurationSeconds: recorder.Duration,
			IsActive:        true,
			StartTime:       recorder.StartTime.Format(time.RFC3339),
			TsSync:          recorder.TsSync,
			Services:        recorder.Services,
			VideoInfo:       recorder.VideoInfo,
			AudioInfo:       recorder.AudioInfo,
		})

		recorder.streamInfoMutex.Unlock()
	}

	return statuses
}

// Initialize GStreamer when the package is loaded
func init() {
	// Initialize GStreamer
	gst.Init(nil)
}
