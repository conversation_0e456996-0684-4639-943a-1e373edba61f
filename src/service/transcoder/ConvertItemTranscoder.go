package transcoder

import (
	"database/sql"
	"fmt"
	"path/filepath"
	"scheduler-desktop/src/document"
	"scheduler-desktop/src/enum"
	"scheduler-desktop/src/helper"
	"scheduler-desktop/src/repository/ConvertItemRepository"
	"scheduler-desktop/src/service/converter"
	"scheduler-desktop/src/service/logger"
)

func TranscodeItem(convertItem *document.ConvertItem, directory string) {
	defer func(convertItem *document.ConvertItem) {
		if err := recover(); err != nil {
			ConvertItemRepository.ChangeStatus(convertItem.ID, int(enum.FAILED))
			logger.Error("Error in time transcoding: ", err)
		}
	}(convertItem)

	ConvertItemRepository.ChangeStatus(convertItem.ID, int(enum.TRANSCODING))

	resolutions := enum.HLSResolutions()

	cLocation := filepath.Join(HLSDirectory, fmt.Sprintf("%d", convertItem.ID))
	tmpFolder := filepath.Join(directory, cLocation)
	helper.CreateFolderIfNotExist(tmpFolder)
	tmpVideoFile := filepath.Join(convertItem.Location, convertItem.Filename)

	convertItem.CLocation = sql.NullString{
		String: cLocation,
		Valid:  true,
	}

	ConvertItemRepository.UpdateConvertItem(convertItem)

	converter.Convert(
		convertItem,
		tmpVideoFile,
		tmpFolder,
		resolutions,
	)

	ConvertItemRepository.ChangeStatus(convertItem.ID, int(enum.SUCCESS))
}
