package queue

import (
	"scheduler-desktop/src/enum"
	"scheduler-desktop/src/repository/ConvertItemRepository"
	"scheduler-desktop/src/service/logger"
	"scheduler-desktop/src/service/transcoder"
	"sync"
	"time"
)

// QueueManager handles the video conversion queue
type QueueManager struct {
	queue         []int64 // Queue of convert item IDs
	mutex         sync.Mutex
	isProcessing  bool
	stopRequested bool
	directory     string
}

var instance *QueueManager
var once sync.Once

// GetInstance returns the singleton instance of QueueManager
func GetInstance() *QueueManager {
	once.Do(func() {
		instance = &QueueManager{
			queue:         make([]int64, 0),
			isProcessing:  false,
			stopRequested: false,
		}
	})
	return instance
}

// Init initializes the queue manager with the directory path
func (qm *QueueManager) Init(directory string) {
	qm.directory = directory

	// Start the queue processor
	go qm.processQueue()

	// Load any queued items from the database
	qm.loadQueuedItems()
}

// loadQueuedItems loads any items with QUEUE status from the database
func (qm *QueueManager) loadQueuedItems() {
	queuedItems := ConvertItemRepository.FindConvertItemsByStatus(int(enum.QUEUE))

	qm.mutex.Lock()
	defer qm.mutex.Unlock()

	for _, item := range queuedItems {
		qm.queue = append(qm.queue, item.ID)
		logger.Log("Added previously queued item to queue: %d", item.ID)
	}
}

// AddToQueue adds a convert item to the queue
func (qm *QueueManager) AddToQueue(convertItemID int64) {
	qm.mutex.Lock()
	defer qm.mutex.Unlock()

	// Check if the item is already in the queue
	for _, id := range qm.queue {
		if id == convertItemID {
			logger.Log("Item %d is already in the queue", convertItemID)
			return
		}
	}

	// Update the status to QUEUE if it's not already
	convertItem := ConvertItemRepository.GetConvertItemById(convertItemID)
	if convertItem.ID == 0 {
		logger.Error("Failed to get convert item %d", convertItemID)
		return
	}

	if convertItem.Status != int(enum.QUEUE) {
		ConvertItemRepository.ChangeStatus(convertItemID, int(enum.QUEUE))
	}

	// Add to the queue
	qm.queue = append(qm.queue, convertItemID)
	logger.Log("Added item to queue: %d", convertItemID)
}

// processQueue processes items in the queue one by one
func (qm *QueueManager) processQueue() {
	for {
		if qm.stopRequested {
			return
		}

		// Check if there are items in the queue
		qm.mutex.Lock()
		if len(qm.queue) == 0 || qm.isProcessing {
			qm.mutex.Unlock()
			time.Sleep(1 * time.Second)
			continue
		}

		// Get the next item from the queue
		convertItemID := qm.queue[0]
		qm.queue = qm.queue[1:]
		qm.isProcessing = true
		qm.mutex.Unlock()

		// Process the item
		qm.processItem(convertItemID)

		// Mark as not processing
		qm.mutex.Lock()
		qm.isProcessing = false
		qm.mutex.Unlock()
	}
}

// processItem processes a single item from the queue
func (qm *QueueManager) processItem(convertItemID int64) {
	// Get the convert item from the database
	convertItem := ConvertItemRepository.GetConvertItemById(convertItemID)

	// Check if the item exists
	if convertItem.ID == 0 {
		logger.Error("Convert item not found: %d", convertItemID)
		return
	}

	// Process the item
	logger.Log("Processing queued item: %d", convertItemID)

	// Transcode the item
	transcoder.TranscodeItem(&convertItem, qm.directory)
}

// Stop stops the queue manager
func (qm *QueueManager) Stop() {
	qm.mutex.Lock()
	defer qm.mutex.Unlock()

	qm.stopRequested = true
}

// GetQueueLength returns the current length of the queue
func (qm *QueueManager) GetQueueLength() int {
	qm.mutex.Lock()
	defer qm.mutex.Unlock()

	return len(qm.queue)
}

// IsProcessing returns whether the queue is currently processing an item
func (qm *QueueManager) IsProcessing() bool {
	qm.mutex.Lock()
	defer qm.mutex.Unlock()

	return qm.isProcessing
}
