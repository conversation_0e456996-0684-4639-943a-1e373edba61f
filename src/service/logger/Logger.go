package logger

import (
	"context"
	"fmt"
	"github.com/wailsapp/wails/v2/pkg/runtime"
	"time"
)

var ctx context.Context

func Init(c context.Context) {
	ctx = c
}

func timestamp() string {
	return time.Now().Format("2006/01/02 15:04:05")
}

func Success(message string, args ...any) {
	msg := format(message, args...)
	runtime.LogInfo(ctx, fmt.Sprintf("%s [success] %s", timestamp(), msg))
	runtime.EventsEmit(ctx, "notification:success", msg)
}

func Log(message string, args ...any) {
	msg := format(message, args...)
	runtime.LogInfo(ctx, fmt.Sprintf("%s [info] %s", timestamp(), msg))
}

func Warn(message string, args ...any) {
	msg := format(message, args...)
	runtime.LogWarning(ctx, fmt.Sprintf("%s [warn] %s", timestamp(), msg))
	runtime.EventsEmit(ctx, "notification:warning", msg)
}

func Error(message string, args ...any) {
	msg := format(message, args...)
	runtime.LogError(ctx, fmt.Sprintf("%s [error] %s", timestamp(), msg))
	runtime.EventsEmit(ctx, "notification:error", msg)
	panic(msg)
}

func format(message string, args ...any) string {
	if len(args) > 0 {
		return fmt.Sprintf(message, args...)
	}
	return message
}
