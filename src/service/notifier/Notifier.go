package notifier

import (
	"context"
	"github.com/wailsapp/wails/v2/pkg/runtime"
	"scheduler-desktop/src/document"
	"scheduler-desktop/src/types"
)

var ctx context.Context

func Init(c context.Context) {
	ctx = c
}

func TranscodeProgress(message types.FFMPEGProgress) {
	runtime.EventsEmit(ctx, "transcode:progress", message)
}

func ChangeStatus(item document.ConvertItem) {
	runtime.EventsEmit(ctx, "change:status", item)
}
