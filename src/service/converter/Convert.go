package converter

import (
	"bufio"
	"bytes"
	"fmt"
	ffmpeg "github.com/u2takey/ffmpeg-go"
	"os/exec"
	"scheduler-desktop/src/document"
	"scheduler-desktop/src/helper"
	"scheduler-desktop/src/service/logger"
	"scheduler-desktop/src/service/notifier"
	"scheduler-desktop/src/types"
	"strconv"
	"strings"
	"sync"
	"time"
)

var ffmpegBinary string
var ffprobeBinary string

func Init(ffmpegBinaryPath string, ffprobeBinaryPath string) {
	ffmpegBinary = ffmpegBinaryPath
	ffprobeBinary = ffprobeBinaryPath
}

func GetDuration(filePath string) (float64, error) {
	cmd := exec.Command(ffprobeBinary,
		"-v", "error",
		"-show_entries", "format=duration",
		"-of", "default=noprint_wrappers=1:nokey=1",
		filePath,
	)

	var out bytes.Buffer
	cmd.Stdout = &out

	if err := cmd.Run(); err != nil {
		return 0, err
	}

	durationStr := strings.TrimSpace(out.String())
	duration, err := strconv.ParseFloat(durationStr, 64)
	if err != nil {
		return 0, err
	}

	return duration, nil
}

func Convert(convertItem *document.ConvertItem, filePath string, outputDir string, resolutions []string) {
	var outputs []*ffmpeg.Stream
	codec := "h264_nvenc"
	isCuda := true

	logger.Log("Starting transcoding %v", filePath)

	cmd := exec.Command("nvidia-smi")
	if err := cmd.Run(); err != nil {
		codec = "libx264"
		isCuda = false
		logger.Warn("NVIDIA CUDA is not supported, CPU with base codec is used")
	}

	var inputArgs []ffmpeg.KwArgs
	if isCuda {
		inputArgs = append(inputArgs, ffmpeg.KwArgs{"hwaccel": "cuda"})
	}

	input := ffmpeg.Input(filePath, inputArgs...).Split()

	for key, resolution := range resolutions {
		details := strings.Split(resolution, "_")
		size := strings.Split(details[0], "x")
		width := size[0]
		height := size[1]
		bitrate := details[1]
		number := strconv.Itoa(key)
		outputResolutionArg := []string{outputDir, resolution}
		outputResolutionDir := strings.Join(outputResolutionArg, "/")
		outputFileArg := []string{outputResolutionDir, helper.FolderNameByFilename(convertItem.Filename) + ".m3u8"}
		outputFile := strings.Join(outputFileArg, "/")

		args := []ffmpeg.KwArgs{
			{"progress": "pipe:1"},
			{"nostats": ""},
			{"c:v": codec},
			{"c:a": "aac"},
			{"map": "0:a"},
			{"profile:v": "high"},
			{"level": "4.1"},
			{"r": "30"},
			{"ac": "2"},
			{"pix_fmt": "yuv420p"},
			{"ar": "44100"},
			{"start_number": "0"},
			{"hls_time": "6"},
			{"hls_playlist_type": "vod"},
			{"hls_list_size": "0"},
			{"threads": "6"},
			{"force_key_frames": "expr:gte(t,n_forced*1)"},
			{"max_muxing_queue_size": "9999"},
			{"movflags": "frag_keyframe+empty_moov"},
			{"b:v": bitrate},
			{"f": "hls"},
		}

		output := input.
			Get(number).
			Filter("scale", ffmpeg.Args{fmt.Sprintf("%s:%s", width, height)}).
			Filter("setsar", ffmpeg.Args{"1:1"}).
			Output(outputFile, args...)

		outputs = append(outputs, output)

		helper.CreateFolderIfNotExist(outputResolutionDir)
	}

	command := ffmpeg.MergeOutputs(outputs...).OverWriteOutput()
	process := command.Compile()
	ffmpegCmd := exec.Command(ffmpegBinary, process.Args[1:]...)
	stdoutPipe, _ := ffmpegCmd.StdoutPipe()

	var wg sync.WaitGroup
	wg.Add(1)

	scanner := bufio.NewScanner(stdoutPipe)
	progress := types.FFMPEGProgress{}
	progress.ConvertItem = *convertItem

	go func() {
		for scanner.Scan() {
			line := scanner.Text()
			parseProgressLine(line, convertItem.Duration, &progress)
			notifier.TranscodeProgress(progress)
		}
		wg.Done()
	}()

	_ = ffmpegCmd.Start()
	wg.Wait()
	_ = ffmpegCmd.Wait()

	logger.Log("Finished transcoding %v", filePath)
}

func parseProgressLine(line string, duration float64, progress *types.FFMPEGProgress) {
	parts := strings.SplitN(line, "=", 2)
	if len(parts) != 2 {
		return
	}
	key, value := parts[0], parts[1]

	switch key {
	case "frame":
		progress.Frame, _ = strconv.Atoi(value)
	case "fps":
		progress.Fps, _ = strconv.ParseFloat(value, 64)
	case "bitrate":
		progress.Bitrate = value
	case "total_size":
		progress.TotalSize, _ = strconv.Atoi(value)
	case "out_time_ms":
		progress.OutTimeMs, _ = strconv.ParseInt(value, 10, 64)
	case "out_time":
		outTime := parseTimeToSeconds(value)
		progress.OutTime = outTime
		if duration > 0 {
			percent := (outTime / duration) * 100
			progress.Percent = fmt.Sprintf("%.2f", percent)
		}
	case "dup_frames":
		progress.DupFrames, _ = strconv.Atoi(value)
	case "drop_frames":
		progress.DropFrames, _ = strconv.Atoi(value)
	case "speed":
		progress.Speed = value
	case "progress":
		progress.Progress = value
	}
}

func parseTimeToSeconds(timeStr string) float64 {
	t, err := time.Parse("15:04:05.00", timeStr)
	if err != nil {
		parts := strings.Split(timeStr, ":")
		if len(parts) == 3 {
			h, _ := strconv.ParseFloat(parts[0], 64)
			m, _ := strconv.ParseFloat(parts[1], 64)
			s, _ := strconv.ParseFloat(parts[2], 64)
			return h*3600 + m*60 + s
		}
		return 0
	}
	return float64(t.Hour()*3600 + t.Minute()*60 + t.Second())
}
