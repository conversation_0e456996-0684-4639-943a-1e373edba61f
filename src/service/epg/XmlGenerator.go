package generator

import (
	"encoding/xml"
	"fmt"
	"os"
	"regexp"
	"scheduler-desktop/src/document"
	"scheduler-desktop/src/service/transcoder"
	"scheduler-desktop/src/utils"
	"time"
)

const SourceInfoUrl = ""
const SourceInfoName = "Showfer EPG data"
const GeneratorInfoName = "Showfer Media"
const SystemEpisodeAttr = "xmltv_ns"
const Lang = "en"

var dir string

func InitXML(folder string) {
	dir = folder
}

func GenerateXmlByGuide(guide document.Guide, schedule document.Schedule) {
	loc, _ := time.LoadLocation(schedule.Timezone)
	_, utcOffset := time.Now().In(loc).Zone()

	bk := document.Tv{
		SourceInfoUrl:     SourceInfoUrl,
		SourceInfoName:    SourceInfoName,
		GeneratorInfoName: GeneratorInfoName,
		Channel: document.Channel{
			Id:          schedule.Name,
			DisplayName: schedule.Name,
			Icon:        utils.Ternary(len(schedule.Icon) > 0, schedule.Icon, ""),
		},
	}

	for _, element := range guide.Elements {
		startEl, _ := time.Parse("2006-01-02T15:04:05Z", element.Start)
		utcTimeStart := startEl.Add(-time.Duration(utcOffset) * time.Second)

		endEl, _ := time.Parse("2006-01-02T15:04:05Z", element.End)
		utcTimeEnd := endEl.Add(-time.Duration(utcOffset) * time.Second)

		var episodeNumber any //If specify the document type.Episode Number, xml will show the episode-num field with an empty value
		if episodeToInt(element.File.Episode.String) > 0 {
			episodeNumber = document.EpisodeNumber{
				Value:  element.File.Episode,
				System: SystemEpisodeAttr,
			}
		}

		re := regexp.MustCompile(`[&./\\#,+()$~%'":*?<>^{}]`)
		title := re.ReplaceAllString(element.Title, "")
		desc := re.ReplaceAllString(element.Description, "")

		program := document.Program{
			Title:   title,
			Start:   utcTimeStart.Format("20060102150405 +0000"),
			Stop:    utcTimeEnd.Format("20060102150405 +0000"),
			Date:    startEl.Format("20060102"),
			Channel: schedule.Name,
			SubTitle: document.SubTitle{
				Value: utils.SubStr(title, 0, 35),
				Lang:  Lang,
			},
			Desc: document.Desc{
				Value: utils.Ternary(len(desc) > 0, desc, title),
				Lang:  Lang,
			},
			EpisodeNumber: episodeNumber,
		}
		bk.Program = append(bk.Program, program)
	}

	xmlFile, err := os.Create(fmt.Sprintf("%s/%s/%v.xml", dir, transcoder.EPGDirectory, schedule.ShortID))
	if err != nil {
		fmt.Println("Error creating XML file: ", err)
		return
	}

	encoder := xml.NewEncoder(xmlFile)
	encoder.Indent("", "\t")
	err = encoder.Encode(&bk)
	if err != nil {
		fmt.Println("Error encoding XML to file: ", err)
		return
	}
}
