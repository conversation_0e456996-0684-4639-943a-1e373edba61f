package generator

import (
	"database/sql"
	"math/rand"
	"regexp"
	"scheduler-desktop/src/document"
	"scheduler-desktop/src/repository/ConvertItemRepository"
	"scheduler-desktop/src/repository/GuideRepository"
	"scheduler-desktop/src/repository/HistoryRepository"
	"scheduler-desktop/src/utils"
	"slices"
	"sort"
	"strconv"
	"strings"
	"time"
)

const (
	ConnectionType = "connection"
	FillerType     = "filler"
	FileType       = "file"
	MinuteOffset   = 10
)

var database *sql.DB

func Init(db *sql.DB) {
	database = db
}

func Generate(schedule document.Schedule, force bool) {
	var count int
	var dayNumber int
	var startTime time.Time
	var nowWithMinuteOffset time.Time
	var currentTimeBlock time.Time
	var currentLastElementTime time.Time
	usedElements := make(map[string][]document.History)
	itemDuration := 0

	loc, _ := time.LoadLocation(schedule.Timezone)
	_, offset := time.Now().In(loc).Zone()

	utcLoc, _ := time.LoadLocation("UTC")
	now := time.Now().In(utcLoc).Add(time.Duration(offset) * time.Second)

	nowWithMinuteOffset = now.Add(MinuteOffset * time.Minute)
	dayNumber = int(nowWithMinuteOffset.Weekday())

	guide, existGuide := GuideRepository.GetGuide(database, schedule.ID)
	isNewGuide := !existGuide

	if isNewGuide || force || len(guide.Elements) == 0 {
		if isNewGuide {
			guide.ScheduleId = schedule.ID
		}
		count = 3 // today + 3 days

		startTime = time.Date(nowWithMinuteOffset.Year(), nowWithMinuteOffset.Month(), nowWithMinuteOffset.Day(), 0, 0, 0, 0, now.Location())
		currentLastElementTime = startTime

		if int(nowWithMinuteOffset.Weekday()) == dayNumber && force {
			currentTimeBlock = getCurrentTimeBlock(nowWithMinuteOffset)
			guide.Elements = utils.ArrayFilter(guide.Elements, func(element document.Element) bool {
				startEl, _ := time.Parse("2006-01-02T15:04:05Z", element.Start)
				return startEl.Unix() < currentTimeBlock.Unix()
			})
			guide.Elements = utils.ArrayFilter(guide.Elements, func(element document.Element) bool {
				return element.Type != FillerType

			})
			if len(guide.Elements) > 0 {
				for _, element := range guide.Elements {
					addedUsedElements(&usedElements, schedule, element)
				}
				lastEl := guide.Elements[len(guide.Elements)-1]
				endLast, _ := time.Parse("2006-01-02T15:04:05Z", lastEl.End)
				currentLastElementTime = endLast
				startTime = currentLastElementTime
				if currentLastElementTime.Unix() > currentTimeBlock.Unix() {
					currentTimeBlock = currentLastElementTime
				}

				endLastElement := time.Date(nowWithMinuteOffset.Year(), nowWithMinuteOffset.Month(), nowWithMinuteOffset.Day(), 0, 0, 0, 0, now.Location())
				for _, element := range guide.Elements {
					startEl, _ := time.Parse("2006-01-02T15:04:05Z", element.Start)
					endEl, _ := time.Parse("2006-01-02T15:04:05Z", element.End)
					if startEl.Unix() > endLastElement.Unix() {
						filler := createFiller(endLastElement, startEl)
						guide.Elements = append(guide.Elements, filler)
					}
					endLastElement = endEl

				}

				sort.Slice(guide.Elements, func(i, j int) bool {
					first, _ := time.Parse("2006-01-02T15:04:05Z", guide.Elements[i].Start)
					second, _ := time.Parse("2006-01-02T15:04:05Z", guide.Elements[j].Start)

					return first.Unix() < second.Unix()
				})
			}
		}

	} else {
		lastEl := guide.Elements[len(guide.Elements)-1]
		startTime, _ = time.Parse("2006-01-02T15:04:05Z", lastEl.End)

		for _, element := range guide.Elements {
			addedUsedElements(&usedElements, schedule, element)
		}

		diff := startTime.Sub(time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, startTime.Location()))
		diffDays := int(diff.Hours() / 24)

		if diffDays >= 4 { // Already created
			GenerateXmlByGuide(guide, schedule)
			return
		}

		count = 4 - diffDays
		dayNumber = dayNumber + diffDays
		if dayNumber > 6 { // Next week
			dayNumber = dayNumber - 7
		}
	}
	for number := 0; number <= count; number++ {
		day := schedule.RegularDays[dayNumber]

		specialDayIndex := slices.IndexFunc(schedule.SpecialDays, func(day document.Day) bool {
			dayDate, _ := time.Parse(time.RFC3339, day.Date)
			return dayDate.Format("2006-02-01") == startTime.Format("2006-02-01")
		})

		if specialDayIndex != -1 {
			day = schedule.SpecialDays[specialDayIndex]
		}

		sort.Slice(day.Items, func(i, j int) bool {
			first, _ := time.Parse("2006-01-02T15:04:05Z", day.Items[i].Start)
			second, _ := time.Parse("2006-01-02T15:04:05Z", day.Items[j].Start)

			return first.Unix() < second.Unix()
		})

		for _, item := range day.Items {
			start, _ := time.Parse("2006-01-02T15:04:05Z", item.Start)
			end, _ := time.Parse("2006-01-02T15:04:05Z", item.End)

			if int(nowWithMinuteOffset.Weekday()) == dayNumber && force && !isNewGuide {
				if start.Unix() < currentTimeBlock.Unix() && end.Unix() <= currentTimeBlock.Unix() {
					continue
				} else if start.Unix() < currentTimeBlock.Unix() && end.Unix() > currentTimeBlock.Unix() {
					if currentTimeBlock.Unix() > currentLastElementTime.Unix() {
						element := createFiller(currentLastElementTime, currentTimeBlock)
						guide.Elements = append(guide.Elements, element)
					}
					if item.Type == "connection" {
						element := createConnectionElement(item, currentTimeBlock, end)
						guide.Elements = append(guide.Elements, element)
					} else {
						elements := getElements(&usedElements, schedule, item, currentTimeBlock, end, 0, &itemDuration)
						guide.Elements = append(guide.Elements, elements...)
					}
					startTime = end
				} else if start.Unix() >= currentTimeBlock.Unix() {
					if start.Unix() > startTime.Unix() {
						element := createFiller(startTime, start)
						guide.Elements = append(guide.Elements, element)
						startTime = start
					}
					if item.Type == "connection" {
						element := createConnectionElement(item, start, end)
						guide.Elements = append(guide.Elements, element)
					} else {
						elements := getElements(&usedElements, schedule, item, start, end, 0, &itemDuration)
						guide.Elements = append(guide.Elements, elements...)

					}
					startTime = end
				}

			} else {
				endDay := startTime.Day()
				if start.Day() != end.Day() {
					endDay = startTime.Day() + 1
				}
				start = time.Date(startTime.Year(), startTime.Month(), startTime.Day(), start.Hour(), start.Minute(), 0, 0, start.Location())
				end = time.Date(startTime.Year(), startTime.Month(), endDay, end.Hour(), end.Minute(), 0, 0, end.Location())

				if start.Unix() > startTime.Unix() {
					element := createFiller(startTime, start)
					guide.Elements = append(guide.Elements, element)
					startTime = start
				}

				if item.Type == "connection" {
					element := createConnectionElement(item, start, end)
					guide.Elements = append(guide.Elements, element)
				} else {
					elements := getElements(&usedElements, schedule, item, start, end, 0, &itemDuration)
					guide.Elements = append(guide.Elements, elements...)
				}

				startTime = end
			}
		}

		if len(day.Items) == 0 {
			guide.Elements = utils.ArrayFilter(guide.Elements, func(element document.Element) bool {
				startEl, _ := time.Parse("2006-01-02T15:04:05Z", element.Start)
				return startEl.Unix() != startTime.Unix()
			})
			element := createFiller(startTime, startTime.Add(24*time.Hour))
			guide.Elements = append(guide.Elements, element)
			startTime = startTime.Add(24 * time.Hour)
		}

		if int(startTime.Weekday()) == dayNumber {
			endOfDay := time.Date(startTime.Year(), startTime.Month(), startTime.Day()+1, 0, 0, 0, 0, utcLoc)
			guide.Elements = utils.ArrayFilter(guide.Elements, func(element document.Element) bool {
				startEl, _ := time.Parse("2006-01-02T15:04:05Z", element.Start)
				return startEl.Unix() != startTime.Unix()
			})
			element := createFiller(startTime, endOfDay)
			guide.Elements = append(guide.Elements, element)

			startTime = endOfDay
		}

		dayNumber++
		if dayNumber > 6 {
			dayNumber = 0
		}
	}

	checkTime := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	// Clear old guide elements
	guide.Elements = utils.ArrayFilter(guide.Elements, func(element document.Element) bool {
		endTime, _ := time.Parse("2006-01-02T15:04:05Z", element.End)
		return checkTime.Sub(endTime).Hours() < (2 * 24) // Last 3 days
	})

	// Clear old special days
	schedule.SpecialDays = utils.ArrayFilter(schedule.SpecialDays, func(day document.Day) bool {
		dayDate, _ := time.Parse(time.RFC3339, day.Date)
		return checkTime.Sub(dayDate).Hours() < (8 * 24) // Last week
	})
	if existGuide {
		GuideRepository.UpdateGuide(database, guide)
	} else {
		GuideRepository.CreateGuide(database, guide)
	}
	GenerateXmlByGuide(guide, schedule)
}

func addedUsedElements(usedElements *map[string][]document.History, schedule document.Schedule, element document.Element) {
	if element.Type == FileType {
		localUsedElements := *usedElements
		localUsedElements[element.File.Folder] = append(localUsedElements[element.File.Folder], document.History{
			ScheduleId: schedule.ID,
			Folder:     element.File.Folder,
			FileName:   element.File.Filename,
			Episode:    episodeToInt(element.File.Episode.String),
		})

		*usedElements = localUsedElements
	}
}

func createFileElements(usedElements *map[string][]document.History, itemDuration *int, schedule document.Schedule, start time.Time, end time.Time, files []document.ConvertItem) []document.Element {
	var elements []document.Element
	duration := end.Sub(start)

	*itemDuration = 0
	return getRandElementsFromList(usedElements, itemDuration, schedule, duration, start, files, elements)
}

func getRandElementsFromList(
	usedElements *map[string][]document.History,
	itemDuration *int,
	schedule document.Schedule,
	duration time.Duration,
	start time.Time,
	files []document.ConvertItem,
	elements []document.Element,
) []document.Element {
	var file document.ConvertItem
	episodes := utils.ArrayFilter(files, func(item document.ConvertItem) bool {
		return episodeToInt(item.Episode.String) > 0 && int(item.Duration) <= int(duration.Seconds())
	})

	files = utils.ArrayFilter(files, func(item document.ConvertItem) bool {
		return int(item.Duration) <= int(duration.Seconds())
	})

	if len(episodes) == 0 {
		if len(files) == 0 && duration.Seconds() >= 30 {
			*itemDuration = int(duration.Seconds())
			return elements
		}
		file = files[rand.Intn(len(files))]
	} else {
		sort.Slice(episodes, func(i, j int) bool {
			return episodeToInt(episodes[i].Episode.String) < episodeToInt(episodes[j].Episode.String)
		})

		file = episodes[0]
	}

	elementDuration := 1800 - (int(file.Duration) % 1800) + int(file.Duration)
	end := start.Add(time.Duration(elementDuration) * time.Second)

	element := document.Element{
		Type:        FileType,
		Title:       file.Name,
		Description: file.Description.String,
		File: document.File{
			FileId:   file.ID,
			Filename: file.Filename,
			Folder:   file.Location,
			Episode:  file.Episode,
		},
		Start: start.Format("2006-01-02T15:04:05Z"),
		End:   end.Format("2006-01-02T15:04:05Z"),
	}
	elements = append(elements, element)
	addedUsedElements(usedElements, schedule, element)

	files = utils.ArrayFilter(files, func(item document.ConvertItem) bool {
		return item.ID != file.ID
	})

	if (int(duration.Seconds()) - elementDuration) > 0 {
		seconds := int(duration.Seconds()) - elementDuration
		duration = time.Duration(seconds * int(time.Second))

		return getRandElementsFromList(usedElements, itemDuration, schedule, duration, end, files, elements)
	}

	return elements
}

func createConnectionElement(item document.Item, start time.Time, end time.Time) document.Element {
	return document.Element{
		Type:        ConnectionType,
		Title:       item.Name,
		Description: item.Description,
		Connection: document.Connection{
			Type:       item.Connection,
			Link:       item.Link,
			Port:       item.Port,
			Mode:       item.Mode,
			ExpireDate: item.ExpireDate,
			ExpireTime: item.ExpireTime,
		},
		Start: start.Format("2006-01-02T15:04:05Z"),
		End:   end.Format("2006-01-02T15:04:05Z"),
	}
}

func createFiller(start time.Time, end time.Time) document.Element {
	return document.Element{
		Type:        FillerType,
		Title:       "Filler",
		Description: "",
		Start:       start.Format("2006-01-02T15:04:05Z"),
		End:         end.Format("2006-01-02T15:04:05Z"),
	}
}

func findConvertItems(usedElements *map[string][]document.History, schedule document.Schedule, item document.Item) []document.ConvertItem {
	var files []document.ConvertItem

	localUsedElements := *usedElements

	for _, file := range item.Files {
		convertItem := ConvertItemRepository.FindByPath(file.Path)
		files = append(files, convertItem)
	}

	for _, folder := range item.Folders {

		if _, has := localUsedElements[folder.Path]; !has {
			localUsedElements[folder.Path] = HistoryRepository.FindAllByScheduleAndFolder(database, schedule.ID, folder.Path)
		}

		convertItems := ConvertItemRepository.FindByFolder(folder.Path)
		items := utils.ArrayFilter(convertItems, func(item document.ConvertItem) bool {
			if _, has := localUsedElements[item.Location]; has {
				return !contains(localUsedElements[item.Location], item)
			}
			return true
		})

		if len(items) == 0 {
			items = convertItems
			localUsedElements[folder.Path] = nil
			HistoryRepository.DeleteByScheduleAndFolder(database, schedule.ID, folder.Path)
		}

		files = append(files, items...)
	}

	*usedElements = localUsedElements
	return files
}

func contains(history []document.History, item document.ConvertItem) bool {
	for _, h := range history {
		if h.FileName == item.Filename {
			return true
		}
	}

	return false
}

func episodeToInt(episodeNum string) int {
	re := regexp.MustCompile("\\d+")
	str := strings.Join(re.FindAllString(episodeNum, -1), "")
	episode, _ := strconv.Atoi(str)

	return episode
}

func getCurrentTimeBlock(currentTime time.Time) time.Time {
	if currentTime.Minute() > 30 {
		currentTime = currentTime.Add(time.Hour).Truncate(time.Hour)
	} else {
		currentTime = currentTime.Truncate(time.Hour).Add(30 * time.Minute)
	}

	return currentTime
}

func getElements(
	usedElements *map[string][]document.History,
	schedule document.Schedule,
	item document.Item,
	start time.Time,
	end time.Time,
	remainDuration int,
	itemDuration *int,
) []document.Element {
	files := findConvertItems(usedElements, schedule, item)
	elements := createFileElements(usedElements, itemDuration, schedule, start, end, files)
	localItemDuration := *itemDuration
	if localItemDuration > 30 {
		startPoint := end.Add(-time.Duration(localItemDuration) * time.Second)
		if localItemDuration == remainDuration {
			elements = append(elements, createFiller(startPoint, end))
		} else {
			elements = append(elements, getElements(
				usedElements,
				schedule,
				item,
				startPoint,
				end,
				localItemDuration,
				itemDuration,
			)...)
		}
	}

	return elements
}
