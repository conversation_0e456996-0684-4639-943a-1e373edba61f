package HistoryRepository

import (
	"database/sql"
	"scheduler-desktop/src/document"
	"scheduler-desktop/src/service/logger"
	"scheduler-desktop/src/types"
)

func FindAllByScheduleAndFolder(
	db *sql.DB,
	scheduleId int64,
	path string,
) []document.History {
	rows, err := db.Query(`
		SELECT folder, filename, episode FROM history
		WHERE schedule_id = ? AND folder = ?
	`, scheduleId, path)
	defer rows.Close()

	if err != nil {
		logger.Error("Error delete history: %s", err)
	}

	var data []document.History
	for rows.Next() {
		var d document.History

		err = rows.Scan(&d.Folder, &d.FileName, &d.Episode)
		if err != nil {
			panic(err)
		}

		data = append(data, d)
	}

	return data
}

func DeleteByScheduleAndFolder(
	db *sql.DB,
	scheduleId int64,
	path string,
) {
	_, err := db.Exec(`
		DELETE FROM history WHERE schedule_id = ? AND folder = ?
	`, scheduleId, path)

	if err != nil {
		logger.Error("Error delete history: %s", err)
	}
}

func Schema() types.TableSchema {
	return types.TableSchema{
		TableName: "history",
		Columns: []string{
			"id INTEGER PRIMARY KEY AUTOINCREMENT",
			"schedule_id INTEGER",
			"folder TEXT NOT NULL",
			"filename TEXT NOT NULL",
			"episode INTEGER DEFAULT NULL",
			"FOREIGN KEY (schedule_id) REFERENCES schedules (id) ON DELETE CASCADE",
		},
	}
}
