package GuideRepository

import (
	"database/sql"
	"encoding/json"
	"errors"
	"scheduler-desktop/src/document"
	"scheduler-desktop/src/service/logger"
	"scheduler-desktop/src/types"
	"time"
)

func UpdateGuide(db *sql.DB, guide document.Guide) {
	_, err := db.Exec(`
		UPDATE guide SET updated_at = ?, elements = ?, schedule_id = ? WHERE id = ?
	`,
		time.Now().Format(time.RFC3339),
		_normalize(guide.Elements),
		guide.ScheduleId,
		guide.ID,
	)

	if err != nil {
		logger.Error("Error in time insert guide: %s", err)
	}
}

func CreateGuide(db *sql.DB, guide document.Guide) int64 {
	result, err := db.Exec(`
		INSERT INTO guide
			(updated_at, elements, schedule_id)
		VALUES (?, ?, ?)
	`,
		time.Now().Format(time.RFC3339),
		_normalize(guide.Elements),
		guide.ScheduleId,
	)

	if err != nil {
		logger.Error("Error in time insert guide: %s", err)
	}

	id, _ := result.LastInsertId()

	return id
}

func GetGuide(db *sql.DB, scheduleId int64) (document.Guide, bool) {
	row := db.QueryRow(`
		SELECT id, updated_at, schedule_id, elements
		FROM guide
		WHERE schedule_id = ?
	`, scheduleId)

	var d document.Guide
	var elements string

	err := row.Scan(&d.ID, &d.UpdatedAt, &d.ScheduleId, &elements)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return d, false
		}
		return d, false
	}
	_ = json.Unmarshal([]byte(elements), &d.Elements)

	return d, true
}

func Schema() types.TableSchema {
	return types.TableSchema{
		TableName: "guide",
		Columns: []string{
			"id INTEGER PRIMARY KEY AUTOINCREMENT",
			"schedule_id INTEGER",
			"updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP",
			"elements TEXT DEFAULT NULL",
			"FOREIGN KEY (schedule_id) REFERENCES schedules (id) ON DELETE CASCADE",
		},
	}
}

func _normalize(object any) string {
	jsonData, err := json.Marshal(object)
	if err != nil {
		panic(err)
	}

	return string(jsonData)
}
