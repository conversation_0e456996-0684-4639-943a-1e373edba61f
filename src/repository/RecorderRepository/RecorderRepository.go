package RecorderRepository

import (
	"database/sql"
	"scheduler-desktop/src/document"
	"scheduler-desktop/src/repository/RtpUrlRepository"
	"scheduler-desktop/src/service/logger"
)

func Init(db *sql.DB) {
	// First ensure the rtp_urls table exists
	RtpUrlRepository.Init(db)

	_, err := db.Exec(`
        CREATE TABLE IF NOT EXISTS recorders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            input TEXT NOT NULL,
            rtp_url_id INTEGER,
            duration TEXT NOT NULL,
            status TEXT NOT NULL,
            vcodec TEXT NOT NULL,
            acodec TEXT NOT NULL,
            resolution TEXT NOT NULL,
            fps REAL NOT NULL,
            sample_rate INTEGER NOT NULL,
            vbitrate INTEGER NOT NULL,
            abitrate INTEGER NOT NULL,
            max_vbitrate INTEGER NOT NULL,
            FOREIGN KEY (rtp_url_id) REFERENCES rtp_urls (id)
        )
    `)
	if err != nil {
		logger.Error("Failed to create recorders table: %v", err)
	}

	// Add rtp_url_id column to existing table if it doesn't exist
	// This is for backward compatibility with existing databases
	_, err = db.Exec(`
        PRAGMA foreign_keys = OFF;

        CREATE TABLE IF NOT EXISTS recorders_new (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            input TEXT NOT NULL,
            rtp_url_id INTEGER,
            duration TEXT NOT NULL,
            status TEXT NOT NULL,
            vcodec TEXT NOT NULL,
            acodec TEXT NOT NULL,
            resolution TEXT NOT NULL,
            fps REAL NOT NULL,
            sample_rate INTEGER NOT NULL,
            vbitrate INTEGER NOT NULL,
            abitrate INTEGER NOT NULL,
            max_vbitrate INTEGER NOT NULL,
            FOREIGN KEY (rtp_url_id) REFERENCES rtp_urls (id)
        );

        INSERT INTO recorders_new
        SELECT id, name, input, NULL, duration, status, vcodec, acodec, resolution, fps, sample_rate, vbitrate, abitrate, max_vbitrate
        FROM recorders;

        DROP TABLE IF EXISTS recorders;

        ALTER TABLE recorders_new RENAME TO recorders;

        PRAGMA foreign_keys = ON;
    `)
	if err != nil {
		logger.Error("Failed to migrate recorders table: %v", err)
	}
}

func CreateRecorder(db *sql.DB, recorder document.Recorder) int64 {
	// First create the RTP URL to get its ID
	rtpUrlID, err := RtpUrlRepository.CreateRtpUrl(db, recorder.Input, 0) // Use 0 temporarily
	if err != nil {
		logger.Error("Failed to create RTP URL: %v", err)
		return 0
	}

	// Now save the recorder with the RTP URL ID
	result, err := db.Exec(`
        INSERT INTO recorders (
            name, input, rtp_url_id, duration, status, vcodec, acodec,
            resolution, fps, sample_rate, vbitrate, abitrate, max_vbitrate
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `,
		recorder.Name, recorder.Input, rtpUrlID, recorder.Duration, recorder.Status,
		recorder.VCodec, recorder.ACodec, recorder.Resolution, recorder.FPS,
		recorder.SampleRate, recorder.VBitrate, recorder.ABitrate, recorder.MaxVBitrate,
	)
	if err != nil {
		logger.Error("Failed to create recorder: %v", err)
		// Delete the RTP URL since the recorder creation failed
		_ = RtpUrlRepository.DeleteRtpUrl(db, rtpUrlID)
		return 0
	}

	id, err := result.LastInsertId()
	if err != nil {
		logger.Error("Failed to get last insert ID: %v", err)
		// Delete the RTP URL since we couldn't get the recorder ID
		_ = RtpUrlRepository.DeleteRtpUrl(db, rtpUrlID)
		return 0
	}

	// Update the RTP URL with the correct recorder ID
	_, err = db.Exec(`
        UPDATE rtp_urls SET
            recorder_id = ?
        WHERE id = ?
    `, id, rtpUrlID)
	if err != nil {
		logger.Error("Failed to update RTP URL with recorder ID: %v", err)
		// Continue even if there's an error updating the RTP URL
	}

	return id
}

func UpdateRecorder(db *sql.DB, recorder document.Recorder) error {
	// First check if there's an existing RTP URL for this recorder
	var existingRtpUrlID sql.NullInt64 // Use sql.NullInt64 to handle NULL values
	err := db.QueryRow("SELECT rtp_url_id FROM recorders WHERE id = ?", recorder.ID).Scan(&existingRtpUrlID)
	if err != nil && err != sql.ErrNoRows {
		logger.Error("Failed to get existing RTP URL ID: %v", err)
		return err
	}

	// Create or update the RTP URL
	rtpUrlID, err := RtpUrlRepository.CreateRtpUrl(db, recorder.Input, recorder.ID)
	if err != nil {
		logger.Error("Failed to create/update RTP URL: %v", err)
		return err
	}

	// Now update the recorder with the new RTP URL ID
	_, err = db.Exec(`
        UPDATE recorders SET
            name=?, input=?, rtp_url_id=?, duration=?, status=?, vcodec=?, acodec=?,
            resolution=?, fps=?, sample_rate=?, vbitrate=?, abitrate=?, max_vbitrate=?
        WHERE id=?
    `,
		recorder.Name, recorder.Input, rtpUrlID, recorder.Duration, recorder.Status,
		recorder.VCodec, recorder.ACodec, recorder.Resolution, recorder.FPS,
		recorder.SampleRate, recorder.VBitrate, recorder.ABitrate, recorder.MaxVBitrate,
		recorder.ID,
	)
	if err != nil {
		logger.Error("Failed to update recorder: %v", err)
		return err
	}

	// If the RTP URL ID has changed, delete the old one if it's not being used
	if existingRtpUrlID.Valid && existingRtpUrlID.Int64 > 0 && existingRtpUrlID.Int64 != rtpUrlID {
		inUse, err := RtpUrlRepository.IsRtpUrlInUse(db, existingRtpUrlID.Int64)
		if err != nil {
			logger.Error("Failed to check if old RTP URL is in use: %v", err)
			// Continue even if there's an error checking if the old RTP URL is in use
		} else if !inUse {
			err = RtpUrlRepository.DeleteRtpUrl(db, existingRtpUrlID.Int64)
			if err != nil {
				logger.Error("Failed to delete old RTP URL: %v", err)
				// Continue even if there's an error deleting the old RTP URL
			}
		}
	}

	return nil
}

func GetRecorders(db *sql.DB) []document.Recorder {
	rows, err := db.Query(`
		SELECT id, name, input, rtp_url_id, duration, status, vcodec, acodec,
		       resolution, fps, sample_rate, vbitrate, abitrate, max_vbitrate
		FROM recorders
	`)
	if err != nil {
		logger.Error("Failed to get recorders: %v", err)
		return nil
	}
	defer rows.Close()

	var recorders []document.Recorder
	for rows.Next() {
		var recorder document.Recorder
		var rtpUrlID sql.NullInt64 // Use NullInt64 to handle NULL values

		err := rows.Scan(
			&recorder.ID, &recorder.Name, &recorder.Input, &rtpUrlID, &recorder.Duration,
			&recorder.Status, &recorder.VCodec, &recorder.ACodec, &recorder.Resolution,
			&recorder.FPS, &recorder.SampleRate, &recorder.VBitrate, &recorder.ABitrate,
			&recorder.MaxVBitrate,
		)
		if err != nil {
			logger.Error("Failed to scan recorder: %v", err)
			continue
		}

		// Set the RtpUrlID if it's not NULL
		if rtpUrlID.Valid {
			recorder.RtpUrlID = rtpUrlID.Int64
		}

		recorders = append(recorders, recorder)
	}
	return recorders
}

func DeleteRecorder(db *sql.DB, id int) error {
	// Delete the recorder
	_, err := db.Exec("DELETE FROM recorders WHERE id = ?", id)
	if err != nil {
		logger.Error("Failed to delete recorder: %v", err)
		return err
	}

	// Delete all RTP URLs associated with this recorder
	err = RtpUrlRepository.DeleteRtpUrlsByRecorderId(db, id)
	if err != nil {
		logger.Error("Failed to delete RTP URLs for recorder %d: %v", id, err)
		// Continue even if there's an error deleting the RTP URLs
	}

	return nil
}

func StartRecorder(db *sql.DB, id int) error {
	_, err := db.Exec("UPDATE recorders SET status = 'running' WHERE id = ?", id)
	if err != nil {
		logger.Error("Failed to start recorder: %v", err)
	}
	return err
}

func StopRecorder(db *sql.DB, id int) error {
	_, err := db.Exec("UPDATE recorders SET status = 'stopped' WHERE id = ?", id)
	if err != nil {
		logger.Error("Failed to stop recorder: %v", err)
	}
	return err
}

func CompleteRecorder(db *sql.DB, id int) error {
	_, err := db.Exec("UPDATE recorders SET status = 'completed' WHERE id = ?", id)
	if err != nil {
		logger.Error("Failed to complete recorder: %v", err)
	}
	return err
}

func FailRecorder(db *sql.DB, id int) error {
	_, err := db.Exec("UPDATE recorders SET status = 'failed' WHERE id = ?", id)
	if err != nil {
		logger.Error("Failed to mark recorder as failed: %v", err)
	}
	return err
}
