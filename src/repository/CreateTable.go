package repository

import (
	"bytes"
	"database/sql"
	"scheduler-desktop/src/repository/ConvertItemRepository"
	"scheduler-desktop/src/repository/GuideRepository"
	"scheduler-desktop/src/repository/HistoryRepository"
	"scheduler-desktop/src/repository/RtpUrlRepository"
	"scheduler-desktop/src/repository/ScheduleRepository"
	"scheduler-desktop/src/types"
	"text/template"
)

const createTableTemplate = `
CREATE TABLE IF NOT EXISTS {{.TableName}} (
{{- range $index, $col := .Columns }}
    {{$col}}{{if not (last $index $.Columns)}},{{end}}
{{- end }}
);
`

func CreateHistoryTable(db *sql.DB) error {
	return _createTable(db, HistoryRepository.Schema())
}

func CreateGuideTable(db *sql.DB) error {
	return _createTable(db, GuideRepository.Schema())
}

func CreateScheduleTable(db *sql.DB) error {
	return _createTable(db, ScheduleRepository.Schema())
}

func CreateConvertItemTable(db *sql.DB) error {
	return _createTable(db, ConvertItemRepository.Schema())
}

func CreateRtpUrlTable(db *sql.DB) error {
	return _createTable(db, RtpUrlRepository.Schema())
}

func _last(i int, arr []string) bool {
	return i == len(arr)-1
}

func _createTable(db *sql.DB, schema types.TableSchema) error {
	funcMap := template.FuncMap{"last": _last}

	tpl := template.Must(template.New("createTable").Funcs(funcMap).Parse(createTableTemplate))
	var query bytes.Buffer
	err := tpl.Execute(&query, schema)
	if err != nil {
		return err
	}

	_, err = db.Exec(query.String())
	return err
}
