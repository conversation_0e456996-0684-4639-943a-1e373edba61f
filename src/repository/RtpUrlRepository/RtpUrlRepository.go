package RtpUrlRepository

import (
	"database/sql"
	"scheduler-desktop/src/document"
	"scheduler-desktop/src/service/logger"
	"scheduler-desktop/src/types"
	"time"
)

func Init(db *sql.DB) {
	// First, create the table if it doesn't exist
	_, err := db.Exec(`
		CREATE TABLE IF NOT EXISTS rtp_urls (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			url TEXT NOT NULL,
			recorder_id INTEGER NOT NULL,
			created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		logger.Error("Failed to create rtp_urls table: %v", err)
		return
	}

	// Check if the updated_at column exists
	var columnExists bool
	err = db.QueryRow(`
		SELECT COUNT(*) > 0
		FROM pragma_table_info('rtp_urls')
		WHERE name = 'updated_at'
	`).Scan(&columnExists)

	if err != nil {
		logger.Error("Failed to check if updated_at column exists: %v", err)
		return
	}

	// If the column doesn't exist, add it
	if !columnExists {
		_, err = db.Exec(`
			ALTER TABLE rtp_urls
			ADD COLUMN updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
		`)
		if err != nil {
			logger.Error("Failed to add updated_at column to rtp_urls table: %v", err)
		}
	}

	// Check if the recorder_id column exists
	err = db.QueryRow(`
		SELECT COUNT(*) > 0
		FROM pragma_table_info('rtp_urls')
		WHERE name = 'recorder_id'
	`).Scan(&columnExists)

	if err != nil {
		logger.Error("Failed to check if recorder_id column exists: %v", err)
		return
	}

	// If the column doesn't exist, add it
	if !columnExists {
		_, err = db.Exec(`
			ALTER TABLE rtp_urls
			ADD COLUMN recorder_id INTEGER NOT NULL DEFAULT 0
		`)
		if err != nil {
			logger.Error("Failed to add recorder_id column to rtp_urls table: %v", err)
		}
	}

	// Remove the UNIQUE constraint from the url column if it exists
	// This is necessary because we now want to allow the same URL to be used by different recorders
	_, err = db.Exec(`
		PRAGMA foreign_keys = OFF;

		CREATE TABLE IF NOT EXISTS rtp_urls_new (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			url TEXT NOT NULL,
			recorder_id INTEGER NOT NULL,
			created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
		);

		INSERT INTO rtp_urls_new
		SELECT id, url, recorder_id, created_at, updated_at
		FROM rtp_urls;

		DROP TABLE IF EXISTS rtp_urls;

		ALTER TABLE rtp_urls_new RENAME TO rtp_urls;

		PRAGMA foreign_keys = ON;
	`)
	if err != nil {
		logger.Error("Failed to migrate rtp_urls table: %v", err)
	}
}

func CreateRtpUrl(db *sql.DB, url string, recorderID int) (int64, error) {
	// Check if URL already exists for this recorder
	var id int64
	err := db.QueryRow("SELECT id FROM rtp_urls WHERE url = ? AND recorder_id = ?", url, recorderID).Scan(&id)
	if err == nil {
		// URL already exists for this recorder, return its ID
		return id, nil
	}

	// URL doesn't exist for this recorder, create a new one
	// Check if the updated_at and recorder_id columns exist
	var updatedAtExists, recorderIdExists bool
	err = db.QueryRow(`
		SELECT COUNT(*) > 0
		FROM pragma_table_info('rtp_urls')
		WHERE name = 'updated_at'
	`).Scan(&updatedAtExists)

	if err != nil {
		logger.Error("Failed to check if updated_at column exists: %v", err)
		return 0, err
	}

	err = db.QueryRow(`
		SELECT COUNT(*) > 0
		FROM pragma_table_info('rtp_urls')
		WHERE name = 'recorder_id'
	`).Scan(&recorderIdExists)

	if err != nil {
		logger.Error("Failed to check if recorder_id column exists: %v", err)
		return 0, err
	}

	var result sql.Result
	if updatedAtExists && recorderIdExists {
		// If both columns exist, include them in the INSERT
		result, err = db.Exec(`
			INSERT INTO rtp_urls (url, recorder_id, updated_at)
			VALUES (?, ?, ?)
		`, url, recorderID, time.Now().Format(time.RFC3339))
	} else if recorderIdExists {
		// If only recorder_id exists
		result, err = db.Exec(`
			INSERT INTO rtp_urls (url, recorder_id)
			VALUES (?, ?)
		`, url, recorderID)
	} else if updatedAtExists {
		// If only updated_at exists
		result, err = db.Exec(`
			INSERT INTO rtp_urls (url, updated_at)
			VALUES (?, ?)
		`, url, time.Now().Format(time.RFC3339))
	} else {
		// If neither column exists, just insert the URL
		result, err = db.Exec(`
			INSERT INTO rtp_urls (url)
			VALUES (?)
		`, url)
	}

	if err != nil {
		logger.Error("Failed to create RTP URL: %v", err)
		return 0, err
	}

	id, err = result.LastInsertId()
	if err != nil {
		logger.Error("Failed to get last insert ID: %v", err)
		return 0, err
	}

	return id, nil
}

func GetRtpUrl(db *sql.DB, id int64) (document.RtpUrl, error) {
	// Check if the updated_at and recorder_id columns exist
	var updatedAtExists, recorderIdExists bool
	err := db.QueryRow(`
		SELECT COUNT(*) > 0
		FROM pragma_table_info('rtp_urls')
		WHERE name = 'updated_at'
	`).Scan(&updatedAtExists)

	if err != nil {
		logger.Error("Failed to check if updated_at column exists: %v", err)
		return document.RtpUrl{}, err
	}

	err = db.QueryRow(`
		SELECT COUNT(*) > 0
		FROM pragma_table_info('rtp_urls')
		WHERE name = 'recorder_id'
	`).Scan(&recorderIdExists)

	if err != nil {
		logger.Error("Failed to check if recorder_id column exists: %v", err)
		return document.RtpUrl{}, err
	}

	var rtpUrl document.RtpUrl
	if updatedAtExists && recorderIdExists {
		row := db.QueryRow(`
			SELECT id, url, recorder_id, created_at, updated_at
			FROM rtp_urls
			WHERE id = ?
		`, id)

		err = row.Scan(&rtpUrl.ID, &rtpUrl.URL, &rtpUrl.RecorderID, &rtpUrl.CreatedAt, &rtpUrl.UpdatedAt)
	} else if recorderIdExists {
		row := db.QueryRow(`
			SELECT id, url, recorder_id, created_at, created_at
			FROM rtp_urls
			WHERE id = ?
		`, id)

		err = row.Scan(&rtpUrl.ID, &rtpUrl.URL, &rtpUrl.RecorderID, &rtpUrl.CreatedAt, &rtpUrl.UpdatedAt)
	} else if updatedAtExists {
		row := db.QueryRow(`
			SELECT id, url, 0, created_at, updated_at
			FROM rtp_urls
			WHERE id = ?
		`, id)

		err = row.Scan(&rtpUrl.ID, &rtpUrl.URL, &rtpUrl.RecorderID, &rtpUrl.CreatedAt, &rtpUrl.UpdatedAt)
	} else {
		row := db.QueryRow(`
			SELECT id, url, 0, created_at, created_at
			FROM rtp_urls
			WHERE id = ?
		`, id)

		err = row.Scan(&rtpUrl.ID, &rtpUrl.URL, &rtpUrl.RecorderID, &rtpUrl.CreatedAt, &rtpUrl.UpdatedAt)
	}

	if err != nil {
		logger.Error("Failed to get RTP URL: %v", err)
		return document.RtpUrl{}, err
	}

	return rtpUrl, nil
}

func GetAllRtpUrls(db *sql.DB) ([]document.RtpUrl, error) {
	// Check if the updated_at and recorder_id columns exist
	var updatedAtExists, recorderIdExists bool
	err := db.QueryRow(`
		SELECT COUNT(*) > 0
		FROM pragma_table_info('rtp_urls')
		WHERE name = 'updated_at'
	`).Scan(&updatedAtExists)

	if err != nil {
		logger.Error("Failed to check if updated_at column exists: %v", err)
		return nil, err
	}

	err = db.QueryRow(`
		SELECT COUNT(*) > 0
		FROM pragma_table_info('rtp_urls')
		WHERE name = 'recorder_id'
	`).Scan(&recorderIdExists)

	if err != nil {
		logger.Error("Failed to check if recorder_id column exists: %v", err)
		return nil, err
	}

	var rows *sql.Rows
	if updatedAtExists && recorderIdExists {
		rows, err = db.Query(`
			SELECT id, url, recorder_id, created_at, updated_at
			FROM rtp_urls
			ORDER BY updated_at DESC
		`)
	} else if recorderIdExists {
		rows, err = db.Query(`
			SELECT id, url, recorder_id, created_at, created_at
			FROM rtp_urls
			ORDER BY created_at DESC
		`)
	} else if updatedAtExists {
		rows, err = db.Query(`
			SELECT id, url, 0, created_at, updated_at
			FROM rtp_urls
			ORDER BY updated_at DESC
		`)
	} else {
		rows, err = db.Query(`
			SELECT id, url, 0, created_at, created_at
			FROM rtp_urls
			ORDER BY created_at DESC
		`)
	}

	if err != nil {
		logger.Error("Failed to get RTP URLs: %v", err)
		return nil, err
	}
	defer rows.Close()

	var rtpUrls []document.RtpUrl
	for rows.Next() {
		var rtpUrl document.RtpUrl
		err := rows.Scan(&rtpUrl.ID, &rtpUrl.URL, &rtpUrl.RecorderID, &rtpUrl.CreatedAt, &rtpUrl.UpdatedAt)
		if err != nil {
			logger.Error("Failed to scan RTP URL row: %v", err)
			continue
		}
		rtpUrls = append(rtpUrls, rtpUrl)
	}

	return rtpUrls, nil
}

func DeleteRtpUrl(db *sql.DB, id int64) error {
	_, err := db.Exec("DELETE FROM rtp_urls WHERE id = ?", id)
	if err != nil {
		logger.Error("Failed to delete RTP URL: %v", err)
		return err
	}
	return nil
}

func DeleteRtpUrlsByRecorderId(db *sql.DB, recorderID int) error {
	_, err := db.Exec("DELETE FROM rtp_urls WHERE recorder_id = ?", recorderID)
	if err != nil {
		logger.Error("Failed to delete RTP URLs for recorder %d: %v", recorderID, err)
		return err
	}
	return nil
}

func IsRtpUrlInUse(db *sql.DB, id int64) (bool, error) {
	var count int
	err := db.QueryRow("SELECT COUNT(*) FROM recorders WHERE rtp_url_id = ?", id).Scan(&count)
	if err != nil {
		logger.Error("Failed to check if RTP URL is in use: %v", err)
		return false, err
	}
	return count > 0, nil
}

func Schema() types.TableSchema {
	return types.TableSchema{
		TableName: "rtp_urls",
		Columns: []string{
			"id INTEGER PRIMARY KEY AUTOINCREMENT",
			"url TEXT NOT NULL",
			"recorder_id INTEGER NOT NULL",
			"created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP",
			"updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP",
		},
	}
}
