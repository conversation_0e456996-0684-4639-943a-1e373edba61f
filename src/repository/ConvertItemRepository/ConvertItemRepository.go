package ConvertItemRepository

import (
	"database/sql"
	"errors"
	"fmt"
	"path/filepath"
	"scheduler-desktop/src/document"
	"scheduler-desktop/src/enum"
	"scheduler-desktop/src/service/logger"
	"scheduler-desktop/src/service/notifier"
	"scheduler-desktop/src/types"
	"strings"
)

var database *sql.DB

func Init(db *sql.DB) {
	database = db
}

func FindByPath(file string) document.ConvertItem {
	row := database.QueryRow(`
		SELECT id, filename, location, duration, status, size, storage_type, created_at, updated_at, c_location, name, description, episode
		FROM convert_items
		WHERE filename = ? AND location = ?
		LIMIT 1
	`,
		filepath.Base(file),
		filepath.Dir(file),
	)

	return _scanRow(row)
}

func FindByFolder(folder string) []document.ConvertItem {
	rows, _ := database.Query(`
		SELECT id, filename, location, duration, status, size, storage_type, created_at, updated_at, c_location, name, description, episode
		FROM convert_items
		WHERE location = ?
	`,
		folder,
	)

	defer rows.Close()

	var data []document.ConvertItem
	for rows.Next() {
		d := _scanRows(rows)
		data = append(data, d)
	}

	return data
}

func FilesToNestedStructure() *types.Folder {
	rows, _ := database.Query(`
		SELECT id, filename, duration, episode, name, description, location
		FROM convert_items
		WHERE status = ?;
	`, int(enum.SUCCESS))

	defer rows.Close()

	var fileRows []types.FileRow

	for rows.Next() {
		var r types.FileRow
		if err := rows.Scan(&r.ID, &r.FileName, &r.Duration, &r.Episode, &r.Name, &r.Description, &r.Location); err != nil {
			logger.Error(err.Error())
		}
		fileRows = append(fileRows, r)
	}

	var root []*types.Folder

	for _, row := range fileRows {
		path := row.Location
		var parts []map[string]interface{}

		if path == "/" {
			parts = append(parts, map[string]interface{}{
				"folder": "",
				"file":   nil,
			})
		} else {
			for _, part := range strings.Split(strings.Trim(path, "/"), "/") {
				parts = append(parts, map[string]interface{}{
					"folder": part,
					"file":   nil,
				})
			}
		}

		file := types.File{
			ID:          row.ID,
			FileName:    row.FileName,
			Duration:    row.Duration,
			Episode:     row.Episode,
			Name:        row.Name,
			Description: row.Description,
		}
		parts[len(parts)-1]["file"] = file

		_addPathToStructure(&root, parts, "", &file)
	}

	if len(root) > 0 {
		return root[0]
	}
	return nil
}

func ChangeStatus(id int64, status int) {
	_, err := database.Exec("UPDATE convert_items SET status=? WHERE id=?", status, id)
	if err != nil {
		panic(err)
	}

	item := GetConvertItemById(id)
	notifier.ChangeStatus(item)
}

func UpdateConvertItem(item *document.ConvertItem) {
	_, err := database.Exec(`
		UPDATE convert_items SET c_location=? WHERE id=?
	`, item.CLocation, item.ID)

	if err != nil {
		panic(err)
	}
}

func UpdateMetadataConvertItem(item *document.ConvertItem) {
	_, err := database.Exec(`
		UPDATE convert_items SET name=?, description=?, episode=? WHERE id=?
	`, item.Name, item.Description, item.Episode, item.ID)

	if err != nil {
		panic(err)
	}
}

func GetConvertItemById(id int64) document.ConvertItem {
	row := database.QueryRow(`
		SELECT id, filename, location, duration, status, size, storage_type, created_at, updated_at, c_location, name, description, episode
		FROM convert_items
		WHERE id = ?
		ORDER BY updated_at DESC
		LIMIT 1
	`, id)

	return _scanRow(row)
}

func DeleteConvertItems(ids []int64) {
	if len(ids) == 0 {
		return
	}

	placeholders := make([]string, len(ids))
	args := make([]interface{}, len(ids))
	for i, id := range ids {
		placeholders[i] = "?"
		args[i] = id
	}

	query := fmt.Sprintf("DELETE FROM convert_items WHERE id IN (%s)", strings.Join(placeholders, ", "))

	_, err := database.Exec(query, args...)
	if err != nil {
		panic(err)
	}
}

func GetSubfolders(db *sql.DB, location string) ([]string, error) {
	query := `
		SELECT DISTINCT
			CASE
				WHEN INSTR(SUBSTR(location, LENGTH(?) + 2), '/') > 0 THEN
					LTRIM(SUBSTR(location, LENGTH(?) + 1, INSTR(SUBSTR(location, LENGTH(?) + 1), '/') - 1), '/')
				ELSE
					LTRIM(SUBSTR(location, LENGTH(?) + 1), '/')
				END AS subfolder
		FROM convert_items
		WHERE location LIKE ? AND location != ?
	`

	likePath := location
	if likePath != "/" && !strings.HasSuffix(likePath, "/") {
		likePath += "/"
	}

	rows, err := db.Query(query,
		location, location, location, location,
		likePath+"%", location,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var subfolders []string
	for rows.Next() {
		var subfolder string
		if err := rows.Scan(&subfolder); err != nil {
			return nil, err
		}
		subfolders = append(subfolders, subfolder)
	}
	return subfolders, nil
}

func FindConvertItemByFilenameAndLocation(filename string, location string) document.ConvertItem {
	row := database.QueryRow(`
		SELECT id, filename, location, duration, status, size, storage_type, created_at, updated_at, c_location, name, description, episode
		FROM convert_items
		WHERE filename = ?
		AND location = ?
		ORDER BY updated_at DESC
		LIMIT 1
	`, filename, location)

	return _scanRow(row)
}

func FindConvertItemsByStatus(status int) []document.ConvertItem {
	rows, err := database.Query(`
		SELECT id, filename, location, duration, status, size, storage_type, created_at, updated_at, c_location, name, description, episode
		FROM convert_items
		WHERE status = ?
		ORDER BY created_at ASC
	`, status)

	if err != nil {
		logger.Error("Error querying convert items by status: %v", err)
		return []document.ConvertItem{}
	}
	defer rows.Close()

	var data []document.ConvertItem
	for rows.Next() {
		d := _scanRows(rows)
		data = append(data, d)
	}

	return data
}

func CreateConvertItem(convertItem document.ConvertItem) int64 {
	result, err := database.Exec(`
		INSERT INTO convert_items
		    (filename, name, location, duration, status, size, storage_type, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
	`,
		convertItem.Filename,
		convertItem.Name,
		convertItem.Location,
		convertItem.Duration,
		convertItem.Status,
		convertItem.Size,
		convertItem.StorageType,
		convertItem.CreatedAt,
		convertItem.UpdatedAt,
	)

	if err != nil {
		panic(err)
	}

	id, _ := result.LastInsertId()

	return id
}

func ListConvertItems(db *sql.DB, pagination types.Pagination) types.ConvertItemListResult {
	var total int
	db.QueryRow(`SELECT COUNT(*) FROM convert_items`).Scan(&total)

	rows, err := db.Query(`
		SELECT id, filename, location, duration, status, size, storage_type, created_at, updated_at, c_location, name, description, episode
		FROM convert_items
		ORDER BY updated_at DESC
		LIMIT ?
		OFFSET ?
	`, pagination.Limit, (pagination.Page-1)*pagination.Limit)
	if err != nil {
		panic(err)
	}
	defer rows.Close()

	var data []document.ConvertItem
	for rows.Next() {
		d := _scanRows(rows)
		data = append(data, d)
	}

	return types.ConvertItemListResult{
		Items:      data,
		TotalItems: total,
	}
}

func ConvertItemsByFolder(db *sql.DB, location string) []document.ConvertItem {
	rows, err := db.Query(`
		SELECT id, filename, location, duration, status, size, storage_type, created_at, updated_at, c_location, name, description, episode
		FROM convert_items
		WHERE location = ?
		ORDER BY updated_at DESC
	`, location)
	if err != nil {
		panic(err)
	}
	defer rows.Close()

	var data []document.ConvertItem
	for rows.Next() {
		d := _scanRows(rows)
		data = append(data, d)
	}

	return data
}

func Schema() types.TableSchema {
	return types.TableSchema{
		TableName: "convert_items",
		Columns: []string{
			"id INTEGER PRIMARY KEY AUTOINCREMENT",
			"location TEXT NOT NULL",
			"filename TEXT NOT NULL",
			"status NUMBER NOT NULL",
			"size NUMBER NOT NULL",
			"c_location TEXT DEFAULT NULL",
			"duration NUMBER DEFAULT NULL",
			"name TEXT DEFAULT NULL",
			"description TEXT DEFAULT NULL",
			"episode TEXT DEFAULT NULL",
			"created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP",
			"updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP",
			"storage_type NUMBER DEFAULT NULL",
		},
	}
}

func _scanRows(rows *sql.Rows) document.ConvertItem {
	var d document.ConvertItem

	err := rows.Scan(&d.ID, &d.Filename, &d.Location, &d.Duration, &d.Status, &d.Size, &d.StorageType, &d.CreatedAt, &d.UpdatedAt, &d.CLocation, &d.Name, &d.Description, &d.Episode)
	if err != nil {
		panic(err)
	}

	return d
}

func _scanRow(row *sql.Row) document.ConvertItem {
	var d document.ConvertItem

	err := row.Scan(&d.ID, &d.Filename, &d.Location, &d.Duration, &d.Status, &d.Size, &d.StorageType, &d.CreatedAt, &d.UpdatedAt, &d.CLocation, &d.Name, &d.Description, &d.Episode)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return d
		}
		panic(err)
	}

	return d
}

func _addPathToStructure(current *[]*types.Folder, parts []map[string]interface{}, currentPath string, file *types.File) {
	if len(parts) == 0 {
		return
	}

	currentFolder := parts[0]["folder"].(string)
	newPath := currentPath + "/" + currentFolder

	var existing *types.Folder
	for _, f := range *current {
		if f.Folder == currentFolder {
			existing = f
			break
		}
	}

	if existing == nil {
		existing = &types.Folder{
			Folder:  currentFolder,
			Path:    newPath,
			Files:   []types.File{},
			Folders: []*types.Folder{},
		}
		if parts[0]["file"] != nil && file != nil {
			existing.Files = append(existing.Files, *file)
		}
		*current = append(*current, existing)
	} else {
		if parts[0]["file"] != nil && file != nil {
			existing.Files = append(existing.Files, *file)
		}
	}

	_addPathToStructure(&existing.Folders, parts[1:], newPath, file)
}
