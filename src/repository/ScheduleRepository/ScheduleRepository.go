package ScheduleRepository

import (
	"database/sql"
	"encoding/json"
	"scheduler-desktop/src/document"
	"scheduler-desktop/src/types"
	"time"
)

func GetSchedule(db *sql.DB, id int64) document.Schedule {
	row := db.QueryRow(`
		SELECT id, updated_at, name, icon, timezone, short_id, output_rtp_url, ads, channels, regular_days, special_days, fillers
		FROM schedules
		WHERE id = ?
	`, id)

	var d document.Schedule
	var ads string
	var channels string
	var regularDays string
	var specialDays string
	var fillers string

	err := row.Scan(&d.ID, &d.UpdatedAt, &d.Name, &d.Icon, &d.Timezone, &d.ShortID, &d.OutputRtpUrl, &ads, &channels, &regularDays, &specialDays, &fillers)
	if err != nil {
		panic(err)
	}
	_ = json.Unmarshal([]byte(ads), &d.Ads)
	_ = json.Unmarshal([]byte(channels), &d.Channels)
	_ = json.Unmarshal([]byte(regularDays), &d.RegularDays)
	_ = json.Unmarshal([]byte(specialDays), &d.SpecialDays)
	_ = json.Unmarshal([]byte(fillers), &d.Fillers)

	return d
}

func UpdateSchedule(db *sql.DB, schedule document.Schedule) {
	_, err := db.Exec(`
		UPDATE schedules SET updated_at = ?, name = ?, icon = ?, timezone = ?, output_rtp_url = ?, ads = ?, channels = ?, regular_days = ?, special_days = ?, fillers = ?
		WHERE id = ?
	`,
		time.Now().Format(time.RFC3339),
		schedule.Name,
		schedule.Icon,
		schedule.Timezone,
		schedule.OutputRtpUrl,
		_normalize(schedule.Ads),
		_normalize(schedule.Channels),
		_normalize(schedule.RegularDays),
		_normalize(schedule.SpecialDays),
		_normalize(schedule.Fillers),
		schedule.ID,
	)

	if err != nil {
		panic(err)
	}
}

func CreateSchedule(db *sql.DB, schedule document.Schedule) int64 {
	result, err := db.Exec(`
		INSERT INTO schedules
		    (name, icon, timezone, short_id, output_rtp_url, ads, channels, regular_days, special_days, fillers)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`,
		schedule.Name,
		schedule.Icon,
		schedule.Timezone,
		schedule.ShortID,
		schedule.OutputRtpUrl,
		_normalize(schedule.Ads),
		_normalize(schedule.Channels),
		_normalize(schedule.RegularDays),
		_normalize(schedule.SpecialDays),
		_normalize(schedule.Fillers),
	)

	if err != nil {
		panic(err)
	}

	id, _ := result.LastInsertId()

	return id
}

func ListSchedules(db *sql.DB, pagination types.Pagination) types.ScheduleListResult {
	var total int
	db.QueryRow(`SELECT COUNT(*) FROM schedules`).Scan(&total)

	rows, err := db.Query(`
		SELECT id, updated_at, name, icon, timezone, short_id, output_rtp_url, ads, channels, regular_days, special_days, fillers
		FROM schedules
		ORDER BY updated_at DESC
		LIMIT ?
		OFFSET ?
	`, pagination.Limit, (pagination.Page-1)*pagination.Limit)
	if err != nil {
		panic(err)
	}
	defer rows.Close()

	var data []document.Schedule
	for rows.Next() {
		var d document.Schedule
		var ads string
		var channels string
		var regularDays string
		var specialDays string
		var fillers string

		err = rows.Scan(&d.ID, &d.UpdatedAt, &d.Name, &d.Icon, &d.Timezone, &d.ShortID, &d.OutputRtpUrl, &ads, &channels, &regularDays, &specialDays, &fillers)
		if err != nil {
			panic(err)
		}
		_ = json.Unmarshal([]byte(ads), &d.Ads)
		_ = json.Unmarshal([]byte(channels), &d.Channels)
		_ = json.Unmarshal([]byte(regularDays), &d.RegularDays)
		_ = json.Unmarshal([]byte(specialDays), &d.SpecialDays)
		_ = json.Unmarshal([]byte(fillers), &d.Fillers)

		data = append(data, d)
	}

	return types.ScheduleListResult{
		Items:      data,
		TotalItems: total,
	}
}

func DeleteSchedule(db *sql.DB, id int) {
	_, err := db.Exec(`DELETE FROM schedules WHERE id = ?`, id)
	if err != nil {
		panic(err)
	}
}

func Schema() types.TableSchema {
	return types.TableSchema{
		TableName: "schedules",
		Columns: []string{
			"id INTEGER PRIMARY KEY AUTOINCREMENT",
			"name TEXT NOT NULL",
			"icon TEXT DEFAULT NULL",
			"timezone TEXT NOT NULL",
			"created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP",
			"updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP",
			"short_id TEXT NOT NULL",
			"autosave BOOLEAN NOT NULL DEFAULT FALSE",
			"output_rtp_url TEXT DEFAULT NULL",
			"ads TEXT DEFAULT NULL",
			"channels TEXT DEFAULT NULL",
			"regular_days TEXT DEFAULT NULL",
			"special_days TEXT DEFAULT NULL",
			"fillers TEXT DEFAULT NULL",
		},
	}
}

func _normalize(object any) string {
	jsonData, err := json.Marshal(object)
	if err != nil {
		panic(err)
	}

	return string(jsonData)
}
