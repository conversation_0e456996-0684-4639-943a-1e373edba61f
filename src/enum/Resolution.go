package enum

type Resolution int

const (
	R384x216B230k Resolution = iota
	R384x216B440k
	R512x288B700k
	R720x404B1200k
	R1280x720B2500k
	R1920x1080B5000k
)

func (d Resolution) String() string {
	return [...]string{
		"384x216_230k",
		"384x216_440k",
		"512x288_700k",
		"720x404_1200k",
		"1280x720_2500k",
		"1920x1080_5000k",
	}[d]
}

func HLSResolutions() []string {
	return []string{
		R384x216B230k.String(),
		R384x216B440k.String(),
		R512x288B700k.String(),
		R720x404B1200k.String(),
		R1280x720B2500k.String(),
		R1920x1080B5000k.String(),
	}
}
