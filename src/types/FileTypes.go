package types

import "database/sql"

type File struct {
	ID          string         `json:"id"`
	FileName    string         `json:"fileName"`
	Duration    float64        `json:"duration"`
	Episode     sql.NullString `json:"episode"`
	Name        string         `json:"name"`
	Description sql.NullString `json:"description"`
}

type Folder struct {
	Folder  string    `json:"folder"`
	Path    string    `json:"path"`
	Files   []File    `json:"files"`
	Folders []*Folder `json:"folders"`
}

type FileRow struct {
	ID          string
	FileName    string
	Duration    float64
	Episode     sql.NullString
	Name        string
	Description sql.NullString
	Location    string
}
