package helper

import (
	"path/filepath"
	"regexp"
	"strings"
)

func NormalizeBucketName(name string) string {
	return strings.Replace(name, " ", "-", -1)
}

func NormalizeFilePath(filePath string) string {
	return strings.TrimPrefix(filePath, "/")
}

func FolderNameByFilename(filename string) string {
	extension := filepath.Ext(filename)
	fileNameWithoutExt := filename[0 : len(filename)-len(extension)]
	reg, _ := regexp.Compile(`[^\w]`)

	return reg.ReplaceAllString(fileNameWithoutExt, "_")
}
