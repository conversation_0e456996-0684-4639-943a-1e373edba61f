package helper

import (
	"os/exec"
	"scheduler-desktop/src/service/logger"
	"strconv"
	"strings"
)

func GetCountGPU() int {
	countGPU := 1
	isCuda := true
	cmd := exec.Command("nvidia-smi")
	if err := cmd.Run(); err != nil {
		isCuda = false
		logger.Warn("NVIDIA SMI is not found")
	}

	if isCuda {
		cmdCheckGpu := "nvidia-smi --query-gpu=name --format=csv,noheader | wc -l"
		out, err := exec.Command("bash", "-c", cmdCheckGpu).Output()
		if err != nil {
			logger.Error("Failed to check GPU with command: " + cmdCheckGpu + ". Error: " + err.Error())
		} else {
			count := strings.TrimSpace(string(out))
			result, err := strconv.Atoi(count)
			if err != nil {
				logger.Error("Failed to check GPU with out: " + count)
			} else {
				countGPU = result
			}
		}
	}

	return countGPU
}
