package document

type Recorder struct {
	ID          int     `json:"id"`
	Name        string  `json:"name"`
	Input       string  `json:"input"`
	RtpUrlID    int64   `json:"rtp_url_id"`
	Duration    string  `json:"duration"`
	Status      string  `json:"status"`
	VCodec      string  `json:"VCodec"`
	ACodec      string  `json:"ACodec"`
	Resolution  string  `json:"resolution"`
	FPS         float64 `json:"FPS"`
	SampleRate  int     `json:"sampleRate"`
	VBitrate    int     `json:"VBitrate"`
	ABitrate    int     `json:"ABitrate"`
	MaxVBitrate int     `json:"MaxVBitrate"`
}
