package document

type Tv struct {
	XMLName           string    `xml:"tv"`
	SourceInfoUrl     string    `xml:"source-info-url,attr"`
	SourceInfoName    string    `xml:"source-info-name,attr"`
	GeneratorInfoName string    `xml:"generator-info-name,attr"`
	Channel           Channel   `xml:"channel"`
	Program           []Program `xml:"programme"`
}

type Channel struct {
	XMLName     string `xml:"channel"`
	Id          string `xml:"id,attr"`
	DisplayName string `xml:"display-name"`
	Icon        string `xml:"icon"`
}

type Program struct {
	XMLName       string   `xml:"programme"`
	Start         string   `xml:"start,attr"`
	Stop          string   `xml:"stop,attr"`
	Channel       string   `xml:"channel,attr"`
	Title         string   `xml:"title"`
	SubTitle      SubTitle `xml:"sub-title"`
	Desc          Desc     `xml:"desc"`
	Date          string   `xml:"date"`
	EpisodeNumber any      `xml:"episode-num,omitempty"`
}

type SubTitle struct {
	Lang  string `xml:"lang,attr"`
	Value string `xml:",chardata"`
}

type Desc struct {
	Lang  string `xml:"lang,attr"`
	Value string `xml:",chardata"`
}

type EpisodeNumber struct {
	Value  any `xml:",chardata"`
	System any `xml:"system,attr"`
}
