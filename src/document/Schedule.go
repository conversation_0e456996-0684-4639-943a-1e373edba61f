package document

type Folder struct {
	Path string `json:"path"`
}

type Item struct {
	Start       string   `json:"start"`
	End         string   `json:"end"`
	Type        string   `json:"type"`
	Connection  string   `json:"connection"`
	Link        string   `json:"link"`
	Name        string   `json:"name"`
	Description string   `json:"description"`
	Port        string   `json:"port"`
	Mode        string   `json:"mode"`
	ExpireDate  string   `json:"expire_date"`
	ExpireTime  string   `json:"expire_time"`
	Folders     []Folder `json:"folders"`
	Files       []Folder `json:"files"`
}

type Day struct {
	Name  string `json:"name"`
	Date  string `json:"date"`
	Items []Item `json:"items"`
}

type AdLink struct {
	Url     string `json:"url"`
	Channel int    `json:"channel"`
}

type Ads struct {
	Links   []AdLink `json:"links"`
	Folders []Folder `json:"folders"`
	Files   []Folder `json:"files"`
}

type Fillers struct {
	Folders []Folder `json:"folders"`
	Files   []Folder `json:"files"`
}

type Schedule struct {
	ID          int64         `json:"id"`
	Name        string        `json:"name"`
	Icon        string        `json:"icon"`
	Timezone    string        `json:"timezone"`
	CreatedAt   string        `json:"created_at"`
	UpdatedAt   string        `json:"updated_at"`
	ShortID     string        `json:"short_id"`
	Autosave    bool          `json:"autosave"`
	OutputRtpUrl string        `json:"output_rtp_url"`
	Ads         Ads           `json:"ads"`
	Channels    []interface{} `json:"channels"`
	RegularDays []Day         `json:"regular_days"`
	SpecialDays []Day         `json:"special_days"`
	Fillers     Fillers       `json:"fillers"`
}
