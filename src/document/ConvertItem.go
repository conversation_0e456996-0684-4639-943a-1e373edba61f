package document

import (
	"database/sql"
)

type ConvertItem struct {
	ID          int64          `json:"id"`
	Location    string         `json:"location"`
	Filename    string         `json:"filename"`
	CLocation   sql.NullString `json:"c_location"`
	Status      int            `json:"status"`
	Size        int64          `json:"size"`
	Duration    float64        `json:"duration"`
	Name        string         `json:"name"`
	Description sql.NullString `json:"description"`
	Episode     sql.NullString `json:"episode"`
	CreatedAt   string         `json:"created_at"`
	UpdatedAt   string         `json:"updated_at"`
	StorageType int            `json:"storage_type"`
}
