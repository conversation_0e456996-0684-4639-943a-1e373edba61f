package document

import "database/sql"

type Connection struct {
	Type       string `json:"type"`
	Link       string `json:"link"`
	Port       string `json:"port"`
	Mode       string `json:"mode"`
	ExpireDate string `json:"expire_date"`
	ExpireTime string `json:"expire_time"`
}

type File struct {
	FileId   int64          `json:"file_id"`
	Folder   string         `json:"folder"`
	Filename string         `json:"filename"`
	Episode  sql.NullString `json:"episode"`
}

type Element struct {
	Start       string     `json:"start"`
	End         string     `json:"end"`
	Title       string     `json:"title"`
	Description string     `json:"description"`
	Type        string     `json:"type"`
	Connection  Connection `json:"connection"`
	File        File       `json:"file"`
}

type Guide struct {
	ID         int       `json:"id"`
	ScheduleId int64     `json:"schedule_id"`
	Elements   []Element `json:"elements"`
	UpdatedAt  string    `json:"updated_at"`
}
