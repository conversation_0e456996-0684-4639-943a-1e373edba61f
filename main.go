package main

import (
	"embed"
	"runtime"

	"github.com/wailsapp/wails/v2"
	"github.com/wailsapp/wails/v2/pkg/menu"
	"github.com/wailsapp/wails/v2/pkg/menu/keys"
	"github.com/wailsapp/wails/v2/pkg/options"
	"github.com/wailsapp/wails/v2/pkg/options/assetserver"
	rt "github.com/wailsapp/wails/v2/pkg/runtime"
)

//go:embed all:frontend/dist
var assets embed.FS

func main() {
	// Create an instance of the app structure
	app := NewApp()

	AppMenu := menu.NewMenu()
	if runtime.GOOS == "darwin" {
		AppMenu.Append(menu.AppMenu())  // On macOS platform, this must be done right after `NewMenu()`
		AppMenu.Append(menu.EditMenu()) // On macOS platform, EditMenu should be appended to enable Cmd+C, Cmd+V, Cmd+Z... shortcuts
	}

	_addViewMenu(AppMenu, app)
	_addNavigateMenu(AppMenu, app)

	// Create application with options
	err := wails.Run(&options.App{
		Title:     "Showfer Scheduler",
		MinWidth:  1024,
		MinHeight: 768,
		Width:     1024,
		Height:    768,
		Menu:      AppMenu,
		AssetServer: &assetserver.Options{
			Assets: assets,
		},
		BackgroundColour: &options.RGBA{R: 27, G: 38, B: 54, A: 1},
		OnStartup:        app.startup,
		Bind: []any{
			app,
		},
		DragAndDrop: &options.DragAndDrop{
			EnableFileDrop:     true,
			DisableWebViewDrop: true,
		},
	})

	if err != nil {
		println("Error:", err.Error())
	}
}

func _addViewMenu(AppMenu *menu.Menu, app *App) {
	viewMenu := AppMenu.AddSubmenu("View")

	viewMenu.AddText("Toggle Fullscreen", keys.CmdOrCtrl("f"), func(_ *menu.CallbackData) {
		if app.ctx != nil {
			isFull := rt.WindowIsFullscreen(app.ctx)
			if isFull {
				rt.WindowUnfullscreen(app.ctx)
			} else {
				rt.WindowFullscreen(app.ctx)
			}
		}
	})
}

func _addNavigateMenu(AppMenu *menu.Menu, app *App) {
	NavigateMenu := AppMenu.AddSubmenu("Navigate")
	NavigateMenu.AddText("Home", keys.CmdOrCtrl("h"), func(_ *menu.CallbackData) {
		if app.ctx != nil {
			rt.EventsEmit(app.ctx, "navigate:home")
		}
	})
	NavigateMenu.AddSeparator()
	NavigateMenu.AddText("Scheduler", keys.CmdOrCtrl("s"), func(_ *menu.CallbackData) {
		if app.ctx != nil {
			rt.EventsEmit(app.ctx, "navigate:scheduler")
		}
	})
	//NavigateMenu.AddText("Transcoder", keys.CmdOrCtrl("t"), func(_ *menu.CallbackData) {
	//	if app.ctx != nil {
	//		rt.EventsEmit(app.ctx, "navigate:transcoder")
	//	}
	//})
	NavigateMenu.AddText("File Manager", keys.CmdOrCtrl("m"), func(_ *menu.CallbackData) {
		if app.ctx != nil {
			rt.EventsEmit(app.ctx, "navigate:file-manager")
		}
	})

	NavigateMenu.AddText("Input Feed", keys.CmdOrCtrl("l"), func(_ *menu.CallbackData) {
		if app.ctx != nil {
			rt.EventsEmit(app.ctx, "navigate:recorder")
		}
	})
	//NavigateMenu.AddSeparator()
	//NavigateMenu.AddText("Settings", keys.CmdOrCtrl(","), func(_ *menu.CallbackData) {
	//	if app.ctx != nil {
	//		rt.EventsEmit(app.ctx, "navigate:settings")
	//	}
	//})
}
