#!/bin/bash

# Traffiq Server Startup Script
# This script ensures only one instance runs at a time

echo "Starting Traffiq Server..."

# Function to kill existing instances
kill_existing() {
    echo "Checking for existing traffiq-server processes..."
    
    # Kill existing traffiq-server processes
    pkill -f "traffiq-server" 2>/dev/null && echo "Killed existing traffiq-server processes"
    
    # Kill anything using port 8080
    lsof -ti:8080 | xargs kill -9 2>/dev/null && echo "Freed port 8080"
    
    # Wait for cleanup
    sleep 2
}

# Check if server is already running
if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "Port 8080 is already in use. Cleaning up..."
    kill_existing
fi

# Change to the backend directory
cd "$(dirname "$0")/web/backend" || exit 1

# Start the server
echo "Starting traffiq-server..."
exec ../../bin/traffiq-server 