#!/bin/bash

# Traffic Blocker Script for Traffiq Backup Servers
# This script provides manual control over stream traffic blocking

SCRIPT_NAME="Traffiq Traffic Blocker"
DB_PATH="./data/showfer.db"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}============================================${NC}"
    echo -e "${BLUE}        ${SCRIPT_NAME}${NC}"
    echo -e "${BLUE}============================================${NC}"
}

print_status() {
    echo -e "${YELLOW}📊 Current Status:${NC}"
    
    # Check server type
    if [ -f "/tmp/backup" ]; then
        echo -e "   Server Type: ${RED}BACKUP${NC}"
    else
        echo -e "   Server Type: ${GREEN}PRIMARY${NC}"
    fi
    
    # Check for blocked rules
    BLOCKED_RULES=$(iptables -L OUTPUT -n | grep "DROP.*udp" | wc -l)
    if [ "$BLOCKED_RULES" -gt 0 ]; then
        echo -e "   Traffic Status: ${RED}BLOCKED${NC} ($BLOCKED_RULES rules)"
    else
        echo -e "   Traffic Status: ${GREEN}UNBLOCKED${NC}"
    fi
    
    echo
}

get_scheduler_outputs() {
    echo -e "${BLUE}📡 Getting scheduler outputs from database...${NC}"
    
    # Use sqlite3 to get output URLs from schedules
    if [ -f "$DB_PATH" ]; then
        OUTPUTS=$(sqlite3 "$DB_PATH" "SELECT output_url FROM schedules WHERE output_url IS NOT NULL AND output_url != '';" 2>/dev/null)
        
        if [ -n "$OUTPUTS" ]; then
            echo -e "${GREEN}Found scheduler outputs:${NC}"
            echo "$OUTPUTS" | while read -r output; do
                echo "   • $output"
            done
            echo
            return 0
        else
            echo -e "${YELLOW}No scheduler outputs found in database${NC}"
            echo
            return 1
        fi
    else
        echo -e "${RED}Database not found at: $DB_PATH${NC}"
        echo
        return 1
    fi
}

block_output() {
    local output_url="$1"
    
    # Parse RTP URL (format: rtp://ip:port)
    if [[ $output_url =~ ^rtp://([^:]+):([0-9]+) ]]; then
        local host="${BASH_REMATCH[1]}"
        local port="${BASH_REMATCH[2]}"
        
        # Check if it's multicast (224.x.x.x to 239.x.x.x)
        if [[ $host =~ ^(22[4-9]|23[0-9])\. ]]; then
            echo -e "   🚫 Blocking multicast traffic to ${RED}$host:$port${NC}"
        else
            echo -e "   🚫 Blocking unicast traffic to ${RED}$host:$port${NC}"
        fi
        
        # Add iptables rule to block OUTPUT traffic
        iptables -A OUTPUT -d "$host" -p udp --dport "$port" -j DROP 2>/dev/null
        
        if [ $? -eq 0 ]; then
            echo -e "   ✅ Successfully blocked $host:$port"
        else
            echo -e "   ❌ Failed to block $host:$port"
        fi
    else
        echo -e "   ❌ Invalid RTP URL format: $output_url"
    fi
}

unblock_output() {
    local output_url="$1"
    
    # Parse RTP URL (format: rtp://ip:port)
    if [[ $output_url =~ ^rtp://([^:]+):([0-9]+) ]]; then
        local host="${BASH_REMATCH[1]}"
        local port="${BASH_REMATCH[2]}"
        
        echo -e "   ✅ Unblocking traffic to ${GREEN}$host:$port${NC}"
        
        # Remove iptables rule
        iptables -D OUTPUT -d "$host" -p udp -m udp --dport "$port" -j DROP 2>/dev/null
        
        if [ $? -eq 0 ]; then
            echo -e "   ✅ Successfully unblocked $host:$port"
        else
            echo -e "   ⚠️  Rule for $host:$port was not found (may already be removed)"
        fi
    else
        echo -e "   ❌ Invalid RTP URL format: $output_url"
    fi
}

block_all_traffic() {
    echo -e "${RED}🔒 Blocking all stream traffic...${NC}"
    
    if get_scheduler_outputs; then
        # Get outputs and block each one
        OUTPUTS=$(sqlite3 "$DB_PATH" "SELECT output_url FROM schedules WHERE output_url IS NOT NULL AND output_url != '';" 2>/dev/null)
        
        if [ -n "$OUTPUTS" ]; then
            echo "$OUTPUTS" | while read -r output; do
                block_output "$output"
            done
        fi
    fi
    
    echo -e "${GREEN}✅ Traffic blocking completed${NC}"
    echo
}

unblock_all_traffic() {
    echo -e "${GREEN}🔓 Unblocking all stream traffic...${NC}"
    
    if get_scheduler_outputs; then
        # Get outputs and unblock each one
        OUTPUTS=$(sqlite3 "$DB_PATH" "SELECT output_url FROM schedules WHERE output_url IS NOT NULL AND output_url != '';" 2>/dev/null)
        
        if [ -n "$OUTPUTS" ]; then
            echo "$OUTPUTS" | while read -r output; do
                unblock_output "$output"
            done
        fi
    fi
    
    echo -e "${GREEN}✅ Traffic unblocking completed${NC}"
    echo
}

show_iptables_rules() {
    echo -e "${BLUE}📋 Current iptables OUTPUT rules:${NC}"
    echo
    iptables -L OUTPUT -n --line-numbers | grep -E "(Chain|target|DROP.*udp)" || echo -e "${YELLOW}No blocking rules found${NC}"
    echo
}

show_usage() {
    cat << EOF
${BLUE}Usage:${NC}
    $0 [command]

${BLUE}Commands:${NC}
    status      - Show current status
    list        - List scheduler outputs from database
    block       - Block all stream traffic (backup mode)
    unblock     - Unblock all stream traffic (primary mode)
    rules       - Show current iptables rules
    help        - Show this help message

${BLUE}Examples:${NC}
    # Block all stream traffic on backup server
    $0 block

    # Unblock all stream traffic when becoming primary
    $0 unblock

    # Check current status
    $0 status

EOF
}

# Main script logic
case "${1:-status}" in
    "status")
        print_header
        print_status
        ;;
    "list")
        print_header
        get_scheduler_outputs
        ;;
    "block")
        print_header
        print_status
        block_all_traffic
        ;;
    "unblock")
        print_header
        print_status
        unblock_all_traffic
        ;;
    "rules")
        print_header
        show_iptables_rules
        ;;
    "help"|"-h"|"--help")
        print_header
        show_usage
        ;;
    *)
        echo -e "${RED}Unknown command: $1${NC}"
        echo
        show_usage
        exit 1
        ;;
esac 