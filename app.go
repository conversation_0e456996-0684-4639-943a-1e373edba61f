package main

import (
	"context"
	"database/sql"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"scheduler-desktop/src/document"
	"scheduler-desktop/src/repository"
	"scheduler-desktop/src/repository/ConvertItemRepository"
	"scheduler-desktop/src/repository/GuideRepository"
	"scheduler-desktop/src/repository/RecorderRepository"
	"scheduler-desktop/src/repository/RtpUrlRepository"
	"scheduler-desktop/src/repository/ScheduleRepository"
	"scheduler-desktop/src/service/converter"
	generator "scheduler-desktop/src/service/epg"
	"scheduler-desktop/src/service/logger"
	"scheduler-desktop/src/service/notifier"
	"scheduler-desktop/src/service/queue"
	recorder_service "scheduler-desktop/src/service/recorder"
	"scheduler-desktop/src/service/transcoder"
	"scheduler-desktop/src/service/uploader"
	"scheduler-desktop/src/service/uploader/storage/LocalStorage"
	"scheduler-desktop/src/types"
	"strconv"
	"strings"
	"time"

	"github.com/wailsapp/wails/v2/pkg/runtime"
	_ "modernc.org/sqlite"
)

// App struct
type App struct {
	ctx       context.Context
	db        *sql.DB
	filesPath string
}

// NewApp creates a new App application struct
func NewApp() *App {
	return &App{}
}

// startup is called when the app starts. The context is saved
// so we can call the runtime methods
func (a *App) startup(ctx context.Context) {
	a.ctx = ctx

	logger.Init(a.ctx)
	notifier.Init(a.ctx)

	ffmpegBinary, ffprobeBinary, err := ExtractFFmpegBinary()
	if err != nil {
		panic(err)
	}
	converter.Init(ffmpegBinary, ffprobeBinary)

	// init database
	db, err := sql.Open("sqlite", "./showfer.db")
	if err != nil {
		panic(err)
	}

	a.db = db

	// Initialize the recorder status manager
	recorder_service.InitStatusManager(a.ctx, a.db)
	currentDir, err := os.Getwd()
	if err != nil {
		panic("Failed to get working directory: " + err.Error())
	}
	a.filesPath = filepath.Join(currentDir, "data")

	a._createTables()

	// Init EPG generator
	generator.Init(a.db)
	generator.InitXML(a.filesPath)
	err = os.MkdirAll(filepath.Join(a.filesPath, transcoder.EPGDirectory), 0755)
	if err != nil {
		panic("Failed to create folder for epg files: " + err.Error())
	}

	// init repository
	ConvertItemRepository.Init(a.db)
	RecorderRepository.Init(a.db)

	// init uploader

	err = os.MkdirAll(a.filesPath, 0755)
	if err != nil {
		panic("Failed to create folder for files: " + err.Error())
	}

	err = os.MkdirAll(filepath.Join(a.filesPath, transcoder.HLSDirectory), 0755)
	if err != nil {
		panic("Failed to create folder for hls files: " + err.Error())
	}

	// additional local server for preview video files
	go func() {
		fs := http.FileServer(http.Dir("data"))
		http.Handle("/data/", http.StripPrefix("/data/", fs))
		http.ListenAndServe(":34567", nil)
	}()

	// Run in background generation EPG XML
	_startPeriodicTask(db)

	// Initialize queue manager
	queueManager := queue.GetInstance()
	queueManager.Init(a.filesPath)
}

func (a *App) CreateSchedule(schedule document.Schedule) int64 {
	return ScheduleRepository.CreateSchedule(a.db, schedule)
}

func (a *App) UpdateSchedule(schedule document.Schedule) document.Guide {
	ScheduleRepository.UpdateSchedule(a.db, schedule)

	guide, _ := GuideRepository.GetGuide(a.db, schedule.ID)

	return guide
}

func (a *App) GetSchedule(id int64) document.Schedule {
	return ScheduleRepository.GetSchedule(a.db, id)
}

func (a *App) GetGuide(scheduleId int64) document.Guide {
	guide, _ := GuideRepository.GetGuide(a.db, scheduleId)

	return guide
}

func (a *App) ListSchedules(pagination types.Pagination) types.ScheduleListResult {
	return ScheduleRepository.ListSchedules(a.db, pagination)
}

func (a *App) DeleteSchedule(id int) {
	ScheduleRepository.DeleteSchedule(a.db, id)
}

func (a *App) ListConvertItems(pagination types.Pagination) types.ConvertItemListResult {
	return ConvertItemRepository.ListConvertItems(a.db, pagination)
}

func (a *App) ConvertItemsByFolder(location string) []document.ConvertItem {
	return ConvertItemRepository.ConvertItemsByFolder(a.db, location)
}

func (a *App) GetSubfolders(location string) []string {
	folders, err := ConvertItemRepository.GetSubfolders(a.db, location)
	if err != nil {
		panic(err)
	}

	return folders
}

func (a *App) FilesToNestedStructure() *types.Folder {
	return ConvertItemRepository.FilesToNestedStructure()
}

func (a *App) DeleteConvertItems(ids []int64) {
	for _, id := range ids {
		item := ConvertItemRepository.GetConvertItemById(id)
		LocalStorage.DeleteFiles(filepath.Join(item.Location, item.Filename), a.filesPath)
	}

	ConvertItemRepository.DeleteConvertItems(ids)
}

func (a *App) UpdateConvertItems(items []document.ConvertItem) {
	for _, item := range items {
		ConvertItemRepository.UpdateMetadataConvertItem(&item)
	}
}

func (a *App) GenerateGuide(scheduleId int64) {
	schedule := ScheduleRepository.GetSchedule(a.db, scheduleId)
	generator.Generate(schedule, true)
}

func (a *App) GetEPGLink(scheduleShortID string) string {
	return filepath.Join(a.filesPath, transcoder.EPGDirectory, fmt.Sprintf("%s.xml", scheduleShortID))
}

func (a *App) SelectFiles() []string {
	files, err := runtime.OpenMultipleFilesDialog(a.ctx, runtime.OpenDialogOptions{
		Title: "Select Video Files",
		Filters: []runtime.FileFilter{
			{
				DisplayName: "Videos (*.mov;*.mp4)",
				Pattern:     "*.mov;*.mp4",
			},
		},
	})
	if err != nil {
		logger.Error("Failed to open files: " + err.Error())
	}

	return files
}

func (a *App) ProcessFiles(paths []string, location string) {
	uploader.Upload(paths, location, a.filesPath)
}

func (a *App) _createTables() {
	_ = repository.CreateScheduleTable(a.db)
	_ = repository.CreateConvertItemTable(a.db)
	_ = repository.CreateGuideTable(a.db)
	_ = repository.CreateHistoryTable(a.db)
	_ = repository.CreateRtpUrlTable(a.db)
	// Initialize recorder table with RTP URL foreign key
	RecorderRepository.Init(a.db)
}

func _startPeriodicTask(db *sql.DB) {
	ticker := time.NewTicker(4 * time.Hour)

	go func() {
		for {
			_generateGuides(db, 1)
			<-ticker.C // wait next iteration
		}
	}()
}

func _generateGuides(db *sql.DB, page int) {
	limit := 100
	result := ScheduleRepository.ListSchedules(db, types.Pagination{
		Limit: limit,
		Page:  page,
	})

	for _, schedule := range result.Items {
		generator.Generate(schedule, false)
	}

	if result.TotalItems > limit*page {
		page += 1
		_generateGuides(db, page)
	}
}

func (a *App) CreateRecorder(recorder document.Recorder) int64 {
	return RecorderRepository.CreateRecorder(a.db, recorder)
}

func (a *App) UpdateRecorder(recorder document.Recorder) error {
	return RecorderRepository.UpdateRecorder(a.db, recorder)
}

func (a *App) GetRecorders() []document.Recorder {
	return RecorderRepository.GetRecorders(a.db)
}

func (a *App) DeleteRecorder(id int) error {
	return RecorderRepository.DeleteRecorder(a.db, id)
}

func (a *App) StartRecorder(id int) error {
	// First, get the recorder to check if it's not already running
	recorders := RecorderRepository.GetRecorders(a.db)
	var recorder document.Recorder
	found := false

	for _, r := range recorders {
		if r.ID == id {
			recorder = r
			found = true
			break
		}
	}

	if !found {
		return fmt.Errorf("recorder with ID %d not found", id)
	}

	// If the recorder is already running, return an error
	if recorder.Status == "running" {
		return fmt.Errorf("recorder with ID %d is already running", id)
	}

	// Get the recordings directory
	recordingsDir := filepath.Join(a.filesPath, "recordings")

	// Create the recordings directory if it doesn't exist
	if err := os.MkdirAll(recordingsDir, 0755); err != nil {
		return fmt.Errorf("failed to create recordings directory: %v", err)
	}

	// Parse duration string to seconds
	var durationSeconds int

	// Check if the duration is in HH:MM:SS format
	if strings.Contains(recorder.Duration, ":") {
		durationParts := strings.Split(recorder.Duration, ":")
		if len(durationParts) != 3 {
			return fmt.Errorf("invalid duration format: %s, expected HH:MM:SS", recorder.Duration)
		}

		hours, err := strconv.Atoi(durationParts[0])
		if err != nil {
			return fmt.Errorf("invalid hours in duration: %v", err)
		}

		minutes, err := strconv.Atoi(durationParts[1])
		if err != nil {
			return fmt.Errorf("invalid minutes in duration: %v", err)
		}

		seconds, err := strconv.Atoi(durationParts[2])
		if err != nil {
			return fmt.Errorf("invalid seconds in duration: %v", err)
		}

		durationSeconds = hours*3600 + minutes*60 + seconds
	} else {
		// Try to parse as a direct number of seconds
		seconds, err := strconv.Atoi(recorder.Duration)
		if err != nil {
			return fmt.Errorf("invalid duration format: %s, expected either HH:MM:SS or number of seconds", recorder.Duration)
		}
		durationSeconds = seconds
	}

	// Update the status in the database first
	err := RecorderRepository.StartRecorder(a.db, id)
	if err != nil {
		return fmt.Errorf("failed to update recorder status in database: %v", err)
	}

	// Start the recording process
	err = recorder_service.StartRecording(
		recorder.ID,
		recorder.Input,
		recordingsDir,
		durationSeconds,
		recorder.VCodec,
		recorder.ACodec,
		recorder.Resolution,
		fmt.Sprintf("%.2f", recorder.FPS),
		recorder.SampleRate,
		recorder.VBitrate,
		recorder.ABitrate,
		recorder.MaxVBitrate,
	)

	if err != nil {
		// If starting the recording fails, revert the database status
		_ = RecorderRepository.StopRecorder(a.db, id)
		return fmt.Errorf("failed to start recording: %v", err)
	}

	return nil
}

func (a *App) StopRecorder(id int) error {
	// First, get the recorder to check if it's running
	recorders := RecorderRepository.GetRecorders(a.db)
	var recorder document.Recorder
	found := false

	for _, r := range recorders {
		if r.ID == id {
			recorder = r
			found = true
			break
		}
	}

	if !found {
		return fmt.Errorf("recorder with ID %d not found", id)
	}

	// If the recorder is not running, nothing to do
	if recorder.Status != "running" {
		return nil
	}

	// Update the database status first
	err := RecorderRepository.StopRecorder(a.db, id)
	if err != nil {
		return fmt.Errorf("failed to update recorder status in database: %v", err)
	}

	// Try to stop the recording process, but don't fail if it's already stopped
	err = recorder_service.StopRecording(id)
	if err != nil {
		// Check if the error is because the recorder is not active
		if strings.Contains(err.Error(), "no active recorder") {
			// This is fine - the recorder process has already stopped naturally
			logger.Log("Recorder %d is already stopped", id)
		} else {
			// Log other errors but don't fail the operation since we've already updated the database
			logger.Log("Failed to stop recording process: %v", err)
		}
	}

	return nil
}

// GetRecorderStatus returns the status of all active recorders
func (a *App) GetRecorderStatus() []recorder_service.RecorderStatus {
	return recorder_service.GetRecorderStatus()
}

// GetRtpUrls returns all saved RTP URLs
func (a *App) GetRtpUrls() []document.RtpUrl {
	urls, err := RtpUrlRepository.GetAllRtpUrls(a.db)
	if err != nil {
		logger.Error("Failed to get RTP URLs: %v", err)
		return []document.RtpUrl{}
	}
	return urls
}
