package main

import (
	"fmt"
	"os"
	_ "os/exec"
	"path/filepath"
	"runtime"
)

func ExtractFFmpegBinary() (string, string, error) {
	var sourceFFmpegPath string
	var sourceFFprobePath string
	switch runtime.GOOS {
	case "windows":
		sourceFFmpegPath = "bin/ffmpeg/windows/ffmpeg.exe"
		sourceFFprobePath = "bin/ffmpeg/windows/ffprobe.exe"
	case "linux":
		sourceFFmpegPath = "bin/ffmpeg/linux/ffmpeg"
		sourceFFprobePath = "bin/ffmpeg/linux/ffprobe"
	case "darwin":
		sourceFFmpegPath = "bin/ffmpeg/darwin/ffmpeg"
		sourceFFprobePath = "bin/ffmpeg/darwin/ffprobe"
	default:
		return "", "", fmt.Errorf("unsupported OS")
	}

	configDir, err := os.UserConfigDir()
	if err != nil {
		return "", "", err
	}
	targetFFmpegPath := filepath.Join(configDir, "showfer-scheduler", "ffmpeg")
	targetFFprobePath := filepath.Join(configDir, "showfer-scheduler", "ffprobe")
	if runtime.GOOS == "windows" {
		targetFFmpegPath += ".exe"
		targetFFprobePath += ".exe"
	}

	_, err = _extractBinary(targetFFmpegPath, sourceFFmpegPath)
	if err != nil {
		return "", "", err
	}

	_, err = _extractBinary(targetFFprobePath, sourceFFprobePath)
	if err != nil {
		return "", "", err
	}

	return targetFFmpegPath, targetFFprobePath, nil
}

func _extractBinary(targetPath string, sourcePath string) (string, error) {
	var err error
	if _, err = os.Stat(targetPath); err == nil {
		return targetPath, nil
	}

	if err = os.MkdirAll(filepath.Dir(targetPath), 0755); err != nil {
		return "", err
	}

	data, err := embeddedFiles.ReadFile(sourcePath)
	if err != nil {
		return "", err
	}

	err = os.WriteFile(targetPath, data, 0755)
	if err != nil {
		return "", err
	}

	return targetPath, nil
}
